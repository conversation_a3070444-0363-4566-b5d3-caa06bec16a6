#!/usr/bin/env python3
"""
AstrBot SaaS Platform - 安全检查脚本
==================================

功能:
- 代码安全扫描 (bandit)
- 依赖漏洞检查 (safety)
- 配置安全检查
- 敏感信息泄露检测
- 安全报告生成

使用方法:
    python scripts/security_check.py [--fix] [--report]
"""

import os
import sys
import json
import subprocess
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import re

# 脚本配置
SCRIPT_DIR = Path(__file__).parent
PROJECT_ROOT = SCRIPT_DIR.parent
SECURITY_REPORT_DIR = PROJECT_ROOT / "security_reports"


class SecurityChecker:
    """安全检查器"""

    def __init__(self, fix_issues: bool = False, generate_report: bool = True):
        """
        初始化安全检查器

        Args:
            fix_issues: 是否尝试自动修复问题
            generate_report: 是否生成安全报告
        """
        self.fix_issues = fix_issues
        self.generate_report = generate_report
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_issues": 0,
                "critical": 0,
                "high": 0,
                "medium": 0,
                "low": 0,
                "fixed": 0,
            },
            "checks": {},
        }

        # 确保报告目录存在
        if self.generate_report:
            SECURITY_REPORT_DIR.mkdir(exist_ok=True)

    def run_bandit_scan(self) -> Dict[str, Any]:
        """
        运行Bandit代码安全扫描

        Returns:
            Dict: 扫描结果
        """
        print("🔍 运行Bandit代码安全扫描...")

        try:
            # 运行bandit扫描
            cmd = [
                "python",
                "-m",
                "bandit",
                "-r",
                str(PROJECT_ROOT / "app"),
                "-f",
                "json",
                "-x",
                str(PROJECT_ROOT / "app/tests"),  # 排除测试文件
                "--severity-level",
                "low",
            ]

            result = subprocess.run(
                cmd, capture_output=True, text=True, cwd=PROJECT_ROOT
            )

            if result.returncode == 0:
                bandit_results = json.loads(result.stdout)

                issues = bandit_results.get("results", [])
                severity_counts = {"LOW": 0, "MEDIUM": 0, "HIGH": 0, "CRITICAL": 0}

                for issue in issues:
                    severity = issue.get("issue_severity", "UNKNOWN")
                    if severity in severity_counts:
                        severity_counts[severity] += 1

                return {
                    "status": "success",
                    "issues_found": len(issues),
                    "severity_breakdown": severity_counts,
                    "details": issues[:10],  # 只返回前10个问题的详情
                    "raw_output": result.stdout,
                }
            else:
                return {"status": "error", "error": result.stderr, "issues_found": 0}

        except FileNotFoundError:
            return {
                "status": "skipped",
                "reason": "bandit not installed",
                "recommendation": "pip install bandit",
            }
        except Exception as e:
            return {"status": "error", "error": str(e), "issues_found": 0}

    def run_safety_check(self) -> Dict[str, Any]:
        """
        运行Safety依赖漏洞检查

        Returns:
            Dict: 检查结果
        """
        print("🔒 运行Safety依赖漏洞检查...")

        try:
            cmd = [
                "python",
                "-m",
                "safety",
                "check",
                "--json",
                "--file",
                str(PROJECT_ROOT / "requirements.txt"),
            ]

            result = subprocess.run(
                cmd, capture_output=True, text=True, cwd=PROJECT_ROOT
            )

            if result.returncode == 0:
                vulnerabilities = json.loads(result.stdout)

                return {
                    "status": "success",
                    "vulnerabilities_found": len(vulnerabilities),
                    "details": vulnerabilities[:5],  # 只返回前5个漏洞详情
                    "raw_output": result.stdout,
                }
            else:
                # Safety在发现漏洞时返回非0退出码
                try:
                    vulnerabilities = json.loads(result.stdout)
                    return {
                        "status": "vulnerabilities_found",
                        "vulnerabilities_found": len(vulnerabilities),
                        "details": vulnerabilities,
                        "raw_output": result.stdout,
                    }
                except:
                    return {
                        "status": "error",
                        "error": result.stderr,
                        "vulnerabilities_found": 0,
                    }

        except FileNotFoundError:
            return {
                "status": "skipped",
                "reason": "safety not installed",
                "recommendation": "pip install safety",
            }
        except Exception as e:
            return {"status": "error", "error": str(e), "vulnerabilities_found": 0}

    def check_sensitive_files(self) -> Dict[str, Any]:
        """
        检查敏感文件和配置

        Returns:
            Dict: 检查结果
        """
        print("🕵️ 检查敏感文件和配置...")

        issues = []

        # 检查是否存在可能包含敏感信息的文件
        sensitive_patterns = [
            r"\.env$",
            r"\.env\..*$",
            r"secrets\..*",
            r".*\.key$",
            r".*\.pem$",
            r".*\.p12$",
            r"config\.ini$",
            r"database\.ini$",
        ]

        for pattern in sensitive_patterns:
            for file_path in PROJECT_ROOT.rglob("*"):
                if re.search(pattern, file_path.name, re.IGNORECASE):
                    # 检查文件是否在.gitignore中
                    gitignore_path = PROJECT_ROOT / ".gitignore"
                    is_ignored = False

                    if gitignore_path.exists():
                        with open(gitignore_path, "r") as f:
                            gitignore_content = f.read()
                            relative_path = file_path.relative_to(PROJECT_ROOT)
                            if (
                                str(relative_path) in gitignore_content
                                or file_path.name in gitignore_content
                            ):
                                is_ignored = True

                    if not is_ignored:
                        issues.append(
                            {
                                "type": "sensitive_file_not_ignored",
                                "file": str(file_path),
                                "description": f"敏感文件 {file_path.name} 可能未在.gitignore中忽略",
                                "severity": "MEDIUM",
                            }
                        )

        # 检查代码中的硬编码密钥
        secret_patterns = [
            (r"password\s*=\s*['\"][^'\"]{8,}['\"]", "hardcoded_password"),
            (r"api_key\s*=\s*['\"][^'\"]{20,}['\"]", "hardcoded_api_key"),
            (r"secret_key\s*=\s*['\"][^'\"]{20,}['\"]", "hardcoded_secret_key"),
            (r"token\s*=\s*['\"][^'\"]{20,}['\"]", "hardcoded_token"),
        ]

        python_files = list(PROJECT_ROOT.rglob("*.py"))
        for file_path in python_files:
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                for pattern, issue_type in secret_patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        # 跳过测试文件和明显的占位符
                        if (
                            "test" in str(file_path).lower()
                            or "example" in match.group().lower()
                            or "placeholder" in match.group().lower()
                            or "change" in match.group().lower()
                        ):
                            continue

                        issues.append(
                            {
                                "type": issue_type,
                                "file": str(file_path),
                                "line": content[: match.start()].count("\n") + 1,
                                "description": f"可能的硬编码敏感信息: {match.group()[:50]}...",
                                "severity": "HIGH",
                            }
                        )
            except Exception:
                continue

        return {"status": "completed", "issues_found": len(issues), "details": issues}

    def check_configuration_security(self) -> Dict[str, Any]:
        """
        检查配置文件安全性

        Returns:
            Dict: 检查结果
        """
        print("⚙️ 检查配置文件安全性...")

        issues = []

        # 检查Docker配置
        dockerfile_path = PROJECT_ROOT / "Dockerfile"
        if dockerfile_path.exists():
            with open(dockerfile_path, "r") as f:
                dockerfile_content = f.read()

            # 检查是否以root用户运行
            if "USER " not in dockerfile_content:
                issues.append(
                    {
                        "type": "docker_root_user",
                        "file": str(dockerfile_path),
                        "description": "Dockerfile未指定非root用户运行",
                        "severity": "MEDIUM",
                        "recommendation": "添加 USER non-root-user 指令",
                    }
                )

            # 检查是否有健康检查
            if "HEALTHCHECK" not in dockerfile_content:
                issues.append(
                    {
                        "type": "docker_no_healthcheck",
                        "file": str(dockerfile_path),
                        "description": "Dockerfile缺少健康检查配置",
                        "severity": "LOW",
                        "recommendation": "添加 HEALTHCHECK 指令",
                    }
                )

        # 检查docker-compose.yml安全配置
        compose_path = PROJECT_ROOT / "docker-compose.yml"
        if compose_path.exists():
            with open(compose_path, "r") as f:
                compose_content = f.read()

            # 检查是否使用默认密码
            default_passwords = ["password", "123456", "admin", "root", "astrbot123"]

            for default_pwd in default_passwords:
                if default_pwd in compose_content.lower():
                    issues.append(
                        {
                            "type": "default_password",
                            "file": str(compose_path),
                            "description": f"可能使用了默认密码: {default_pwd}",
                            "severity": "HIGH",
                            "recommendation": "使用强密码并通过环境变量配置",
                        }
                    )

        return {"status": "completed", "issues_found": len(issues), "details": issues}

    def generate_security_report(self) -> str:
        """
        生成安全报告

        Returns:
            str: 报告文件路径
        """
        if not self.generate_report:
            return ""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = SECURITY_REPORT_DIR / f"security_report_{timestamp}.json"

        # 生成详细报告
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)

        # 生成简化的Markdown报告
        md_report_file = SECURITY_REPORT_DIR / f"security_report_{timestamp}.md"
        self._generate_markdown_report(md_report_file)

        print(f"📋 安全报告已生成:")
        print(f"   - JSON报告: {report_file}")
        print(f"   - Markdown报告: {md_report_file}")

        return str(report_file)

    def _generate_markdown_report(self, file_path: Path):
        """生成Markdown格式的安全报告"""

        md_content = f"""# 🔒 AstrBot SaaS 安全检查报告

**生成时间**: {self.results['timestamp']}

## 📊 安全状况总览

| 检查项目 | 状态 | 发现问题数 |
|---------|------|----------|
"""

        for check_name, check_result in self.results["checks"].items():
            status = check_result.get("status", "unknown")
            issues = check_result.get("issues_found", 0) + check_result.get(
                "vulnerabilities_found", 0
            )

            status_emoji = {
                "success": "✅",
                "completed": "✅",
                "vulnerabilities_found": "⚠️",
                "error": "❌",
                "skipped": "⏭️",
            }.get(status, "❓")

            md_content += f"| {check_name} | {status_emoji} {status} | {issues} |\n"

        md_content += f"""

## 🎯 安全建议

### 高优先级修复
- 修复所有HIGH和CRITICAL级别的安全问题
- 更新存在已知漏洞的依赖包
- 检查和更新所有默认密码

### 一般建议
- 定期运行安全扫描
- 保持依赖包及时更新
- 实施代码审查流程
- 配置自动化安全监控

## 📋 详细检查结果

"""

        for check_name, check_result in self.results["checks"].items():
            md_content += f"### {check_name}\n\n"

            if check_result.get("details"):
                md_content += "**发现的问题:**\n\n"
                for detail in check_result["details"][:5]:  # 只显示前5个问题
                    if isinstance(detail, dict):
                        severity = detail.get("severity", "UNKNOWN")
                        description = detail.get("description", "No description")
                        md_content += f"- **{severity}**: {description}\n"
                    else:
                        md_content += f"- {detail}\n"
                md_content += "\n"

            if check_result.get("recommendation"):
                md_content += f"**建议**: {check_result['recommendation']}\n\n"

        md_content += """
---
**报告说明**: 此报告由AstrBot SaaS安全检查工具自动生成。建议定期运行以确保系统安全。
"""

        with open(file_path, "w", encoding="utf-8") as f:
            f.write(md_content)

    def run_all_checks(self):
        """运行所有安全检查"""

        print("🚀 开始AstrBot SaaS安全检查...")
        print("=" * 50)

        # 1. 代码安全扫描
        bandit_result = self.run_bandit_scan()
        self.results["checks"]["code_security_scan"] = bandit_result

        # 2. 依赖漏洞检查
        safety_result = self.run_safety_check()
        self.results["checks"]["dependency_vulnerabilities"] = safety_result

        # 3. 敏感文件检查
        sensitive_files_result = self.check_sensitive_files()
        self.results["checks"]["sensitive_files_check"] = sensitive_files_result

        # 4. 配置安全检查
        config_security_result = self.check_configuration_security()
        self.results["checks"]["configuration_security"] = config_security_result

        # 汇总结果
        total_issues = 0
        for check_result in self.results["checks"].values():
            issues = check_result.get("issues_found", 0) + check_result.get(
                "vulnerabilities_found", 0
            )
            total_issues += issues

        self.results["summary"]["total_issues"] = total_issues

        # 生成报告
        if self.generate_report:
            self.generate_security_report()

        print("=" * 50)
        print(f"🎯 安全检查完成! 共发现 {total_issues} 个问题")

        if total_issues > 0:
            print("⚠️  建议查看详细报告并及时修复安全问题")
            return 1
        else:
            print("✅ 未发现安全问题")
            return 0


def main():
    """主函数"""

    parser = argparse.ArgumentParser(description="AstrBot SaaS 安全检查工具")
    parser.add_argument("--fix", action="store_true", help="尝试自动修复问题")
    parser.add_argument("--no-report", action="store_true", help="不生成报告文件")

    args = parser.parse_args()

    # 初始化安全检查器
    checker = SecurityChecker(fix_issues=args.fix, generate_report=not args.no_report)

    # 运行检查
    exit_code = checker.run_all_checks()

    sys.exit(exit_code)


if __name__ == "__main__":
    main()
