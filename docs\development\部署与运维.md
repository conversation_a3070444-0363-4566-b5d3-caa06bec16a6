# 🚀 AstrBot SaaS平台 - 部署与运维手册 (v1.1)

**文档版本**: v1.1 | **最后更新**: 2025年 | **维护团队**: DevOps执行者 & 技术文档专家

---

## 📋 **1. 部署架构概览**

### **1.1 技术栈**
- **容器化**: Docker
- **容器编排**: Kubernetes (K8s) v1.28+
- **CI/CD**: GitHub Actions
- **监控**: Prometheus, Grafana, Jaeger, AlertManager
- **日志**: Loki, Promtail
- **安全**: <PERSON><PERSON>, Falco, OPA Gatekeeper (计划中)

### **1.2 部署流程图**
```mermaid
graph TD
    A[代码提交到Git] --> B{GitHub Actions CI/CD}
    B --> C[代码质量检查 & 单元/集成测试]
    C --> E[构建安全Docker镜像]
    E --> F[Trivy安全扫描]
    F --> G[推送到GHCR镜像仓库]
    G --> H{部署到Kubernetes}
    H -- 自动 --> I[Staging环境部署]
    I --> J[自动化E2E/安全测试]
    J -- 手动审批 --> K[生产环境部署]
    K --> L[实时监控与告警]
```

---

## 🛠️ **2. 自动化部署流程**

### **2.1 CI/CD流水线**
- **触发条件**: `main`或`develop`分支的push，或手动触发。
- **配置文件**: `.github/workflows/deploy.yml`
- **核心阶段**:
  1. **代码质量与测试**: `black`, `ruff`, `mypy`, `pytest`。
  2. **构建与扫描**: `Dockerfile.production` 多阶段安全构建，`Trivy` 漏洞扫描。
  3. **部署**: `scripts/deploy.sh` 脚本执行部署，支持多环境。

### **2.2 智能化部署脚本**
- **脚本位置**: `scripts/deploy.sh`
- **核心功能**:
  - **多环境支持**: `dev`, `staging`, `production`。
  - **健康检查**: 自动检查应用和数据库状态。
  - **自动回滚**: 部署失败时自动回滚到上一个稳定版本。
  - **参数化部署**: 支持自定义副本数、镜像标签等。

### **2.3 部署命令示例**
```bash
# 部署到Staging环境
./scripts/deploy.sh --environment staging --replicas 2

# 部署到生产环境 (使用Git SHA作为标签)
./scripts/deploy.sh --environment production --replicas 5 --image $(git rev-parse --short HEAD)

# 回滚生产环境部署
./scripts/deploy.sh --rollback --environment production
```

---

## 🐳 **3. 容器化配置**

### **3.1 生产级Dockerfile**
- **文件**: `Dockerfile.production`
- **核心特性**:
  - **多阶段构建**: 减少最终镜像大小 (~200MB)。
  - **安全加固**: 非root用户，只读文件系统，最小化依赖。
  - **性能优化**: 缓存依赖项，优化启动命令。

### **3.2 Docker Compose (开发环境)**
- **文件**: `docker-compose.dev.yml`
- **核心特性**:
  - **热重载**: 代码更改自动重启服务。
  - **调试支持**: 暴露调试端口 (5678)。
  - **本地数据持久化**: PostgreSQL和Redis数据卷。
  - **管理工具**: 可选启动PgAdmin, Redis-Commander。

---

## ☸️ **4. Kubernetes配置**

### **4.1 目录结构**
```
k8s/
├── deployment.yaml              # 基础部署配置
├── production-deployment.yaml   # 生产环境优化配置
└── security/
    ├── rbac/
    |   ├── service-accounts.yaml
    |   └── roles-and-bindings.yaml
    ├── pod-security/
    |   ├── namespace-pss.yaml
    |   └── default-security-context.yaml
    └── network-policies/
        ├── 00-default-deny-all.yaml
        ├── 01-allow-dns.yaml
        └── 10-app-api-policy.yaml
```

### **4.2 安全配置**
- **RBAC**: 细粒度的权限控制，遵循最小权限原则。
- **Pod安全标准 (PSS)**: `restricted` 级别，强制最严格的安全策略。
- **网络策略**: 默认拒绝所有流量，白名单模式允许必要通信。

### **4.3 部署与服务**
- **Deployment**: `RollingUpdate`策略，实现零停机部署。
- **Service**: `ClusterIP`服务，用于集群内部通信。
- **Ingress**: `Nginx Ingress Controller`，管理外部访问。
- **HPA**: 水平Pod自动扩缩容，应对流量变化。

---

## 📊 **5. 监控与运维**

### **5.1 监控栈**
- **Prometheus**: 收集和存储所有指标。
- **Grafana**: 可视化仪表盘，展示系统状态。
- **AlertManager**: 告警管理和多渠道通知。
- **Jaeger**: 分布式链路追踪，分析请求链路。
- **Loki/Promtail**: 日志聚合与查询。

### **5.2 核心仪表盘**
- **应用性能仪表盘**: API响应时间、QPS、错误率。
- **系统资源仪表盘**: CPU、内存、磁盘使用率。
- **数据库仪表盘**: 查询性能、连接数、缓存命中率。
- **安全事件仪表盘**: 登录失败、异常访问、漏洞告警。

### **5.3 告警规则**
- **配置文件**: `monitoring/prometheus/alerts.yml`
- **P0 - 紧急告警**: 服务中断、数据库宕机。
- **P1 - 重要告警**: 错误率飙升、性能显著下降。
- **P2 - 一般告警**: 资源使用率过高、部分功能异常。

### **5.4 故障排查 (SOP)**

#### **场景1: API服务响应慢**
1.  **检查Grafana**: 查看API响应时间仪表盘，定位慢接口。
2.  **检查Jaeger**: 查看慢接口的分布式链路，定位瓶颈（数据库、缓存、外部调用）。
3.  **检查日志**: 在Loki中查询相关请求日志，分析错误信息。
4.  **检查资源**: 查看Pod资源使用率，判断是否需要扩容。

#### **场景2: Pod频繁重启**
1.  **检查Pod状态**: `kubectl get pods -n astrbot-saas -l app=astrbot-saas`.
2.  **查看Pod日志**: `kubectl logs <pod-name> -n astrbot-saas`.
3.  **检查事件**: `kubectl describe pod <pod-name> -n astrbot-saas`，查看`Events`部分。
4.  **检查资源限制**: 确认`requests`和`limits`是否合理。

#### **场景3: 数据库连接失败**
1.  **检查数据库Pod**: `kubectl get pod -l app=postgres -n astrbot-saas`.
2.  **检查Service**: `kubectl get svc postgres-service -n astrbot-saas`.
3.  **测试网络策略**: 确保API Pod可以访问数据库Pod的5432端口。
4.  **检查凭据**: 确认Secret中的数据库密码是否正确。

---

## 🔄 **6. 备份与恢复**

### **6.1 数据库备份**
- **策略**: 每日全量备份，每小时增量备份。
- **工具**: `pg_dump`, `wal-g`.
- **存储**: 备份文件加密后存储到异地对象存储。

### **6.2 配置备份**
- **策略**: 所有Kubernetes配置文件存储在Git仓库中，版本化管理。
- **工具**: Git, GitHub Actions.

### **6.3 灾难恢复演练**
- **频率**: 每季度一次。
- **场景**: 模拟主数据库故障、跨区域故障。
- **目标**: RTO < 4小时, RPO < 1小时。

---

*本文档是AstrBot SaaS平台的运维核心指南，所有运维操作应遵循此文档。*
