# Kubernetes安全专业知识

## 🔐 Kubernetes安全架构

### 1. 集群安全基础
- **API Server安全**：认证、授权、准入控制、审计日志
- **etcd安全**：加密存储、网络隔离、访问控制、备份安全
- **网络安全**：CNI插件、网络策略、服务网格、入站流量控制
- **节点安全**：主机加固、容器运行时、kubelet配置、安全内核

### 2. Pod安全标准
- **Pod Security Standards**：Privileged、Baseline、Restricted级别
- **Security Context**：用户ID、组ID、文件系统权限、特权模式
- **Resource Limits**：CPU、内存、存储限制、防止资源耗尽
- **Capabilities**：Linux能力集管理、最小权限原则

### 3. RBAC权限控制
- **ServiceAccount**：Pod身份、令牌管理、权限绑定
- **Role/ClusterRole**：权限定义、资源范围、动作授权
- **RoleBinding/ClusterRoleBinding**：用户角色绑定、权限继承
- **最小权限原则**：按需授权、定期审计、权限回收

### 4. 网络安全策略
- **NetworkPolicy**：流量控制、微分段、默认拒绝策略
- **Ingress安全**：TLS终止、WAF保护、速率限制
- **Service Mesh**：Istio、Linkerd、加密通信、零信任网络
- **CNI安全**：Calico、Cilium、网络隔离、流量监控

## 🛡️ 容器镜像安全

### 1. 镜像构建安全
- **基础镜像选择**：官方镜像、最小化镜像、漏洞扫描
- **多阶段构建**：减少攻击面、分离构建和运行环境
- **安全实践**：非root用户、只读文件系统、删除不必要组件
- **镜像签名**：Cosign、Notary、供应链安全

### 2. 镜像扫描和管理
- **漏洞扫描**：Trivy、Clair、Anchore、持续扫描
- **合规检查**：CIS基准、安全策略、配置验证
- **镜像仓库**：Harbor、权限控制、审计日志
- **镜像更新**：自动化更新、版本管理、回滚策略

### 3. 运行时安全
- **容器逃逸防护**：seccomp、AppArmor、SELinux
- **文件系统保护**：只读根文件系统、临时文件系统
- **进程监控**：异常进程检测、系统调用监控
- **网络监控**：流量分析、异常连接检测

## 🔧 安全工具和实践

### 1. 准入控制器
- **OPA Gatekeeper**：策略即代码、约束模板、违规检测
- **Validating Webhook**：自定义验证、安全策略执行
- **Mutating Webhook**：自动注入、安全配置增强
- **Pod Security Admission**：内置安全标准执行

### 2. 运行时安全监控
- **Falco**：运行时威胁检测、异常行为告警
- **Twistlock/Prisma Cloud**：企业级容器安全平台
- **Aqua Security**：容器安全生命周期管理
- **Sysdig Secure**：运行时保护和合规

### 3. 密钥管理
- **Kubernetes Secrets**：敏感数据管理、加密存储
- **External Secrets**：外部密钥集成、动态同步
- **HashiCorp Vault**：企业级密钥管理、动态密钥
- **AWS KMS/Azure Key Vault**：云原生密钥管理

### 4. 审计和合规
- **审计日志**：API访问记录、资源变更跟踪
- **合规检查**：CIS Kubernetes Benchmark、NSA指南
- **安全扫描**：kube-bench、kube-hunter、集群安全评估
- **策略管理**：安全策略定义、执行监控、违规处理

## 📋 安全配置最佳实践

### 1. 集群配置加固
```yaml
# API Server安全配置
apiVersion: v1
kind: Pod
spec:
  containers:
  - name: kube-apiserver
    command:
    - kube-apiserver
    - --anonymous-auth=false
    - --authorization-mode=RBAC
    - --audit-log-maxage=30
    - --audit-log-maxbackup=3
    - --audit-log-maxsize=100
    - --audit-log-path=/var/log/apiserver/audit.log
    - --tls-cipher-suites=TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256
```

### 2. Pod安全配置
```yaml
apiVersion: v1
kind: Pod
spec:
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    fsGroup: 2000
    seccompProfile:
      type: RuntimeDefault
  containers:
  - name: app
    securityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      capabilities:
        drop:
        - ALL
    resources:
      limits:
        memory: "128Mi"
        cpu: "500m"
      requests:
        memory: "64Mi"
        cpu: "250m"
```

### 3. 网络策略配置
```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deny-all-ingress
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
```

### 4. RBAC配置示例
```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: pod-reader
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "watch", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: read-pods
subjects:
- kind: ServiceAccount
  name: pod-reader
  namespace: default
roleRef:
  kind: Role
  name: pod-reader
  apiGroup: rbac.authorization.k8s.io
```

## 🚨 威胁检测和响应

### 1. 常见威胁模式
- **容器逃逸**：特权提升、内核漏洞利用、配置错误
- **横向移动**：网络扫描、凭据窃取、服务发现
- **数据泄露**：敏感信息暴露、配置泄露、日志泄露
- **拒绝服务**：资源耗尽、网络洪水、API滥用

### 2. 检测规则配置
```yaml
# Falco规则示例
- rule: Container with Sensitive Mount
  desc: >
    Detect containers with sensitive host paths mounted
  condition: >
    container and
    k8s_containers and
    (fd.name startswith /proc or
     fd.name startswith /var/run/docker.sock or
     fd.name startswith /var/lib/kubelet or
     fd.name startswith /dev)
  output: >
    Sensitive path mounted (user=%ka.user.name verb=%ka.verb 
    uri=%ka.uri.path resp=%ka.response_code)
  priority: WARNING
```

### 3. 事件响应流程
- **威胁检测**：自动化监控、异常告警、威胁情报
- **事件分类**：严重级别、影响范围、响应优先级
- **隔离措施**：Pod隔离、网络隔离、节点隔离
- **取证分析**：日志收集、镜像分析、行为分析
- **恢复措施**：系统清理、配置修复、安全加固 