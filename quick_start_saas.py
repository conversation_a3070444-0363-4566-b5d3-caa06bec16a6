#!/usr/bin/env python3
"""
🚀 AstrBot SaaS平台快速启动工具
DevOps专业工具 - 快速诊断并启动SaaS平台
"""

import os
import sys
import subprocess
import time
from pathlib import Path


def main():
    """主启动流程"""
    print("🚀 AstrBot SaaS平台快速启动")
    print("=" * 50)
    
    # 1. 切换到saas-platform目录
    saas_dir = Path(__file__).parent / "saas-platform"
    if not saas_dir.exists():
        print("❌ saas-platform目录不存在")
        return 1
    
    os.chdir(saas_dir)
    print(f"📍 工作目录: {os.getcwd()}")
    
    # 2. 检查.env文件
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env文件不存在")
        return 1
    print("✅ .env文件存在")
    
    # 3. 设置环境变量
    print("⚙️ 设置环境变量...")
    env_vars = {
        'DATABASE_URL': 'postgresql+asyncpg://astrbot:astrbot123@localhost:5432/astrbot_saas',
        'REDIS_URL': 'redis://:redis123@localhost:6379/0',
        'SECRET_KEY': '09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7',
        'FIRST_SUPERUSER': '<EMAIL>',
        'FIRST_SUPERUSER_PASSWORD': 'ChangeMeASAP!',
        'BACKEND_CORS_ORIGINS': 'http://localhost:3000,http://localhost:8080,http://localhost:6185',
        'ENVIRONMENT': 'development',
        'DEBUG': 'true',
        'LOG_LEVEL': 'INFO',
        'PROJECT_NAME': 'AstrBot SaaS Platform'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
    
    # 4. 检查依赖
    print("📦 检查关键依赖...")
    try:
        import fastapi
        import uvicorn
        print("✅ FastAPI和Uvicorn已安装")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("💡 请运行: pip install fastapi uvicorn")
        return 1
    
    # 5. 测试应用导入
    print("🧪 测试应用导入...")
    try:
        from app.main import app
        print("✅ 应用导入成功")
    except Exception as e:
        print(f"❌ 应用导入失败: {e}")
        return 1
    
    # 6. 启动服务器
    print("🌐 启动Uvicorn服务器...")
    try:
        import uvicorn
        print("🎯 启动信息:")
        print("  🌐 服务地址: http://localhost:8000")
        print("  📚 API文档: http://localhost:8000/docs")
        print("  🔍 健康检查: http://localhost:8000/health")
        print("  ⏹️ 停止服务: Ctrl+C")
        print("\n" + "=" * 50)
        
        # 启动服务器
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True,
            reload=False  # 避免reload问题
        )
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        return 0
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
