# AstrBot SaaS Platform - 生产级多阶段Dockerfile
# DevOps优化：多阶段构建、安全加固、体积优化、性能提升

# =====================================================
# Stage 1: Base Python Environment
# =====================================================
FROM python:3.11-slim as python-base

# 设置Python环境变量优化
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    POETRY_VERSION=1.6.1

# 创建非root用户（安全最佳实践）
RUN groupadd --gid 1000 appuser && \
    useradd --uid 1000 --gid appuser --shell /bin/bash --create-home appuser

# 系统依赖安装（最小化原则）
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 安装Poetry（依赖管理最佳实践）
RUN pip install poetry==$POETRY_VERSION
RUN poetry config virtualenvs.create false

# =====================================================
# Stage 2: Dependencies Installation
# =====================================================
FROM python-base as deps-base

# 复制依赖文件
COPY pyproject.toml poetry.lock ./

# 安装生产依赖（缓存优化）
RUN poetry install --only=main --no-dev --no-interaction --no-ansi

# =====================================================
# Stage 3: Application Build
# =====================================================
FROM python-base as app-build

# 复制已安装的依赖
COPY --from=deps-base /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=deps-base /usr/local/bin /usr/local/bin

# 创建应用目录结构
WORKDIR /app

# 复制应用代码（利用Docker层缓存）
COPY --chown=appuser:appuser ./app ./app
COPY --chown=appuser:appuser ./alembic ./alembic
COPY --chown=appuser:appuser ./alembic.ini ./
COPY --chown=appuser:appuser ./pyproject.toml ./poetry.lock ./

# 创建必要的目录（权限控制）
RUN mkdir -p /app/logs /app/static /app/uploads && \
    chown -R appuser:appuser /app

# =====================================================
# Stage 4: Production Runtime
# =====================================================
FROM python:3.11-slim as production

# 环境变量设置
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/home/<USER>/.local/bin:$PATH" \
    APP_ENV=production

# 创建相同的用户
RUN groupadd --gid 1000 appuser && \
    useradd --uid 1000 --gid appuser --shell /bin/bash --create-home appuser

# 安装运行时依赖（最小化）
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 复制Python环境和应用
COPY --from=app-build /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=app-build /usr/local/bin /usr/local/bin
COPY --from=app-build --chown=appuser:appuser /app /app

# 创建启动脚本
COPY --chown=appuser:appuser <<EOF /app/start.sh
#!/bin/bash
set -e

# 健康检查函数
health_check() {
    curl -f http://localhost:8000/api/v1/health || exit 1
}

# 数据库连接检查
db_check() {
    python -c "
import asyncio
import asyncpg
import os
async def check_db():
    try:
        conn = await asyncpg.connect(os.getenv('DATABASE_URL'))
        await conn.close()
        print('Database connection successful')
    except Exception as e:
        print(f'Database connection failed: {e}')
        exit(1)
asyncio.run(check_db())
    "
}

# 启动前检查
echo "Starting AstrBot SaaS Platform..."
echo "Environment: \$APP_ENV"

# 等待数据库就绪
echo "Checking database connection..."
timeout 30 db_check

# 启动应用
echo "Starting application server..."
exec uvicorn app.main:app \\
    --host 0.0.0.0 \\
    --port 8000 \\
    --workers \${UVICORN_WORKERS:-4} \\
    --worker-class uvicorn.workers.UvicornWorker \\
    --max-requests \${UVICORN_MAX_REQUESTS:-10000} \\
    --max-requests-jitter \${UVICORN_MAX_REQUESTS_JITTER:-1000} \\
    --access-log \\
    --log-level info
EOF

RUN chmod +x /app/start.sh

# 切换到非root用户
USER appuser

# 工作目录
WORKDIR /app

# 健康检查（Docker原生支持）
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["/app/start.sh"]

# =====================================================
# Metadata Labels（最佳实践）
# =====================================================
LABEL maintainer="AstrBot DevOps Team" \
      version="1.0.0" \
      description="AstrBot SaaS Platform - Production Ready Container" \
      org.opencontainers.image.title="AstrBot SaaS Platform" \
      org.opencontainers.image.description="Multi-tenant AI Customer Service SaaS Platform" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.vendor="AstrBot Team" \
      org.opencontainers.image.licenses="MIT" \
      org.opencontainers.image.source="https://github.com/astrbot/saas-platform" \
      org.opencontainers.image.documentation="https://docs.astrbot.com" 