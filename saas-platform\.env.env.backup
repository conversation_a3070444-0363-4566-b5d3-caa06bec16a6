    # This is a template for the .env file.
    # Copy this file to .env and fill in the values.

    # --- Core Application Settings ---
    # A strong, randomly generated secret key is required for security.
    # You can generate one with: openssl rand -hex 32
    SECRET_KEY=09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7

    # --- Database Settings ---
    # These should match the values in docker-compose.yml
    POSTGRES_SERVER=db
    POSTGRES_USER=astrbot_user
    POSTGRES_PASSWORD=astrbot_password
    POSTGRES_DB=astrbot_db
    POSTGRES_PORT=5432

    # --- Redis Settings ---
    REDIS_HOST=redis
    REDIS_PORT=6379

    # --- Initial Superuser ---
    # This is required for the application to start.
    # ────────────── 账号初始化 ──────────────
    FIRST_SUPERUSER=<EMAIL>
    FIRST_SUPERUSER_PASSWORD=ChangeMeASAP!


    # --- CORS Origins ---
    # A comma-separated list of allowed origins.
    # Use "*" for development, but be more specific in production.
    BACKEND_CORS_ORIGINS='["http://localhost:3000","http://localhost:8080"]'

    env_file:
  - .env