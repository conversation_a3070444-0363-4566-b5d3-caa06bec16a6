"""
API v1 路由模块

统一管理所有API v1版本的路由：
- 租户管理
- 用户管理
- 会话管理
- 消息管理
- WebSocket通信
- LLM服务
- AI功能
- Webhook
- 实例管理
- RBAC权限
- 用户角色
- 数据分析
"""

from datetime import datetime

from fastapi import APIRouter

from .ai_features import router as ai_features_router
from .analytics import router as analytics_router
from .instances import router as instances_router
from .messages import router as messages_router
from .rbac import router as rbac_router

# from .users import router as users_router  # TODO: 创建users.py模块 ，测试时注释，正式环境启用
from .sessions import router as sessions_router
from .tenants import router as tenants_router
from .user_roles import router as user_roles_router
from .webhooks import router as webhooks_router
from .websocket import router as websocket_router

# 创建主路由器
api_router = APIRouter()

# 注册所有子路由（修复：移除重复的prefix）
api_router.include_router(tenants_router)

api_router.include_router(sessions_router)

api_router.include_router(messages_router)

api_router.include_router(websocket_router)

api_router.include_router(ai_features_router)

api_router.include_router(webhooks_router)

api_router.include_router(instances_router)

api_router.include_router(rbac_router)

api_router.include_router(user_roles_router)

api_router.include_router(analytics_router)


# 健康检查端点
@api_router.get("/health", tags=["系统"])
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "version": "v1",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "tenant_management": "active",
            "session_management": "active",
            "message_management": "active",
        },
    }
