#!/usr/bin/env python3
"""
AstrBot SaaS Platform - 测试代码优化工具
======================================

专门用于优化测试代码中的冗余，包括：
1. 创建公共测试工具类
2. 提取重复的测试数据工厂
3. 统一Mock和断言模式
4. 重构重复的测试逻辑

作者: Quality Improvement Manager
日期: 2025/01/20
"""

import os
import ast
import re
from pathlib import Path
from typing import Dict, List, Set, Any
from collections import defaultdict, Counter

class TestCodeOptimizer:
    """测试代码优化器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.test_patterns = defaultdict(list)
        self.common_imports = Counter()
        self.mock_patterns = defaultdict(list)
        self.assertion_patterns = defaultdict(list)
    
    def analyze_test_files(self):
        """分析测试文件，识别重复模式"""
        print("🔍 分析测试文件，识别重复模式...")
        
        test_files = list(self.project_root.rglob("test_*.py"))
        print(f"📁 发现测试文件: {len(test_files)} 个")
        
        for test_file in test_files:
            self._analyze_single_test_file(test_file)
        
        print(f"✅ 分析完成")
        return self._generate_optimization_plan()
    
    def _analyze_single_test_file(self, test_file: Path):
        """分析单个测试文件"""
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            # 分析导入
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        self.common_imports[alias.name] += 1
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        for alias in node.names:
                            import_name = f"from {node.module} import {alias.name}"
                            self.common_imports[import_name] += 1
                            
                            # 识别Mock模式
                            if 'mock' in node.module.lower() or alias.name in ['Mock', 'MagicMock', 'patch']:
                                self.mock_patterns[alias.name].append(str(test_file))
            
            # 分析测试函数
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef) and node.name.startswith('test_'):
                    self._analyze_test_function(node, test_file, content)
                    
        except (SyntaxError, UnicodeDecodeError) as e:
            print(f"⚠️ 无法解析文件 {test_file}: {e}")
    
    def _analyze_test_function(self, func_node: ast.FunctionDef, test_file: Path, content: str):
        """分析测试函数"""
        func_source = ast.get_source_segment(content, func_node)
        if not func_source:
            return
        
        # 识别断言模式
        for line in func_source.split('\n'):
            line = line.strip()
            if line.startswith('assert'):
                # 标准化断言模式
                normalized_assert = re.sub(r'["\'].*?["\']', '""', line)
                normalized_assert = re.sub(r'\b\d+\b', 'NUM', normalized_assert)
                self.assertion_patterns[normalized_assert].append(str(test_file))
        
        # 识别测试数据模式
        if 'test_data' in func_source or 'sample_' in func_source:
            self.test_patterns['test_data'].append({
                'file': str(test_file),
                'function': func_node.name,
                'content': func_source[:200]  # 只保存前200字符用于分析
            })
        
        # 识别fixture使用模式
        if '@pytest.fixture' in func_source or 'fixture' in func_source:
            self.test_patterns['fixtures'].append({
                'file': str(test_file),
                'function': func_node.name
            })
    
    def _generate_optimization_plan(self) -> Dict[str, Any]:
        """生成优化计划"""
        plan = {
            'common_imports': dict(self.common_imports.most_common(20)),
            'mock_patterns': dict(self.mock_patterns),
            'assertion_patterns': dict(self.assertion_patterns),
            'test_patterns': dict(self.test_patterns),
            'recommendations': []
        }
        
        # 生成建议
        if len(self.common_imports) > 10:
            plan['recommendations'].append({
                'category': 'imports',
                'priority': 'HIGH',
                'description': '创建公共测试导入模块',
                'details': f'发现{len(self.common_imports)}个重复导入，建议创建conftest.py统一管理'
            })
        
        frequent_assertions = [k for k, v in self.assertion_patterns.items() if len(v) > 3]
        if frequent_assertions:
            plan['recommendations'].append({
                'category': 'assertions',
                'priority': 'MEDIUM',
                'description': '创建自定义断言工具',
                'details': f'发现{len(frequent_assertions)}个重复断言模式'
            })
        
        if len(self.test_patterns['test_data']) > 5:
            plan['recommendations'].append({
                'category': 'test_data',
                'priority': 'HIGH',
                'description': '创建测试数据工厂',
                'details': f'发现{len(self.test_patterns["test_data"])}个重复测试数据模式'
            })
        
        return plan
    
    def generate_common_test_utils(self, plan: Dict[str, Any]):
        """生成公共测试工具代码"""
        print("🛠️ 生成公共测试工具代码...")
        
        # 1. 生成公共conftest.py
        self._generate_conftest(plan)
        
        # 2. 生成测试数据工厂
        self._generate_test_data_factory(plan)
        
        # 3. 生成自定义断言工具
        self._generate_assertion_helpers(plan)
        
        # 4. 生成Mock工具
        self._generate_mock_helpers(plan)
    
    def _generate_conftest(self, plan: Dict[str, Any]):
        """生成或更新conftest.py"""
        conftest_path = self.project_root / "tests" / "conftest.py"
        
        # 读取现有conftest.py
        existing_content = ""
        if conftest_path.exists():
            with open(conftest_path, 'r', encoding='utf-8') as f:
                existing_content = f.read()
        
        # 生成新的导入部分
        common_imports = plan['common_imports']
        new_imports = []
        
        for import_name, count in common_imports.items():
            if count > 3 and import_name not in existing_content:
                new_imports.append(import_name)
        
        if new_imports:
            import_section = "\n".join(f"# Common import used in {common_imports[imp]} test files" 
                                     for imp in new_imports[:10])
            import_section += "\n" + "\n".join(new_imports[:10])
            
            # 更新conftest.py
            if "# AUTO-GENERATED COMMON IMPORTS" not in existing_content:
                updated_content = existing_content + f"""

# AUTO-GENERATED COMMON IMPORTS
# Generated by TestCodeOptimizer
{import_section}

# Common test fixtures can be added here
"""
                with open(conftest_path, 'w', encoding='utf-8') as f:
                    f.write(updated_content)
                
                print(f"✅ 更新了 {conftest_path}")
    
    def _generate_test_data_factory(self, plan: Dict[str, Any]):
        """生成测试数据工厂"""
        factory_path = self.project_root / "tests" / "helpers" / "test_data_factory.py"
        factory_path.parent.mkdir(exist_ok=True)
        
        if factory_path.exists():
            print(f"📁 {factory_path} 已存在，跳过生成")
            return
        
        factory_code = '''"""
AstrBot SaaS Platform - 测试数据工厂
==================================

提供统一的测试数据创建工具，减少测试代码重复

自动生成: TestCodeOptimizer
"""

from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import uuid
from faker import Faker

fake = Faker()

class TestDataFactory:
    """测试数据工厂类"""
    
    @staticmethod
    def create_tenant_data(
        name: Optional[str] = None,
        email: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """创建租户测试数据"""
        return {
            "name": name or fake.company(),
            "email": email or fake.email(),
            "created_at": datetime.utcnow().isoformat(),
            "is_active": True,
            **kwargs
        }
    
    @staticmethod
    def create_user_data(
        username: Optional[str] = None,
        email: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """创建用户测试数据"""
        return {
            "username": username or fake.user_name(),
            "email": email or fake.email(),
            "full_name": fake.name(),
            "is_active": True,
            "created_at": datetime.utcnow().isoformat(),
            **kwargs
        }
    
    @staticmethod
    def create_session_data(
        user_id: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """创建会话测试数据"""
        return {
            "user_id": user_id or fake.random_int(min=1, max=1000),
            "session_id": str(uuid.uuid4()),
            "started_at": datetime.utcnow().isoformat(),
            "status": "active",
            **kwargs
        }
    
    @staticmethod
    def create_message_data(
        session_id: Optional[str] = None,
        content: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """创建消息测试数据"""
        return {
            "session_id": session_id or str(uuid.uuid4()),
            "content": content or fake.text(max_nb_chars=200),
            "sender": "user",
            "timestamp": datetime.utcnow().isoformat(),
            **kwargs
        }
    
    @staticmethod
    def create_api_response_data(
        status_code: int = 200,
        data: Optional[Dict] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """创建API响应测试数据"""
        return {
            "status_code": status_code,
            "data": data or {},
            "message": "success",
            "timestamp": datetime.utcnow().isoformat(),
            **kwargs
        }

# 便捷函数
def sample_tenant(**kwargs):
    """快速创建租户数据"""
    return TestDataFactory.create_tenant_data(**kwargs)

def sample_user(**kwargs):
    """快速创建用户数据"""
    return TestDataFactory.create_user_data(**kwargs)

def sample_session(**kwargs):
    """快速创建会话数据"""
    return TestDataFactory.create_session_data(**kwargs)

def sample_message(**kwargs):
    """快速创建消息数据"""
    return TestDataFactory.create_message_data(**kwargs)
'''
        
        with open(factory_path, 'w', encoding='utf-8') as f:
            f.write(factory_code)
        
        print(f"✅ 创建了测试数据工厂: {factory_path}")
    
    def _generate_assertion_helpers(self, plan: Dict[str, Any]):
        """生成自定义断言工具"""
        assertion_path = self.project_root / "tests" / "helpers" / "assertion_helpers.py"
        assertion_path.parent.mkdir(exist_ok=True)
        
        if assertion_path.exists():
            print(f"📁 {assertion_path} 已存在，跳过生成")
            return
        
        assertion_code = '''"""
AstrBot SaaS Platform - 断言工具
===============================

提供常用的自定义断言工具，简化测试代码

自动生成: TestCodeOptimizer
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import json

class AssertionHelpers:
    """断言辅助工具类"""
    
    @staticmethod
    def assert_response_success(response, expected_status=200):
        """断言API响应成功"""
        assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}"
        assert "error" not in response.json(), f"Response contains error: {response.json()}"
    
    @staticmethod
    def assert_response_error(response, expected_status=400):
        """断言API响应错误"""
        assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}"
        response_data = response.json()
        assert "error" in response_data or "detail" in response_data, "Response should contain error information"
    
    @staticmethod
    def assert_dict_contains(actual_dict: Dict, expected_subset: Dict):
        """断言字典包含指定的键值对"""
        for key, expected_value in expected_subset.items():
            assert key in actual_dict, f"Key '{key}' not found in {actual_dict}"
            assert actual_dict[key] == expected_value, f"Expected {key}={expected_value}, got {actual_dict[key]}"
    
    @staticmethod
    def assert_datetime_recent(dt_string: str, max_seconds_ago: int = 60):
        """断言时间戳是最近的"""
        dt = datetime.fromisoformat(dt_string.replace('Z', '+00:00'))
        now = datetime.utcnow().replace(tzinfo=dt.tzinfo)
        diff = (now - dt).total_seconds()
        assert diff <= max_seconds_ago, f"Datetime {dt_string} is {diff} seconds old, expected <= {max_seconds_ago}"
    
    @staticmethod
    def assert_list_contains_item(items: List[Dict], **filters):
        """断言列表包含符合条件的项目"""
        matching_items = [
            item for item in items 
            if all(item.get(k) == v for k, v in filters.items())
        ]
        assert len(matching_items) > 0, f"No items found matching {filters} in {items}"
    
    @staticmethod
    def assert_pagination_response(response_data: Dict, page: int = 1, per_page: int = 10):
        """断言分页响应格式"""
        required_keys = ['items', 'total', 'page', 'per_page', 'pages']
        for key in required_keys:
            assert key in response_data, f"Pagination response missing key: {key}"
        
        assert response_data['page'] == page, f"Expected page {page}, got {response_data['page']}"
        assert response_data['per_page'] == per_page, f"Expected per_page {per_page}, got {response_data['per_page']}"
        assert isinstance(response_data['items'], list), "Items should be a list"

# 便捷函数
def assert_success(response, status=200):
    """快速断言成功响应"""
    AssertionHelpers.assert_response_success(response, status)

def assert_error(response, status=400):
    """快速断言错误响应"""
    AssertionHelpers.assert_response_error(response, status)

def assert_contains(actual, expected):
    """快速断言包含关系"""
    AssertionHelpers.assert_dict_contains(actual, expected)

def assert_recent(dt_string, max_seconds=60):
    """快速断言时间最近"""
    AssertionHelpers.assert_datetime_recent(dt_string, max_seconds)
'''
        
        with open(assertion_path, 'w', encoding='utf-8') as f:
            f.write(assertion_code)
        
        print(f"✅ 创建了断言工具: {assertion_path}")
    
    def _generate_mock_helpers(self, plan: Dict[str, Any]):
        """生成Mock工具"""
        mock_path = self.project_root / "tests" / "helpers" / "mock_helpers.py"
        mock_path.parent.mkdir(exist_ok=True)
        
        if mock_path.exists():
            print(f"📁 {mock_path} 已存在，跳过生成")
            return
        
        mock_code = '''"""
AstrBot SaaS Platform - Mock工具
===============================

提供常用的Mock工具和配置，减少测试代码重复

自动生成: TestCodeOptimizer
"""

from unittest.mock import Mock, MagicMock, AsyncMock, patch
from typing import Dict, Any, Optional, List
import asyncio

class MockHelpers:
    """Mock辅助工具类"""
    
    @staticmethod
    def create_mock_db_session():
        """创建模拟数据库会话"""
        mock_session = AsyncMock()
        mock_session.add = Mock()
        mock_session.commit = AsyncMock()
        mock_session.rollback = AsyncMock()
        mock_session.refresh = AsyncMock()
        mock_session.close = AsyncMock()
        return mock_session
    
    @staticmethod
    def create_mock_tenant(tenant_id: int = 1, name: str = "Test Tenant"):
        """创建模拟租户对象"""
        mock_tenant = Mock()
        mock_tenant.id = tenant_id
        mock_tenant.name = name
        mock_tenant.email = f"test@{name.lower().replace(' ', '')}.com"
        mock_tenant.is_active = True
        return mock_tenant
    
    @staticmethod
    def create_mock_user(user_id: int = 1, username: str = "testuser"):
        """创建模拟用户对象"""
        mock_user = Mock()
        mock_user.id = user_id
        mock_user.username = username
        mock_user.email = f"{username}@test.com"
        mock_user.is_active = True
        mock_user.tenant_id = 1
        return mock_user
    
    @staticmethod
    def create_mock_service_response(
        data: Optional[Any] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ):
        """创建模拟服务响应"""
        mock_response = Mock()
        mock_response.data = data
        mock_response.success = success
        mock_response.error_message = error_message
        return mock_response
    
    @staticmethod
    def create_mock_async_client():
        """创建模拟异步HTTP客户端"""
        mock_client = AsyncMock()
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json = AsyncMock(return_value={"status": "success"})
        mock_client.get = AsyncMock(return_value=mock_response)
        mock_client.post = AsyncMock(return_value=mock_response)
        mock_client.put = AsyncMock(return_value=mock_response)
        mock_client.delete = AsyncMock(return_value=mock_response)
        return mock_client
    
    @staticmethod
    def create_mock_redis():
        """创建模拟Redis客户端"""
        mock_redis = AsyncMock()
        mock_redis.get = AsyncMock(return_value=None)
        mock_redis.set = AsyncMock(return_value=True)
        mock_redis.delete = AsyncMock(return_value=1)
        mock_redis.exists = AsyncMock(return_value=False)
        return mock_redis

class AsyncContextManager:
    """异步上下文管理器工具"""
    
    def __init__(self, mock_obj):
        self.mock_obj = mock_obj
    
    async def __aenter__(self):
        return self.mock_obj
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass

# 便捷函数
def mock_db_session():
    """快速创建数据库会话Mock"""
    return MockHelpers.create_mock_db_session()

def mock_tenant(tenant_id=1, name="Test Tenant"):
    """快速创建租户Mock"""
    return MockHelpers.create_mock_tenant(tenant_id, name)

def mock_user(user_id=1, username="testuser"):
    """快速创建用户Mock"""
    return MockHelpers.create_mock_user(user_id, username)

def mock_async_client():
    """快速创建异步客户端Mock"""
    return MockHelpers.create_mock_async_client()

def mock_service_success(data=None):
    """快速创建成功响应Mock"""
    return MockHelpers.create_mock_service_response(data=data, success=True)

def mock_service_error(error_message="Test error"):
    """快速创建错误响应Mock"""
    return MockHelpers.create_mock_service_response(success=False, error_message=error_message)
'''
        
        with open(mock_path, 'w', encoding='utf-8') as f:
            f.write(mock_code)
        
        print(f"✅ 创建了Mock工具: {mock_path}")
    
    def generate_optimization_report(self, plan: Dict[str, Any]):
        """生成优化报告"""
        print("📋 生成测试代码优化报告...")
        
        report = []
        report.append("=" * 80)
        report.append("🛠️ AstrBot SaaS Platform - 测试代码优化报告")
        report.append("=" * 80)
        report.append(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 分析统计
        report.append("📊 测试代码分析统计")
        report.append("-" * 40)
        report.append(f"🔄 重复导入: {len(plan['common_imports'])} 个")
        report.append(f"🎭 Mock模式: {len(plan['mock_patterns'])} 种")
        report.append(f"✅ 断言模式: {len(plan['assertion_patterns'])} 种")
        report.append(f"📦 测试数据模式: {len(plan['test_patterns']['test_data'])} 个")
        report.append("")
        
        # 优化建议
        report.append("💡 优化建议")
        report.append("-" * 40)
        for i, rec in enumerate(plan['recommendations'], 1):
            report.append(f"{i}. 【{rec['priority']}】{rec['description']}")
            report.append(f"   📝 {rec['details']}")
            report.append("")
        
        # 生成的工具
        report.append("🛠️ 已生成的优化工具")
        report.append("-" * 40)
        report.append("1. tests/helpers/test_data_factory.py - 测试数据工厂")
        report.append("2. tests/helpers/assertion_helpers.py - 自定义断言工具")
        report.append("3. tests/helpers/mock_helpers.py - Mock工具集")
        report.append("4. 更新的 tests/conftest.py - 公共配置和导入")
        report.append("")
        
        # 使用示例
        report.append("📖 使用示例")
        report.append("-" * 40)
        report.append("# 使用测试数据工厂")
        report.append("from tests.helpers.test_data_factory import sample_tenant, sample_user")
        report.append("tenant_data = sample_tenant(name='Custom Tenant')")
        report.append("")
        report.append("# 使用断言工具")
        report.append("from tests.helpers.assertion_helpers import assert_success, assert_contains")
        report.append("assert_success(response)")
        report.append("assert_contains(response.json(), {'status': 'success'})")
        report.append("")
        report.append("# 使用Mock工具")
        report.append("from tests.helpers.mock_helpers import mock_db_session, mock_tenant")
        report.append("mock_session = mock_db_session()")
        report.append("mock_tenant_obj = mock_tenant(name='Test')")
        
        report_content = "\n".join(report)
        
        # 保存报告
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = self.project_root / f"test_optimization_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(report_content)
        print()
        print(f"📄 报告已保存: {report_file}")

def main():
    """主函数"""
    optimizer = TestCodeOptimizer()
    
    # 分析测试文件
    plan = optimizer.analyze_test_files()
    
    # 生成优化工具
    optimizer.generate_common_test_utils(plan)
    
    # 生成报告
    optimizer.generate_optimization_report(plan)
    
    print("\n🎉 测试代码优化完成！")
    print("💡 建议下一步:")
    print("   1. 审查生成的工具代码")
    print("   2. 逐步重构现有测试文件使用新工具")
    print("   3. 在新测试中优先使用这些工具")

if __name__ == "__main__":
    main() 