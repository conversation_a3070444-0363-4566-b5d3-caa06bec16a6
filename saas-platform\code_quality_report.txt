📊 AstrBot SaaS - 代码质量检查报告
============================================================
生成时间: 2025-06-13 18:18:58

📈 总体质量评估
------------------------------
检查文件数: 59
代码总行数: 19708
质量评分: 96.9/100

🚨 严重问题: 0
❌ 错误: 7
⚠️  警告: 108

📦 模块: api
--------------------
文件数: 14
代码行数: 5212
质量评分: 95.49/100
问题统计: 🚨0 ❌0 ⚠️47

📦 模块: services
--------------------
文件数: 20
代码行数: 9485
质量评分: 97.05/100
问题统计: 🚨0 ❌7 ⚠️42

📦 模块: models
--------------------
文件数: 6
代码行数: 1548
质量评分: 98.39/100
问题统计: 🚨0 ❌0 ⚠️5

📦 模块: schemas
--------------------
文件数: 8
代码行数: 1541
质量评分: 98.7/100
问题统计: 🚨0 ❌0 ⚠️4

📦 模块: core
--------------------
文件数: 9
代码行数: 1767
质量评分: 97.17/100
问题统计: 🚨0 ❌0 ⚠️10

📦 模块: utils
--------------------
文件数: 2
代码行数: 155
质量评分: 100.0/100
问题统计: 🚨0 ❌0 ⚠️0

🎯 改进建议
--------------------
2. ❌ 修复代码错误
3. ⚠️  逐步改善最佳实践违规
4. 📚 参考项目开发规范文档

🎖️  代码质量等级: 🏆 优秀
