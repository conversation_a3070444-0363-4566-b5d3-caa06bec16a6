"""
测试配置和共享fixtures
"""

import asyncio
from collections.abc import AsyncGenerator, Generator

import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from unittest.mock import AsyncMock, Mock, patch
from uuid import uuid4, UUID
from datetime import datetime
from fastapi.testclient import TestClient

from app.core.database import Base, get_db
from app.main import app
from app.models.tenant import Tenant, TenantStatus
from app.models.user import User
from app.models.session import Session, SessionStatus
from app.models.message import Message, MessageType, SenderType
from app.schemas.tenant import TenantCreate, TenantRead
from app.schemas.user import UserCreate, UserRead
from app.schemas.session import SessionCreate
from app.schemas.message import MessageRead
from app.models.role import Role, Permission

# 测试数据库配置 - 使用内存数据库
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# 创建测试引擎 - 关键修复：确保所有连接共享同一个数据库
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    connect_args={
        "check_same_thread": False,
    },
    poolclass=StaticPool,
    echo=True,  # 调试时可查看SQL语句
)

# 创建测试会话工厂
TestingSessionLocal = sessionmaker(
    test_engine, class_=AsyncSession, expire_on_commit=False
)


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """创建测试事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def setup_test_db():
    """设置测试数据库"""
    # 关键修复：强制替换应用程序的数据库引擎
    from app.core import database

    # 保存原始引擎
    original_engine = database.engine
    original_sessionlocal = database.AsyncSessionLocal

    # 替换为测试引擎
    database.engine = test_engine
    database.AsyncSessionLocal = TestingSessionLocal

    # 创建所有表
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield

    # 清理 - 删除所有表
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

    # 恢复原始引擎
    database.engine = original_engine
    database.AsyncSessionLocal = original_sessionlocal


@pytest_asyncio.fixture
async def db_session(setup_test_db) -> AsyncGenerator[AsyncSession, None]:
    """提供数据库会话"""
    async with TestingSessionLocal() as session:
        yield session


async def override_get_db() -> AsyncGenerator[AsyncSession, None]:
    """覆盖应用程序的数据库依赖"""
    async with TestingSessionLocal() as session:
        yield session


@pytest.fixture
async def client(setup_test_db) -> AsyncGenerator[AsyncClient, None]:
    """创建测试客户端"""
    # 覆盖应用程序的数据库依赖
    app.dependency_overrides[get_db] = override_get_db

    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac

    # 清理依赖覆盖
    app.dependency_overrides.clear()


# E2E测试的数据fixtures
@pytest.fixture
async def test_tenant_and_users(db_session: AsyncSession):
    """创建测试租户和用户数据"""
    from app.models.tenant import Tenant, TenantPlan, TenantStatus
    from app.models.user import User

    # 创建测试租户
    tenant = Tenant(
        id="5681d7bbbc4e42008a9e46ce04ed298d",
        name="测试企业",
        email="<EMAIL>",
        status=TenantStatus.ACTIVE,
        plan=TenantPlan.BASIC,
        API_KEY = "test_api_key_for_testing",
        extra_data={},
    )
    db_session.add(tenant)

    # 创建测试用户
    agent_user = User(
        id="webchat:test_agent_001",
        tenant_id=tenant.id,
        platform="webchat",
        user_id="test_agent_001",
        nickname="测试客服",
        extra_data={},
    )

    customer_user = User(
        id="webchat:test_customer_001",
        tenant_id=tenant.id,
        platform="webchat",
        user_id="test_customer_001",
        nickname="测试用户",
        extra_data={},
    )

    db_session.add(agent_user)
    db_session.add(customer_user)
    await db_session.commit()

    return {"tenant": tenant, "agent_user": agent_user, "customer_user": customer_user}


@pytest_asyncio.fixture
async def test_tenant(db_session: AsyncSession):
    """
    创建测试租户
    """
    # TODO: 在实现Tenant模型后，这里创建测试租户
    # from app.models.tenant import Tenant
    #
    # tenant = Tenant(
    #     name="Test Company",
    #     email="<EMAIL>",
    #     domain="test.example.com",
    #     is_active=True
    # )
    # db_session.add(tenant)
    # await db_session.commit()
    # await db_session.refresh(tenant)
    # return tenant

    # 临时返回模拟数据
    return {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "name": "Test Company",
        "email": "<EMAIL>",
    }


@pytest_asyncio.fixture
async def test_user(db_session: AsyncSession, test_tenant):
    """
    创建测试用户
    """
    # TODO: 在实现User模型后，这里创建测试用户
    # from app.models.user import User
    #
    # user = User(
    #     email="<EMAIL>",
    #     hashed_password = os.getenv("TEST_PASSWORD", "test_password"),
    #     tenant_id=test_tenant.id,
    #     is_active=True
    # )
    # db_session.add(user)
    # await db_session.commit()
    # await db_session.refresh(user)
    # return user

    # 临时返回模拟数据
    return {
        "id": "550e8400-e29b-41d4-a716-446655440001",
        "email": "<EMAIL>",
        "tenant_id": test_tenant["id"],
    }


@pytest_asyncio.fixture
async def auth_token(test_user):
    """
    生成测试用的JWT token
    """
    # TODO: 在实现JWT服务后，这里生成真实token
    # from app.core.security import create_access_token
    #
    # token_data = {
    #     "sub": str(test_user.id),
    #     "tenant_id": str(test_user.tenant_id)
    # }
    # return create_access_token(data=token_data)

    # 临时返回模拟token
    return "mock_jwt_token"


@pytest_asyncio.fixture
async def authenticated_client(client: AsyncClient, auth_token: str) -> AsyncClient:
    """
    创建已认证的测试客户端
    """
    client.headers.update({"Authorization": f"Bearer {auth_token}"})
    return client


# 测试数据工厂函数
def create_test_session_data(tenant_id: str, user_id: str = "test_user"):
    """
    创建测试会话数据
    """
    return {
        "user_id": user_id,
        "tenant_id": tenant_id,
        "platform": "test_platform",
        "channel": "test_channel",
        "status": "active",
    }


def create_test_message_data(
    session_id: str,
    tenant_id: str,
    message_type: str = "text",
    content: str = "Test message",
):
    """
    创建测试消息数据
    """
    return {
        "session_id": session_id,
        "tenant_id": tenant_id,
        "content": content,
        "message_type": message_type,
        "sender_type": "user",
        "platform": "test_platform",
    }


@pytest.fixture
def anyio_backend():
    """配置asyncio后端"""
    return "asyncio"


@pytest.fixture
def mock_settings():
    """Mock的设置配置"""
    with patch("app.core.config.settings") as mock:
        mock.DATABASE_URL = "sqlite+aiosqlite:///:memory:"
        mock.SECRET_KEY = "test-secret-key"
        mock.ACCESS_TOKEN_EXPIRE_MINUTES = 30
        mock.OPENAI_API_KEY = "test_api_key_for_testing"
        yield mock


@pytest.fixture
async def mock_db_session():
    """
    Mock的数据库会话，正确配置所有常见的数据库操作
    """
    mock_session = AsyncMock(spec=AsyncSession)

    # 配置execute方法的返回值
    mock_result = AsyncMock()
    mock_result.scalar_one_or_none = AsyncMock()
    mock_result.scalars = AsyncMock()
    mock_result.scalars.return_value = AsyncMock()
    mock_result.scalars.return_value.all = AsyncMock()
    mock_result.scalar = AsyncMock()

    mock_session.execute.return_value = mock_result

    # 配置事务方法
    mock_session.commit = AsyncMock()
    mock_session.rollback = AsyncMock()
    mock_session.refresh = AsyncMock()
    mock_session.add = Mock()
    mock_session.delete = AsyncMock()
    mock_session.merge = AsyncMock()
    mock_session.flush = AsyncMock()

    # 配置查询构建器
    mock_session.query = Mock()

    return mock_session


@pytest.fixture
def test_client():
    """FastAPI测试客户端"""
    return TestClient(app)


@pytest.fixture
async def async_client():
    """异步HTTP客户端"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def sample_tenant():
    """示例租户对象"""
    tenant = Tenant(
        id=uuid4(),
        name="Test Company",
        email="<EMAIL>",
        plan="basic",
        status=TenantStatus.ACTIVE,
        API_KEY = "test_api_key_for_testing",
    )
    # 模拟必要的属性
    tenant.created_at = "2023-01-01T00:00:00"
    tenant.updated_at = "2023-01-01T00:00:00"
    tenant.extra_data = {}
    return tenant


@pytest.fixture
def sample_tenant_create():
    """示例租户创建数据"""
    return TenantCreate(
        name="Test Company",
        email="<EMAIL>",
        plan="basic",
        metadata={"industry": "technology"},
    )


@pytest.fixture
def sample_user(sample_tenant):
    """示例用户对象"""
    user = User(
        id="test_platform:testuser123",
        platform="test_platform",
        user_id="testuser123",
        nickname="Test User",
        tenant_id=sample_tenant.id,
        extra_data={"email": "<EMAIL>"},
    )
    user.created_at = "2023-01-01T00:00:00"
    user.updated_at = "2023-01-01T00:00:00"
    return user


@pytest.fixture
def sample_session(sample_tenant, sample_user):
    """示例会话对象"""
    session = Session(
        id=uuid4(),
        tenant_id=sample_tenant.id,
        user_id=sample_user.id,
        platform="test_platform",
        status=SessionStatus.ACTIVE,
        channel_type="direct",
        priority=5,
    )
    session.created_at = "2023-01-01T00:00:00"
    session.updated_at = "2023-01-01T00:00:00"
    return session


@pytest.fixture
def sample_message(sample_session, sample_user):
    """示例消息对象"""
    message = Message(
        id=1,
        session_id=sample_session.id,
        tenant_id=sample_session.tenant_id,
        sender_id=sample_user.id,
        sender_type=SenderType.USER,
        message_type=MessageType.TEXT,
        content="Hello, this is a test message",
        timestamp=datetime.fromisoformat("2023-01-01T00:00:00"),
    )
    message.created_at = "2023-01-01T00:00:00"
    return message


@pytest.fixture
def mock_auth_user(sample_user):
    """Mock认证用户"""
    with patch("app.api.deps.get_current_user") as mock:
        mock.return_value = sample_user
        yield sample_user


@pytest.fixture
def mock_tenant_service():
    """Mock租户服务"""
    with patch("app.services.tenant_service.TenantService") as mock:
        yield mock


@pytest.fixture
def mock_llm_provider():
    """Mock LLM提供商"""
    with patch("app.services.llm.base_provider.BaseLLMProvider") as mock:
        mock_instance = mock.return_value
        mock_instance.generate_response = AsyncMock(return_value="Test response")
        mock_instance.analyze_context = AsyncMock(
            return_value={"sentiment": "positive"}
        )
        yield mock_instance


# 清理hooks
@pytest.fixture(autouse=True)
async def cleanup_after_test():
    """测试后清理"""
    yield
    # 测试完成后清理任何全局状态
    pass


def create_test_message(
    tenant_id: str,
    session_id: str, 
    sender_id: str = "test-sender",
    content: str = "Test message",
    message_type=MessageType.TEXT,
    sender_type=SenderType.USER,
    timestamp=None
):
    """创建测试消息，确保所有必需字段都有正确的值"""
    from datetime import datetime
    
    return Message(
        tenant_id=tenant_id,
        session_id=session_id,
        content=content,
        message_type=message_type,
        sender_type=sender_type,
        sender_id=sender_id,
        timestamp=timestamp or datetime.utcnow(),
        attachments=[],
        extra_data={}
    )

def create_test_session(
    tenant_id: str,
    user_id: str = "test-user",
    platform: str = "web"
):
    """创建测试会话，确保所有必需字段都有正确的值"""
    from app.models.session import ChannelType
    
    return Session(
        id=uuid4(),
        tenant_id=tenant_id,
        user_id=user_id,
        platform=platform,  # 确保platform字段不为空
        status=SessionStatus.WAITING,
        channel_type=ChannelType.DIRECT,
        priority=5,
        extra_data={}
    )


# AUTO-GENERATED COMMON IMPORTS
# Generated by TestCodeOptimizer
# Common import used in 44 test files
# Common import used in 35 test files
# Common import used in 18 test files
# Common import used in 15 test files
# Common import used in 15 test files
# Common import used in 14 test files
# Common import used in 13 test files
# Common import used in 12 test files
# Common import used in 11 test files
from unittest.mock import patch
from unittest.mock import Mock
from unittest.mock import MagicMock
from app.models.tenant import TenantStatus
from app.models.session import SessionStatus
from app.models.message import SenderType
from fastapi import HTTPException
from app.models.tenant import TenantPlan
from uuid import UUID

# Common test fixtures can be added here
