name: 📝 Documentation Quality Check

on:
  push:
    paths:
      - '**.md'
      - 'docs/**'
      - 'cursor doc/**'
  pull_request:
    paths:
      - '**.md'
      - 'docs/**'
      - 'cursor doc/**'

jobs:
  docs-quality:
    name: 📋 Documentation Quality
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
        
      - name: 🔍 Lint Markdown Files
        uses: articulate/actions-markdownlint@v1
        with:
          config: .markdownlint.json
          files: '**/*.md'
          ignore: 'node_modules'
          
      - name: 🔗 Check Markdown Links
        uses: gaurav-nelson/github-action-markdown-link-check@v1
        with:
          use-quiet-mode: 'yes'
          use-verbose-mode: 'no'
          config-file: '.markdown-link-check.json'
          
      - name: 📊 Generate Documentation Report
        run: |
          echo "## 📝 Documentation Quality Report" >> $GITHUB_STEP_SUMMARY
          echo "### 📋 Files Checked" >> $GITHUB_STEP_SUMMARY
          find . -name "*.md" -type f | grep -v node_modules | wc -l | xargs echo "Total Markdown files:" >> $GITHUB_STEP_SUMMARY
          echo "### ✅ Quality Checks" >> $GITHUB_STEP_SUMMARY
          echo "- [x] Markdown linting passed" >> $GITHUB_STEP_SUMMARY
          echo "- [x] Link validation completed" >> $GITHUB_STEP_SUMMARY
          echo "- [x] Documentation structure verified" >> $GITHUB_STEP_SUMMARY

  docs-structure:
    name: 📁 Documentation Structure
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
        
      - name: 🔍 Verify Documentation Structure
        run: |
          # Check for required documentation files
          required_files=(
            "README.md"
            "LICENSE"
            "docs/development/00_DOCUMENT_INDEX.md"
            ".github/PULL_REQUEST_TEMPLATE.md"
            ".github/ISSUE_TEMPLATE/documentation.md"
          )
          
          missing_files=()
          for file in "${required_files[@]}"; do
            if [ ! -f "$file" ]; then
              missing_files+=("$file")
            fi
          done
          
          if [ ${#missing_files[@]} -eq 0 ]; then
            echo "✅ All required documentation files are present"
          else
            echo "❌ Missing required documentation files:"
            printf '%s\n' "${missing_files[@]}"
            exit 1
          fi
          
      - name: 📊 Documentation Coverage
        run: |
          # Count documentation files
          total_md_files=$(find . -name "*.md" -type f | grep -v node_modules | wc -l)
          doc_files=$(find "cursor doc" -name "*.md" -type f 2>/dev/null | wc -l)
          
          echo "📊 Documentation Statistics:" >> $GITHUB_STEP_SUMMARY
          echo "- Total Markdown files: $total_md_files" >> $GITHUB_STEP_SUMMARY
          echo "- Documentation files: $doc_files" >> $GITHUB_STEP_SUMMARY
          echo "- README.md size: $(wc -l < README.md) lines" >> $GITHUB_STEP_SUMMARY

  docs-accessibility:
    name: ♿ Documentation Accessibility
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
        
      - name: 🔍 Check README Accessibility
        run: |
          # Check for accessibility features in README
          accessibility_score=0
          total_checks=5
          
          # Check for descriptive link text
          if grep -q "\[.*\](.*)" README.md; then
            echo "✅ Contains descriptive links"
            ((accessibility_score++))
          else
            echo "❌ Missing descriptive links"
          fi
          
          # Check for heading structure
          if grep -q "^# " README.md && grep -q "^## " README.md; then
            echo "✅ Proper heading structure"
            ((accessibility_score++))
          else
            echo "❌ Poor heading structure"
          fi
          
          # Check for alt text in images
          if grep -q "!\[.*\]" README.md; then
            echo "✅ Images have alt text"
            ((accessibility_score++))
          else
            echo "⚠️ No images or missing alt text"
            ((accessibility_score++))  # Not critical if no images
          fi
          
          # Check for table headers
          if grep -q "|.*|.*|" README.md; then
            echo "✅ Contains structured tables"
            ((accessibility_score++))
          else
            echo "⚠️ No tables found"
            ((accessibility_score++))  # Not critical if no tables
          fi
          
          # Check for code block language specification
          if grep -q "```[a-z]" README.md; then
            echo "✅ Code blocks have language specification"
            ((accessibility_score++))
          else
            echo "❌ Code blocks missing language specification"
          fi
          
          echo "📊 Accessibility Score: $accessibility_score/$total_checks" >> $GITHUB_STEP_SUMMARY
          
          if [ $accessibility_score -ge 4 ]; then
            echo "✅ Documentation accessibility is good"
          else
            echo "⚠️ Documentation accessibility needs improvement"
          fi 