# tests/security/test_k8s_security_policies.py
# 自动化测试Kubernetes安全配置

import pytest
from kubernetes import client, config
from kubernetes.client.rest import ApiException

# 加载Kubernetes配置
try:
    config.load_kube_config()
except config.ConfigException:
    config.load_incluster_config()

v1 = client.CoreV1Api()
rbac_v1 = client.RbacAuthorizationV1Api()
networking_v1 = client.NetworkingV1Api()

NAMESPACE = "astrbot-saas"

@pytest.mark.security
class TestKubernetesSecurityPolicies:
    
    # =====================================================
    # RBAC权限控制测试
    # =====================================================
    def test_service_accounts_exist(self):
        """验证ServiceAccount已创建"""
        accounts = ["astrbot-saas-api", "astrbot-saas-worker", "astrbot-saas-cronjob"]
        for sa_name in accounts:
            try:
                v1.read_namespaced_service_account(name=sa_name, namespace=NAMESPACE)
            except ApiException as e:
                pytest.fail(f"ServiceAccount {sa_name} not found: {e}")

    def test_roles_and_bindings_exist(self):
        """验证Role和RoleBinding已创建"""
        roles = ["astrbot-api-role", "astrbot-worker-role"]
        for role_name in roles:
            try:
                rbac_v1.read_namespaced_role(name=role_name, namespace=NAMESPACE)
            except ApiException as e:
                pytest.fail(f"Role {role_name} not found: {e}")

        bindings = ["astrbot-api-binding", "astrbot-worker-binding"]
        for binding_name in bindings:
            try:
                rbac_v1.read_namespaced_role_binding(name=binding_name, namespace=NAMESPACE)
            except ApiException as e:
                pytest.fail(f"RoleBinding {binding_name} not found: {e}")

    # =====================================================
    # Pod安全策略测试
    # =====================================================
    def test_namespace_pod_security_standard(self):
        """验证命名空间的Pod安全标准"""
        try:
            ns = v1.read_namespace(name=NAMESPACE)
            labels = ns.metadata.labels
            assert labels.get("pod-security.kubernetes.io/enforce") == "restricted"
            assert labels.get("pod-security.kubernetes.io/audit") == "restricted"
            assert labels.get("pod-security.kubernetes.io/warn") == "restricted"
        except ApiException as e:
            pytest.fail(f"Failed to read namespace {NAMESPACE}: {e}")

    def test_deployment_security_context(self):
        """验证Deployment的安全上下文配置"""
        try:
            apps_v1 = client.AppsV1Api()
            deployment = apps_v1.read_namespaced_deployment(name="astrbot-saas", namespace=NAMESPACE)
            pod_spec = deployment.spec.template.spec
            
            # Pod级别安全上下文
            assert pod_spec.security_context.run_as_non_root is True
            assert pod_spec.security_context.run_as_user is not None
            
            # 容器级别安全上下文
            container_sc = pod_spec.containers[0].security_context
            assert container_sc.allow_privilege_escalation is False
            assert container_sc.read_only_root_filesystem is True
            assert "ALL" in container_sc.capabilities.drop
        except ApiException as e:
            pytest.fail(f"Failed to read deployment astrbot-saas: {e}")
        except (AttributeError, IndexError) as e:
            pytest.fail(f"Deployment security context format error: {e}")

    # =====================================================
    # 网络策略测试
    # =====================================================
    def test_default_deny_network_policy(self):
        """验证默认拒绝网络策略存在"""
        try:
            networking_v1.read_namespaced_network_policy(name="default-deny-all", namespace=NAMESPACE)
        except ApiException as e:
            pytest.fail(f"NetworkPolicy default-deny-all not found: {e}")

    def test_dns_allow_network_policy(self):
        """验证DNS允许网络策略存在"""
        try:
            networking_v1.read_namespaced_network_policy(name="allow-dns-access", namespace=NAMESPACE)
        except ApiException as e:
            pytest.fail(f"NetworkPolicy allow-dns-access not found: {e}")

    def test_api_network_policy(self):
        """验证API服务的网络策略"""
        try:
            policy = networking_v1.read_namespaced_network_policy(name="astrbot-api-policy", namespace=NAMESPACE)
            
            # 验证入站规则
            assert len(policy.spec.ingress) > 0
            
            # 验证出站规则
            assert len(policy.spec.egress) > 0
            
        except ApiException as e:
            pytest.fail(f"NetworkPolicy astrbot-api-policy not found: {e}")

    def test_pod_to_pod_communication(self):
        """模拟测试Pod间通信是否被策略正确控制"""
        # 这个测试需要一个更复杂的设置来创建测试Pod并尝试连接
        # 在这里我们只做一个概念性的占位符
        assert True, "Pod-to-pod communication test requires a live cluster setup"

# 可选的本地运行入口
if __name__ == "__main__":
    pytest.main(["-v", __file__]) 