"""
MessageService单元测试
测试消息管理服务的各种功能和异常处理逻辑
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from uuid import UUID, uuid4
from datetime import datetime
from fastapi import HTTPException
import os

from app.services.message_service import MessageService
from app.schemas.message import MessageCreate, MessageRead
from app.models.message import Message, MessageType, SenderType, MessageStatus
from app.schemas.session import SessionRead
from app.models.session import SessionStatus, ChannelType


class TestMessageService:
    """MessageService单元测试类"""

    @pytest.fixture
    def mock_db(self):
        """Mock数据库会话"""
        db = AsyncMock()
        # 正确模拟bind属性
        mock_bind = Mock()
        mock_bind.url = "sqlite:///test.db"  # 模拟SQLite环境
        db.bind = mock_bind

        # 模拟数据库操作方法
        db.add = Mock()
        db.commit = AsyncMock()
        db.rollback = AsyncMock()
        db.refresh = AsyncMock()
        db.execute = AsyncMock()

        return db

    @pytest.fixture
    def mock_session_service(self):
        """Mock会话服务"""
        service = AsyncMock()
        service.get_session = AsyncMock()
        service.update_last_message_time = AsyncMock()
        return service

    @pytest.fixture
    def message_service(self, mock_db, mock_session_service):
        """创建MessageService实例"""
        return MessageService(mock_db, mock_session_service)

    @pytest.fixture
    def tenant_id(self):
        """租户ID"""
        return uuid4()

    @pytest.fixture
    def session_id(self):
        """会话ID"""
        return uuid4()

    @pytest.fixture
    def sample_message_create(self, session_id):
        """示例消息创建数据"""
        return MessageCreate(
            session_id=session_id,
            content="Test message content",
            message_type=MessageType.TEXT,
            sender_type=SenderType.USER,
            sender_id="user123",
            metadata={"key": "value"},
            attachments=[],
        )

    @pytest.fixture
    def sample_session_read(self, session_id, tenant_id):
        """示例会话读取数据"""
        return SessionRead(
            id=session_id,
            tenant_id=tenant_id,
            user_id="user123",
            platform="test_platform",
            status=SessionStatus.ACTIVE,
            channel_type=ChannelType.DIRECT,
            priority=5,
            assigned_staff_id=None,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            last_message_at=None,
            closed_at=None,
            context_summary=None,
        )

    @pytest.mark.asyncio
    async def test_store_message_success(
        self,
        message_service,
        mock_db,
        mock_session_service,
        sample_message_create,
        tenant_id,
        sample_session_read,
    ):
        """测试成功存储消息"""
        # Arrange - 设置Mock行为
        mock_session_service.get_session.return_value = sample_session_read
        mock_session_service.update_last_message_time.return_value = True

        # 模拟SQLite ID生成
        mock_result = Mock()
        mock_result.fetchone.return_value = (123,)  # 模拟返回的ID
        mock_db.execute.return_value = mock_result

        # 模拟成功的数据库操作
        mock_db.commit.return_value = None
        mock_db.rollback.return_value = None

        # 创建模拟的Message对象
        mock_message = Mock(spec=Message)
        mock_message.id = 123
        mock_message.tenant_id = tenant_id
        mock_message.session_id = sample_message_create.session_id
        mock_message.content = sample_message_create.content
        mock_message.message_type = sample_message_create.message_type
        mock_message.sender_type = sample_message_create.sender_type
        mock_message.sender_id = sample_message_create.sender_id
        mock_message.timestamp = datetime.utcnow()
        mock_message.attachments = []
        mock_message.extra_data = {"key": "value"}
        mock_message.platform_message_id = None
        mock_message.reply_to_id = None
        mock_message.created_at = datetime.utcnow()

        mock_db.refresh.return_value = None

        # Mock Message类的构造函数
        with patch("app.services.message_service.Message", return_value=mock_message):
            # Mock MessageRead.model_validate
            with patch("app.services.message_service.MessageRead") as mock_message_read:
                expected_response = MessageRead(
                    id=123,
                    tenant_id=tenant_id,
                    session_id=sample_message_create.session_id,
                    content=sample_message_create.content,
                    message_type=sample_message_create.message_type,
                    sender_type=sample_message_create.sender_type,
                    sender_id=sample_message_create.sender_id,
                    sender_name=None,
                    timestamp=mock_message.timestamp,
                    attachments=[],
                    metadata={"key": "value"},
                    platform_message_id=None,
                    reply_to_id=None,
                    created_at=mock_message.created_at,
                )
                mock_message_read.model_validate.return_value = expected_response

                # Act - 执行测试
                result = await message_service.store_message(
                    sample_message_create, tenant_id
                )

                # Assert - 验证结果
                assert result is not None
                assert result.content == sample_message_create.content

                # 验证调用
                mock_session_service.get_session.assert_called_once_with(
                    sample_message_create.session_id, tenant_id
                )
                mock_db.add.assert_called_once()
                mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_store_message_session_not_found(
        self, message_service, mock_session_service, sample_message_create, tenant_id
    ):
        """测试会话不存在的情况"""
        # Arrange
        mock_session_service.get_session.return_value = None

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await message_service.store_message(sample_message_create, tenant_id)

        assert exc_info.value.status_code == 404
        assert "会话不存在或没有访问权限" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_store_message_sqlite_id_generation(
        self,
        message_service,
        mock_db,
        mock_session_service,
        sample_message_create,
        tenant_id,
        sample_session_read,
    ):
        """测试SQLite环境下的ID生成"""
        # Arrange
        mock_session_service.get_session.return_value = sample_session_read
        mock_session_service.update_last_message_time.return_value = True

        # 模拟SQLite ID查询结果
        mock_result = Mock()
        mock_result.fetchone.return_value = (456,)
        mock_db.execute.return_value = mock_result

        # 创建模拟Message对象
        mock_message = Mock(spec=Message)
        mock_message.id = 456
        mock_message.tenant_id = tenant_id
        mock_message.session_id = sample_message_create.session_id
        mock_message.content = sample_message_create.content
        mock_message.message_type = sample_message_create.message_type
        mock_message.sender_type = sample_message_create.sender_type
        mock_message.sender_id = sample_message_create.sender_id
        mock_message.timestamp = datetime.utcnow()
        mock_message.attachments = []
        mock_message.extra_data = {"key": "value"}
        mock_message.platform_message_id = None
        mock_message.reply_to_id = None
        mock_message.created_at = datetime.utcnow()

        with patch("app.services.message_service.Message", return_value=mock_message):
            with patch("app.services.message_service.MessageRead") as mock_message_read:
                mock_message_read.model_validate.return_value = Mock(content="test")

                # Act
                await message_service.store_message(sample_message_create, tenant_id)

                # Assert - 验证SQLite ID查询被调用
                mock_db.execute.assert_called_once()
                call_args = mock_db.execute.call_args[0][0]
                assert "SELECT COALESCE(MAX(id), 0) + 1" in str(call_args)

    @pytest.mark.asyncio
    async def test_get_session_messages_success(
        self, message_service, mock_db, session_id, tenant_id
    ):
        """测试成功获取会话消息列表"""
        # Arrange
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_db.execute.return_value = mock_result

        # Act
        result = await message_service.get_session_messages(session_id, tenant_id)

        # Assert
        assert isinstance(result, list)
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_session_messages_with_filters(
        self, message_service, mock_db, session_id, tenant_id
    ):
        """测试带过滤条件的消息查询"""
        # Arrange
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_db.execute.return_value = mock_result

        # Act
        result = await message_service.get_session_messages(
            session_id,
            tenant_id,
            skip=10,
            limit=20,
            message_type=MessageType.TEXT,
            before_time=datetime.utcnow(),
        )

        # Assert
        assert isinstance(result, list)
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_search_messages_success(self, message_service, mock_db, tenant_id):
        """测试消息搜索功能"""
        # Arrange
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_db.execute.return_value = mock_result

        # Act
        result = await message_service.search_messages(tenant_id, "test query")

        # Assert
        assert isinstance(result, list)
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_search_messages_with_filters(
        self, message_service, mock_db, tenant_id, session_id
    ):
        """测试带过滤条件的消息搜索"""
        # Arrange
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_db.execute.return_value = mock_result

        # Act
        result = await message_service.search_messages(
            tenant_id,
            "test query",
            session_id=session_id,
            user_id="user123",
            start_time=datetime.utcnow(),
            end_time=datetime.utcnow(),
        )

        # Assert
        assert isinstance(result, list)
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_message_statistics_success(
        self, message_service, mock_db, tenant_id
    ):
        """测试获取消息统计信息"""
        # Arrange - 正确Mock复杂的数据库查询响应
        # Mock总数查询结果
        mock_total_result = Mock()
        mock_total_result.scalar.return_value = 100

        # Mock按类型统计查询结果
        mock_type_result = Mock()
        mock_type_result.fetchall.return_value = [
            (MessageType.TEXT, 50),
            (MessageType.IMAGE, 30),
            (MessageType.SYSTEM, 20),
        ]

        # Mock按发送者类型统计查询结果
        mock_sender_result = Mock()
        mock_sender_result.fetchall.return_value = [
            (SenderType.USER, 60),
            (SenderType.STAFF, 40),
        ]

        # 设置execute的side_effect来按顺序返回不同的结果
        mock_db.execute.side_effect = [
            mock_total_result,  # 第一次调用：总数查询
            mock_type_result,  # 第二次调用：按类型统计
            mock_sender_result,  # 第三次调用：按发送者类型统计
        ]

        # Act
        result = await message_service.get_message_statistics(tenant_id)

        # Assert
        assert isinstance(result, dict)
        assert "total_messages" in result
        assert "message_types" in result
        assert "sender_types" in result
        assert result["total_messages"] == 100
        # 验证数据库被调用了3次（总数、类型统计、发送者统计）
        assert mock_db.execute.call_count == 3

    @pytest.mark.asyncio
    async def test_get_message_statistics_with_time_range(
        self, message_service, mock_db, tenant_id
    ):
        """测试带时间范围的消息统计"""
        # Arrange - 同样正确Mock复杂的数据库查询响应
        mock_total_result = Mock()
        mock_total_result.scalar.return_value = 50

        mock_type_result = Mock()
        mock_type_result.fetchall.return_value = [
            (MessageType.TEXT, 25),
            (MessageType.IMAGE, 15),
            (MessageType.SYSTEM, 10),
        ]

        mock_sender_result = Mock()
        mock_sender_result.fetchall.return_value = [
            (SenderType.USER, 30),
            (SenderType.STAFF, 20),
        ]

        mock_db.execute.side_effect = [
            mock_total_result,
            mock_type_result,
            mock_sender_result,
        ]

        start_time = datetime.utcnow()
        end_time = datetime.utcnow()

        # Act
        result = await message_service.get_message_statistics(
            tenant_id, start_time=start_time, end_time=end_time
        )

        # Assert
        assert isinstance(result, dict)
        assert result["total_messages"] == 50
        assert mock_db.execute.call_count == 3

    @pytest.mark.asyncio
    async def test_database_error_handling(
        self,
        message_service,
        mock_db,
        mock_session_service,
        sample_message_create,
        tenant_id,
        sample_session_read,
    ):
        """测试数据库错误处理"""
        # Arrange
        mock_session_service.get_session.return_value = sample_session_read
        mock_session_service.update_last_message_time.return_value = True

        # 模拟数据库提交失败
        mock_db.commit.side_effect = Exception("Database commit failed")

        # 模拟SQLite ID生成
        mock_result = Mock()
        mock_result.fetchone.return_value = (123,)
        mock_db.execute.return_value = mock_result

        # 创建模拟Message对象
        mock_message = Mock(spec=Message)
        mock_message.id = 123

        with patch("app.services.message_service.Message", return_value=mock_message):
            # Act & Assert
            with pytest.raises(HTTPException) as exc_info:
                await message_service.store_message(sample_message_create, tenant_id)

            assert exc_info.value.status_code == 500
            assert "消息存储事务提交失败" in exc_info.value.detail

            # 验证rollback被调用
            mock_db.rollback.assert_called()

    @pytest.mark.asyncio
    async def test_session_update_failure_handling(
        self,
        message_service,
        mock_db,
        mock_session_service,
        sample_message_create,
        tenant_id,
        sample_session_read,
    ):
        """测试会话更新失败处理"""
        # Arrange
        mock_session_service.get_session.return_value = sample_session_read
        # 模拟会话更新失败，但不影响消息存储
        mock_session_service.update_last_message_time.side_effect = Exception(
            "Session update failed"
        )

        # 模拟SQLite ID生成和其他成功操作
        mock_result = Mock()
        mock_result.fetchone.return_value = (123,)
        mock_db.execute.return_value = mock_result

        mock_message = Mock(spec=Message)
        mock_message.id = 123
        mock_message.tenant_id = tenant_id
        mock_message.session_id = sample_message_create.session_id
        mock_message.content = sample_message_create.content
        mock_message.message_type = sample_message_create.message_type
        mock_message.sender_type = sample_message_create.sender_type
        mock_message.sender_id = sample_message_create.sender_id
        mock_message.timestamp = datetime.utcnow()
        mock_message.attachments = []
        mock_message.extra_data = {"key": "value"}
        mock_message.platform_message_id = None
        mock_message.reply_to_id = None
        mock_message.created_at = datetime.utcnow()

        with patch("app.services.message_service.Message", return_value=mock_message):
            with patch("app.services.message_service.MessageRead") as mock_message_read:
                mock_message_read.model_validate.return_value = Mock(content="test")

                # Act - 会话更新失败不应该影响消息存储
                result = await message_service.store_message(
                    sample_message_create, tenant_id
                )

                # Assert
                assert result is not None
                # 验证仍然调用了数据库提交
                mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_service_initialization(self, mock_db, mock_session_service):
        """测试服务初始化"""
        service = MessageService(mock_db, mock_session_service)
        assert service.db is mock_db
        assert service.session_service is mock_session_service

    @pytest.mark.asyncio
    async def test_message_read_creation_fallback(
        self,
        message_service,
        mock_db,
        mock_session_service,
        sample_message_create,
        tenant_id,
        sample_session_read,
    ):
        """测试在MessageRead.model_validate失败时能够回退"""
        # Arrange
        mock_session_service.get_session.return_value = sample_session_read
        mock_db.execute.return_value.fetchone.return_value = (123,)  # 模拟ID

        with patch("app.services.message_service.Message", return_value=Mock(id=123)):
            with patch(
                "app.services.message_service.MessageRead.model_validate",
                side_effect=Exception("Pydantic validation failed"),
            ):
                # Act & Assert
                with pytest.raises(HTTPException) as exc_info:
                    await message_service.store_message(
                    sample_message_create, tenant_id
                )

                assert exc_info.value.status_code == 500
                assert "消息存储失败，请稍后重试" in exc_info.value.detail
                mock_db.rollback.assert_called_once()
