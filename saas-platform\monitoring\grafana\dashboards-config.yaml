# AstrBot SaaS - Grafana仪表板配置
# 使用YAML格式，更易维护和版本控制

apiVersion: 1

providers:
  - name: 'AstrBot SaaS Dashboards'
    orgId: 1
    folder: 'AstrBot SaaS'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards

# 仪表板配置模板
dashboard_templates:
  # 核心业务指标仪表板
  business_overview:
    title: "🏢 AstrBot SaaS - 业务总览"
    tags: 
      - "astrbot"
      - "saas" 
      - "business"
    refresh: "30s"
    time_range: "1h"
    panels:
      - type: "stat"
        title: "活跃租户数"
        metric: "astrbot_active_tenants"
        thresholds: [1000, 5000]
        
      - type: "stat" 
        title: "活跃会话数"
        metric: "astrbot_active_sessions"
        thresholds: [10000, 50000]
        
      - type: "timeseries"
        title: "API请求速率"
        metrics:
          - "rate(astrbot_api_requests_total[5m])"
          - "rate(astrbot_messages_total[5m])"

  # 系统性能指标仪表板  
  system_performance:
    title: "🖥️ AstrBot SaaS - 系统性能"
    tags: 
      - "astrbot"
      - "saas"
      - "performance"
    refresh: "30s"
    time_range: "1h"
    panels:
      - type: "timeseries"
        title: "CPU使用率"
        metric: "100 - (avg(irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)"
        unit: "percent"
        
      - type: "timeseries"
        title: "内存使用率"
        metric: "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100"
        unit: "percent"
        
      - type: "timeseries"
        title: "API响应时间"
        metrics:
          - "histogram_quantile(0.95, rate(astrbot_request_duration_seconds_bucket[5m]) * 1000)"
          - "histogram_quantile(0.50, rate(astrbot_request_duration_seconds_bucket[5m]) * 1000)"
        unit: "ms"

  # 数据库和缓存监控
  database_cache:
    title: "🗄️ AstrBot SaaS - 数据库&缓存"
    tags: 
      - "astrbot"
      - "saas"
      - "database"
      - "cache"
    refresh: "30s"
    time_range: "1h"
    panels:
      - type: "timeseries"
        title: "PostgreSQL操作速率"
        metrics:
          - "rate(postgres_stat_database_tup_inserted_total[5m])"
          - "rate(postgres_stat_database_tup_updated_total[5m])"
          - "rate(postgres_stat_database_tup_deleted_total[5m])"
          
      - type: "timeseries"
        title: "Redis性能指标"
        metrics:
          - "rate(redis_commands_total[5m])"
          - "redis_connected_clients"

  # 错误监控和告警
  errors_alerts:
    title: "⚠️ AstrBot SaaS - 错误&告警"
    tags: 
      - "astrbot"
      - "saas"
      - "errors"
      - "alerts"
    refresh: "30s"
    time_range: "1h"
    panels:
      - type: "timeseries"
        title: "服务错误率"
        metrics:
          - "rate(astrbot_api_requests_total{status=~\"4..|5..\"}[5m]) / rate(astrbot_api_requests_total[5m])"
          - "rate(astrbot_llm_errors_total[5m]) / rate(astrbot_llm_requests_total[5m])"
        unit: "percentunit"
        
      - type: "stat"
        title: "活跃告警状态"
        metric: "ALERTS{alertstate=\"firing\"}"
        color_mapping:
          0: "green"
          "1-999": "red"

# 告警规则配置
alerting_rules:
  - name: "astrbot_high_error_rate"
    condition: "rate(astrbot_api_requests_total{status=~\"5..\"}[5m]) > 0.1"
    for: "5m"
    severity: "critical"
    message: "AstrBot API错误率过高"
    
  - name: "astrbot_high_response_time"
    condition: "histogram_quantile(0.95, rate(astrbot_request_duration_seconds_bucket[5m])) > 2"
    for: "10m"
    severity: "warning"
    message: "AstrBot API响应时间过长"
    
  - name: "astrbot_no_active_tenants"
    condition: "astrbot_active_tenants == 0"
    for: "30m"
    severity: "warning"
    message: "无活跃租户" 