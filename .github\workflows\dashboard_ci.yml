name: AstrBot Dashboard CI

on: 
  push:
    paths:
      - 'dashboard/**'
      - '.github/workflows/dashboard_ci.yml'
  pull_request:
    paths:
      - 'dashboard/**'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check if dashboard exists
        id: check_dashboard
        run: |
          if [ -d "dashboard" ]; then
            echo "dashboard_exists=true" >> $GITHUB_OUTPUT
          else
            echo "dashboard_exists=false" >> $GITHUB_OUTPUT
            echo "⚠️ Dashboard目录不存在，跳过构建"
          fi

      - name: Setup Node.js
        if: steps.check_dashboard.outputs.dashboard_exists == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: dashboard/package.json

      - name: Install dependencies
        if: steps.check_dashboard.outputs.dashboard_exists == 'true'
        run: |
          cd dashboard
          npm ci || npm install

      - name: Build dashboard
        if: steps.check_dashboard.outputs.dashboard_exists == 'true'
        run: |
          cd dashboard
          npm run build

      - name: Inject Commit SHA
        if: steps.check_dashboard.outputs.dashboard_exists == 'true'
        id: get_sha
        run: |
          echo "COMMIT_SHA=$(git rev-parse HEAD)" >> $GITHUB_ENV
          mkdir -p dashboard/dist/assets
          echo $COMMIT_SHA > dashboard/dist/assets/version

      - name: Archive production artifacts
        if: steps.check_dashboard.outputs.dashboard_exists == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: dist-without-markdown
          path: |
            dashboard/dist
            !dist/**/*.md

      - name: Skip notification
        if: steps.check_dashboard.outputs.dashboard_exists == 'false'
        run: |
          echo "✅ Dashboard CI跳过 - 未检测到dashboard目录"
