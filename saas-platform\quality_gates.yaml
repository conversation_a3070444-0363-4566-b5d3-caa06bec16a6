# AstrBot SaaS Platform - 质量门禁配置
# 定义各阶段的质量检查标准和阈值

quality_gates:
  # 提交阶段质量门禁
  commit_stage:
    name: "提交质量检查"
    checks:
      - name: "代码风格检查"
        tool: "black"
        config: "pyproject.toml"
        threshold: "100%"
        blocking: true
        
      - name: "类型检查"
        tool: "mypy"
        config: "mypy.ini"
        threshold: "0 errors"
        blocking: true
        
      - name: "安全检查"
        tool: "bandit"
        threshold: "0 high severity"
        blocking: true
        
      - name: "依赖漏洞检查"
        tool: "safety"
        threshold: "0 vulnerabilities"
        blocking: true
        
      - name: "单元测试"
        tool: "pytest"
        path: "tests/unit/"
        threshold: "100%"
        blocking: true
        coverage_threshold: "85%"
        
  # 集成阶段质量门禁
  integration_stage:
    name: "集成质量检查"
    checks:
      - name: "集成测试"
        tool: "pytest"
        path: "tests/integration/"
        threshold: "95%"
        blocking: true
        
      - name: "API契约测试"
        tool: "pytest"
        path: "tests/contract/"
        threshold: "100%"
        blocking: true
        
      - name: "数据库迁移测试"
        tool: "alembic"
        threshold: "success"
        blocking: true
        
      - name: "性能基准测试"
        tool: "locust"
        config: "tests/performance/baseline.py"
        thresholds:
          response_time_95th: "500ms"
          failure_rate: "1%"
        blocking: false
        
  # 部署阶段质量门禁
  deployment_stage:
    name: "部署质量检查"
    checks:
      - name: "端到端测试"
        tool: "pytest"
        path: "tests/e2e/"
        threshold: "100%"
        blocking: true
        
      - name: "烟雾测试"
        tool: "pytest"
        path: "tests/smoke/"
        threshold: "100%"
        blocking: true
        
      - name: "安全扫描"
        tool: "docker scan"
        threshold: "0 critical"
        blocking: true
        
      - name: "部署验证"
        tool: "healthcheck"
        endpoints:
          - "/api/v1/health"
          - "/api/v1/metrics"
        threshold: "all healthy"
        blocking: true

# 质量指标阈值配置
quality_thresholds:
  code_coverage:
    unit_tests: 85%
    integration_tests: 75%
    overall: 80%
    
  test_metrics:
    unit_test_pass_rate: 100%
    integration_test_pass_rate: 95%
    e2e_test_pass_rate: 100%
    
  performance_metrics:
    api_response_time_p95: 500  # ms
    api_response_time_p99: 1000  # ms
    throughput_min: 100  # requests/second
    error_rate_max: 1%
    
  security_metrics:
    critical_vulnerabilities: 0
    high_vulnerabilities: 0
    medium_vulnerabilities_max: 5
    
  code_quality:
    cyclomatic_complexity_max: 10
    code_duplication_max: 3%
    maintainability_index_min: 70

# 质量报告配置
reporting:
  formats:
    - json
    - markdown
    - html
    
  destinations:
    - file: "reports/quality_report.json"
    - slack: "${SLACK_QUALITY_CHANNEL}"
    - email: "${QUALITY_TEAM_EMAIL}"
    
  schedule:
    daily_report: "08:00"
    weekly_summary: "Monday 09:00"
    release_report: "on_release"

# 异常处理配置
exception_handling:
  retry_attempts: 3
  timeout_minutes: 30
  
  escalation:
    critical_failure:
      - notify_team_lead
      - create_incident
      - block_deployment
      
    warning_threshold:
      - notify_developers
      - create_issue
      - continue_pipeline

# 工具集成配置
tool_integrations:
  pytest:
    config_file: "pyproject.toml"
    markers:
      - unit
      - integration
      - e2e
      - performance
      - security
      
  sonarqube:
    server_url: "${SONAR_SERVER_URL}"
    project_key: "astrbot-saas"
    quality_gate: "AstrBot-SaaS-Gate"
    
  grafana:
    dashboard_id: "astrbot-saas-quality"
    alerts:
      - test_failure_rate
      - coverage_drop
      - performance_degradation 