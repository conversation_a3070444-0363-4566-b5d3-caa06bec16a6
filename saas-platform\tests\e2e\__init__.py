"""
AstrBot SaaS Platform - 端到端测试框架

本模块提供完整的业务流程端到端测试，确保从用户注册到AI服务交付的全链路质量保证。

核心测试场景：
1. 租户全生命周期管理
2. 用户会话管理流程
3. 消息处理与AI响应链路
4. 多租户隔离与安全验证
5. 系统集成与第三方服务
6. 业务异常场景与恢复能力

测试覆盖范围：
- 前端用户界面交互
- API接口调用链路
- 数据库事务完整性
- 第三方服务集成
- 业务规则执行
- 异常处理机制
- 性能与可用性

测试质量标准：
- 功能完整性 >= 95%
- 业务流程覆盖率 >= 90%
- 异常场景覆盖率 >= 80%
- 测试执行稳定性 >= 98%

使用方式：
    from tests.e2e import BusinessProcessTest
    
    # 执行特定业务场景测试
    process_test = BusinessProcessTest("tenant_onboarding")
    await process_test.execute()
"""

from .business_process_test import BusinessProcessTest
# from .e2e_test_suite import E2ETestSuite
# from .scenario_manager import ScenarioManager
# from .test_data_factory import TestDataFactory
# from .assertion_framework import AssertionFramework

__all__ = [
    'BusinessProcessTest',
    # 'E2ETestSuite', 
    # 'ScenarioManager',
    # 'TestDataFactory',
    # 'AssertionFramework'
]

__version__ = '1.0.0' 