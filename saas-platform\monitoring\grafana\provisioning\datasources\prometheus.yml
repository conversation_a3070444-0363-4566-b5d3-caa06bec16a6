# Grafana数据源配置
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    uid: prometheus_uid
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      manageAlerts: true
      prometheusType: Prometheus
      prometheusVersion: 2.40.0
      cacheLevel: 'High'
      disableRecordingRules: false
      incrementalQueryOverlapWindow: 10m
    version: 1
    
  - name: Loki
    type: loki
    uid: loki_uid
    access: proxy
    url: http://loki:3100
    isDefault: false
    editable: true
    jsonData:
      maxLines: 1000
      derivedFields:
        - datasourceUid: jaeger_uid
          matcherRegex: '"trace_id":"(\w+)"'
          name: TraceID
          url: '$${__value.raw}'
          urlDisplayLabel: 'View Trace'
    version: 1
    
  - name: Jaeger
    type: jaeger
    uid: jaeger_uid
    access: proxy
    url: http://jaeger:16686
    isDefault: false
    editable: true
    jsonData:
      tracesToLogsV2:
        datasourceUid: loki_uid
        spanStartTimeShift: '1h'
        spanEndTimeShift: '-1h'
        tags: ['job', 'instance', 'pod', 'namespace']
        filterByTraceID: false
        filterBySpanID: false
        customQuery: true
        query: 'method="${__span.tags.method}"'
      tracesToMetrics:
        datasourceUid: prometheus_uid
        spanStartTimeShift: '1h'
        spanEndTimeShift: '-1h'
        tags: [{ key: 'service.name', value: 'service' }, { key: 'job' }]
        queries:
          - name: 'Sample query'
            query: 'sum(rate(traces_spanmetrics_latency_bucket{$$__tags}[5m]))'
      serviceMap:
        datasourceUid: prometheus_uid
      nodeGraph:
        enabled: true
      search:
        hide: false
      spanBar:
        type: 'Tag'
        tag: 'http.path'
    version: 1 