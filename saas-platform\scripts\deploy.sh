#!/bin/bash

# AstrBot SaaS Platform - 智能化部署脚本
# DevOps最佳实践：多环境支持、健康检查、自动回滚、错误处理

set -euo pipefail

# =====================================================
# 配置和常量定义
# =====================================================
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="/tmp/astrbot-deploy-$(date +%Y%m%d-%H%M%S).log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_ENVIRONMENT="staging"
DEFAULT_NAMESPACE="astrbot-saas"
DEFAULT_TIMEOUT=300
DEFAULT_REPLICAS=3

# =====================================================
# 工具函数
# =====================================================
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")  echo -e "${GREEN}[INFO]${NC} $timestamp - $message" | tee -a "$LOG_FILE" ;;
        "WARN")  echo -e "${YELLOW}[WARN]${NC} $timestamp - $message" | tee -a "$LOG_FILE" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $timestamp - $message" | tee -a "$LOG_FILE" ;;
        "DEBUG") echo -e "${BLUE}[DEBUG]${NC} $timestamp - $message" | tee -a "$LOG_FILE" ;;
    esac
}

error_exit() {
    log "ERROR" "$1"
    exit 1
}

check_prerequisites() {
    log "INFO" "检查部署前提条件..."
    
    # 检查必需的工具
    local tools=("kubectl" "docker" "helm")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            error_exit "必需工具未安装: $tool"
        fi
    done
    
    # 检查Kubernetes连接
    if ! kubectl cluster-info &> /dev/null; then
        error_exit "无法连接到Kubernetes集群"
    fi
    
    # 检查Docker镜像构建环境
    if ! docker info &> /dev/null; then
        error_exit "Docker服务未运行"
    fi
    
    log "INFO" "前提条件检查通过"
}

# =====================================================
# 参数解析
# =====================================================
usage() {
    cat << EOF
AstrBot SaaS Platform 部署脚本

用法: $0 [选项]

选项:
    -e, --environment ENV     部署环境 (dev|staging|production) [默认: $DEFAULT_ENVIRONMENT]
    -n, --namespace NAMESPACE K8s命名空间 [默认: $DEFAULT_NAMESPACE]
    -t, --timeout SECONDS    部署超时时间 [默认: $DEFAULT_TIMEOUT]
    -r, --replicas COUNT      副本数量 [默认: $DEFAULT_REPLICAS]
    -i, --image TAG          镜像标签 [默认: latest]
    --skip-build             跳过镜像构建
    --skip-migration         跳过数据库迁移
    --dry-run               试运行模式
    --rollback              回滚到上一个版本
    -h, --help              显示帮助信息

示例:
    $0 --environment production --replicas 5
    $0 --rollback --environment staging
    $0 --dry-run --environment dev

EOF
}

# 解析命令行参数
ENVIRONMENT="$DEFAULT_ENVIRONMENT"
NAMESPACE="$DEFAULT_NAMESPACE"
TIMEOUT="$DEFAULT_TIMEOUT"
REPLICAS="$DEFAULT_REPLICAS"
IMAGE_TAG="latest"
SKIP_BUILD=false
SKIP_MIGRATION=false
DRY_RUN=false
ROLLBACK=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -r|--replicas)
            REPLICAS="$2"
            shift 2
            ;;
        -i|--image)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-migration)
            SKIP_MIGRATION=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --rollback)
            ROLLBACK=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            error_exit "未知参数: $1"
            ;;
    esac
done

# 验证环境参数
case $ENVIRONMENT in
    dev|staging|production) ;;
    *) error_exit "无效的环境: $ENVIRONMENT (支持: dev, staging, production)" ;;
esac

# =====================================================
# 部署函数
# =====================================================
build_image() {
    if [[ "$SKIP_BUILD" == "true" ]]; then
        log "INFO" "跳过镜像构建"
        return 0
    fi
    
    log "INFO" "构建Docker镜像..."
    
    local dockerfile="Dockerfile.production"
    local image_name="astrbot/saas-platform:$IMAGE_TAG"
    
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        dockerfile="Dockerfile.dev"
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "INFO" "[DRY RUN] 将构建镜像: $image_name"
        return 0
    fi
    
    # 构建镜像
    docker build \
        --file "$PROJECT_ROOT/$dockerfile" \
        --tag "$image_name" \
        --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --build-arg VCS_REF="$(git rev-parse --short HEAD)" \
        --build-arg VERSION="$IMAGE_TAG" \
        "$PROJECT_ROOT" || error_exit "镜像构建失败"
    
    # 推送镜像（生产环境）
    if [[ "$ENVIRONMENT" == "production" ]]; then
        log "INFO" "推送镜像到仓库..."
        docker push "$image_name" || error_exit "镜像推送失败"
    fi
    
    log "INFO" "镜像构建完成: $image_name"
}

prepare_namespace() {
    log "INFO" "准备Kubernetes命名空间..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "INFO" "[DRY RUN] 将创建命名空间: $NAMESPACE"
        return 0
    fi
    
    # 创建命名空间（如果不存在）
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        kubectl create namespace "$NAMESPACE"
        log "INFO" "创建命名空间: $NAMESPACE"
    fi
    
    # 设置标签
    kubectl label namespace "$NAMESPACE" \
        environment="$ENVIRONMENT" \
        managed-by=astrbot-deploy \
        --overwrite
}

database_migration() {
    if [[ "$SKIP_MIGRATION" == "true" ]]; then
        log "INFO" "跳过数据库迁移"
        return 0
    fi
    
    log "INFO" "执行数据库迁移..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "INFO" "[DRY RUN] 将执行数据库迁移"
        return 0
    fi
    
    # 创建迁移Job
    local migration_job="migration-$(date +%s)"
    
    kubectl apply -f - <<EOF
apiVersion: batch/v1
kind: Job
metadata:
  name: $migration_job
  namespace: $NAMESPACE
spec:
  ttlSecondsAfterFinished: 300
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: migrate
        image: astrbot/saas-platform:$IMAGE_TAG
        command: ["python", "-m", "alembic", "upgrade", "head"]
        envFrom:
        - configMapRef:
            name: astrbot-config
        - secretRef:
            name: astrbot-secrets
        env:
        - name: DATABASE_URL
          value: "postgresql+asyncpg://\$(DATABASE_USER):\$(DATABASE_PASSWORD)@\$(DATABASE_HOST):\$(DATABASE_PORT)/\$(DATABASE_NAME)"
EOF

    # 等待迁移完成
    log "INFO" "等待数据库迁移完成..."
    kubectl wait --for=condition=complete job/$migration_job \
        --namespace="$NAMESPACE" \
        --timeout="${TIMEOUT}s" || error_exit "数据库迁移失败"
    
    log "INFO" "数据库迁移完成"
}

deploy_application() {
    log "INFO" "部署应用到Kubernetes..."
    
    local deployment_file="$PROJECT_ROOT/k8s/deployment.yaml"
    if [[ "$ENVIRONMENT" == "production" ]]; then
        deployment_file="$PROJECT_ROOT/k8s/production-deployment.yaml"
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "INFO" "[DRY RUN] 将部署配置: $deployment_file"
        return 0
    fi
    
    # 更新镜像标签
    local temp_file=$(mktemp)
    sed "s|image: astrbot/saas-platform:latest|image: astrbot/saas-platform:$IMAGE_TAG|g" \
        "$deployment_file" > "$temp_file"
    
    # 更新副本数
    sed -i "s|replicas: [0-9]*|replicas: $REPLICAS|g" "$temp_file"
    
    # 应用配置
    kubectl apply -f "$temp_file" --namespace="$NAMESPACE"
    rm "$temp_file"
    
    log "INFO" "应用配置已应用"
}

health_check() {
    log "INFO" "执行健康检查..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "INFO" "[DRY RUN] 将执行健康检查"
        return 0
    fi
    
    # 等待Pod就绪
    log "INFO" "等待Pod就绪..."
    kubectl wait --for=condition=ready pod \
        --selector=app=astrbot-saas \
        --namespace="$NAMESPACE" \
        --timeout="${TIMEOUT}s" || error_exit "Pod启动超时"
    
    # 等待部署完成
    kubectl rollout status deployment/astrbot-saas \
        --namespace="$NAMESPACE" \
        --timeout="${TIMEOUT}s" || error_exit "部署更新失败"
    
    # 健康检查
    local service_url
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        service_url="http://localhost:8000/api/v1/health"
    else
        service_url="https://api.astrbot.com/api/v1/health"
    fi
    
    log "INFO" "测试应用健康状态..."
    for i in {1..10}; do
        if curl -f -s "$service_url" > /dev/null; then
            log "INFO" "健康检查通过"
            return 0
        fi
        log "WARN" "健康检查失败，重试 $i/10..."
        sleep 10
    done
    
    error_exit "健康检查失败"
}

rollback_deployment() {
    log "INFO" "回滚部署..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "INFO" "[DRY RUN] 将回滚部署"
        return 0
    fi
    
    kubectl rollout undo deployment/astrbot-saas \
        --namespace="$NAMESPACE" || error_exit "回滚失败"
    
    kubectl rollout status deployment/astrbot-saas \
        --namespace="$NAMESPACE" \
        --timeout="${TIMEOUT}s" || error_exit "回滚验证失败"
    
    log "INFO" "回滚完成"
}

cleanup() {
    log "INFO" "清理临时资源..."
    
    # 清理临时文件
    if [[ -f "$temp_file" ]]; then
        rm -f "$temp_file"
    fi
    
    # 清理旧的迁移Job
    kubectl delete jobs -l job-type=migration \
        --namespace="$NAMESPACE" \
        --field-selector=status.successful=1 \
        --ignore-not-found=true
}

# =====================================================
# 主执行流程
# =====================================================
main() {
    log "INFO" "开始AstrBot SaaS Platform部署"
    log "INFO" "环境: $ENVIRONMENT | 命名空间: $NAMESPACE | 镜像标签: $IMAGE_TAG"
    
    # 设置清理trap
    trap cleanup EXIT
    
    # 检查前提条件
    check_prerequisites
    
    # 执行回滚或部署
    if [[ "$ROLLBACK" == "true" ]]; then
        rollback_deployment
    else
        build_image
        prepare_namespace
        database_migration
        deploy_application
        health_check
    fi
    
    log "INFO" "部署完成！日志文件: $LOG_FILE"
    log "INFO" "应用地址: https://api.astrbot.com (生产) 或 http://localhost:8000 (开发)"
}

# 执行主流程
main "$@" 