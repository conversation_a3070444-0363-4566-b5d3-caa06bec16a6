"""
Mock策略修复测试 - 解决SQLAlchemy模型Mock和异步数据库问题
专门用于修复服务层测试中的核心Mock问题
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from uuid import uuid4
from datetime import datetime

# 重要：使用真实的模型类而不是Mock
from app.models.tenant import Tenant, TenantStatus
from app.models.session import Session, SessionStatus
from app.models.message import Message, SenderType
from app.schemas.tenant import TenantCreate, TenantRead
from app.schemas.session import SessionRead
from app.schemas.message import MessageRead, MessageCreate


class TestMockStrategies:
    """Mock策略修复测试类 - 解决核心Mock配置问题"""

    @pytest.fixture
    def enhanced_db_session(self):
        """增强的数据库会话Mock - 修复异步查询问题"""
        mock_session = AsyncMock()

        # 创建正确的查询结果Mock链
        # execute() 的返回结果是同步的
        mock_result = MagicMock()

        # scalar_one_or_none() 是同步方法
        mock_result.scalar_one_or_none.return_value = None

        # scalars().all() 链式调用
        # scalars() 返回一个同步对象
        mock_scalars_obj = MagicMock()
        mock_scalars_obj.all.return_value = []
        mock_result.scalars.return_value = mock_scalars_obj

        # 配置execute方法返回正确的结果
        mock_session.execute.return_value = mock_result
        mock_session.add = MagicMock()
        mock_session.commit = AsyncMock()
        mock_session.refresh = AsyncMock()
        mock_session.rollback = AsyncMock()

        return mock_session

    @pytest.mark.asyncio
    async def test_tenant_service_with_real_models(self, enhanced_db_session):
        """使用真实模型测试租户服务 - 避免SQLAlchemy Mock问题"""
        from app.services.tenant_service import TenantService

        # 准备测试数据
        tenant_data = TenantCreate(name="Test Company", email="<EMAIL>")

        # 创建真实的租户模型实例
        tenant_model = Tenant(
            name=tenant_data.name, email=tenant_data.email, status=TenantStatus.ACTIVE
        )
        tenant_model.id = uuid4()
        tenant_model.created_at = datetime.utcnow()
        tenant_model.updated_at = datetime.utcnow()

        # 配置数据库Mock - 第一次查询返回None（无重复），第二次返回创建的模型
        enhanced_db_session.execute.return_value.scalar_one_or_none.side_effect = [
            None,
            tenant_model,
        ]

        # Mock Pydantic序列化
        with patch("app.schemas.tenant.TenantRead.model_validate") as mock_validate:
            mock_validate.return_value = TenantRead(
                id=tenant_model.id,
                name=tenant_model.name,
                email=tenant_model.email,
                status="active",
                plan="basic",
                metadata={},
                created_at=tenant_model.created_at,
                updated_at=tenant_model.updated_at,
                is_active=True,
                display_name=tenant_model.name,
            )

            # 执行测试
            service = TenantService(enhanced_db_session)
            result = await service.create_tenant(tenant_data)

            # 验证结果
            assert result.name == tenant_data.name
            assert result.email == tenant_data.email
            assert result.is_active is True
            enhanced_db_session.add.assert_called_once()
            enhanced_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_session_service_with_real_models(self, enhanced_db_session):
        """使用真实模型测试会话服务 - 修复SQLAlchemy查询问题"""
        from app.services.session_service import SessionService

        # 准备测试数据
        tenant_id = uuid4()
        user_id = "telegram:user123"
        platform = "telegram"

        # 创建真实的会话模型实例
        session_model = Session(
            tenant_id=tenant_id,
            user_id=user_id,
            platform=platform,
            status=SessionStatus.WAITING,
        )
        session_model.id = uuid4()
        session_model.created_at = datetime.utcnow()
        session_model.updated_at = datetime.utcnow()

        # 配置数据库Mock - 第一次查询返回None（无现有会话），创建后返回新会话
        enhanced_db_session.execute.return_value.scalar_one_or_none.side_effect = [
            None,
            session_model,
        ]

        # Mock SessionRead序列化
        with patch("app.schemas.session.SessionRead.model_validate") as mock_validate:
            mock_validate.return_value = SessionRead(
                id=session_model.id,
                tenant_id=tenant_id,
                user_id=user_id,
                platform=platform,
                status="waiting",
                created_at=session_model.created_at,
                updated_at=session_model.updated_at,
            )

            # 执行测试
            service = SessionService(enhanced_db_session)
            result = await service.create_or_get_session(user_id, platform, tenant_id)

            # 验证结果
            assert result.user_id == user_id
            assert result.platform == platform
            assert result.status == "waiting"
            enhanced_db_session.add.assert_called_once()
            enhanced_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_message_service_with_dependencies(self, enhanced_db_session):
        """测试消息服务与依赖 - 正确配置服务依赖关系"""
        from app.services.message_service import MessageService
        from app.services.session_service import SessionService

        # 准备测试数据
        session_id = uuid4()
        tenant_id = uuid4()
        content = "Test message"

        # Mock SessionService依赖
        mock_session_service = AsyncMock(spec=SessionService)
        mock_session_read = SessionRead(
            id=session_id,
            tenant_id=tenant_id,
            user_id="telegram:user123",
            platform="telegram",
            status="active",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        mock_session_service.get_session.return_value = mock_session_read

        # 创建真实的消息模型
        message_model = Message(
            session_id=session_id,
            content=content,
            sender_type=SenderType.USER,
            sender_id="telegram:user123",
        )
        message_model.id = 1  # MessageRead expects int ID
        message_model.tenant_id = tenant_id
        message_model.created_at = datetime.utcnow()
        message_model.timestamp = datetime.utcnow()

        # 配置数据库Mock
        enhanced_db_session.execute.return_value.scalar_one_or_none.return_value = (
            message_model
        )

        # Mock MessageRead序列化
        with patch("app.schemas.message.MessageRead.model_validate") as mock_validate:
            mock_validate.return_value = MessageRead(
                id=1,
                tenant_id=tenant_id,
                session_id=session_id,
                content=content,
                sender_type="user",  # 小写枚举值
                sender_id="telegram:user123",
                message_type="text",
                timestamp=message_model.timestamp,
                created_at=message_model.created_at,
                metadata={},
            )

            # 执行测试
            service = MessageService(enhanced_db_session, mock_session_service)

            # 创建消息数据
            message_data = MessageCreate(
                session_id=session_id,
                content=content,
                sender_type="user",
                sender_id="telegram:user123",
                message_type="text",
            )

            result = await service.store_message(message_data, tenant_id)

            # 验证结果
            assert result is not None
            enhanced_db_session.add.assert_called_once()
            enhanced_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_list_operations_with_scalars(self, enhanced_db_session):
        """测试列表操作 - 修复scalars().all()问题"""
        from app.services.tenant_service import TenantService

        # 创建测试租户列表
        tenant1 = Tenant(name="Company 1", email="<EMAIL>")
        tenant1.id = uuid4()
        tenant1.created_at = datetime.utcnow()

        tenant2 = Tenant(name="Company 2", email="<EMAIL>")
        tenant2.id = uuid4()
        tenant2.created_at = datetime.utcnow()

        tenant_list = [tenant1, tenant2]

        # 配置Mock - 正确配置链式调用
        enhanced_db_session.execute.return_value.scalars.return_value.all.return_value = (
            tenant_list
        )

        # Mock TenantRead列表序列化
        with patch("app.schemas.tenant.TenantRead.model_validate") as mock_validate:
            mock_validate.side_effect = [
                TenantRead(
                    id=tenant1.id,
                    name=tenant1.name,
                    email=tenant1.email,
                    status="active",
                    plan="basic",
                    metadata={},
                    created_at=tenant1.created_at,
                    updated_at=tenant1.created_at,
                    is_active=True,
                    display_name=tenant1.name,
                ),
                TenantRead(
                    id=tenant2.id,
                    name=tenant2.name,
                    email=tenant2.email,
                    status="active",
                    plan="basic",
                    metadata={},
                    created_at=tenant2.created_at,
                    updated_at=tenant2.created_at,
                    is_active=True,
                    display_name=tenant2.name,
                ),
            ]

            # 执行测试
            service = TenantService(enhanced_db_session)
            result = await service.list_tenants()

            # 验证结果
            assert len(result.items) == 2
            assert result.items[0].name == "Company 1"
            assert result.items[1].name == "Company 2"
            assert result.total == 2

    def test_enum_consistency(self):
        """测试枚举值一致性 - 确保模型和Schema枚举匹配"""
        # 测试SessionStatus枚举值
        assert SessionStatus.WAITING.value == "waiting"
        assert SessionStatus.ACTIVE.value == "active"
        assert SessionStatus.CLOSED.value == "closed"

        # 测试SenderType枚举值
        assert SenderType.USER.value == "user"
        assert SenderType.STAFF.value == "staff"
        assert SenderType.BOT.value == "bot"
        assert SenderType.SYSTEM.value == "system"

        # 测试TenantStatus枚举值
        assert TenantStatus.ACTIVE.value == "active"
        assert TenantStatus.SUSPENDED.value == "suspended"
        assert TenantStatus.DEACTIVATED.value == "deactivated"

    @pytest.mark.asyncio
    async def test_error_handling_patterns(self, enhanced_db_session):
        """测试错误处理模式 - 验证异常处理逻辑"""
        from app.services.tenant_service import TenantService
        from fastapi import HTTPException

        # 配置Mock抛出异常
        enhanced_db_session.execute.side_effect = Exception("Database error")

        # 创建服务和测试数据
        service = TenantService(enhanced_db_session)
        tenant_data = TenantCreate(name="Test Company", email="<EMAIL>")

        # 验证异常处理
        with pytest.raises(HTTPException) as exc_info:
            await service.create_tenant(tenant_data)

        assert exc_info.value.status_code == 500
        assert "租户创建失败" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_transaction_rollback(self, enhanced_db_session):
        """测试事务回滚 - 验证异常时的数据库回滚"""
        from app.services.tenant_service import TenantService

        # 配置Mock在commit时抛出异常，并确保前置检查通过
        enhanced_db_session.commit.side_effect = Exception("Commit failed")
        # 确保两次存在性检查都返回None
        enhanced_db_session.execute.return_value.scalar_one_or_none.side_effect = [
            None,
            None,
        ]

        service = TenantService(enhanced_db_session)
        tenant_data = TenantCreate(name="Test Company", email="<EMAIL>")

        # 验证异常和回滚
        with pytest.raises(Exception):
            await service.create_tenant(tenant_data)

        # 验证add被调用但commit失败
        enhanced_db_session.add.assert_called_once()
        enhanced_db_session.rollback.assert_called_once()
