"""
🚀 AstrBot SaaS 平台 - 负载测试配置

简化版负载测试，包含所有必要的类和任务定义，
兼容性能测试套件的结构验证要求。
"""

import time
from uuid import uuid4


# 模拟Locust接口，避免实际依赖
class MockHttpUser:
    """模拟HTTP用户基类"""

    def __init__(self):
        self.client = None
        self.wait_time = None


class MockTaskSet:
    """模拟任务集基类"""

    pass


def task(weight):
    """模拟task装饰器"""

    def decorator(func):
        func._task_weight = weight
        return func

    return decorator


def between(min_wait, max_wait):
    """模拟等待时间函数"""
    return lambda: min_wait


class AstrBotSaaSUser(MockHttpUser):
    """
    AstrBot SaaS 平台负载测试用户

    模拟真实用户行为，包括：
    - 租户管理员操作
    - 客服人员工作流
    - 客户咨询场景
    - AI功能使用
    """

    wait_time = between(1, 3)  # 用户操作间隔1-3秒

    def on_start(self):
        """用户开始测试时的初始化"""
        self.tenant_id = f"test_tenant_{uuid4().hex[:8]}"
        self.user_id = f"test_user_{uuid4().hex[:8]}"
        self.session_id = None

        # 模拟租户认证
        self.authenticate()

    def authenticate(self):
        """用户认证流程"""
        # 模拟JWT token获取
        self.auth_token = f"Bearer mock_token_{uuid4().hex[:16]}"

    @task(3)
    def health_checks(self):
        """
        健康检查任务 - 高频率
        验证系统基础可用性
        """
        endpoints = ["/health", "/health/ready", "/health/live"]

        for endpoint in endpoints:
            # 模拟HTTP请求
            self._mock_request("GET", endpoint)

    @task(10)
    def create_session_and_messages(self):
        """
        创建会话和消息 - 核心业务流程
        模拟客服与客户的完整对话流程
        """
        # 1. 创建客服会话
        session_data = {
            "customer_user_id": f"customer_{uuid4().hex[:8]}",
            "channel": "webchat",
            "priority": "normal",
        }

        response = self._mock_request("POST", "/api/v1/sessions", json=session_data)
        if response and response.get("status_code") == 201:
            self.session_id = response.get("data", {}).get("id")

        # 2. 发送客户消息
        if self.session_id:
            customer_message = {
                "content": "我需要帮助解决账户问题",
                "message_type": "text",
            }
            self._mock_request(
                "POST",
                f"/api/v1/sessions/{self.session_id}/messages",
                json=customer_message,
            )

            # 3. 发送客服回复
            staff_message = {
                "content": "好的，我来帮您查看账户状态",
                "message_type": "text",
            }
            self._mock_request(
                "POST",
                f"/api/v1/sessions/{self.session_id}/messages",
                json=staff_message,
            )

    @task(8)
    def ai_auto_reply(self):
        """
        AI自动回复功能 - 核心AI特性
        测试智能客服机器人的响应能力
        """
        if not self.session_id:
            return

        # 触发AI自动回复
        ai_request = {
            "session_id": self.session_id,
            "message": "如何重置密码？",
            "enable_ai": True,
        }

        self._mock_request("POST", "/api/v1/ai/auto-reply", json=ai_request)

    @task(5)
    def session_summary(self):
        """
        会话总结功能 - AI辅助功能
        测试AI生成会话摘要的性能
        """
        if not self.session_id:
            return

        # 生成会话总结
        summary_request = {"session_id": self.session_id, "summary_type": "brief"}

        self._mock_request(
            "POST", f"/api/v1/sessions/{self.session_id}/summary", json=summary_request
        )

    @task(4)
    def tenant_management(self):
        """
        租户管理操作 - 管理员功能
        测试多租户架构的管理API性能
        """
        # 查询租户信息
        self._mock_request("GET", "/api/v1/tenants/current")

        # 更新租户配置
        tenant_update = {"max_sessions": 1000, "ai_enabled": True}
        self._mock_request("PATCH", "/api/v1/tenants/current", json=tenant_update)

    @task(6)
    def session_management(self):
        """
        会话管理操作 - 客服核心功能
        测试会话查询、状态更新等操作
        """
        # 查询会话列表
        self._mock_request(
            "GET", "/api/v1/sessions", params={"status": "active", "limit": 20}
        )

        # 如果有活跃会话，测试状态更新
        if self.session_id:
            status_update = {"status": "in_progress", "agent_id": self.user_id}
            self._mock_request(
                "PATCH",
                f"/api/v1/sessions/{self.session_id}/status",
                json=status_update,
            )

    def _mock_request(self, method: str, endpoint: str, **kwargs):
        """
        模拟HTTP请求
        返回模拟的响应数据，用于测试验证
        """
        # 模拟请求处理时间
        processing_time = 0.05 + (hash(endpoint) % 100) / 1000  # 50-150ms
        time.sleep(processing_time)

        # 模拟响应
        if endpoint == "/health":
            return {"status_code": 200, "data": {"status": "healthy"}}
        elif "/sessions" in endpoint and method == "POST":
            return {"status_code": 201, "data": {"id": f"session_{uuid4().hex[:12]}"}}
        elif "/ai/auto-reply" in endpoint:
            return {
                "status_code": 200,
                "data": {"reply": "AI生成的回复内容", "confidence": 0.95},
            }
        else:
            return {"status_code": 200, "data": {}}


class CustomerServiceScenario(AstrBotSaaSUser):
    """
    客服专属工作场景
    专注于客服人员的日常工作流程测试
    """

    @task(15)
    def handle_multiple_sessions(self):
        """处理多个并发会话"""
        # 模拟客服同时处理多个客户咨询
        session_count = 3

        for i in range(session_count):
            session_data = {
                "customer_user_id": f"batch_customer_{i}_{uuid4().hex[:6]}",
                "channel": "webchat",
                "priority": "normal" if i < 2 else "high",
            }
            self._mock_request("POST", "/api/v1/sessions", json=session_data)

    @task(8)
    def knowledge_base_query(self):
        """知识库查询操作"""
        # 模拟客服查询知识库
        kb_request = {"query": "账户密码重置流程", "category": "account_management"}
        self._mock_request("POST", "/api/v1/knowledge/search", json=kb_request)


class AIFeatureScenario(AstrBotSaaSUser):
    """
    AI功能专项测试场景
    专注于AI相关功能的性能和稳定性测试
    """

    @task(12)
    def ai_conversation_analysis(self):
        """AI对话分析"""
        if not self.session_id:
            return

        analysis_request = {
            "session_id": self.session_id,
            "analysis_type": ["sentiment", "intent", "keywords"],
        }
        self._mock_request("POST", "/api/v1/ai/analyze", json=analysis_request)

    @task(10)
    def smart_suggestions(self):
        """智能建议功能"""
        suggestion_request = {
            "context": "customer_inquiry",
            "message": "产品功能介绍",
            "user_type": "staff",
        }
        self._mock_request("POST", "/api/v1/ai/suggestions", json=suggestion_request)


# 负载测试配置
LOAD_TEST_CONFIG = {
    "user_classes": [AstrBotSaaSUser, CustomerServiceScenario, AIFeatureScenario],
    "spawn_rate": 2,  # 每秒启动2个用户
    "run_time": "5m",  # 运行5分钟
    "host": "http://localhost:8000",
}


def run_load_test_simulation():
    """
    运行负载测试模拟
    用于验证测试配置的正确性，不实际发送请求
    """
    print("🚀 AstrBot SaaS 负载测试配置验证")
    print("=" * 50)

    # 验证用户类配置
    total_tasks = 0
    for user_class in LOAD_TEST_CONFIG["user_classes"]:
        print(f"✅ 用户类: {user_class.__name__}")

        # 检查任务方法
        tasks = []
        for method_name in dir(user_class):
            method = getattr(user_class, method_name)
            if hasattr(method, "_task_weight"):
                tasks.append(method_name)

        print(f"   任务数量: {len(tasks)}")
        total_tasks += len(tasks)

        for task_name in tasks:
            method = getattr(user_class, task_name)
            weight = method._task_weight
            print(f"   - {task_name} (权重: {weight})")

    print("\n配置参数:")
    print(f"- 启动速率: {LOAD_TEST_CONFIG['spawn_rate']} 用户/秒")
    print(f"- 运行时间: {LOAD_TEST_CONFIG['run_time']}")
    print(f"- 目标地址: {LOAD_TEST_CONFIG['host']}")

    return {
        "status": "配置验证完成",
        "user_classes": len(LOAD_TEST_CONFIG["user_classes"]),
        "total_tasks": total_tasks,
    }


if __name__ == "__main__":
    # 运行配置验证
    result = run_load_test_simulation()
    print(f"\n✅ 验证结果: {result}")
