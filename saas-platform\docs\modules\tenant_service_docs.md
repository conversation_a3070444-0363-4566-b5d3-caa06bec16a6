# 📖 技术文档：租户服务 (TenantService)

## 🎯 1. 模块概述

**功能**：提供租户的创建、查询、更新、删除等全生命周期管理功能。

**核心职责**：
- **租户管理**：实现租户的CRUD操作。
- **数据隔离**：确保所有操作都基于`tenant_id`进行，保证数据隔离。
- **API密钥管理**：为租户生成、验证和撤销API密钥。
- **统计信息**：提供租户相关的统计数据。

## 🚀 2. 快速使用

### 2.1 依赖注入

在API端点中注入`TenantService`：

```python
from app.services.tenant_service import TenantService, get_tenant_service

@router.post("/tenants")
async def create_tenant(
    tenant_data: TenantCreate,
    tenant_service: TenantService = Depends(get_tenant_service),
):
    return await tenant_service.create_tenant(tenant_data)
```

### 2.2 核心方法

- **`create_tenant(tenant_data)`** - 创建新租户
- **`get_tenant(tenant_id)`** - 获取租户信息
- **`list_tenants(skip, limit, ...)`** - 获取租户列表
- **`update_tenant(tenant_id, update_data)`** - 更新租户信息
- **`delete_tenant(tenant_id)`** - 删除租户

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    subgraph "核心依赖"
        A[FastAPI] --> B(TenantService)
        C[SQLAlchemy] --> B
        D[Pydantic] --> B
    end

    subgraph "模块交互"
        B --> E(Tenant Model)
    end

    style B fill:#c8e6c9
```

### 3.2 数据流

**创建租户流程**：
1. **API接收**：接收创建租户的请求。
2. **服务处理**：
   - 验证邮箱和企业名称的唯一性。
   - 创建`Tenant`对象。
   - 将租户对象添加到数据库并提交。
3. **响应返回**：返回创建的`TenantRead`对象。

## 🔧 4. API参考

| 方法 | HTTP动词 | 端点 | 描述 |
|---|---|---|---|
| `create_tenant` | `POST` | `/api/v1/tenants` | 创建租户 |
| `get_tenant` | `GET` | `/api/v1/tenants/{tenant_id}` | 获取租户详情 |
| `list_tenants` | `GET` | `/api/v1/tenants` | 获取租户列表 |
| `update_tenant` | `PUT` | `/api/v1/tenants/{tenant_id}` | 更新租户信息 |
| `delete_tenant` | `DELETE`| `/api/v1/tenants/{tenant_id}` | 删除租户 |

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_tenant_service.py`
- **集成测试**：`tests/integration/test_tenant_api_integration.py`

## 💡 6. 维护与扩展

- **软删除**：当前是物理删除，可以改为软删除，只标记`status`为`DELETED`。
- **租户配额**：可以与配额服务集成，对租户的资源使用进行限制。
- **邀请机制**：可以添加邀请用户加入租户的功能。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 