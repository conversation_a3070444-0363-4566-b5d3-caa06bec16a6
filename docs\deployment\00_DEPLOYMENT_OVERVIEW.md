# 🚀 AstrBot SaaS Platform - 部署文档总览

## 📋 文档导航

本目录包含 AstrBot SaaS Platform 的完整部署和运维文档。

### 📁 文档结构

```
docs/deployment/
├── 00_DEPLOYMENT_OVERVIEW.md          # 本文档 - 部署总览
├── kubernetes/                        # Kubernetes部署
│   ├── production-deployment.yaml     # 生产环境部署配置
│   ├── staging-deployment.yaml        # 测试环境部署配置
│   └── security/                      # 安全配置
├── monitoring/                        # 监控和观测
│   ├── prometheus/                    # Prometheus配置
│   ├── grafana/                      # Grafana仪表板
│   └── alerting/                     # 告警规则
├── scripts/                          # 部署脚本
└── guides/                           # 操作指南
```

## 🎯 快速导航

### 🏗️ **部署环境配置**
- **生产环境**: `kubernetes/production-deployment.yaml`
- **测试环境**: `kubernetes/staging-deployment.yaml`
- **开发环境**: `../development/技术栈.md` (本地开发配置)

### 🔐 **安全配置**
- **RBAC权限**: `kubernetes/security/rbac/`
- **网络策略**: `kubernetes/security/network-policies/`
- **Pod安全**: `kubernetes/security/pod-security/`

### 📊 **监控运维**
- **Prometheus配置**: `monitoring/prometheus/prometheus.yml`
- **Grafana仪表板**: `monitoring/grafana/dashboards-config.yaml`
- **告警规则**: `monitoring/alerting/alerts.yml`

### 🛠️ **操作指南**
- **初始部署**: `guides/初始部署指南.md`
- **更新升级**: `guides/更新升级指南.md`
- **故障排除**: `guides/故障排除指南.md`

## 🚨 关键注意事项

### 🔒 **安全要求**
- 所有敏感配置必须通过Secret管理
- 禁用ServiceAccount token自动挂载
- 启用Pod安全上下文限制

### 📈 **性能优化**
- 启用HPA自动扩缩容 (3-20副本)
- 配置资源请求和限制
- 使用反亲和性确保高可用

### 🔍 **监控告警**
- API错误率 > 1% 时触发告警
- 响应时间 > 2s 时触发告警
- 资源使用率 > 80% 时触发告警

---

📖 **完整部署指南**: 参考 `../development/部署与运维.md` 获取详细的部署步骤和配置说明。 