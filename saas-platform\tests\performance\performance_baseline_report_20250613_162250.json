{"test_summary": {"total_tests": 62, "successful_tests": 57, "success_rate": 91.93548387096774, "test_duration_seconds": 2.024063}, "performance_metrics": {"avg_response_time_ms": 200.03891457947705, "min_response_time_ms": 9.850766965665727, "max_response_time_ms": 735.6499118196148, "median_response_time_ms": 129.14955635239204}, "test_results": [{"endpoint": "/health", "method": "GET", "response_time_ms": 9.850766965665727, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.569502}, {"endpoint": "/health", "method": "GET", "response_time_ms": 11.243018706416564, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.6204607}, {"endpoint": "/health", "method": "GET", "response_time_ms": 10.176164304380746, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.67095}, {"endpoint": "/health", "method": "GET", "response_time_ms": 14.638346474092273, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.721449}, {"endpoint": "/health", "method": "GET", "response_time_ms": 10.713308677171902, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.7715938}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 12.984530511943582, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.8224287}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 14.3206891400351, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.8733878}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 77.08245031553223, "expected_time_ms": 15, "success": false, "status_code": 500, "timestamp": **********.923439}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 16.238327154154447, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.973577}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 12.642774508225491, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.0240164}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 364.2555443046452, "expected_time_ms": 100, "success": false, "status_code": 500, "timestamp": **********.0744283}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 136.70391393523573, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.1249373}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 520.8888437641957, "expected_time_ms": 100, "success": false, "status_code": 500, "timestamp": **********.1754546}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 136.2413059306069, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.2256014}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 130.11617120903423, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.2760122}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 170.58804636981006, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.3265154}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 644.6692827877739, "expected_time_ms": 200, "success": false, "status_code": 500, "timestamp": **********.3772128}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 711.6666092154846, "expected_time_ms": 200, "success": false, "status_code": 500, "timestamp": **********.4276743}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 282.9781098922963, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.478005}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 166.44353145353668, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.528168}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 223.3207524955282, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.578232}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 172.68291655224442, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.6289806}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 154.80720466184385, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.679295}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 199.0421790505003, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.7296727}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 119.88535258153536, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.7799988}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 79.45716136188898, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.8340263}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 74.01791873968018, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.8846014}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 118.39080759995342, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.934688}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 71.06404375150657, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.985202}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 128.18294149574984, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1749802970.0357494}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 735.6499118196148, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1749802970.0861597}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 649.1272514463075, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1749802970.1369932}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 352.0882683674037, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1749802970.187558}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 731.4104925774732, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1749802970.2378433}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 704.2696058992722, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1749802970.2882123}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 252.2198021618953, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1749802970.3390508}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 422.85885645566975, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1749802970.3903234}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 343.86412176958595, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1749802970.4405334}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 397.6471776669556, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1749802970.4908462}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 346.5430935131287, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1749802970.5412142}, {"operation": "SELECT租户列表", "response_time_ms": 85.57131990449454, "expected_time_ms": 50, "success": true}, {"operation": "SELECT租户列表", "response_time_ms": 84.76203003386753, "expected_time_ms": 50, "success": true}, {"operation": "SELECT租户列表", "response_time_ms": 85.46230942447549, "expected_time_ms": 50, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 137.04438639526666, "expected_time_ms": 80, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 116.30612141373499, "expected_time_ms": 80, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 132.11481505570072, "expected_time_ms": 80, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 88.87125739909055, "expected_time_ms": 60, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 57.017271687826444, "expected_time_ms": 60, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 95.30775365564628, "expected_time_ms": 60, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 62.81901031775345, "expected_time_ms": 40, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 48.43251748818088, "expected_time_ms": 40, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 45.558442306733824, "expected_time_ms": 40, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 26.755802628814745, "expected_time_ms": 30, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 40.826207579019155, "expected_time_ms": 30, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 24.27358586945948, "expected_time_ms": 30, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 257.9179587512503, "expected_time_ms": 150, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 242.9885907052452, "expected_time_ms": 150, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 239.25606940513325, "expected_time_ms": 150, "success": true}, {"scenario": "5个并发用户", "concurrent_users": 5, "avg_response_time_ms": 155.31293402502678, "p95_response_time_ms": 179.22196312672983, "max_response_time_ms": 172.49675058307616, "expected_max_ms": 200, "success": true}, {"scenario": "10个并发用户", "concurrent_users": 10, "avg_response_time_ms": 214.7662994703856, "p95_response_time_ms": 240.5620033705783, "max_response_time_ms": 239.8960548488788, "expected_max_ms": 350, "success": true}, {"scenario": "20个并发用户", "concurrent_users": 20, "avg_response_time_ms": 310.91715551743545, "p95_response_time_ms": 337.4009863396456, "max_response_time_ms": 337.4493897308295, "expected_max_ms": 600, "success": true}, {"scenario": "50个并发用户", "concurrent_users": 50, "avg_response_time_ms": 616.6293196682358, "p95_response_time_ms": 638.2440409079396, "max_response_time_ms": 639.9412732953443, "expected_max_ms": 1200, "success": true}], "recommendations": ["🔧 数据库连接池优化: 确保数据库连接池大小适合并发负载", "📊 API响应时间监控: 建立API响应时间监控和告警机制", "🚀 缓存策略: 对频繁查询的数据实施Redis缓存", "⚡ 异步处理: AI功能采用异步处理减少响应时间", "🔍 SQL查询优化: 审查慢查询并添加适当索引", "📈 负载均衡: 考虑在高并发场景下实施负载均衡", "💾 数据库分片: 大量数据时考虑数据库分片策略", "🛡️ 限流保护: 实施API限流保护系统稳定性"]}