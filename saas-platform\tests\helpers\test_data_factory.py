# 测试数据工厂
"""
端到端测试数据工厂类
提供：标准化测试数据生成、对象创建、JWT令牌生成
确保：数据一致性、测试隔离、可重复性
"""

import uuid
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import jwt
from passlib.context import CryptContext

from app.models import Tenant, User, Session as ChatSession, Message
from app.schemas.tenant import TenantCreate
from app.schemas.user import UserCreate
from app.core.config import settings


class TestDataFactory:
    """测试数据工厂类"""
    
    # 密码加密上下文
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    @classmethod
    def create_tenant(
        cls, 
        name: str = "测试企业",
        email: str = "<EMAIL>",
        **kwargs
    ) -> Tenant:
        """创建测试租户"""
        tenant_data = {
            "id": str(uuid.uuid4()),
            "name": name,
            "email": email,
            "status": "active",
            "plan": "standard",
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            **kwargs
        }
        
        # 创建租户对象（模拟数据库记录）
        tenant = Tenant(**tenant_data)
        return tenant
    
    @classmethod
    def create_user(
        cls,
        tenant_id: str,
        email: str = "<EMAIL>",
        role: str = "staff",
        password: str = "test_password",
        **kwargs
    ) -> User:
        """创建测试用户"""
        user_data = {
            "id": str(uuid.uuid4()),
            "tenant_id": tenant_id,
            "email": email,
            "hashed_password": cls.pwd_context.hash(password),
            "role": role,
            "name": kwargs.get("name", "测试用户"),
            "is_active": True,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            **kwargs
        }
        
        user = User(**user_data)
        return user
    
    @classmethod
    def create_chat_session(
        cls,
        tenant_id: str,
        user_id: str = "test_user_123",
        platform: str = "wechat",
        status: str = "waiting",
        **kwargs
    ) -> ChatSession:
        """创建测试会话"""
        session_data = {
            "id": str(uuid.uuid4()),
            "tenant_id": tenant_id,
            "user_id": user_id,
            "platform": platform,
            "status": status,
            "priority": "normal",
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            **kwargs
        }
        
        session = ChatSession(**session_data)
        return session
    
    @classmethod
    def create_message(
        cls,
        tenant_id: str,
        session_id: str,
        content: str = "测试消息",
        message_type: str = "text",
        sender_type: str = "user",
        **kwargs
    ) -> Message:
        """创建测试消息"""
        message_data = {
            "id": str(uuid.uuid4()),
            "tenant_id": tenant_id,
            "session_id": session_id,
            "content": content,
            "type": message_type,
            "sender_type": sender_type,
            "timestamp": datetime.now(),
            "created_at": datetime.now(),
            **kwargs
        }
        
        message = Message(**message_data)
        return message
    
    @classmethod
    def generate_jwt_token(
        cls, 
        user: User, 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """生成JWT测试令牌"""
        if expires_delta is None:
            expires_delta = timedelta(hours=24)
        
        payload = {
            "sub": user.id,
            "user_id": user.id,
            "tenant_id": user.tenant_id,
            "email": user.email,
            "role": user.role,
            "exp": datetime.utcnow() + expires_delta,
            "iat": datetime.utcnow(),
            "type": "access_token"
        }
        
        token = jwt.encode(
            payload, 
            settings.SECRET_KEY, 
            algorithm=settings.ALGORITHM
        )
        return token
    
    @classmethod
    def create_webhook_payload(
        cls,
        tenant_id: str,
        session_id: str,
        message_data: Dict[str, Any],
        event_type: str = "new_message"
    ) -> Dict[str, Any]:
        """创建Webhook负载数据"""
        return {
            "tenant_id": tenant_id,
            "session_id": session_id,
            "event_type": event_type,
            "message": {
                "id": message_data.get("id", str(uuid.uuid4())),
                "user_id": message_data.get("user_id", "test_user"),
                "platform": message_data.get("platform", "wechat"),
                "content": message_data.get("content", "测试消息"),
                "type": message_data.get("type", "text"),
                "timestamp": message_data.get("timestamp", datetime.now().isoformat()),
                **message_data
            },
            "user_info": {
                "nickname": "测试用户",
                "avatar_url": "https://example.com/avatar.jpg"
            },
            "timestamp": datetime.now().isoformat()
        }
    
    @classmethod
    def create_voice_message_data(
        cls,
        user_id: str = "voice_user_123",
        platform: str = "wechat",
        duration: float = 10.5,
        file_size: int = 204800
    ) -> Dict[str, Any]:
        """创建语音消息测试数据"""
        return {
            "id": str(uuid.uuid4()),
            "user_id": user_id,
            "platform": platform,
            "content": f"voice_file_{uuid.uuid4().hex[:8]}.amr",
            "type": "voice",
            "metadata": {
                "duration": duration,
                "file_size": file_size,
                "format": "amr"
            },
            "timestamp": datetime.now().isoformat()
        }
    
    @classmethod
    def create_asr_result(
        cls,
        text: str = "这是语音识别的结果文本",
        confidence: float = 0.95,
        language: str = "zh-CN"
    ) -> Dict[str, Any]:
        """创建ASR结果测试数据"""
        return {
            "task_id": str(uuid.uuid4()),
            "status": "success",
            "result": {
                "text": text,
                "confidence": confidence,
                "language": language,
                "duration": 10.5,
                "segments": [
                    {
                        "start": 0.0,
                        "end": 10.5,
                        "text": text,
                        "confidence": confidence
                    }
                ]
            },
            "processing_time": 2.8,
            "timestamp": datetime.now().isoformat()
        }
    
    @classmethod
    def create_llm_response(
        cls,
        reply: str = "这是AI生成的回复",
        confidence: float = 0.88,
        model: str = "gpt-3.5-turbo"
    ) -> Dict[str, Any]:
        """创建LLM响应测试数据"""
        return {
            "reply": reply,
            "confidence": confidence,
            "model": model,
            "token_usage": {
                "prompt_tokens": 150,
                "completion_tokens": 45,
                "total_tokens": 195
            },
            "processing_time": 1.2,
            "timestamp": datetime.now().isoformat()
        }
    
    @classmethod
    def create_blacklist_entry(
        cls,
        user_id: str = "spam_user_123",
        platform: str = "wechat",
        reason: str = "恶意用户"
    ) -> Dict[str, Any]:
        """创建黑名单条目测试数据"""
        return {
            "id": str(uuid.uuid4()),
            "user_id": user_id,
            "platform": platform,
            "reason": reason,
            "created_by": "test_admin",
            "created_at": datetime.now().isoformat(),
            "expires_at": (datetime.now() + timedelta(days=30)).isoformat(),
            "is_active": True
        }
    
    @classmethod
    def create_session_summary(
        cls,
        resolution_status: str = "resolved",
        customer_satisfaction: str = "satisfied"
    ) -> Dict[str, Any]:
        """创建会话总结测试数据"""
        return {
            "summary": "客户咨询产品功能，已详细介绍并解决疑问。客户表示满意。",
            "resolution_status": resolution_status,
            "customer_satisfaction": customer_satisfaction,
            "key_points": [
                "客户主要关心产品的技术支持服务",
                "详细介绍了7x24小时支持方式",
                "提供了技术支持热线联系方式"
            ],
            "follow_up_needed": False,
            "tags": ["产品咨询", "技术支持", "已解决"],
            "duration": 12.5,
            "message_count": 8,
            "generated_at": datetime.now().isoformat()
        }
    
    @classmethod
    def create_performance_metrics(cls) -> Dict[str, Any]:
        """创建性能指标测试数据"""
        return {
            "response_time": {
                "avg": 185.6,
                "p50": 150.2,
                "p95": 320.8,
                "p99": 450.1
            },
            "throughput": {
                "requests_per_second": 125.8,
                "messages_per_minute": 450
            },
            "error_rate": 0.02,
            "availability": 99.95,
            "cpu_usage": 45.2,
            "memory_usage": 68.7,
            "database_connections": 23,
            "timestamp": datetime.now().isoformat()
        }
    
    @classmethod
    def create_tenant_config(
        cls,
        tenant_id: str,
        include_im: bool = True,
        include_llm: bool = True,
        include_asr: bool = True
    ) -> Dict[str, Any]:
        """创建租户配置测试数据"""
        config = {
            "tenant_id": tenant_id,
            "updated_at": datetime.now().isoformat()
        }
        
        if include_im:
            config["im_config"] = {
                "platform": "wechat",
                "app_id": "wx_test_app_id",
                "app_secret_ref": "encrypted_secret_ref_123",
                "webhook_url": f"https://astrbot.instance.{tenant_id}.com/webhook",
                "enabled": True
            }
        
        if include_llm:
            config["llm_config"] = {
                "provider": "dify",
                "api_key_ref": "dify_key_ref_456",
                "model_config": {
                    "temperature": 0.7,
                    "max_tokens": 1000,
                    "stream": False
                },
                "enabled": True
            }
        
        if include_asr:
            config["asr_config"] = {
                "provider": "whisper",
                "api_key_ref": "whisper_key_ref_789",
                "language": "zh-CN",
                "enabled": True
            }
        
        return config
    
    @classmethod
    def create_test_scenarios(cls) -> List[Dict[str, Any]]:
        """创建标准测试场景数据"""
        return [
            {
                "name": "用户咨询产品功能",
                "user_messages": [
                    "你好，我想了解一下你们的产品",
                    "主要功能有哪些？",
                    "价格怎么样？"
                ],
                "expected_responses": [
                    "您好！很高兴为您服务",
                    "我们的产品主要功能包括",
                    "关于价格，我们有多种套餐"
                ],
                "tags": ["产品咨询", "功能介绍", "价格询问"]
            },
            {
                "name": "技术支持请求",
                "user_messages": [
                    "我遇到了技术问题",
                    "软件无法正常启动",
                    "有技术支持吗？"
                ],
                "expected_responses": [
                    "很抱歉您遇到了技术问题",
                    "请描述一下具体的错误信息",
                    "我们提供7x24小时技术支持"
                ],
                "tags": ["技术支持", "问题排查", "软件故障"]
            },
            {
                "name": "投诉处理",
                "user_messages": [
                    "我要投诉",
                    "服务质量有问题",
                    "希望得到解决"
                ],
                "expected_responses": [
                    "非常抱歉给您带来不好的体验",
                    "我们会认真处理您的投诉",
                    "请详细说明遇到的问题"
                ],
                "tags": ["投诉处理", "服务质量", "问题解决"]
            }
        ]
    
    @classmethod
    def create_load_test_data(cls, count: int = 100) -> List[Dict[str, Any]]:
        """创建负载测试数据"""
        test_data = []
        
        for i in range(count):
            test_data.append({
                "session_id": str(uuid.uuid4()),
                "user_id": f"load_test_user_{i}",
                "platform": "wechat",
                "messages": [
                    {
                        "content": f"负载测试消息 {i}-{j}",
                        "type": "text",
                        "timestamp": datetime.now().isoformat()
                    }
                    for j in range(5)  # 每个会话5条消息
                ]
            })
        
        return test_data


# 工厂方法的便捷函数
def create_test_tenant(**kwargs) -> Tenant:
    """创建测试租户的便捷函数"""
    return TestDataFactory.create_tenant(**kwargs)


def create_test_user(tenant_id: str, **kwargs) -> User:
    """创建测试用户的便捷函数"""
    return TestDataFactory.create_user(tenant_id, **kwargs)


def create_test_session(tenant_id: str, **kwargs) -> ChatSession:
    """创建测试会话的便捷函数"""
    return TestDataFactory.create_chat_session(tenant_id, **kwargs)


def create_test_message(tenant_id: str, session_id: str, **kwargs) -> Message:
    """创建测试消息的便捷函数"""
    return TestDataFactory.create_message(tenant_id, session_id, **kwargs) 