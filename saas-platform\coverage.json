{"meta": {"format": 3, "version": "7.6.12", "timestamp": "2025-06-17T18:39:06.491371", "branch_coverage": true, "show_contexts": false}, "files": {"app\\api\\deps.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 163, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 163, "excluded_lines": 0, "num_branches": 38, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 38}, "missing_lines": [6, 7, 9, 10, 11, 13, 14, 22, 23, 26, 29, 32, 33, 40, 43, 44, 50, 53, 54, 60, 75, 76, 78, 79, 80, 82, 85, 98, 100, 101, 103, 104, 105, 106, 107, 108, 111, 124, 126, 128, 129, 131, 133, 134, 135, 136, 137, 138, 141, 160, 162, 164, 165, 168, 169, 171, 173, 174, 175, 176, 177, 178, 181, 197, 199, 201, 202, 205, 206, 208, 210, 211, 212, 213, 216, 231, 232, 235, 248, 249, 251, 253, 256, 257, 259, 260, 263, 264, 265, 267, 269, 270, 271, 272, 275, 286, 287, 288, 289, 292, 294, 295, 297, 298, 300, 301, 302, 303, 304, 305, 307, 311, 312, 313, 316, 335, 336, 338, 340, 342, 343, 344, 346, 347, 349, 350, 352, 354, 355, 356, 357, 360, 363, 364, 366, 368, 370, 372, 374, 376, 380, 383, 390, 393, 400, 403, 416, 417, 422, 435, 437, 439, 440, 443, 445, 446, 449, 450, 452, 454, 455, 456, 457], "excluded_lines": [], "executed_branches": [], "missing_branches": [[75, 76], [75, 78], [79, 80], [79, 82], [128, 129], [128, 131], [164, 165], [164, 168], [168, 169], [168, 171], [201, 202], [201, 205], [205, 206], [205, 208], [231, -216], [231, 232], [248, 249], [248, 251], [259, 260], [259, 263], [264, 265], [264, 267], [297, -286], [297, 298], [335, 336], [335, 338], [346, 347], [346, 349], [349, 350], [349, 352], [416, -403], [416, 417], [439, 440], [439, 443], [445, 446], [445, 449], [449, 450], [449, 452]], "functions": {"AuthenticationError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [33], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AuthorizationError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [44], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantAccessError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [54], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_current_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [75, 76, 78, 79, 80, 82], "excluded_lines": [], "executed_branches": [], "missing_branches": [[75, 76], [75, 78], [79, 80], [79, 82]]}, "get_current_user_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [98, 100, 101, 103, 104, 105, 106, 107, 108], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_current_tenant_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [124, 126, 128, 129, 131, 133, 134, 135, 136, 137, 138], "excluded_lines": [], "executed_branches": [], "missing_branches": [[128, 129], [128, 131]]}, "get_current_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [160, 162, 164, 165, 168, 169, 171, 173, 174, 175, 176, 177, 178], "excluded_lines": [], "executed_branches": [], "missing_branches": [[164, 165], [164, 168], [168, 169], [168, 171]]}, "get_current_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [197, 199, 201, 202, 205, 206, 208, 210, 211, 212, 213], "excluded_lines": [], "executed_branches": [], "missing_branches": [[201, 202], [201, 205], [205, 206], [205, 208]]}, "require_tenant_access": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [231, 232], "excluded_lines": [], "executed_branches": [], "missing_branches": [[231, -216], [231, 232]]}, "get_optional_current_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [248, 249, 251, 253, 256, 257, 259, 260, 263, 264, 265, 267, 269, 270, 271, 272], "excluded_lines": [], "executed_branches": [], "missing_branches": [[248, 249], [248, 251], [259, 260], [259, 263], [264, 265], [264, 267]]}, "require_role": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [286, 307], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_role.check_role": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [287, 288, 289, 292, 294, 295, 297, 298, 300, 301, 302, 303, 304, 305], "excluded_lines": [], "executed_branches": [], "missing_branches": [[297, -286], [297, 298]]}, "require_api_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [335, 336, 338, 340, 342, 343, 344, 346, 347, 349, 350, 352, 354, 355, 356, 357], "excluded_lines": [], "executed_branches": [], "missing_branches": [[335, 336], [335, 338], [346, 347], [346, 349], [349, 350], [349, 352]]}, "TenantContext.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [364], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantContext.set_tenant_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [368], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantContext.get_tenant_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [372], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantContext.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [376], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "set_tenant_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [390], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_tenant_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [400], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "validate_tenant_resource_access": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [416, 417], "excluded_lines": [], "executed_branches": [], "missing_branches": [[416, -403], [416, 417]]}, "get_current_tenant_from_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [435, 437, 439, 440, 443, 445, 446, 449, 450, 452, 454, 455, 456, 457], "excluded_lines": [], "executed_branches": [], "missing_branches": [[439, 440], [439, 443], [445, 446], [445, 449], [449, 450], [449, 452]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 38, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 38, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [6, 7, 9, 10, 11, 13, 14, 22, 23, 26, 29, 32, 40, 43, 50, 53, 60, 85, 111, 141, 181, 216, 235, 275, 311, 312, 313, 316, 360, 363, 366, 370, 374, 380, 383, 393, 403, 422], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"AuthenticationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [33], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AuthorizationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [44], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantAccessError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [54], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantContext": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [364, 368, 372, 376], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 156, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 156, "excluded_lines": 0, "num_branches": 38, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 38}, "missing_lines": [6, 7, 9, 10, 11, 13, 14, 22, 23, 26, 29, 32, 40, 43, 50, 53, 60, 75, 76, 78, 79, 80, 82, 85, 98, 100, 101, 103, 104, 105, 106, 107, 108, 111, 124, 126, 128, 129, 131, 133, 134, 135, 136, 137, 138, 141, 160, 162, 164, 165, 168, 169, 171, 173, 174, 175, 176, 177, 178, 181, 197, 199, 201, 202, 205, 206, 208, 210, 211, 212, 213, 216, 231, 232, 235, 248, 249, 251, 253, 256, 257, 259, 260, 263, 264, 265, 267, 269, 270, 271, 272, 275, 286, 287, 288, 289, 292, 294, 295, 297, 298, 300, 301, 302, 303, 304, 305, 307, 311, 312, 313, 316, 335, 336, 338, 340, 342, 343, 344, 346, 347, 349, 350, 352, 354, 355, 356, 357, 360, 363, 366, 370, 374, 380, 383, 390, 393, 400, 403, 416, 417, 422, 435, 437, 439, 440, 443, 445, 446, 449, 450, 452, 454, 455, 456, 457], "excluded_lines": [], "executed_branches": [], "missing_branches": [[75, 76], [75, 78], [79, 80], [79, 82], [128, 129], [128, 131], [164, 165], [164, 168], [168, 169], [168, 171], [201, 202], [201, 205], [205, 206], [205, 208], [231, -216], [231, 232], [248, 249], [248, 251], [259, 260], [259, 263], [264, 265], [264, 267], [297, -286], [297, 298], [335, 336], [335, 338], [346, 347], [346, 349], [349, 350], [349, 352], [416, -403], [416, 417], [439, 440], [439, 443], [445, 446], [445, 449], [449, 450], [449, 452]]}}}, "app\\api\\v1\\ai_features.py": {"executed_lines": [1, 6, 7, 8, 10, 11, 20, 21, 22, 24, 32, 38, 39, 40, 41, 42, 43, 108, 110, 115, 116, 117, 121, 123, 126, 127, 129, 130, 134, 137, 138, 140, 141, 142, 144, 145, 148, 149, 151, 152, 153, 155, 158, 159, 163, 166, 170, 175, 176, 247, 253, 254, 255, 296, 307, 308, 311, 312, 447, 448, 449, 450, 451, 503, 504, 505, 507, 508, 509, 510, 542, 543, 571, 610], "summary": {"covered_lines": 39, "num_statements": 151, "percent_covered": 23.668639053254438, "percent_covered_display": "23.67", "missing_lines": 112, "excluded_lines": 0, "num_branches": 18, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 17}, "missing_lines": [9, 29, 30, 31, 33, 34, 35, 45, 51, 58, 59, 60, 62, 63, 64, 66, 67, 72, 74, 80, 84, 85, 86, 87, 88, 94, 95, 97, 98, 99, 104, 106, 107, 109, 122, 133, 143, 154, 162, 165, 169, 177, 180, 186, 187, 203, 205, 208, 218, 220, 222, 225, 230, 242, 246, 256, 262, 265, 266, 276, 278, 281, 282, 283, 290, 291, 292, 293, 294, 313, 314, 315, 318, 319, 325, 326, 329, 332, 333, 345, 346, 352, 355, 359, 369, 371, 374, 378, 384, 385, 391, 394, 395, 401, 405, 415, 416, 417, 423, 434, 437, 438, 439, 440, 443, 444, 454, 455, 456, 459, 462, 471], "excluded_lines": [], "executed_branches": [[450, 451]], "missing_branches": [[58, 59], [58, 94], [66, 67], [66, 74], [94, 95], [94, 116], [220, 222], [220, 225], [283, 290], [283, 291], [437, 438], [437, 439], [439, 440], [439, 443], [450, 454], [455, 456], [455, 459]], "functions": {"get_tenant_from_auth": {"executed_lines": [121], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [51, 122], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_tenant_from_auth._get_tenant": {"executed_lines": [108, 110, 115, 116, 117], "summary": {"covered_lines": 4, "num_statements": 29, "percent_covered": 11.428571428571429, "percent_covered_display": "11.43", "missing_lines": 25, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [58, 59, 60, 62, 63, 64, 66, 67, 72, 74, 80, 84, 85, 86, 87, 88, 94, 95, 97, 98, 99, 104, 106, 107, 109], "excluded_lines": [], "executed_branches": [], "missing_branches": [[58, 59], [58, 94], [66, 67], [66, 74], [94, 95], [94, 116]]}, "generate_auto_reply": {"executed_lines": [247, 253, 254, 255], "summary": {"covered_lines": 3, "num_statements": 15, "percent_covered": 17.647058823529413, "percent_covered_display": "17.65", "missing_lines": 12, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [203, 205, 208, 218, 220, 222, 225, 230, 242, 246, 256, 262], "excluded_lines": [], "executed_branches": [], "missing_branches": [[220, 222], [220, 225]]}, "generate_auto_reply_stream": {"executed_lines": [296, 307, 308, 311, 312], "summary": {"covered_lines": 4, "num_statements": 10, "percent_covered": 40.0, "percent_covered_display": "40.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [276, 278, 281, 313, 314, 315], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "generate_auto_reply_stream.generate_stream": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [282, 283, 290, 291, 292, 293, 294], "excluded_lines": [], "executed_branches": [], "missing_branches": [[283, 290], [283, 291]]}, "get_session_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [325, 326, 329], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_reply_suggestions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [345, 346, 352], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "check_auto_reply_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [369, 371, 374, 378, 384, 385, 391], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ai_features_health_check": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [401, 405, 415, 416, 417], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "_calculate_confidence_score": {"executed_lines": [447, 448, 449, 450, 451], "summary": {"covered_lines": 3, "num_statements": 14, "percent_covered": 18.181818181818183, "percent_covered_display": "18.18", "missing_lines": 11, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 7}, "missing_lines": [434, 437, 438, 439, 440, 443, 444, 454, 455, 456, 459], "excluded_lines": [], "executed_branches": [[450, 451]], "missing_branches": [[437, 438], [437, 439], [439, 440], [439, 443], [450, 454], [455, 456], [455, 459]]}, "_log_summary_generation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [471], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 7, 8, 10, 11, 20, 21, 22, 24, 32, 38, 39, 40, 41, 42, 43, 123, 126, 127, 129, 130, 134, 137, 138, 140, 141, 142, 144, 145, 148, 149, 151, 152, 153, 155, 158, 159, 163, 166, 170, 175, 176], "summary": {"covered_lines": 25, "num_statements": 55, "percent_covered": 45.45454545454545, "percent_covered_display": "45.45", "missing_lines": 30, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 29, 30, 31, 33, 34, 35, 45, 133, 143, 154, 162, 165, 169, 177, 180, 186, 187, 265, 266, 318, 319, 332, 333, 355, 359, 394, 395, 423, 462], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"AutoReplyRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AutoReplyResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionSummaryRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ReplySuggestionsRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SentimentAnalysisRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 7, 8, 10, 11, 20, 21, 22, 24, 32, 38, 39, 40, 41, 42, 43, 108, 110, 115, 116, 117, 121, 123, 126, 127, 129, 130, 134, 137, 138, 140, 141, 142, 144, 145, 148, 149, 151, 152, 153, 155, 158, 159, 163, 166, 170, 175, 176, 247, 253, 254, 255, 296, 307, 308, 311, 312, 447, 448, 449, 450, 451], "summary": {"covered_lines": 39, "num_statements": 151, "percent_covered": 23.668639053254438, "percent_covered_display": "23.67", "missing_lines": 112, "excluded_lines": 0, "num_branches": 18, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 17}, "missing_lines": [9, 29, 30, 31, 33, 34, 35, 45, 51, 58, 59, 60, 62, 63, 64, 66, 67, 72, 74, 80, 84, 85, 86, 87, 88, 94, 95, 97, 98, 99, 104, 106, 107, 109, 122, 133, 143, 154, 162, 165, 169, 177, 180, 186, 187, 203, 205, 208, 218, 220, 222, 225, 230, 242, 246, 256, 262, 265, 266, 276, 278, 281, 282, 283, 290, 291, 292, 293, 294, 313, 314, 315, 318, 319, 325, 326, 329, 332, 333, 345, 346, 352, 355, 359, 369, 371, 374, 378, 384, 385, 391, 394, 395, 401, 405, 415, 416, 417, 423, 434, 437, 438, 439, 440, 443, 444, 454, 455, 456, 459, 462, 471], "excluded_lines": [], "executed_branches": [[450, 451]], "missing_branches": [[58, 59], [58, 94], [66, 67], [66, 74], [94, 95], [94, 116], [220, 222], [220, 225], [283, 290], [283, 291], [437, 438], [437, 439], [439, 440], [439, 443], [450, 454], [455, 456], [455, 459]]}}}, "app\\api\\v1\\analytics.py": {"executed_lines": [1, 12, 13, 14, 16, 17, 19, 20, 21, 22, 23, 24, 36, 37, 40, 43, 46, 47, 53, 109, 110, 116, 174, 175, 181, 214, 215, 221, 257, 258, 264, 337, 338, 344, 419, 420, 426], "summary": {"covered_lines": 24, "num_statements": 107, "percent_covered": 20.869565217391305, "percent_covered_display": "20.87", "missing_lines": 83, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [15, 18, 38, 41, 44, 74, 76, 77, 78, 79, 82, 85, 90, 91, 93, 101, 105, 106, 107, 139, 141, 142, 143, 144, 147, 150, 155, 156, 158, 166, 170, 171, 172, 196, 197, 198, 200, 206, 210, 211, 212, 238, 239, 240, 242, 249, 253, 254, 255, 281, 282, 283, 286, 288, 291, 292, 293, 294, 297, 319, 327, 331, 332, 335, 361, 362, 366, 369, 374, 387, 404, 411, 415, 416, 417, 445, 447, 452, 461, 469, 473, 474, 475], "excluded_lines": [], "executed_branches": [], "missing_branches": [[76, 77], [76, 78], [78, 79], [78, 82], [141, 142], [141, 143], [143, 144], [143, 147]], "functions": {"get_session_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [74, 76, 77, 78, 79, 82, 85, 90, 91, 93, 101, 105, 106, 107], "excluded_lines": [], "executed_branches": [], "missing_branches": [[76, 77], [76, 78], [78, 79], [78, 82]]}, "get_message_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [139, 141, 142, 143, 144, 147, 150, 155, 156, 158, 166, 170, 171, 172], "excluded_lines": [], "executed_branches": [], "missing_branches": [[141, 142], [141, 143], [143, 144], [143, 147]]}, "get_realtime_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [196, 197, 198, 200, 206, 210, 211, 212], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_trend_analysis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [238, 239, 240, 242, 249, 253, 254, 255], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_dashboard_overview": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [281, 282, 283, 286, 288, 291, 292, 293, 294, 297, 319, 327, 331, 332, 335], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "generate_custom_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [361, 362, 366, 369, 374, 387, 404, 411, 415, 416, 417], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "export_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [445, 447, 452, 461, 469, 473, 474, 475], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 12, 13, 14, 16, 17, 19, 20, 21, 22, 23, 24, 36, 37, 40, 43, 46, 47, 53, 109, 110, 116, 174, 175, 181, 214, 215, 221, 257, 258, 264, 337, 338, 344, 419, 420, 426], "summary": {"covered_lines": 24, "num_statements": 29, "percent_covered": 82.75862068965517, "percent_covered_display": "82.76", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [15, 18, 38, 41, 44], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 12, 13, 14, 16, 17, 19, 20, 21, 22, 23, 24, 36, 37, 40, 43, 46, 47, 53, 109, 110, 116, 174, 175, 181, 214, 215, 221, 257, 258, 264, 337, 338, 344, 419, 420, 426], "summary": {"covered_lines": 24, "num_statements": 107, "percent_covered": 20.869565217391305, "percent_covered_display": "20.87", "missing_lines": 83, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [15, 18, 38, 41, 44, 74, 76, 77, 78, 79, 82, 85, 90, 91, 93, 101, 105, 106, 107, 139, 141, 142, 143, 144, 147, 150, 155, 156, 158, 166, 170, 171, 172, 196, 197, 198, 200, 206, 210, 211, 212, 238, 239, 240, 242, 249, 253, 254, 255, 281, 282, 283, 286, 288, 291, 292, 293, 294, 297, 319, 327, 331, 332, 335, 361, 362, 366, 369, 374, 387, 404, 411, 415, 416, 417, 445, 447, 452, 461, 469, 473, 474, 475], "excluded_lines": [], "executed_branches": [], "missing_branches": [[76, 77], [76, 78], [78, 79], [78, 82], [141, 142], [141, 143], [143, 144], [143, 147]]}}}, "app\\api\\v1\\instances.py": {"executed_lines": [1, 6, 7, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 22, 25, 29, 30, 32, 33, 34, 37, 38, 40, 41, 44, 45, 47, 48, 49, 52, 53, 55, 56, 59, 60, 61, 115, 116, 117, 153, 154, 155, 206, 207, 208, 252, 253, 254, 300, 301, 302, 340, 341, 342, 390, 391, 392, 459, 460, 461, 503, 504, 505], "summary": {"covered_lines": 39, "num_statements": 146, "percent_covered": 26.35135135135135, "percent_covered_display": "26.35", "missing_lines": 107, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [8, 12, 20, 23, 26, 35, 42, 50, 57, 77, 78, 80, 87, 93, 97, 98, 104, 106, 107, 113, 133, 134, 136, 140, 149, 150, 151, 171, 172, 174, 178, 184, 188, 189, 195, 197, 198, 204, 226, 227, 229, 233, 239, 243, 244, 250, 270, 271, 273, 277, 278, 283, 287, 288, 289, 290, 296, 318, 319, 321, 325, 329, 330, 336, 358, 359, 361, 367, 374, 378, 379, 382, 384, 385, 386, 408, 409, 411, 418, 424, 428, 429, 435, 437, 438, 444, 448, 449, 455, 477, 478, 480, 486, 493, 497, 498, 499, 518, 520, 521, 525, 526, 529, 549, 553, 554, 555], "excluded_lines": [], "executed_branches": [], "missing_branches": [[277, 278], [277, 283]], "functions": {"generate_instance_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [77, 78, 80, 87, 93, 97, 98, 104, 106, 107, 113], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "list_instance_tokens": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [133, 134, 136, 140, 149, 150, 151], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "revoke_instance_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [171, 172, 174, 178, 184, 188, 189, 195, 197, 198, 204], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "rotate_instance_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [226, 227, 229, 233, 239, 243, 244, 250], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_instance_credentials": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [270, 271, 273, 277, 278, 283, 287, 288, 289, 290, 296], "excluded_lines": [], "executed_branches": [], "missing_branches": [[277, 278], [277, 283]]}, "get_instance_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [318, 319, 321, 325, 329, 330, 336], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "update_tenant_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [358, 359, 361, 367, 374, 378, 379, 382, 384, 385, 386], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "push_config_to_instance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [408, 409, 411, 418, 424, 428, 429, 435, 437, 438, 444, 448, 449, 455], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "broadcast_config_update": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [477, 478, 480, 486, 493, 497, 498, 499], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "instances_health_check": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [518, 520, 521, 525, 526, 529, 549, 553, 554, 555], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 7, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 22, 25, 29, 30, 32, 33, 34, 37, 38, 40, 41, 44, 45, 47, 48, 49, 52, 53, 55, 56, 59, 60, 61, 115, 116, 117, 153, 154, 155, 206, 207, 208, 252, 253, 254, 300, 301, 302, 340, 341, 342, 390, 391, 392, 459, 460, 461, 503, 504, 505], "summary": {"covered_lines": 39, "num_statements": 48, "percent_covered": 81.25, "percent_covered_display": "81.25", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 12, 20, 23, 26, 35, 42, 50, 57], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"InstanceTokenRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "InstanceConfigUpdate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ConfigPushRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ConfigBroadcastRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 7, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 22, 25, 29, 30, 32, 33, 34, 37, 38, 40, 41, 44, 45, 47, 48, 49, 52, 53, 55, 56, 59, 60, 61, 115, 116, 117, 153, 154, 155, 206, 207, 208, 252, 253, 254, 300, 301, 302, 340, 341, 342, 390, 391, 392, 459, 460, 461, 503, 504, 505], "summary": {"covered_lines": 39, "num_statements": 146, "percent_covered": 26.35135135135135, "percent_covered_display": "26.35", "missing_lines": 107, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [8, 12, 20, 23, 26, 35, 42, 50, 57, 77, 78, 80, 87, 93, 97, 98, 104, 106, 107, 113, 133, 134, 136, 140, 149, 150, 151, 171, 172, 174, 178, 184, 188, 189, 195, 197, 198, 204, 226, 227, 229, 233, 239, 243, 244, 250, 270, 271, 273, 277, 278, 283, 287, 288, 289, 290, 296, 318, 319, 321, 325, 329, 330, 336, 358, 359, 361, 367, 374, 378, 379, 382, 384, 385, 386, 408, 409, 411, 418, 424, 428, 429, 435, 437, 438, 444, 448, 449, 455, 477, 478, 480, 486, 493, 497, 498, 499, 518, 520, 521, 525, 526, 529, 549, 553, 554, 555], "excluded_lines": [], "executed_branches": [], "missing_branches": [[277, 278], [277, 283]]}}}, "app\\api\\v1\\messages.py": {"executed_lines": [1, 7, 8, 9, 11, 13, 14, 15, 16, 17, 18, 21, 24, 25, 28, 35, 88, 95, 155, 161, 235, 241, 318, 324, 375, 381], "summary": {"covered_lines": 25, "num_statements": 87, "percent_covered": 28.735632183908045, "percent_covered_display": "28.74", "missing_lines": 62, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [52, 53, 62, 64, 71, 73, 74, 75, 76, 82, 116, 117, 126, 130, 137, 139, 140, 141, 142, 149, 183, 184, 192, 203, 205, 212, 221, 222, 223, 224, 230, 265, 266, 274, 286, 288, 295, 304, 305, 306, 307, 313, 338, 339, 346, 350, 357, 361, 362, 363, 364, 370, 399, 400, 407, 411, 417, 421, 422, 423, 424, 425], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"send_message": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [52, 53, 62, 64, 71, 73, 74, 75, 76, 82], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "process_incoming_message": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [116, 117, 126, 130, 137, 139, 140, 141, 142, 149], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_session_messages": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [183, 184, 192, 203, 205, 212, 221, 222, 223, 224, 230], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "search_messages": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [265, 266, 274, 286, 288, 295, 304, 305, 306, 307, 313], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "update_message_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [338, 339, 346, 350, 357, 361, 362, 363, 364, 370], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_message_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [399, 400, 407, 411, 417, 421, 422, 423, 424, 425], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 7, 8, 9, 11, 13, 14, 15, 16, 17, 18, 21, 24, 25, 28, 35, 88, 95, 155, 161, 235, 241, 318, 324, 375, 381], "summary": {"covered_lines": 25, "num_statements": 25, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 7, 8, 9, 11, 13, 14, 15, 16, 17, 18, 21, 24, 25, 28, 35, 88, 95, 155, 161, 235, 241, 318, 324, 375, 381], "summary": {"covered_lines": 25, "num_statements": 87, "percent_covered": 28.735632183908045, "percent_covered_display": "28.74", "missing_lines": 62, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [52, 53, 62, 64, 71, 73, 74, 75, 76, 82, 116, 117, 126, 130, 137, 139, 140, 141, 142, 149, 183, 184, 192, 203, 205, 212, 221, 222, 223, 224, 230, 265, 266, 274, 286, 288, 295, 304, 305, 306, 307, 313, 338, 339, 346, 350, 357, 361, 362, 363, 364, 370, 399, 400, 407, 411, 417, 421, 422, 423, 424, 425], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\api\\v1\\rbac.py": {"executed_lines": [1, 6, 7, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 22, 25, 29, 30, 31, 95, 96, 97, 149, 150, 151, 222, 223, 224, 284, 285, 286, 350, 351, 352], "summary": {"covered_lines": 21, "num_statements": 86, "percent_covered": 23.863636363636363, "percent_covered_display": "23.86", "missing_lines": 65, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [8, 11, 20, 23, 26, 45, 46, 47, 51, 66, 78, 84, 85, 91, 108, 109, 110, 112, 119, 136, 137, 143, 166, 167, 168, 177, 185, 203, 204, 210, 211, 212, 218, 237, 238, 239, 245, 260, 267, 273, 274, 280, 298, 299, 300, 302, 303, 307, 314, 336, 337, 338, 339, 346, 365, 366, 367, 371, 379, 395, 396, 403, 404, 405, 412], "excluded_lines": [], "executed_branches": [], "missing_branches": [[302, 303], [302, 307]], "functions": {"list_permissions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [45, 46, 47, 51, 66, 78, 84, 85, 91], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "initialize_default_permissions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [108, 109, 110, 112, 119, 136, 137, 143], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "create_role": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [166, 167, 168, 177, 185, 203, 204, 210, 211, 212, 218], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "list_roles": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [237, 238, 239, 245, 260, 267, 273, 274, 280], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_role": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [298, 299, 300, 302, 303, 307, 314, 336, 337, 338, 339, 346], "excluded_lines": [], "executed_branches": [], "missing_branches": [[302, 303], [302, 307]]}, "update_role_permissions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [365, 366, 367, 371, 379, 395, 396, 403, 404, 405, 412], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 7, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 22, 25, 29, 30, 31, 95, 96, 97, 149, 150, 151, 222, 223, 224, 284, 285, 286, 350, 351, 352], "summary": {"covered_lines": 21, "num_statements": 26, "percent_covered": 80.76923076923077, "percent_covered_display": "80.77", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 11, 20, 23, 26], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 6, 7, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 22, 25, 29, 30, 31, 95, 96, 97, 149, 150, 151, 222, 223, 224, 284, 285, 286, 350, 351, 352], "summary": {"covered_lines": 21, "num_statements": 86, "percent_covered": 23.863636363636363, "percent_covered_display": "23.86", "missing_lines": 65, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [8, 11, 20, 23, 26, 45, 46, 47, 51, 66, 78, 84, 85, 91, 108, 109, 110, 112, 119, 136, 137, 143, 166, 167, 168, 177, 185, 203, 204, 210, 211, 212, 218, 237, 238, 239, 245, 260, 267, 273, 274, 280, 298, 299, 300, 302, 303, 307, 314, 336, 337, 338, 339, 346, 365, 366, 367, 371, 379, 395, 396, 403, 404, 405, 412], "excluded_lines": [], "executed_branches": [], "missing_branches": [[302, 303], [302, 307]]}}}, "app\\api\\v1\\sessions.py": {"executed_lines": [1, 7, 8, 10, 11, 13, 14, 21, 22, 23, 24, 30, 31, 34, 35, 38, 44, 193, 196, 203, 280, 286, 328, 334, 394, 400, 467, 473], "summary": {"covered_lines": 27, "num_statements": 146, "percent_covered": 16.265060240963855, "percent_covered_display": "16.27", "missing_lines": 119, "excluded_lines": 0, "num_branches": 20, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 20}, "missing_lines": [51, 52, 53, 55, 56, 57, 59, 60, 65, 67, 73, 77, 78, 79, 80, 81, 87, 88, 89, 91, 92, 94, 95, 100, 102, 103, 106, 107, 108, 112, 113, 114, 115, 116, 122, 123, 125, 128, 131, 133, 134, 135, 141, 143, 144, 145, 151, 152, 153, 158, 164, 166, 167, 168, 171, 172, 173, 177, 178, 179, 180, 181, 187, 188, 219, 220, 228, 229, 237, 247, 254, 262, 264, 265, 266, 267, 274, 298, 299, 301, 302, 306, 312, 314, 315, 316, 317, 323, 354, 355, 363, 369, 376, 380, 381, 382, 383, 389, 420, 421, 431, 441, 443, 449, 458, 459, 460, 461, 462, 486, 488, 494, 498, 503, 525, 531, 533, 534, 535], "excluded_lines": [], "executed_branches": [], "missing_branches": [[51, 52], [51, 87], [59, 60], [59, 67], [87, 88], [87, 122], [94, 95], [94, 102], [122, 123], [122, 187], [133, 134], [133, 141], [143, 144], [143, 151], [151, 152], [151, 158], [229, 237], [229, 247], [301, 302], [301, 306]], "functions": {"get_tenant_from_auth": {"executed_lines": [44, 193], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_tenant_from_auth._get_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 64, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 64, "excluded_lines": 0, "num_branches": 16, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 16}, "missing_lines": [51, 52, 53, 55, 56, 57, 59, 60, 65, 67, 73, 77, 78, 79, 80, 81, 87, 88, 89, 91, 92, 94, 95, 100, 102, 103, 106, 107, 108, 112, 113, 114, 115, 116, 122, 123, 125, 128, 131, 133, 134, 135, 141, 143, 144, 145, 151, 152, 153, 158, 164, 166, 167, 168, 171, 172, 173, 177, 178, 179, 180, 181, 187, 188], "excluded_lines": [], "executed_branches": [], "missing_branches": [[51, 52], [51, 87], [59, 60], [59, 67], [87, 88], [87, 122], [94, 95], [94, 102], [122, 123], [122, 187], [133, 134], [133, 141], [143, 144], [143, 151], [151, 152], [151, 158]]}, "create_or_get_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [219, 220, 228, 229, 237, 247, 254, 262, 264, 265, 266, 267, 274], "excluded_lines": [], "executed_branches": [], "missing_branches": [[229, 237], [229, 247]]}, "get_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [298, 299, 301, 302, 306, 312, 314, 315, 316, 317, 323], "excluded_lines": [], "executed_branches": [], "missing_branches": [[301, 302], [301, 306]]}, "update_session_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [354, 355, 363, 369, 376, 380, 381, 382, 383, 389], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "list_sessions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [420, 421, 431, 441, 443, 449, 458, 459, 460, 461, 462], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_session_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [486, 488, 494, 498, 503, 525, 531, 533, 534, 535], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 7, 8, 10, 11, 13, 14, 21, 22, 23, 24, 30, 31, 34, 35, 38, 196, 203, 280, 286, 328, 334, 394, 400, 467, 473], "summary": {"covered_lines": 25, "num_statements": 25, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 7, 8, 10, 11, 13, 14, 21, 22, 23, 24, 30, 31, 34, 35, 38, 44, 193, 196, 203, 280, 286, 328, 334, 394, 400, 467, 473], "summary": {"covered_lines": 27, "num_statements": 146, "percent_covered": 16.265060240963855, "percent_covered_display": "16.27", "missing_lines": 119, "excluded_lines": 0, "num_branches": 20, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 20}, "missing_lines": [51, 52, 53, 55, 56, 57, 59, 60, 65, 67, 73, 77, 78, 79, 80, 81, 87, 88, 89, 91, 92, 94, 95, 100, 102, 103, 106, 107, 108, 112, 113, 114, 115, 116, 122, 123, 125, 128, 131, 133, 134, 135, 141, 143, 144, 145, 151, 152, 153, 158, 164, 166, 167, 168, 171, 172, 173, 177, 178, 179, 180, 181, 187, 188, 219, 220, 228, 229, 237, 247, 254, 262, 264, 265, 266, 267, 274, 298, 299, 301, 302, 306, 312, 314, 315, 316, 317, 323, 354, 355, 363, 369, 376, 380, 381, 382, 383, 389, 420, 421, 431, 441, 443, 449, 458, 459, 460, 461, 462, 486, 488, 494, 498, 503, 525, 531, 533, 534, 535], "excluded_lines": [], "executed_branches": [], "missing_branches": [[51, 52], [51, 87], [59, 60], [59, 67], [87, 88], [87, 122], [94, 95], [94, 102], [122, 123], [122, 187], [133, 134], [133, 141], [143, 144], [143, 151], [151, 152], [151, 158], [229, 237], [229, 247], [301, 302], [301, 306]]}}}, "app\\api\\v1\\tenants.py": {"executed_lines": [1, 6, 7, 9, 10, 12, 13, 14, 15, 16, 17, 18, 21, 22, 25, 32, 121, 124, 131, 167, 173, 221, 227, 286, 292, 343, 349, 413, 419, 469, 475, 544, 550], "summary": {"covered_lines": 32, "num_statements": 175, "percent_covered": 15.458937198067632, "percent_covered_display": "15.46", "missing_lines": 143, "excluded_lines": 0, "num_branches": 32, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 32}, "missing_lines": [38, 39, 40, 42, 43, 44, 46, 47, 52, 54, 60, 64, 65, 66, 67, 68, 74, 75, 78, 80, 82, 83, 86, 87, 88, 90, 91, 94, 96, 101, 105, 106, 107, 108, 109, 115, 116, 143, 144, 149, 151, 155, 157, 158, 159, 160, 161, 185, 187, 188, 193, 198, 199, 200, 204, 210, 212, 213, 214, 215, 216, 243, 245, 246, 251, 256, 257, 258, 262, 268, 272, 273, 274, 275, 281, 306, 308, 309, 314, 319, 320, 321, 326, 328, 332, 334, 335, 336, 337, 338, 369, 371, 381, 382, 385, 392, 402, 403, 404, 405, 408, 433, 435, 436, 441, 447, 449, 455, 459, 460, 461, 462, 463, 491, 493, 494, 499, 504, 505, 511, 515, 516, 520, 526, 530, 531, 532, 533, 539, 564, 566, 567, 572, 578, 580, 581, 585, 591, 601, 602, 603, 604, 605], "excluded_lines": [], "executed_branches": [], "missing_branches": [[38, 39], [38, 74], [46, 47], [46, 54], [74, 75], [74, 115], [90, 91], [90, 96], [187, 188], [187, 198], [199, 200], [199, 204], [245, 246], [245, 256], [257, 258], [257, 262], [308, 309], [308, 319], [320, 321], [320, 326], [435, 436], [435, 447], [493, 494], [493, 504], [504, 505], [504, 511], [515, 516], [515, 520], [566, 567], [566, 578], [580, 581], [580, 585]], "functions": {"get_tenant_from_mixed_auth": {"executed_lines": [32, 121], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_tenant_from_mixed_auth._get_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 37, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 37, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [38, 39, 40, 42, 43, 44, 46, 47, 52, 54, 60, 64, 65, 66, 67, 68, 74, 75, 78, 80, 82, 83, 86, 87, 88, 90, 91, 94, 96, 101, 105, 106, 107, 108, 109, 115, 116], "excluded_lines": [], "executed_branches": [], "missing_branches": [[38, 39], [38, 74], [46, 47], [46, 54], [74, 75], [74, 115], [90, 91], [90, 96]]}, "create_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [143, 144, 149, 151, 155, 157, 158, 159, 160, 161], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [185, 187, 188, 193, 198, 199, 200, 204, 210, 212, 213, 214, 215, 216], "excluded_lines": [], "executed_branches": [], "missing_branches": [[187, 188], [187, 198], [199, 200], [199, 204]]}, "update_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [243, 245, 246, 251, 256, 257, 258, 262, 268, 272, 273, 274, 275, 281], "excluded_lines": [], "executed_branches": [], "missing_branches": [[245, 246], [245, 256], [257, 258], [257, 262]]}, "delete_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [306, 308, 309, 314, 319, 320, 321, 326, 328, 332, 334, 335, 336, 337, 338], "excluded_lines": [], "executed_branches": [], "missing_branches": [[308, 309], [308, 319], [320, 321], [320, 326]]}, "list_tenants": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [369, 371, 381, 382, 385, 392, 402, 403, 404, 405, 408], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_tenant_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [433, 435, 436, 441, 447, 449, 455, 459, 460, 461, 462, 463], "excluded_lines": [], "executed_branches": [], "missing_branches": [[435, 436], [435, 447]]}, "update_tenant_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [491, 493, 494, 499, 504, 505, 511, 515, 516, 520, 526, 530, 531, 532, 533, 539], "excluded_lines": [], "executed_branches": [], "missing_branches": [[493, 494], [493, 504], [504, 505], [504, 511], [515, 516], [515, 520]]}, "regenerate_api_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [564, 566, 567, 572, 578, 580, 581, 585, 591, 601, 602, 603, 604, 605], "excluded_lines": [], "executed_branches": [], "missing_branches": [[566, 567], [566, 578], [580, 581], [580, 585]]}, "": {"executed_lines": [1, 6, 7, 9, 10, 12, 13, 14, 15, 16, 17, 18, 21, 22, 25, 124, 131, 167, 173, 221, 227, 286, 292, 343, 349, 413, 419, 469, 475, 544, 550], "summary": {"covered_lines": 30, "num_statements": 30, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 6, 7, 9, 10, 12, 13, 14, 15, 16, 17, 18, 21, 22, 25, 32, 121, 124, 131, 167, 173, 221, 227, 286, 292, 343, 349, 413, 419, 469, 475, 544, 550], "summary": {"covered_lines": 32, "num_statements": 175, "percent_covered": 15.458937198067632, "percent_covered_display": "15.46", "missing_lines": 143, "excluded_lines": 0, "num_branches": 32, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 32}, "missing_lines": [38, 39, 40, 42, 43, 44, 46, 47, 52, 54, 60, 64, 65, 66, 67, 68, 74, 75, 78, 80, 82, 83, 86, 87, 88, 90, 91, 94, 96, 101, 105, 106, 107, 108, 109, 115, 116, 143, 144, 149, 151, 155, 157, 158, 159, 160, 161, 185, 187, 188, 193, 198, 199, 200, 204, 210, 212, 213, 214, 215, 216, 243, 245, 246, 251, 256, 257, 258, 262, 268, 272, 273, 274, 275, 281, 306, 308, 309, 314, 319, 320, 321, 326, 328, 332, 334, 335, 336, 337, 338, 369, 371, 381, 382, 385, 392, 402, 403, 404, 405, 408, 433, 435, 436, 441, 447, 449, 455, 459, 460, 461, 462, 463, 491, 493, 494, 499, 504, 505, 511, 515, 516, 520, 526, 530, 531, 532, 533, 539, 564, 566, 567, 572, 578, 580, 581, 585, 591, 601, 602, 603, 604, 605], "excluded_lines": [], "executed_branches": [], "missing_branches": [[38, 39], [38, 74], [46, 47], [46, 54], [74, 75], [74, 115], [90, 91], [90, 96], [187, 188], [187, 198], [199, 200], [199, 204], [245, 246], [245, 256], [257, 258], [257, 262], [308, 309], [308, 319], [320, 321], [320, 326], [435, 436], [435, 447], [493, 494], [493, 504], [504, 505], [504, 511], [515, 516], [515, 520], [566, 567], [566, 578], [580, 581], [580, 585]]}}}, "app\\api\\v1\\user_roles.py": {"executed_lines": [1, 6, 7, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 22, 25, 29, 30, 31, 98, 99, 100, 172, 173, 174, 233, 234, 235], "summary": {"covered_lines": 17, "num_statements": 80, "percent_covered": 18.88888888888889, "percent_covered_display": "18.89", "missing_lines": 63, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [8, 11, 20, 23, 26, 44, 45, 46, 53, 54, 58, 66, 77, 78, 85, 86, 87, 94, 113, 114, 115, 119, 120, 126, 130, 138, 149, 150, 151, 152, 159, 160, 161, 168, 186, 187, 188, 190, 191, 195, 196, 198, 206, 220, 221, 222, 223, 229, 250, 251, 254, 257, 258, 264, 265, 271, 278, 288, 300, 301, 302, 303, 311], "excluded_lines": [], "executed_branches": [], "missing_branches": [[53, 54], [53, 58], [119, 120], [119, 130], [190, 191], [190, 195], [257, 258], [257, 271], [264, 265], [264, 271]], "functions": {"assign_role_to_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [44, 45, 46, 53, 54, 58, 66, 77, 78, 85, 86, 87, 94], "excluded_lines": [], "executed_branches": [], "missing_branches": [[53, 54], [53, 58]]}, "remove_role_from_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [113, 114, 115, 119, 120, 126, 130, 138, 149, 150, 151, 152, 159, 160, 161, 168], "excluded_lines": [], "executed_branches": [], "missing_branches": [[119, 120], [119, 130]]}, "get_user_permissions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [186, 187, 188, 190, 191, 195, 196, 198, 206, 220, 221, 222, 223, 229], "excluded_lines": [], "executed_branches": [], "missing_branches": [[190, 191], [190, 195]]}, "check_permission": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [250, 251, 254, 257, 258, 264, 265, 271, 278, 288, 300, 301, 302, 303, 311], "excluded_lines": [], "executed_branches": [], "missing_branches": [[257, 258], [257, 271], [264, 265], [264, 271]]}, "": {"executed_lines": [1, 6, 7, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 22, 25, 29, 30, 31, 98, 99, 100, 172, 173, 174, 233, 234, 235], "summary": {"covered_lines": 17, "num_statements": 22, "percent_covered": 77.27272727272727, "percent_covered_display": "77.27", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 11, 20, 23, 26], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 6, 7, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 22, 25, 29, 30, 31, 98, 99, 100, 172, 173, 174, 233, 234, 235], "summary": {"covered_lines": 17, "num_statements": 80, "percent_covered": 18.88888888888889, "percent_covered_display": "18.89", "missing_lines": 63, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [8, 11, 20, 23, 26, 44, 45, 46, 53, 54, 58, 66, 77, 78, 85, 86, 87, 94, 113, 114, 115, 119, 120, 126, 130, 138, 149, 150, 151, 152, 159, 160, 161, 168, 186, 187, 188, 190, 191, 195, 196, 198, 206, 220, 221, 222, 223, 229, 250, 251, 254, 257, 258, 264, 265, 271, 278, 288, 300, 301, 302, 303, 311], "excluded_lines": [], "executed_branches": [], "missing_branches": [[53, 54], [53, 58], [119, 120], [119, 130], [190, 191], [190, 195], [257, 258], [257, 271], [264, 265], [264, 271]]}}}, "app\\api\\v1\\webhooks.py": {"executed_lines": [1, 6, 7, 9, 10, 12, 13, 14, 15, 16, 17, 18, 21, 24, 27, 28, 29, 30, 31, 32, 33, 34, 115, 116, 117, 118, 119, 120, 121, 184, 185, 186, 242, 243, 244, 313, 314, 315, 316, 317, 318], "summary": {"covered_lines": 18, "num_statements": 77, "percent_covered": 19.35483870967742, "percent_covered_display": "19.35", "missing_lines": 59, "excluded_lines": 0, "num_branches": 16, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 16}, "missing_lines": [8, 11, 19, 20, 23, 26, 42, 51, 53, 54, 55, 58, 59, 64, 65, 67, 69, 70, 76, 77, 78, 85, 91, 92, 108, 110, 111, 137, 141, 142, 143, 146, 147, 165, 167, 168, 173, 174, 181, 182, 183, 193, 203, 205, 209, 210, 211, 214, 215, 231, 233, 234, 239, 240, 241, 269, 273, 274, 275], "excluded_lines": [], "executed_branches": [], "missing_branches": [[54, 55], [54, 58], [58, 59], [58, 64], [110, 111], [110, 116], [117, 118], [117, 121], [167, 168], [167, 173], [173, 174], [173, 181], [233, 234], [233, 239], [240, 241], [240, 244]], "functions": {"get_webhook_service": {"executed_lines": [31], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_astrbot_webhook": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [51, 53, 54, 55, 58, 59, 64, 65, 67, 69, 70, 76, 77, 78, 85], "excluded_lines": [], "executed_branches": [], "missing_branches": [[54, 55], [54, 58], [58, 59], [58, 64]]}, "webhook_health_check": {"executed_lines": [115, 116, 117, 118, 119, 120, 121], "summary": {"covered_lines": 4, "num_statements": 11, "percent_covered": 26.666666666666668, "percent_covered_display": "26.67", "missing_lines": 7, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [108, 110, 111, 137, 141, 142, 143], "excluded_lines": [], "executed_branches": [], "missing_branches": [[110, 111], [110, 116], [117, 118], [117, 121]]}, "test_webhook_endpoint": {"executed_lines": [184, 185, 186], "summary": {"covered_lines": 2, "num_statements": 16, "percent_covered": 10.0, "percent_covered_display": "10.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [165, 167, 168, 173, 174, 181, 182, 183, 193, 203, 205, 209, 210, 211], "excluded_lines": [], "executed_branches": [], "missing_branches": [[167, 168], [167, 173], [173, 174], [173, 181]]}, "get_webhook_config": {"executed_lines": [242, 243, 244], "summary": {"covered_lines": 1, "num_statements": 11, "percent_covered": 6.666666666666667, "percent_covered_display": "6.67", "missing_lines": 10, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [231, 233, 234, 239, 240, 241, 269, 273, 274, 275], "excluded_lines": [], "executed_branches": [], "missing_branches": [[233, 234], [233, 239], [240, 241], [240, 244]]}, "": {"executed_lines": [1, 6, 7, 9, 10, 12, 13, 14, 15, 16, 17, 18, 21, 24, 27, 28, 29, 30, 32, 33, 34], "summary": {"covered_lines": 10, "num_statements": 23, "percent_covered": 43.47826086956522, "percent_covered_display": "43.48", "missing_lines": 13, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 11, 19, 20, 23, 26, 42, 91, 92, 146, 147, 214, 215], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 6, 7, 9, 10, 12, 13, 14, 15, 16, 17, 18, 21, 24, 27, 28, 29, 30, 31, 32, 33, 34, 115, 116, 117, 118, 119, 120, 121, 184, 185, 186, 242, 243, 244], "summary": {"covered_lines": 18, "num_statements": 77, "percent_covered": 19.35483870967742, "percent_covered_display": "19.35", "missing_lines": 59, "excluded_lines": 0, "num_branches": 16, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 16}, "missing_lines": [8, 11, 19, 20, 23, 26, 42, 51, 53, 54, 55, 58, 59, 64, 65, 67, 69, 70, 76, 77, 78, 85, 91, 92, 108, 110, 111, 137, 141, 142, 143, 146, 147, 165, 167, 168, 173, 174, 181, 182, 183, 193, 203, 205, 209, 210, 211, 214, 215, 231, 233, 234, 239, 240, 241, 269, 273, 274, 275], "excluded_lines": [], "executed_branches": [], "missing_branches": [[54, 55], [54, 58], [58, 59], [58, 64], [110, 111], [110, 116], [117, 118], [117, 121], [167, 168], [167, 173], [173, 174], [173, 181], [233, 234], [233, 239], [240, 241], [240, 244]]}}}, "app\\api\\v1\\websocket.py": {"executed_lines": [1, 5, 6, 8, 9, 10, 12, 13, 14, 15, 16, 17, 20, 24, 25, 27, 29, 31, 33, 44, 66, 69, 78, 89, 113, 137, 140, 143, 144, 145, 146, 207, 208, 209, 301, 306, 311, 316], "summary": {"covered_lines": 19, "num_statements": 148, "percent_covered": 10.0, "percent_covered_display": "10.00", "missing_lines": 129, "excluded_lines": 0, "num_branches": 42, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 42}, "missing_lines": [7, 11, 18, 19, 22, 26, 35, 37, 40, 41, 43, 51, 53, 54, 57, 58, 61, 62, 63, 64, 70, 74, 75, 77, 82, 84, 85, 86, 87, 93, 95, 96, 98, 99, 100, 101, 102, 103, 109, 112, 115, 117, 118, 120, 121, 122, 123, 124, 125, 131, 134, 135, 139, 142, 159, 161, 162, 163, 164, 167, 170, 171, 173, 174, 176, 177, 180, 188, 189, 194, 195, 202, 204, 205, 206, 217, 218, 220, 222, 225, 226, 227, 232, 234, 235, 238, 244, 246, 247, 249, 251, 252, 253, 256, 257, 258, 263, 266, 273, 276, 292, 296, 297, 303, 305, 308, 310, 313, 315, 318, 320, 323, 324, 332, 333, 334, 335, 337, 338, 339, 341, 342, 343, 344, 347, 348, 349, 353, 354], "excluded_lines": [], "executed_branches": [], "missing_branches": [[40, 41], [40, 43], [53, 54], [53, 61], [57, 58], [57, 61], [61, 62], [61, 66], [63, 61], [63, 64], [74, 75], [74, 77], [84, 85], [84, 89], [86, 87], [86, 89], [95, 96], [95, 98], [99, 100], [99, 112], [112, -93], [112, 113], [117, 118], [117, 120], [121, 122], [121, 134], [134, -115], [134, 135], [162, 163], [162, 167], [220, 222], [220, 244], [226, 227], [226, 234], [244, 246], [244, 249], [249, 251], [249, 292], [257, 258], [257, 266], [333, 334], [333, 337]], "functions": {"ConnectionManager.__init__": {"executed_lines": [31, 33], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ConnectionManager.connect": {"executed_lines": [44], "summary": {"covered_lines": 1, "num_statements": 5, "percent_covered": 14.285714285714286, "percent_covered_display": "14.29", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [37, 40, 41, 43], "excluded_lines": [], "executed_branches": [], "missing_branches": [[40, 41], [40, 43]]}, "ConnectionManager.disconnect": {"executed_lines": [66], "summary": {"covered_lines": 1, "num_statements": 9, "percent_covered": 5.882352941176471, "percent_covered_display": "5.88", "missing_lines": 8, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [53, 54, 57, 58, 61, 62, 63, 64], "excluded_lines": [], "executed_branches": [], "missing_branches": [[53, 54], [53, 61], [57, 58], [57, 61], [61, 62], [61, 66], [63, 61], [63, 64]]}, "ConnectionManager.subscribe_to_session": {"executed_lines": [78], "summary": {"covered_lines": 1, "num_statements": 4, "percent_covered": 16.666666666666668, "percent_covered_display": "16.67", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [74, 75, 77], "excluded_lines": [], "executed_branches": [], "missing_branches": [[74, 75], [74, 77]]}, "ConnectionManager.unsubscribe_from_session": {"executed_lines": [89], "summary": {"covered_lines": 1, "num_statements": 5, "percent_covered": 11.11111111111111, "percent_covered_display": "11.11", "missing_lines": 4, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [84, 85, 86, 87], "excluded_lines": [], "executed_branches": [], "missing_branches": [[84, 85], [84, 89], [86, 87], [86, 89]]}, "ConnectionManager.broadcast_to_tenant": {"executed_lines": [113], "summary": {"covered_lines": 1, "num_statements": 11, "percent_covered": 5.882352941176471, "percent_covered_display": "5.88", "missing_lines": 10, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [95, 96, 98, 99, 100, 101, 102, 103, 109, 112], "excluded_lines": [], "executed_branches": [], "missing_branches": [[95, 96], [95, 98], [99, 100], [99, 112], [112, -93], [112, 113]]}, "ConnectionManager.broadcast_to_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [117, 118, 120, 121, 122, 123, 124, 125, 131, 134, 135], "excluded_lines": [], "executed_branches": [], "missing_branches": [[117, 118], [117, 120], [121, 122], [121, 134], [134, -115], [134, 135]]}, "websocket_endpoint": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 21, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [159, 161, 162, 163, 164, 167, 170, 171, 173, 174, 176, 177, 180, 188, 189, 194, 195, 202, 204, 205, 206], "excluded_lines": [], "executed_branches": [], "missing_branches": [[162, 163], [162, 167]]}, "handle_websocket_message": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 28, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 28, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [217, 218, 220, 222, 225, 226, 227, 232, 234, 235, 238, 244, 246, 247, 249, 251, 252, 253, 256, 257, 258, 263, 266, 273, 276, 292, 296, 297], "excluded_lines": [], "executed_branches": [], "missing_branches": [[220, 222], [220, 244], [226, 227], [226, 234], [244, 246], [244, 249], [249, 251], [249, 292], [257, 258], [257, 266]]}, "broadcast_message_to_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [305], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "broadcast_notification_to_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [310], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_session_connections_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [315], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_tenant_connections_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [320], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "websocket_session_endpoint": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [332, 333, 334, 335, 337, 338, 339, 341, 342, 343, 344, 347, 348, 349, 353, 354], "excluded_lines": [], "executed_branches": [], "missing_branches": [[333, 334], [333, 337]]}, "": {"executed_lines": [1, 5, 6, 8, 9, 10, 12, 13, 14, 15, 16, 17, 20, 24, 25, 27, 29, 69, 137, 140, 143, 144, 145, 146, 207, 208, 209, 301, 306, 311, 316], "summary": {"covered_lines": 12, "num_statements": 32, "percent_covered": 37.5, "percent_covered_display": "37.50", "missing_lines": 20, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [7, 11, 18, 19, 22, 26, 35, 51, 70, 82, 93, 115, 139, 142, 303, 308, 313, 318, 323, 324], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"ConnectionManager": {"executed_lines": [31, 33, 44, 66, 78, 89, 113], "summary": {"covered_lines": 7, "num_statements": 47, "percent_covered": 9.333333333333334, "percent_covered_display": "9.33", "missing_lines": 40, "excluded_lines": 0, "num_branches": 28, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 28}, "missing_lines": [37, 40, 41, 43, 53, 54, 57, 58, 61, 62, 63, 64, 74, 75, 77, 84, 85, 86, 87, 95, 96, 98, 99, 100, 101, 102, 103, 109, 112, 117, 118, 120, 121, 122, 123, 124, 125, 131, 134, 135], "excluded_lines": [], "executed_branches": [], "missing_branches": [[40, 41], [40, 43], [53, 54], [53, 61], [57, 58], [57, 61], [61, 62], [61, 66], [63, 61], [63, 64], [74, 75], [74, 77], [84, 85], [84, 89], [86, 87], [86, 89], [95, 96], [95, 98], [99, 100], [99, 112], [112, -93], [112, 113], [117, 118], [117, 120], [121, 122], [121, 134], [134, -115], [134, 135]]}, "": {"executed_lines": [1, 5, 6, 8, 9, 10, 12, 13, 14, 15, 16, 17, 20, 24, 25, 27, 29, 69, 137, 140, 143, 144, 145, 146, 207, 208, 209, 301, 306, 311, 316], "summary": {"covered_lines": 12, "num_statements": 101, "percent_covered": 10.434782608695652, "percent_covered_display": "10.43", "missing_lines": 89, "excluded_lines": 0, "num_branches": 14, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 14}, "missing_lines": [7, 11, 18, 19, 22, 26, 35, 51, 70, 82, 93, 115, 139, 142, 159, 161, 162, 163, 164, 167, 170, 171, 173, 174, 176, 177, 180, 188, 189, 194, 195, 202, 204, 205, 206, 217, 218, 220, 222, 225, 226, 227, 232, 234, 235, 238, 244, 246, 247, 249, 251, 252, 253, 256, 257, 258, 263, 266, 273, 276, 292, 296, 297, 303, 305, 308, 310, 313, 315, 318, 320, 323, 324, 332, 333, 334, 335, 337, 338, 339, 341, 342, 343, 344, 347, 348, 349, 353, 354], "excluded_lines": [], "executed_branches": [], "missing_branches": [[162, 163], [162, 167], [220, 222], [220, 244], [226, 227], [226, 234], [244, 246], [244, 249], [249, 251], [249, 292], [257, 258], [257, 266], [333, 334], [333, 337]]}}}, "app\\core\\config\\settings.py": {"executed_lines": [1, 7, 9, 10, 13, 14, 16, 24, 25, 26, 27, 28, 31, 32, 35, 36, 37, 40, 43, 50, 51, 54, 56, 57, 60, 62, 63, 67, 68, 69, 70, 71, 73, 75, 76, 78, 81, 82, 83, 84, 85, 88, 89, 94, 96, 97, 99, 103, 105, 106, 107, 108, 111, 114, 117, 119, 120, 122, 123, 125, 128, 129, 130, 131, 132, 133, 134, 137, 138, 141, 143, 144, 146, 148, 149, 154, 155, 159, 160, 164, 165, 169, 170, 174, 175, 179, 180, 184, 185, 189, 190, 195, 196, 203, 204, 207, 208, 211, 214, 225, 227, 228, 244, 246, 279, 282], "summary": {"covered_lines": 104, "num_statements": 143, "percent_covered": 64.24581005586592, "percent_covered_display": "64.25", "missing_lines": 39, "excluded_lines": 0, "num_branches": 36, "num_partial_branches": 9, "covered_branches": 11, "missing_branches": 25}, "missing_lines": [58, 61, 64, 79, 91, 100, 112, 124, 151, 157, 162, 167, 172, 177, 182, 187, 192, 230, 232, 233, 234, 235, 242, 249, 256, 257, 258, 259, 261, 265, 267, 268, 269, 270, 271, 272, 273, 275, 291], "excluded_lines": [], "executed_branches": [[57, 60], [60, 62], [62, 63], [78, 81], [88, 89], [99, 103], [111, 114], [123, 125], [227, 228], [227, 244], [228, 227]], "missing_branches": [[57, 58], [60, 61], [62, 64], [78, 79], [88, 91], [99, 100], [111, 112], [123, 124], [228, 230], [232, 233], [232, 234], [234, 235], [234, 242], [257, 258], [257, 275], [258, 257], [258, 259], [261, 265], [261, 273], [265, 267], [265, 273], [268, 269], [268, 273], [270, 271], [270, 273]], "functions": {"Settings.assemble_cors_origins": {"executed_lines": [54, 56, 57, 60, 62, 63], "summary": {"covered_lines": 6, "num_statements": 9, "percent_covered": 60.0, "percent_covered_display": "60.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 3, "covered_branches": 3, "missing_branches": 3}, "missing_lines": [58, 61, 64], "excluded_lines": [], "executed_branches": [[57, 60], [60, 62], [62, 63]], "missing_branches": [[57, 58], [60, 61], [62, 64]]}, "Settings.assemble_db_connection": {"executed_lines": [78, 81, 82, 83, 84, 85, 88, 89], "summary": {"covered_lines": 8, "num_statements": 10, "percent_covered": 71.42857142857143, "percent_covered_display": "71.43", "missing_lines": 2, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 2, "covered_branches": 2, "missing_branches": 2}, "missing_lines": [79, 91], "excluded_lines": [], "executed_branches": [[78, 81], [88, 89]], "missing_branches": [[78, 79], [88, 91]]}, "Settings.assemble_redis_connection": {"executed_lines": [99, 103, 105, 106, 107, 108, 111, 114], "summary": {"covered_lines": 8, "num_statements": 10, "percent_covered": 71.42857142857143, "percent_covered_display": "71.43", "missing_lines": 2, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 2, "covered_branches": 2, "missing_branches": 2}, "missing_lines": [100, 112], "excluded_lines": [], "executed_branches": [[99, 103], [111, 114]], "missing_branches": [[99, 100], [111, 112]]}, "Settings.validate_log_level": {"executed_lines": [122, 123, 125], "summary": {"covered_lines": 3, "num_statements": 4, "percent_covered": 66.66666666666667, "percent_covered_display": "66.67", "missing_lines": 1, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 1}, "missing_lines": [124], "excluded_lines": [], "executed_branches": [[123, 125]], "missing_branches": [[123, 124]]}, "Settings.is_development": {"executed_lines": [146], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Settings.is_production": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [151], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Settings.HOST": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [157], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Settings.PORT": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [162], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Settings.DB_HOST": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [167], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Settings.DB_USER": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [172], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Settings.DB_PASSWORD": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [177], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Settings.DB_NAME": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [182], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Settings.DB_PORT": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [187], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Settings.CORS_ALLOWED_ORIGINS": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [192], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Settings.__init__": {"executed_lines": [214, 225, 227, 228, 244], "summary": {"covered_lines": 5, "num_statements": 11, "percent_covered": 42.10526315789474, "percent_covered_display": "42.11", "missing_lines": 6, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 1, "covered_branches": 3, "missing_branches": 5}, "missing_lines": [230, 232, 233, 234, 235, 242], "excluded_lines": [], "executed_branches": [[227, 228], [227, 244], [228, 227]], "missing_branches": [[228, 230], [232, 233], [232, 234], [234, 235], [234, 242]]}, "Settings.__str__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 12, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 12}, "missing_lines": [249, 256, 257, 258, 259, 261, 265, 267, 268, 269, 270, 271, 272, 273, 275], "excluded_lines": [], "executed_branches": [], "missing_branches": [[257, 258], [257, 275], [258, 257], [258, 259], [261, 265], [261, 273], [265, 267], [265, 273], [268, 269], [268, 273], [270, 271], [270, 273]]}, "get_settings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [291], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 7, 9, 10, 13, 14, 16, 24, 25, 26, 27, 28, 31, 32, 35, 36, 37, 40, 43, 50, 51, 67, 68, 69, 70, 71, 73, 75, 76, 94, 96, 97, 117, 119, 120, 128, 129, 130, 131, 132, 133, 134, 137, 138, 141, 143, 144, 148, 149, 154, 155, 159, 160, 164, 165, 169, 170, 174, 175, 179, 180, 184, 185, 189, 190, 195, 196, 203, 204, 207, 208, 211, 246, 279, 282], "summary": {"covered_lines": 73, "num_statements": 73, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"Settings": {"executed_lines": [54, 56, 57, 60, 62, 63, 78, 81, 82, 83, 84, 85, 88, 89, 99, 103, 105, 106, 107, 108, 111, 114, 122, 123, 125, 146, 214, 225, 227, 228, 244], "summary": {"covered_lines": 31, "num_statements": 69, "percent_covered": 40.0, "percent_covered_display": "40.00", "missing_lines": 38, "excluded_lines": 0, "num_branches": 36, "num_partial_branches": 9, "covered_branches": 11, "missing_branches": 25}, "missing_lines": [58, 61, 64, 79, 91, 100, 112, 124, 151, 157, 162, 167, 172, 177, 182, 187, 192, 230, 232, 233, 234, 235, 242, 249, 256, 257, 258, 259, 261, 265, 267, 268, 269, 270, 271, 272, 273, 275], "excluded_lines": [], "executed_branches": [[57, 60], [60, 62], [62, 63], [78, 81], [88, 89], [99, 103], [111, 114], [123, 125], [227, 228], [227, 244], [228, 227]], "missing_branches": [[57, 58], [60, 61], [62, 64], [78, 79], [88, 91], [99, 100], [111, 112], [123, 124], [228, 230], [232, 233], [232, 234], [234, 235], [234, 242], [257, 258], [257, 275], [258, 257], [258, 259], [261, 265], [261, 273], [265, 267], [265, 273], [268, 269], [268, 273], [270, 271], [270, 273]]}, "": {"executed_lines": [1, 7, 9, 10, 13, 14, 16, 24, 25, 26, 27, 28, 31, 32, 35, 36, 37, 40, 43, 50, 51, 67, 68, 69, 70, 71, 73, 75, 76, 94, 96, 97, 117, 119, 120, 128, 129, 130, 131, 132, 133, 134, 137, 138, 141, 143, 144, 148, 149, 154, 155, 159, 160, 164, 165, 169, 170, 174, 175, 179, 180, 184, 185, 189, 190, 195, 196, 203, 204, 207, 208, 211, 246, 279, 282], "summary": {"covered_lines": 73, "num_statements": 74, "percent_covered": 98.64864864864865, "percent_covered_display": "98.65", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [291], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\core\\database.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 55, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 55, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [6, 8, 9, 11, 12, 15, 18, 21, 27, 31, 42, 51, 64, 65, 66, 67, 68, 69, 70, 71, 73, 74, 77, 85, 86, 87, 93, 94, 95, 96, 97, 100, 105, 106, 107, 108, 109, 110, 111, 114, 121, 122, 124, 125, 126, 127, 128, 129, 130, 134, 141, 143, 144, 145, 147], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"get_db": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [64, 65, 66, 67, 68, 69, 70, 71, 73, 74], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "init_db": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [85, 86, 87, 93, 94, 95, 96, 97], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "close_db": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [105, 106, 107, 108, 109, 110, 111], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "check_db_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [121, 122, 124, 125, 126, 127, 128, 129, 130], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "health_check": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [141, 143, 144, 145, 147], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [6, 8, 9, 11, 12, 15, 18, 21, 27, 31, 42, 51, 77, 100, 114, 134], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"Base": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 55, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 55, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [6, 8, 9, 11, 12, 15, 18, 21, 27, 31, 42, 51, 64, 65, 66, 67, 68, 69, 70, 71, 73, 74, 77, 85, 86, 87, 93, 94, 95, 96, 97, 100, 105, 106, 107, 108, 109, 110, 111, 114, 121, 122, 124, 125, 126, 127, 128, 129, 130, 134, 141, 143, 144, 145, 147], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\core\\middleware.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 126, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 126, "excluded_lines": 0, "num_branches": 28, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 28}, "missing_lines": [6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 20, 21, 27, 29, 32, 41, 53, 67, 68, 71, 73, 75, 76, 83, 86, 88, 97, 100, 101, 109, 110, 112, 114, 116, 117, 125, 138, 140, 141, 149, 164, 166, 177, 178, 181, 182, 183, 185, 187, 200, 202, 204, 205, 206, 207, 209, 210, 211, 214, 215, 218, 221, 227, 228, 233, 234, 235, 236, 239, 245, 252, 265, 268, 269, 270, 271, 272, 277, 278, 282, 285, 292, 301, 302, 303, 304, 306, 323, 326, 327, 331, 337, 339, 341, 352, 353, 354, 357, 358, 360, 370, 373, 374, 376, 379, 380, 383, 385, 392, 394, 395, 397, 400, 407, 421, 429, 430, 432, 433, 436, 439, 442, 447, 449], "excluded_lines": [], "executed_branches": [], "missing_branches": [[75, 76], [75, 86], [177, 178], [177, 181], [181, 182], [181, 185], [182, 181], [182, 183], [205, 206], [205, 214], [209, 210], [209, 214], [215, 218], [215, 221], [277, 278], [277, 282], [326, 327], [326, 337], [353, 354], [353, 357], [373, 374], [373, 376], [394, 395], [394, 397], [429, 430], [429, 432], [432, 433], [432, 436]], "functions": {"TenantContextMiddleware.dispatch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 24, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [67, 68, 71, 73, 75, 76, 83, 86, 88, 97, 100, 101, 109, 110, 112, 114, 116, 117, 125, 138, 140, 141, 149, 164], "excluded_lines": [], "executed_branches": [], "missing_branches": [[75, 76], [75, 86]]}, "TenantContextMiddleware._should_skip_tenant_check": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [177, 178, 181, 182, 183, 185], "excluded_lines": [], "executed_branches": [], "missing_branches": [[177, 178], [177, 181], [181, 182], [181, 185], [182, 181], [182, 183]]}, "TenantContextMiddleware._extract_and_set_tenant_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 20, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [200, 202, 204, 205, 206, 207, 209, 210, 211, 214, 215, 218, 221, 227, 228, 233, 234, 235, 236, 239], "excluded_lines": [], "executed_branches": [], "missing_branches": [[205, 206], [205, 214], [209, 210], [209, 214], [215, 218], [215, 221]]}, "SecurityHeadersMiddleware.dispatch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [265, 268, 269, 270, 271, 272, 277, 278, 282], "excluded_lines": [], "executed_branches": [], "missing_branches": [[277, 278], [277, 282]]}, "RateLimitMiddleware.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [301, 302, 303, 304], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "RateLimitMiddleware.dispatch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [323, 326, 327, 331, 337, 339], "excluded_lines": [], "executed_branches": [], "missing_branches": [[326, 327], [326, 337]]}, "RateLimitMiddleware._get_client_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [352, 353, 354, 357, 358], "excluded_lines": [], "executed_branches": [], "missing_branches": [[353, 354], [353, 357]]}, "RateLimitMiddleware._is_rate_limited": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [370, 373, 374, 376, 379, 380, 383], "excluded_lines": [], "executed_branches": [], "missing_branches": [[373, 374], [373, 376]]}, "RateLimitMiddleware._record_request": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [392, 394, 395, 397], "excluded_lines": [], "executed_branches": [], "missing_branches": [[394, 395], [394, 397]]}, "RequestLoggingMiddleware.dispatch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [421, 429, 430, 432, 433, 436, 439, 442, 447, 449], "excluded_lines": [], "executed_branches": [], "missing_branches": [[429, 430], [429, 432], [432, 433], [432, 436]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 31, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 31, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 20, 21, 27, 29, 32, 41, 53, 166, 187, 245, 252, 285, 292, 306, 341, 360, 385, 400, 407], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"TenantContextMiddleware": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 50, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 50, "excluded_lines": 0, "num_branches": 14, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 14}, "missing_lines": [67, 68, 71, 73, 75, 76, 83, 86, 88, 97, 100, 101, 109, 110, 112, 114, 116, 117, 125, 138, 140, 141, 149, 164, 177, 178, 181, 182, 183, 185, 200, 202, 204, 205, 206, 207, 209, 210, 211, 214, 215, 218, 221, 227, 228, 233, 234, 235, 236, 239], "excluded_lines": [], "executed_branches": [], "missing_branches": [[75, 76], [75, 86], [177, 178], [177, 181], [181, 182], [181, 185], [182, 181], [182, 183], [205, 206], [205, 214], [209, 210], [209, 214], [215, 218], [215, 221]]}, "SecurityHeadersMiddleware": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [265, 268, 269, 270, 271, 272, 277, 278, 282], "excluded_lines": [], "executed_branches": [], "missing_branches": [[277, 278], [277, 282]]}, "RateLimitMiddleware": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 26, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 26, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [301, 302, 303, 304, 323, 326, 327, 331, 337, 339, 352, 353, 354, 357, 358, 370, 373, 374, 376, 379, 380, 383, 392, 394, 395, 397], "excluded_lines": [], "executed_branches": [], "missing_branches": [[326, 327], [326, 337], [353, 354], [353, 357], [373, 374], [373, 376], [394, 395], [394, 397]]}, "RequestLoggingMiddleware": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [421, 429, 430, 432, 433, 436, 439, 442, 447, 449], "excluded_lines": [], "executed_branches": [], "missing_branches": [[429, 430], [429, 432], [432, 433], [432, 436]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 31, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 31, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 20, 21, 27, 29, 32, 41, 53, 166, 187, 245, 252, 285, 292, 306, 341, 360, 385, 400, 407], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\core\\permissions.py": {"executed_lines": [1, 6, 7, 8, 10, 11, 12, 14, 15, 16, 17, 18, 19, 22, 25, 28, 29, 31, 32, 55, 128, 129, 131, 133, 140, 142, 143, 196, 267, 337, 338, 341, 342, 343, 346, 347, 348, 351, 352, 353, 356, 357, 358, 361, 362, 363, 366, 367, 370, 371, 372, 375, 376, 377, 380, 381, 385, 386, 390, 391, 397, 402, 407, 412, 417, 422, 427, 432, 437, 442, 443], "summary": {"covered_lines": 34, "num_statements": 151, "percent_covered": 19.428571428571427, "percent_covered_display": "19.43", "missing_lines": 117, "excluded_lines": 0, "num_branches": 24, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 24}, "missing_lines": [9, 13, 20, 23, 26, 46, 47, 48, 51, 56, 73, 74, 75, 77, 78, 79, 82, 84, 90, 91, 98, 99, 106, 113, 122, 124, 126, 132, 141, 163, 165, 172, 173, 180, 186, 194, 197, 213, 214, 215, 217, 218, 219, 221, 222, 228, 229, 231, 232, 238, 239, 246, 248, 249, 250, 256, 261, 263, 265, 268, 284, 285, 286, 288, 289, 290, 292, 293, 299, 300, 302, 303, 309, 311, 312, 318, 322, 330, 332, 334, 344, 349, 354, 359, 364, 368, 373, 378, 382, 384, 387, 389, 392, 394, 398, 400, 403, 405, 408, 410, 413, 415, 418, 420, 423, 425, 428, 430, 433, 435, 438, 440, 466, 468, 475, 476, 482], "excluded_lines": [], "executed_branches": [], "missing_branches": [[82, 84], [82, 90], [98, 99], [98, 113], [172, 173], [172, 186], [221, 222], [221, 228], [231, 232], [231, 248], [232, 231], [232, 238], [248, 249], [248, 261], [292, 293], [292, 299], [302, 303], [302, 311], [303, 302], [303, 309], [311, 312], [311, 322], [475, 476], [475, 482]], "functions": {"PermissionError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [46, 47, 48, 51], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_permission": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [73, 74, 126], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_permission.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [75, 124], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_permission.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [77, 78, 79, 82, 84, 90, 91, 98, 99, 106, 113, 122], "excluded_lines": [], "executed_branches": [], "missing_branches": [[82, 84], [82, 90], [98, 99], [98, 113]]}, "PermissionChecker.__init__": {"executed_lines": [133, 140], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [141], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "PermissionChecker.__call__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [163, 165, 172, 173, 180, 186, 194], "excluded_lines": [], "executed_branches": [], "missing_branches": [[172, 173], [172, 186]]}, "require_any_permission": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [213, 214, 265], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_any_permission.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [215, 263], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_any_permission.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 17, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [217, 218, 219, 221, 222, 228, 229, 231, 232, 238, 239, 246, 248, 249, 250, 256, 261], "excluded_lines": [], "executed_branches": [], "missing_branches": [[221, 222], [221, 228], [231, 232], [231, 248], [232, 231], [232, 238], [248, 249], [248, 261]]}, "require_all_permissions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [284, 285, 334], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_all_permissions.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [286, 332], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_all_permissions.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [288, 289, 290, 292, 293, 299, 300, 302, 303, 309, 311, 312, 318, 322, 330], "excluded_lines": [], "executed_branches": [], "missing_branches": [[292, 293], [292, 299], [302, 303], [302, 311], [303, 302], [303, 309], [311, 312], [311, 322]]}, "CommonPermissions.can_view_analytics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [384], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "CommonPermissions.can_create_analytics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [389], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "CommonPermissions.can_export_analytics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [394], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_tenant_read": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [400], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_tenant_write": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [405], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_session_read": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [410], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_session_write": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [415], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_message_read": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [420], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_message_write": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [425], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_role_manage": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [430], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_ai_use": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [435], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "require_instance_manage": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [440], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "check_permission_dependency": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [466, 468, 475, 476, 482], "excluded_lines": [], "executed_branches": [], "missing_branches": [[475, 476], [475, 482]]}, "": {"executed_lines": [1, 6, 7, 8, 10, 11, 12, 14, 15, 16, 17, 18, 19, 22, 25, 28, 29, 31, 32, 55, 128, 129, 131, 142, 143, 196, 267, 337, 338, 341, 342, 343, 346, 347, 348, 351, 352, 353, 356, 357, 358, 361, 362, 363, 366, 367, 370, 371, 372, 375, 376, 377, 380, 381, 385, 386, 390, 391, 397, 402, 407, 412, 417, 422, 427, 432, 437, 442, 443], "summary": {"covered_lines": 33, "num_statements": 62, "percent_covered": 53.225806451612904, "percent_covered_display": "53.23", "missing_lines": 29, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 13, 20, 23, 26, 56, 132, 197, 268, 344, 349, 354, 359, 364, 368, 373, 378, 382, 387, 392, 398, 403, 408, 413, 418, 423, 428, 433, 438], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"PermissionError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [46, 47, 48, 51], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "PermissionChecker": {"executed_lines": [133, 140], "summary": {"covered_lines": 1, "num_statements": 9, "percent_covered": 9.090909090909092, "percent_covered_display": "9.09", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [141, 163, 165, 172, 173, 180, 186, 194], "excluded_lines": [], "executed_branches": [], "missing_branches": [[172, 173], [172, 186]]}, "CommonPermissions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [384, 389, 394], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 7, 8, 10, 11, 12, 14, 15, 16, 17, 18, 19, 22, 25, 28, 29, 31, 32, 55, 128, 129, 131, 142, 143, 196, 267, 337, 338, 341, 342, 343, 346, 347, 348, 351, 352, 353, 356, 357, 358, 361, 362, 363, 366, 367, 370, 371, 372, 375, 376, 377, 380, 381, 385, 386, 390, 391, 397, 402, 407, 412, 417, 422, 427, 432, 437, 442, 443], "summary": {"covered_lines": 33, "num_statements": 135, "percent_covered": 21.019108280254777, "percent_covered_display": "21.02", "missing_lines": 102, "excluded_lines": 0, "num_branches": 22, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 22}, "missing_lines": [9, 13, 20, 23, 26, 56, 73, 74, 75, 77, 78, 79, 82, 84, 90, 91, 98, 99, 106, 113, 122, 124, 126, 132, 197, 213, 214, 215, 217, 218, 219, 221, 222, 228, 229, 231, 232, 238, 239, 246, 248, 249, 250, 256, 261, 263, 265, 268, 284, 285, 286, 288, 289, 290, 292, 293, 299, 300, 302, 303, 309, 311, 312, 318, 322, 330, 332, 334, 344, 349, 354, 359, 364, 368, 373, 378, 382, 387, 392, 398, 400, 403, 405, 408, 410, 413, 415, 418, 420, 423, 425, 428, 430, 433, 435, 438, 440, 466, 468, 475, 476, 482], "excluded_lines": [], "executed_branches": [], "missing_branches": [[82, 84], [82, 90], [98, 99], [98, 113], [221, 222], [221, 228], [231, 232], [231, 248], [232, 231], [232, 238], [248, 249], [248, 261], [292, 293], [292, 299], [302, 303], [302, 311], [303, 302], [303, 309], [311, 312], [311, 322], [475, 476], [475, 482]]}}}, "app\\core\\security.py": {"executed_lines": [1, 5, 6, 7, 9, 10, 12, 15, 18, 19, 21, 24, 25, 27, 30, 31, 33, 36, 50, 63, 64, 106, 107, 140, 183, 205, 227, 228, 258, 289, 309, 345, 346, 348, 350, 352, 356, 360, 367, 370, 380, 402], "summary": {"covered_lines": 10, "num_statements": 124, "percent_covered": 6.329113924050633, "percent_covered_display": "6.33", "missing_lines": 114, "excluded_lines": 0, "num_branches": 34, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 34}, "missing_lines": [8, 11, 13, 16, 22, 28, 34, 37, 48, 51, 61, 80, 81, 83, 88, 96, 97, 100, 104, 120, 121, 123, 127, 134, 138, 141, 156, 158, 163, 164, 167, 168, 169, 171, 172, 174, 176, 177, 178, 179, 180, 181, 184, 197, 198, 200, 201, 203, 206, 216, 217, 218, 220, 221, 222, 224, 225, 244, 247, 250, 252, 259, 274, 275, 277, 278, 285, 287, 290, 300, 301, 304, 305, 307, 310, 320, 321, 323, 326, 327, 329, 332, 333, 336, 337, 340, 341, 343, 349, 351, 353, 355, 357, 359, 361, 364, 368, 371, 378, 381, 392, 393, 395, 397, 398, 399, 400, 403, 416, 417, 419, 420, 421, 423], "excluded_lines": [], "executed_branches": [], "missing_branches": [[80, 81], [80, 83], [96, 97], [96, 100], [120, 121], [120, 123], [163, 164], [163, 167], [168, 169], [168, 171], [171, 172], [171, 174], [200, 201], [200, 203], [220, 221], [220, 222], [277, 278], [277, 285], [320, 321], [320, 323], [326, 327], [326, 329], [332, 333], [332, 336], [336, 337], [336, 340], [340, 341], [340, 343], [392, 393], [392, 395], [416, 417], [416, 419], [420, 421], [420, 423]], "functions": {"verify_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [48], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_password_hash": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [61], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "create_access_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [80, 81, 83, 88, 96, 97, 100, 104], "excluded_lines": [], "executed_branches": [], "missing_branches": [[80, 81], [80, 83], [96, 97], [96, 100]]}, "create_refresh_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [120, 121, 123, 127, 134, 138], "excluded_lines": [], "executed_branches": [], "missing_branches": [[120, 121], [120, 123]]}, "verify_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [156, 158, 163, 164, 167, 168, 169, 171, 172, 174, 176, 177, 178, 179, 180, 181], "excluded_lines": [], "executed_branches": [], "missing_branches": [[163, 164], [163, 167], [168, 169], [168, 171], [171, 172], [171, 174]]}, "get_subject_from_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [197, 198, 200, 201, 203], "excluded_lines": [], "executed_branches": [], "missing_branches": [[200, 201], [200, 203]]}, "get_tenant_id_from_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [216, 217, 218, 220, 221, 222, 224, 225], "excluded_lines": [], "executed_branches": [], "missing_branches": [[220, 221], [220, 222]]}, "create_auth_tokens": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [244, 247, 250, 252], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "refresh_access_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [274, 275, 277, 278, 285, 287], "excluded_lines": [], "executed_branches": [], "missing_branches": [[277, 278], [277, 285]]}, "generate_api_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [300, 301, 304, 305, 307], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "validate_api_key_format": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [320, 321, 323, 326, 327, 329, 332, 333, 336, 337, 340, 341, 343], "excluded_lines": [], "executed_branches": [], "missing_branches": [[320, 321], [320, 323], [326, 327], [326, 329], [332, 333], [332, 336], [336, 337], [336, 340], [340, 341], [340, 343]]}, "TokenBlacklist.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [351], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TokenBlacklist.add_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [355], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TokenBlacklist.is_blacklisted": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [359], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TokenBlacklist.remove_expired_tokens": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [364], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "logout_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [378], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "is_token_valid": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [392, 393, 395, 397, 398, 399, 400], "excluded_lines": [], "executed_branches": [], "missing_branches": [[392, 393], [392, 395]]}, "extract_token_from_header": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [416, 417, 419, 420, 421, 423], "excluded_lines": [], "executed_branches": [], "missing_branches": [[416, 417], [416, 419], [420, 421], [420, 423]]}, "": {"executed_lines": [1, 5, 6, 7, 9, 10, 12, 15, 18, 19, 21, 24, 25, 27, 30, 31, 33, 36, 50, 63, 64, 106, 107, 140, 183, 205, 227, 228, 258, 289, 309, 345, 346, 348, 350, 352, 356, 360, 367, 370, 380, 402], "summary": {"covered_lines": 10, "num_statements": 33, "percent_covered": 30.303030303030305, "percent_covered_display": "30.30", "missing_lines": 23, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 11, 13, 16, 22, 28, 34, 37, 51, 141, 184, 206, 259, 290, 310, 349, 353, 357, 361, 368, 371, 381, 403], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"SecurityError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TokenExpiredError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "InvalidTokenError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TokenBlacklist": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [351, 355, 359, 364], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 5, 6, 7, 9, 10, 12, 15, 18, 19, 21, 24, 25, 27, 30, 31, 33, 36, 50, 63, 64, 106, 107, 140, 183, 205, 227, 228, 258, 289, 309, 345, 346, 348, 350, 352, 356, 360, 367, 370, 380, 402], "summary": {"covered_lines": 10, "num_statements": 120, "percent_covered": 6.4935064935064934, "percent_covered_display": "6.49", "missing_lines": 110, "excluded_lines": 0, "num_branches": 34, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 34}, "missing_lines": [8, 11, 13, 16, 22, 28, 34, 37, 48, 51, 61, 80, 81, 83, 88, 96, 97, 100, 104, 120, 121, 123, 127, 134, 138, 141, 156, 158, 163, 164, 167, 168, 169, 171, 172, 174, 176, 177, 178, 179, 180, 181, 184, 197, 198, 200, 201, 203, 206, 216, 217, 218, 220, 221, 222, 224, 225, 244, 247, 250, 252, 259, 274, 275, 277, 278, 285, 287, 290, 300, 301, 304, 305, 307, 310, 320, 321, 323, 326, 327, 329, 332, 333, 336, 337, 340, 341, 343, 349, 353, 357, 361, 368, 371, 378, 381, 392, 393, 395, 397, 398, 399, 400, 403, 416, 417, 419, 420, 421, 423], "excluded_lines": [], "executed_branches": [], "missing_branches": [[80, 81], [80, 83], [96, 97], [96, 100], [120, 121], [120, 123], [163, 164], [163, 167], [168, 169], [168, 171], [171, 172], [171, 174], [200, 201], [200, 203], [220, 221], [220, 222], [277, 278], [277, 285], [320, 321], [320, 323], [326, 327], [326, 329], [332, 333], [332, 336], [336, 337], [336, 340], [340, 341], [340, 343], [392, 393], [392, 395], [416, 417], [416, 419], [420, 421], [420, 423]]}}}, "app\\main.py": {"executed_lines": [1, 7, 8, 10, 11, 14, 22, 31, 34, 35, 44, 45], "summary": {"covered_lines": 11, "num_statements": 13, "percent_covered": 84.61538461538461, "percent_covered_display": "84.62", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [37, 47], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"root": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [37], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "health_check": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [47], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 7, 8, 10, 11, 14, 22, 31, 34, 35, 44, 45], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 7, 8, 10, 11, 14, 22, 31, 34, 35, 44, 45], "summary": {"covered_lines": 11, "num_statements": 13, "percent_covered": 84.61538461538461, "percent_covered_display": "84.62", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [37, 47], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\models\\message.py": {"executed_lines": [1, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 24, 25, 26, 27, 28, 29, 32, 33, 35, 36, 37, 38, 41, 42, 44, 45, 46, 47, 50, 51, 56, 65, 69, 72, 73, 74, 80, 81, 82, 88, 89, 90, 91, 92, 93, 108, 109, 110, 123, 124, 128, 131, 132, 136, 139, 140, 141, 142, 143, 144, 152, 154, 156, 157, 180, 184, 185, 189, 190, 194, 195, 199, 200, 204, 205, 209, 244, 250, 261, 273, 288, 297, 306, 307, 308, 309, 336, 347, 348, 349, 374, 385, 386, 387], "summary": {"covered_lines": 53, "num_statements": 97, "percent_covered": 50.467289719626166, "percent_covered_display": "50.47", "missing_lines": 44, "excluded_lines": 6, "num_branches": 10, "num_partial_branches": 3, "covered_branches": 1, "missing_branches": 9}, "missing_lines": [9, 16, 18, 22, 30, 31, 34, 40, 43, 49, 52, 55, 66, 122, 125, 133, 153, 187, 192, 197, 202, 207, 210, 212, 214, 224, 245, 247, 249, 256, 257, 258, 260, 268, 269, 270, 272, 283, 284, 285, 287, 294, 296, 406], "excluded_lines": [178, 179, 180, 181, 182, 183], "executed_branches": [[20, 21]], "missing_branches": [[20, 25], [244, 245], [244, 247], [256, 257], [256, 258], [268, 269], [268, 270], [283, 284], [283, 285]], "functions": {"Message.__repr__": {"executed_lines": [180], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 5, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [179, 180, 181, 182, 183], "executed_branches": [], "missing_branches": []}, "Message.__str__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [187], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Message.is_from_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [192], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Message.is_from_staff": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [197], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Message.is_system_message": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [202], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Message.has_attachments": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [207], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Message.attachment_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [212], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Message.to_dict": {"executed_lines": [244], "summary": {"covered_lines": 1, "num_statements": 4, "percent_covered": 16.666666666666668, "percent_covered_display": "16.67", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [224, 245, 247], "excluded_lines": [], "executed_branches": [], "missing_branches": [[244, 245], [244, 247]]}, "Message.add_attachment": {"executed_lines": [250], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [256, 257, 258], "excluded_lines": [], "executed_branches": [], "missing_branches": [[256, 257], [256, 258]]}, "Message.update_metadata": {"executed_lines": [261], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [268, 269, 270], "excluded_lines": [], "executed_branches": [], "missing_branches": [[268, 269], [268, 270]]}, "Message.get_metadata": {"executed_lines": [273], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [283, 284, 285], "excluded_lines": [], "executed_branches": [], "missing_branches": [[283, 284], [283, 285]]}, "Message.mark_as_read": {"executed_lines": [288], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [294], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Message.is_read_by": {"executed_lines": [297, 306], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Message.create_user_message": {"executed_lines": [336], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Message.create_staff_message": {"executed_lines": [374], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Message.create_system_message": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [406], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 24, 25, 26, 27, 28, 29, 32, 33, 35, 36, 37, 38, 41, 42, 44, 45, 46, 47, 50, 51, 56, 65, 69, 72, 73, 74, 80, 81, 82, 88, 89, 90, 91, 92, 93, 108, 109, 110, 123, 124, 128, 131, 132, 136, 139, 140, 141, 142, 143, 144, 152, 154, 156, 157, 184, 185, 189, 190, 194, 195, 199, 200, 204, 205, 209, 307, 308, 309, 347, 348, 349, 385, 386, 387], "summary": {"covered_lines": 49, "num_statements": 73, "percent_covered": 66.66666666666667, "percent_covered_display": "66.67", "missing_lines": 24, "excluded_lines": 1, "num_branches": 2, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 1}, "missing_lines": [9, 16, 18, 22, 30, 31, 34, 40, 43, 49, 52, 55, 66, 122, 125, 133, 153, 210, 214, 249, 260, 272, 287, 296], "excluded_lines": [178], "executed_branches": [[20, 21]], "missing_branches": [[20, 25]]}}, "classes": {"MessageType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SenderType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Message": {"executed_lines": [180, 244, 250, 261, 273, 288, 297, 306, 336, 374], "summary": {"covered_lines": 4, "num_statements": 24, "percent_covered": 12.5, "percent_covered_display": "12.50", "missing_lines": 20, "excluded_lines": 5, "num_branches": 8, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [187, 192, 197, 202, 207, 212, 224, 245, 247, 256, 257, 258, 268, 269, 270, 283, 284, 285, 294, 406], "excluded_lines": [179, 180, 181, 182, 183], "executed_branches": [], "missing_branches": [[244, 245], [244, 247], [256, 257], [256, 258], [268, 269], [268, 270], [283, 284], [283, 285]]}, "": {"executed_lines": [1, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 24, 25, 26, 27, 28, 29, 32, 33, 35, 36, 37, 38, 41, 42, 44, 45, 46, 47, 50, 51, 56, 65, 69, 72, 73, 74, 80, 81, 82, 88, 89, 90, 91, 92, 93, 108, 109, 110, 123, 124, 128, 131, 132, 136, 139, 140, 141, 142, 143, 144, 152, 154, 156, 157, 184, 185, 189, 190, 194, 195, 199, 200, 204, 205, 209, 307, 308, 309, 347, 348, 349, 385, 386, 387], "summary": {"covered_lines": 49, "num_statements": 73, "percent_covered": 66.66666666666667, "percent_covered_display": "66.67", "missing_lines": 24, "excluded_lines": 1, "num_branches": 2, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 1}, "missing_lines": [9, 16, 18, 22, 30, 31, 34, 40, 43, 49, 52, 55, 66, 122, 125, 133, 153, 210, 214, 249, 260, 272, 287, 296], "excluded_lines": [178], "executed_branches": [[20, 21]], "missing_branches": [[20, 25]]}}}, "app\\models\\role.py": {"executed_lines": [1, 6, 8, 9, 10, 11, 13, 16, 17, 18, 19, 20, 21, 22, 34, 35, 36, 46, 47, 48, 50, 51, 52, 53, 57, 60, 64, 65, 68, 70, 73, 75, 77, 78, 79, 81, 82, 84, 87, 91, 94, 98, 99, 100, 102, 103, 104, 105, 108, 134], "summary": {"covered_lines": 27, "num_statements": 46, "percent_covered": 51.851851851851855, "percent_covered_display": "51.85", "missing_lines": 19, "excluded_lines": 4, "num_branches": 8, "num_partial_branches": 3, "covered_branches": 1, "missing_branches": 7}, "missing_lines": [7, 12, 15, 56, 63, 66, 67, 83, 90, 97, 101, 119, 130, 131, 133, 139, 141, 143, 150], "excluded_lines": [74, 75, 116, 117], "executed_branches": [[17, 18]], "missing_branches": [[17, 22], [130, 131], [130, 133], [133, 134], [133, 141], [134, 133], [134, 139]], "functions": {"Permission.__repr__": {"executed_lines": [75], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 1, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [75], "executed_branches": [], "missing_branches": []}, "Role.__repr__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 1, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [117], "executed_branches": [], "missing_branches": []}, "Role.has_permission": {"executed_lines": [134], "summary": {"covered_lines": 1, "num_statements": 6, "percent_covered": 8.333333333333334, "percent_covered_display": "8.33", "missing_lines": 5, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [130, 131, 133, 139, 141], "excluded_lines": [], "executed_branches": [], "missing_branches": [[130, 131], [130, 133], [133, 134], [133, 141], [134, 133], [134, 139]]}, "Role.get_permissions_list": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [150], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 8, 9, 10, 11, 13, 16, 17, 18, 19, 20, 21, 22, 34, 35, 36, 46, 47, 48, 50, 51, 52, 53, 57, 60, 64, 65, 68, 70, 73, 77, 78, 79, 81, 82, 84, 87, 91, 94, 98, 99, 100, 102, 103, 104, 105, 108], "summary": {"covered_lines": 26, "num_statements": 39, "percent_covered": 65.85365853658537, "percent_covered_display": "65.85", "missing_lines": 13, "excluded_lines": 2, "num_branches": 2, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 1}, "missing_lines": [7, 12, 15, 56, 63, 66, 67, 83, 90, 97, 101, 119, 143], "excluded_lines": [74, 116], "executed_branches": [[17, 18]], "missing_branches": [[17, 22]]}}, "classes": {"Permission": {"executed_lines": [75], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 1, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [75], "executed_branches": [], "missing_branches": []}, "Role": {"executed_lines": [134], "summary": {"covered_lines": 1, "num_statements": 7, "percent_covered": 7.6923076923076925, "percent_covered_display": "7.69", "missing_lines": 6, "excluded_lines": 1, "num_branches": 6, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [130, 131, 133, 139, 141, 150], "excluded_lines": [117], "executed_branches": [], "missing_branches": [[130, 131], [130, 133], [133, 134], [133, 141], [134, 133], [134, 139]]}, "": {"executed_lines": [1, 6, 8, 9, 10, 11, 13, 16, 17, 18, 19, 20, 21, 22, 34, 35, 36, 46, 47, 48, 50, 51, 52, 53, 57, 60, 64, 65, 68, 70, 73, 77, 78, 79, 81, 82, 84, 87, 91, 94, 98, 99, 100, 102, 103, 104, 105, 108], "summary": {"covered_lines": 26, "num_statements": 39, "percent_covered": 65.85365853658537, "percent_covered_display": "65.85", "missing_lines": 13, "excluded_lines": 2, "num_branches": 2, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 1}, "missing_lines": [7, 12, 15, 56, 63, 66, 67, 83, 90, 97, 101, 119, 143], "excluded_lines": [74, 116], "executed_branches": [[17, 18]], "missing_branches": [[17, 22]]}}}, "app\\models\\session.py": {"executed_lines": [1, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 24, 25, 26, 27, 30, 31, 33, 34, 35, 38, 39, 45, 57, 58, 61, 63, 69, 71, 77, 78, 79, 80, 81, 82, 97, 102, 103, 104, 115, 117, 123, 127, 128, 131, 133, 139, 140, 148, 151, 152, 156, 157, 160, 162, 185, 186, 190, 191, 195, 196, 209, 216, 227, 237, 247, 251, 287, 294, 306, 321, 330, 336, 338, 339, 340, 341, 342], "summary": {"covered_lines": 40, "num_statements": 101, "percent_covered": 35.8974358974359, "percent_covered_display": "35.90", "missing_lines": 61, "excluded_lines": 3, "num_branches": 16, "num_partial_branches": 2, "covered_branches": 2, "missing_branches": 14}, "missing_lines": [9, 16, 18, 22, 29, 32, 36, 40, 41, 44, 55, 125, 155, 187, 189, 192, 194, 197, 199, 201, 202, 204, 206, 208, 210, 211, 212, 213, 215, 222, 223, 224, 226, 233, 234, 236, 243, 244, 245, 249, 250, 253, 255, 257, 267, 288, 289, 291, 293, 301, 302, 303, 305, 316, 317, 318, 320, 327, 329, 337, 363], "excluded_lines": [183, 184, 185], "executed_branches": [[20, 21], [336, 339]], "missing_branches": [[20, 26], [204, 206], [204, 208], [210, 211], [210, 213], [223, -215], [223, 224], [288, 289], [288, 291], [301, 302], [301, 303], [316, 317], [316, 318], [336, 337]], "functions": {"Session.__repr__": {"executed_lines": [185], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 2, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [184, 185], "executed_branches": [], "missing_branches": []}, "Session.__str__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [189], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Session.is_active": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [194], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Session.is_closed": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [199], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Session.duration_minutes": {"executed_lines": [209], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [204, 206, 208, 210, 211, 212, 213], "excluded_lines": [], "executed_branches": [], "missing_branches": [[204, 206], [204, 208], [210, 211], [210, 213]]}, "Session.assign_staff": {"executed_lines": [216], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [222, 223, 224], "excluded_lines": [], "executed_branches": [], "missing_branches": [[223, -215], [223, 224]]}, "Session.transfer_to_staff": {"executed_lines": [227], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [233, 234], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Session.close_session": {"executed_lines": [237], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [243, 244, 245], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Session.timeout_session": {"executed_lines": [251], "summary": {"covered_lines": 1, "num_statements": 3, "percent_covered": 33.333333333333336, "percent_covered_display": "33.33", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [249, 250], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Session.update_last_message_time": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [255], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Session.to_dict": {"executed_lines": [287], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [267, 288, 289, 291], "excluded_lines": [], "executed_branches": [], "missing_branches": [[288, 289], [288, 291]]}, "Session.update_metadata": {"executed_lines": [294], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [301, 302, 303], "excluded_lines": [], "executed_branches": [], "missing_branches": [[301, 302], [301, 303]]}, "Session.get_metadata": {"executed_lines": [306], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [316, 317, 318], "excluded_lines": [], "executed_branches": [], "missing_branches": [[316, 317], [316, 318]]}, "Session.update_context_summary": {"executed_lines": [321], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [327], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Session.set_priority": {"executed_lines": [330, 336, 338, 339], "summary": {"covered_lines": 2, "num_statements": 3, "percent_covered": 60.0, "percent_covered_display": "60.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 1}, "missing_lines": [337], "excluded_lines": [], "executed_branches": [[336, 339]], "missing_branches": [[336, 337]]}, "Session.create_for_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [363], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 24, 25, 26, 27, 30, 31, 33, 34, 35, 38, 39, 45, 57, 58, 61, 63, 69, 71, 77, 78, 79, 80, 81, 82, 97, 102, 103, 104, 115, 117, 123, 127, 128, 131, 133, 139, 140, 148, 151, 152, 156, 157, 160, 162, 186, 190, 191, 195, 196, 247, 340, 341, 342], "summary": {"covered_lines": 37, "num_statements": 64, "percent_covered": 57.57575757575758, "percent_covered_display": "57.58", "missing_lines": 27, "excluded_lines": 1, "num_branches": 2, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 1}, "missing_lines": [9, 16, 18, 22, 29, 32, 36, 40, 41, 44, 55, 125, 155, 187, 192, 197, 201, 202, 215, 226, 236, 253, 257, 293, 305, 320, 329], "excluded_lines": [183], "executed_branches": [[20, 21]], "missing_branches": [[20, 26]]}}, "classes": {"SessionStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ChannelType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Session": {"executed_lines": [185, 209, 216, 227, 237, 251, 287, 294, 306, 321, 330, 336, 338, 339], "summary": {"covered_lines": 3, "num_statements": 37, "percent_covered": 7.8431372549019605, "percent_covered_display": "7.84", "missing_lines": 34, "excluded_lines": 2, "num_branches": 14, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 13}, "missing_lines": [189, 194, 199, 204, 206, 208, 210, 211, 212, 213, 222, 223, 224, 233, 234, 243, 244, 245, 249, 250, 255, 267, 288, 289, 291, 301, 302, 303, 316, 317, 318, 327, 337, 363], "excluded_lines": [184, 185], "executed_branches": [[336, 339]], "missing_branches": [[204, 206], [204, 208], [210, 211], [210, 213], [223, -215], [223, 224], [288, 289], [288, 291], [301, 302], [301, 303], [316, 317], [316, 318], [336, 337]]}, "": {"executed_lines": [1, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 24, 25, 26, 27, 30, 31, 33, 34, 35, 38, 39, 45, 57, 58, 61, 63, 69, 71, 77, 78, 79, 80, 81, 82, 97, 102, 103, 104, 115, 117, 123, 127, 128, 131, 133, 139, 140, 148, 151, 152, 156, 157, 160, 162, 186, 190, 191, 195, 196, 247, 340, 341, 342], "summary": {"covered_lines": 37, "num_statements": 64, "percent_covered": 57.57575757575758, "percent_covered_display": "57.58", "missing_lines": 27, "excluded_lines": 1, "num_branches": 2, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 1}, "missing_lines": [9, 16, 18, 22, 29, 32, 36, 40, 41, 44, 55, 125, 155, 187, 192, 197, 201, 202, 215, 226, 236, 253, 257, 293, 305, 320, 329], "excluded_lines": [183], "executed_branches": [[20, 21]], "missing_branches": [[20, 26]]}}}, "app\\models\\tenant.py": {"executed_lines": [1, 5, 6, 7, 9, 10, 11, 12, 13, 14, 16, 19, 20, 22, 23, 24, 27, 28, 30, 31, 32, 35, 36, 42, 52, 53, 57, 58, 59, 60, 61, 62, 63, 74, 75, 85, 86, 87, 88, 89, 91, 97, 98, 107, 110, 113, 117, 118, 142, 150, 159, 163, 167, 168, 172, 173, 177, 178, 183, 187, 204, 214, 222, 229, 233, 237, 245], "summary": {"covered_lines": 32, "num_statements": 94, "percent_covered": 28.448275862068964, "percent_covered_display": "28.45", "missing_lines": 62, "excluded_lines": 3, "num_branches": 22, "num_partial_branches": 5, "covered_branches": 1, "missing_branches": 21}, "missing_lines": [8, 15, 17, 21, 25, 29, 33, 37, 38, 41, 50, 131, 134, 135, 138, 139, 140, 141, 143, 144, 145, 147, 149, 151, 153, 155, 157, 161, 162, 164, 170, 174, 175, 179, 180, 182, 184, 185, 190, 192, 194, 215, 216, 218, 220, 221, 228, 230, 232, 234, 236, 238, 240, 242, 244, 252, 253, 254, 256, 267, 268, 269], "excluded_lines": [166, 167, 168], "executed_branches": [[19, 20]], "missing_branches": [[19, 25], [134, 135], [134, 138], [138, 139], [138, 140], [140, 141], [140, 142], [142, 143], [142, 144], [144, 145], [144, 147], [151, 153], [151, 155], [159, 161], [159, 164], [215, 216], [215, 218], [252, 253], [252, 254], [267, 268], [267, 269]], "functions": {"Tenant.__init__": {"executed_lines": [142], "summary": {"covered_lines": 1, "num_statements": 11, "percent_covered": 4.761904761904762, "percent_covered_display": "4.76", "missing_lines": 10, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [134, 135, 138, 139, 140, 141, 143, 144, 145, 147], "excluded_lines": [], "executed_branches": [], "missing_branches": [[134, 135], [134, 138], [138, 139], [138, 140], [140, 141], [140, 142], [142, 143], [142, 144], [144, 145], [144, 147]]}, "Tenant.__setattr__": {"executed_lines": [150], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [151, 153, 155], "excluded_lines": [], "executed_branches": [], "missing_branches": [[151, 153], [151, 155]]}, "Tenant.__getattribute__": {"executed_lines": [159, 163], "summary": {"covered_lines": 1, "num_statements": 4, "percent_covered": 16.666666666666668, "percent_covered_display": "16.67", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [161, 162, 164], "excluded_lines": [], "executed_branches": [], "missing_branches": [[159, 161], [159, 164]]}, "Tenant.__repr__": {"executed_lines": [167, 168], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 2, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [167, 168], "executed_branches": [], "missing_branches": []}, "Tenant.__str__": {"executed_lines": [172], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Tenant.is_active": {"executed_lines": [177], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Tenant.display_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [182], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Tenant.tenant_metadata": {"executed_lines": [187], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Tenant.get_metadata_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [192], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Tenant.to_dict": {"executed_lines": [204, 214], "summary": {"covered_lines": 1, "num_statements": 4, "percent_covered": 16.666666666666668, "percent_covered_display": "16.67", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [215, 216, 218], "excluded_lines": [], "executed_branches": [], "missing_branches": [[215, 216], [215, 218]]}, "Tenant.generate_api_key": {"executed_lines": [222, 229], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [228, 230], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Tenant.activate": {"executed_lines": [233], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [234], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Tenant.suspend": {"executed_lines": [237], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [238], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Tenant.deactivate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [242], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Tenant.update_metadata": {"executed_lines": [245], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [252, 253, 254], "excluded_lines": [], "executed_branches": [], "missing_branches": [[252, 253], [252, 254]]}, "Tenant.get_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [267, 268, 269], "excluded_lines": [], "executed_branches": [], "missing_branches": [[267, 268], [267, 269]]}, "": {"executed_lines": [1, 5, 6, 7, 9, 10, 11, 12, 13, 14, 16, 19, 20, 22, 23, 24, 27, 28, 30, 31, 32, 35, 36, 42, 52, 53, 57, 58, 59, 60, 61, 62, 63, 74, 75, 85, 86, 87, 88, 89, 91, 97, 98, 107, 110, 113, 117, 118, 173, 178, 183], "summary": {"covered_lines": 26, "num_statements": 56, "percent_covered": 46.55172413793103, "percent_covered_display": "46.55", "missing_lines": 30, "excluded_lines": 1, "num_branches": 2, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 1}, "missing_lines": [8, 15, 17, 21, 25, 29, 33, 37, 38, 41, 50, 131, 149, 157, 170, 174, 175, 179, 180, 184, 185, 190, 194, 220, 221, 232, 236, 240, 244, 256], "excluded_lines": [166], "executed_branches": [[19, 20]], "missing_branches": [[19, 25]]}}, "classes": {"TenantStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantPlan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "Tenant": {"executed_lines": [142, 150, 159, 163, 167, 168, 172, 177, 187, 204, 214, 222, 229, 233, 237, 245], "summary": {"covered_lines": 6, "num_statements": 38, "percent_covered": 10.344827586206897, "percent_covered_display": "10.34", "missing_lines": 32, "excluded_lines": 2, "num_branches": 20, "num_partial_branches": 4, "covered_branches": 0, "missing_branches": 20}, "missing_lines": [134, 135, 138, 139, 140, 141, 143, 144, 145, 147, 151, 153, 155, 161, 162, 164, 182, 192, 215, 216, 218, 228, 230, 234, 238, 242, 252, 253, 254, 267, 268, 269], "excluded_lines": [167, 168], "executed_branches": [], "missing_branches": [[134, 135], [134, 138], [138, 139], [138, 140], [140, 141], [140, 142], [142, 143], [142, 144], [144, 145], [144, 147], [151, 153], [151, 155], [159, 161], [159, 164], [215, 216], [215, 218], [252, 253], [252, 254], [267, 268], [267, 269]]}, "": {"executed_lines": [1, 5, 6, 7, 9, 10, 11, 12, 13, 14, 16, 19, 20, 22, 23, 24, 27, 28, 30, 31, 32, 35, 36, 42, 52, 53, 57, 58, 59, 60, 61, 62, 63, 74, 75, 85, 86, 87, 88, 89, 91, 97, 98, 107, 110, 113, 117, 118, 173, 178, 183], "summary": {"covered_lines": 26, "num_statements": 56, "percent_covered": 46.55172413793103, "percent_covered_display": "46.55", "missing_lines": 30, "excluded_lines": 1, "num_branches": 2, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 1}, "missing_lines": [8, 15, 17, 21, 25, 29, 33, 37, 38, 41, 50, 131, 149, 157, 170, 174, 175, 179, 180, 184, 185, 190, 194, 220, 221, 232, 236, 240, 244, 256], "excluded_lines": [166], "executed_branches": [[19, 20]], "missing_branches": [[19, 25]]}}}, "app\\models\\user.py": {"executed_lines": [1, 5, 7, 8, 9, 10, 12, 15, 16, 22, 35, 36, 39, 41, 47, 50, 51, 55, 56, 59, 61, 67, 68, 78, 81, 82, 83, 84, 91, 92, 93, 116, 117, 121, 122, 126, 133, 134, 141, 170, 196, 203, 215, 240, 247, 261, 291, 309], "summary": {"covered_lines": 18, "num_statements": 91, "percent_covered": 15.447154471544716, "percent_covered_display": "15.45", "missing_lines": 73, "excluded_lines": 3, "num_branches": 32, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 31}, "missing_lines": [6, 11, 13, 17, 18, 21, 33, 49, 53, 77, 118, 120, 123, 125, 127, 128, 130, 132, 135, 136, 137, 139, 140, 151, 153, 154, 167, 168, 171, 172, 174, 176, 186, 197, 198, 200, 202, 210, 211, 212, 214, 225, 226, 227, 229, 237, 238, 239, 242, 243, 244, 246, 257, 260, 271, 272, 273, 274, 276, 283, 284, 286, 287, 288, 289, 290, 298, 300, 302, 323, 328, 331, 332], "excluded_lines": [114, 115, 116], "executed_branches": [[15, 16]], "missing_branches": [[15, 21], [135, 136], [135, 137], [167, 168], [167, 170], [171, 172], [171, 174], [197, 198], [197, 200], [210, 211], [210, 212], [225, 226], [225, 227], [237, 238], [237, 242], [242, 243], [242, 244], [271, 272], [271, 274], [272, 271], [272, 273], [286, 287], [286, 300], [287, 286], [287, 288], [288, 286], [288, 289], [290, 288], [290, 291], [331, -323], [331, 332]], "functions": {"User.__repr__": {"executed_lines": [116], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 2, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [115, 116], "executed_branches": [], "missing_branches": []}, "User.__str__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [120], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "User.display_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [125], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "User.platform_user_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [130], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "User.__init__": {"executed_lines": [133, 134], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [135, 136, 137], "excluded_lines": [], "executed_branches": [], "missing_branches": [[135, 136], [135, 137]]}, "User.create_user_id": {"executed_lines": [141], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [151], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "User.parse_user_id": {"executed_lines": [170], "summary": {"covered_lines": 1, "num_statements": 6, "percent_covered": 10.0, "percent_covered_display": "10.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [167, 168, 171, 172, 174], "excluded_lines": [], "executed_branches": [], "missing_branches": [[167, 168], [167, 170], [171, 172], [171, 174]]}, "User.to_dict": {"executed_lines": [196], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [186, 197, 198, 200], "excluded_lines": [], "executed_branches": [], "missing_branches": [[197, 198], [197, 200]]}, "User.update_metadata": {"executed_lines": [203], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [210, 211, 212], "excluded_lines": [], "executed_branches": [], "missing_branches": [[210, 211], [210, 212]]}, "User.get_metadata": {"executed_lines": [215], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [225, 226, 227], "excluded_lines": [], "executed_branches": [], "missing_branches": [[225, 226], [225, 227]]}, "User.update_nickname": {"executed_lines": [240], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [237, 238, 239, 242, 243, 244], "excluded_lines": [], "executed_branches": [], "missing_branches": [[237, 238], [237, 242], [242, 243], [242, 244]]}, "User.is_same_platform_user": {"executed_lines": [247], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [257], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "User.has_permission": {"executed_lines": [261], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [271, 272, 273, 274], "excluded_lines": [], "executed_branches": [], "missing_branches": [[271, 272], [271, 274], [272, 271], [272, 273]]}, "User.get_all_permissions": {"executed_lines": [291], "summary": {"covered_lines": 1, "num_statements": 10, "percent_covered": 5.555555555555555, "percent_covered_display": "5.56", "missing_lines": 9, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [283, 284, 286, 287, 288, 289, 290, 298, 300], "excluded_lines": [], "executed_branches": [], "missing_branches": [[286, 287], [286, 300], [287, 286], [287, 288], [288, 286], [288, 289], [290, 288], [290, 291]]}, "User.get_roles_list": {"executed_lines": [309], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "update_tenant_relationships": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [328, 331, 332], "excluded_lines": [], "executed_branches": [], "missing_branches": [[331, -323], [331, 332]]}, "": {"executed_lines": [1, 5, 7, 8, 9, 10, 12, 15, 16, 22, 35, 36, 39, 41, 47, 50, 51, 55, 56, 59, 61, 67, 68, 78, 81, 82, 83, 84, 91, 92, 93, 117, 121, 122, 126], "summary": {"covered_lines": 15, "num_statements": 43, "percent_covered": 35.55555555555556, "percent_covered_display": "35.56", "missing_lines": 28, "excluded_lines": 1, "num_branches": 2, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 1}, "missing_lines": [6, 11, 13, 17, 18, 21, 33, 49, 53, 77, 118, 123, 127, 128, 132, 139, 140, 153, 154, 176, 202, 214, 229, 246, 260, 276, 302, 323], "excluded_lines": [114], "executed_branches": [[15, 16]], "missing_branches": [[15, 21]]}}, "classes": {"User": {"executed_lines": [116, 133, 134, 141, 170, 196, 203, 215, 240, 247, 261, 291, 309], "summary": {"covered_lines": 3, "num_statements": 45, "percent_covered": 4.109589041095891, "percent_covered_display": "4.11", "missing_lines": 42, "excluded_lines": 2, "num_branches": 28, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 28}, "missing_lines": [120, 125, 130, 135, 136, 137, 151, 167, 168, 171, 172, 174, 186, 197, 198, 200, 210, 211, 212, 225, 226, 227, 237, 238, 239, 242, 243, 244, 257, 271, 272, 273, 274, 283, 284, 286, 287, 288, 289, 290, 298, 300], "excluded_lines": [115, 116], "executed_branches": [], "missing_branches": [[135, 136], [135, 137], [167, 168], [167, 170], [171, 172], [171, 174], [197, 198], [197, 200], [210, 211], [210, 212], [225, 226], [225, 227], [237, 238], [237, 242], [242, 243], [242, 244], [271, 272], [271, 274], [272, 271], [272, 273], [286, 287], [286, 300], [287, 286], [287, 288], [288, 286], [288, 289], [290, 288], [290, 291]]}, "": {"executed_lines": [1, 5, 7, 8, 9, 10, 12, 15, 16, 22, 35, 36, 39, 41, 47, 50, 51, 55, 56, 59, 61, 67, 68, 78, 81, 82, 83, 84, 91, 92, 93, 117, 121, 122, 126], "summary": {"covered_lines": 15, "num_statements": 46, "percent_covered": 32.0, "percent_covered_display": "32.00", "missing_lines": 31, "excluded_lines": 1, "num_branches": 4, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 3}, "missing_lines": [6, 11, 13, 17, 18, 21, 33, 49, 53, 77, 118, 123, 127, 128, 132, 139, 140, 153, 154, 176, 202, 214, 229, 246, 260, 276, 302, 323, 328, 331, 332], "excluded_lines": [114], "executed_branches": [[15, 16]], "missing_branches": [[15, 21], [331, -323], [331, 332]]}}}, "app\\schemas\\analytics.py": {"executed_lines": [1, 10, 11, 12, 14, 17, 18, 20, 21, 22, 23, 24, 27, 28, 30, 31, 34, 35, 37, 38, 39, 40, 43, 44, 46, 47, 48, 49, 50, 53, 54, 56, 57, 58, 59, 60, 63, 64, 66, 67, 68, 69, 70, 73, 74, 76, 77, 78, 79, 80, 81, 82, 85, 86, 88, 89, 90, 91, 92, 95, 96, 98, 99, 100, 103, 104, 105, 106, 109, 110, 113, 114, 115, 118, 119, 120, 123, 124, 126, 127, 128, 129, 130, 133, 134, 136, 137, 138, 139, 140, 141], "summary": {"covered_lines": 62, "num_statements": 79, "percent_covered": 78.48101265822785, "percent_covered_display": "78.48", "missing_lines": 17, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [13, 15, 25, 32, 41, 51, 61, 71, 83, 93, 101, 107, 111, 116, 121, 131, 142], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 10, 11, 12, 14, 17, 18, 20, 21, 22, 23, 24, 27, 28, 30, 31, 34, 35, 37, 38, 39, 40, 43, 44, 46, 47, 48, 49, 50, 53, 54, 56, 57, 58, 59, 60, 63, 64, 66, 67, 68, 69, 70, 73, 74, 76, 77, 78, 79, 80, 81, 82, 85, 86, 88, 89, 90, 91, 92, 95, 96, 98, 99, 100, 103, 104, 105, 106, 109, 110, 113, 114, 115, 118, 119, 120, 123, 124, 126, 127, 128, 129, 130, 133, 134, 136, 137, 138, 139, 140, 141], "summary": {"covered_lines": 62, "num_statements": 79, "percent_covered": 78.48101265822785, "percent_covered_display": "78.48", "missing_lines": 17, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [13, 15, 25, 32, 41, 51, 61, 71, 83, 93, 101, 107, 111, 116, 121, 131, 142], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"AnalyticsFilter": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TimeSeriesData": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionStatsResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageStatsResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "RealtimeMetrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TrendAnalysis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "CustomReportRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "CustomReportResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DashboardOverview": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ExportRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ExportResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 10, 11, 12, 14, 17, 18, 20, 21, 22, 23, 24, 27, 28, 30, 31, 34, 35, 37, 38, 39, 40, 43, 44, 46, 47, 48, 49, 50, 53, 54, 56, 57, 58, 59, 60, 63, 64, 66, 67, 68, 69, 70, 73, 74, 76, 77, 78, 79, 80, 81, 82, 85, 86, 88, 89, 90, 91, 92, 95, 96, 98, 99, 100, 103, 104, 105, 106, 109, 110, 113, 114, 115, 118, 119, 120, 123, 124, 126, 127, 128, 129, 130, 133, 134, 136, 137, 138, 139, 140, 141], "summary": {"covered_lines": 62, "num_statements": 79, "percent_covered": 78.48101265822785, "percent_covered_display": "78.48", "missing_lines": 17, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [13, 15, 25, 32, 41, 51, 61, 71, 83, 93, 101, 107, 111, 116, 121, 131, 142], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\schemas\\auth.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 109, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 109, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [6, 7, 8, 10, 13, 16, 17, 18, 20, 31, 34, 35, 36, 37, 38, 39, 41, 55, 58, 59, 60, 63, 66, 68, 69, 71, 72, 73, 75, 88, 91, 92, 93, 95, 106, 109, 111, 118, 121, 122, 123, 125, 136, 139, 140, 141, 145, 146, 148, 149, 150, 152, 163, 166, 168, 173, 176, 177, 178, 182, 183, 185, 186, 187, 189, 200, 203, 204, 205, 206, 207, 208, 209, 211, 227, 230, 232, 237, 240, 241, 242, 243, 245, 257, 260, 261, 262, 263, 264, 265, 266, 267, 268, 270, 288, 291, 292, 293, 294, 295, 296, 297, 298, 299, 301, 319, 322, 323, 325], "excluded_lines": [], "executed_branches": [], "missing_branches": [[71, 72], [71, 73], [148, 149], [148, 150], [185, 186], [185, 187]], "functions": {"RegisterRequest.passwords_match": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [71, 72, 73], "excluded_lines": [], "executed_branches": [], "missing_branches": [[71, 72], [71, 73]]}, "ChangePasswordRequest.passwords_match": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [148, 149, 150], "excluded_lines": [], "executed_branches": [], "missing_branches": [[148, 149], [148, 150]]}, "ResetPasswordConfirm.passwords_match": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [185, 186, 187], "excluded_lines": [], "executed_branches": [], "missing_branches": [[185, 186], [185, 187]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 100, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 100, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [6, 7, 8, 10, 13, 16, 17, 18, 20, 31, 34, 35, 36, 37, 38, 39, 41, 55, 58, 59, 60, 63, 66, 68, 69, 75, 88, 91, 92, 93, 95, 106, 109, 111, 118, 121, 122, 123, 125, 136, 139, 140, 141, 145, 146, 152, 163, 166, 168, 173, 176, 177, 178, 182, 183, 189, 200, 203, 204, 205, 206, 207, 208, 209, 211, 227, 230, 232, 237, 240, 241, 242, 243, 245, 257, 260, 261, 262, 263, 264, 265, 266, 267, 268, 270, 288, 291, 292, 293, 294, 295, 296, 297, 298, 299, 301, 319, 322, 323, 325], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"LoginRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "LoginResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "RegisterRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [71, 72, 73], "excluded_lines": [], "executed_branches": [], "missing_branches": [[71, 72], [71, 73]]}, "RegisterResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "RefreshTokenRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "RefreshTokenResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ChangePasswordRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [148, 149, 150], "excluded_lines": [], "executed_branches": [], "missing_branches": [[148, 149], [148, 150]]}, "ResetPasswordRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ResetPasswordConfirm": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [185, 186, 187], "excluded_lines": [], "executed_branches": [], "missing_branches": [[185, 186], [185, 187]]}, "TokenInfo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "LogoutRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ApiKeyCreateRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ApiKeyResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UserProfile": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AuthError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 100, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 100, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [6, 7, 8, 10, 13, 16, 17, 18, 20, 31, 34, 35, 36, 37, 38, 39, 41, 55, 58, 59, 60, 63, 66, 68, 69, 75, 88, 91, 92, 93, 95, 106, 109, 111, 118, 121, 122, 123, 125, 136, 139, 140, 141, 145, 146, 152, 163, 166, 168, 173, 176, 177, 178, 182, 183, 189, 200, 203, 204, 205, 206, 207, 208, 209, 211, 227, 230, 232, 237, 240, 241, 242, 243, 245, 257, 260, 261, 262, 263, 264, 265, 266, 267, 268, 270, 288, 291, 292, 293, 294, 295, 296, 297, 298, 299, 301, 319, 322, 323, 325], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\schemas\\common.py": {"executed_lines": [1, 6, 8, 11, 14, 15, 21, 22, 23, 25, 26, 34, 35, 41, 42, 43, 44, 45, 46, 47, 48, 50, 57, 74, 81, 82, 83, 84, 85, 98, 104, 105, 106, 115, 119, 122, 126, 127, 129, 130, 134, 138, 141, 144, 145, 147], "summary": {"covered_lines": 27, "num_statements": 41, "percent_covered": 65.85365853658537, "percent_covered_display": "65.85", "missing_lines": 14, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 12, 24, 52, 54, 55, 73, 80, 97, 102, 103, 118, 123, 124], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"PaginatedResponse.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [52, 54, 55], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 8, 11, 14, 15, 21, 22, 23, 25, 26, 34, 35, 41, 42, 43, 44, 45, 46, 47, 48, 50, 57, 74, 81, 82, 83, 84, 85, 98, 104, 105, 106, 115, 119, 122, 126, 127, 129, 130, 134, 138], "summary": {"covered_lines": 27, "num_statements": 38, "percent_covered": 71.05263157894737, "percent_covered_display": "71.05", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 12, 24, 73, 80, 97, 102, 103, 118, 123, 124], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"StandardResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "PaginatedResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [52, 54, 55], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ErrorResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "HealthCheck": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "PaginationParams": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SearchParams": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 8, 11, 14, 15, 21, 22, 23, 25, 26, 34, 35, 41, 42, 43, 44, 45, 46, 47, 48, 50, 57, 74, 81, 82, 83, 84, 85, 98, 104, 105, 106, 115, 119, 122, 126, 127, 129, 130, 134, 138], "summary": {"covered_lines": 27, "num_statements": 38, "percent_covered": 71.05263157894737, "percent_covered_display": "71.05", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 12, 24, 73, 80, 97, 102, 103, 118, 123, 124], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\schemas\\message.py": {"executed_lines": [1, 7, 8, 9, 11, 13, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 51, 52, 53, 54, 57, 58, 59, 61, 62, 63, 65, 66, 67, 69, 70, 71, 73, 74, 75, 77, 78, 81, 82, 84, 87, 88, 89, 90, 91, 94, 95, 96, 101, 104, 105, 107, 110, 111, 112, 113, 114, 115, 116, 117, 118, 121, 122, 124, 125, 128, 129, 132, 133, 135, 138, 139, 142, 143, 144, 145, 148, 149, 151, 152, 153, 156, 157, 158, 159, 160, 163, 164, 166, 169, 172, 173, 175, 176, 177, 178, 183, 184, 186, 187, 188, 189, 190, 191, 192, 195, 196, 198, 199, 200, 201, 202, 205, 206, 208, 209, 210, 211, 212, 213, 214], "summary": {"covered_lines": 123, "num_statements": 123, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"MessageRead.is_from_user": {"executed_lines": [59], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageRead.is_from_staff": {"executed_lines": [63], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageRead.is_system_message": {"executed_lines": [67], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageRead.has_attachments": {"executed_lines": [71], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageRead.attachment_count": {"executed_lines": [75], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 7, 8, 9, 11, 13, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 51, 52, 53, 54, 57, 58, 61, 62, 65, 66, 69, 70, 73, 74, 77, 78, 81, 82, 84, 87, 88, 89, 90, 91, 94, 95, 96, 101, 104, 105, 107, 110, 111, 112, 113, 114, 115, 116, 117, 118, 121, 122, 124, 125, 128, 129, 132, 133, 135, 138, 139, 142, 143, 144, 145, 148, 149, 151, 152, 153, 156, 157, 158, 159, 160, 163, 164, 166, 169, 172, 173, 175, 176, 177, 178, 183, 184, 186, 187, 188, 189, 190, 191, 192, 195, 196, 198, 199, 200, 201, 202, 205, 206, 208, 209, 210, 211, 212, 213, 214], "summary": {"covered_lines": 118, "num_statements": 118, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"MessageCreate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageRead": {"executed_lines": [59, 63, 67, 71, 75], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageRead.Config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "IncomingMessageData": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageSearchParams": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageStatusUpdate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageUpdate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageStatistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageBatchCreate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageBatchResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageAttachment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageThread": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageExport": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 7, 8, 9, 11, 13, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 51, 52, 53, 54, 57, 58, 61, 62, 65, 66, 69, 70, 73, 74, 77, 78, 81, 82, 84, 87, 88, 89, 90, 91, 94, 95, 96, 101, 104, 105, 107, 110, 111, 112, 113, 114, 115, 116, 117, 118, 121, 122, 124, 125, 128, 129, 132, 133, 135, 138, 139, 142, 143, 144, 145, 148, 149, 151, 152, 153, 156, 157, 158, 159, 160, 163, 164, 166, 169, 172, 173, 175, 176, 177, 178, 183, 184, 186, 187, 188, 189, 190, 191, 192, 195, 196, 198, 199, 200, 201, 202, 205, 206, 208, 209, 210, 211, 212, 213, 214], "summary": {"covered_lines": 118, "num_statements": 118, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\schemas\\session.py": {"executed_lines": [1, 5, 6, 7, 9, 11, 14, 15, 17, 18, 19, 22, 23, 27, 28, 30, 31, 32, 33, 34, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 53, 54, 56, 57, 58, 59, 63, 64, 66, 67, 68, 73, 74, 76, 77, 78, 79, 80, 81], "summary": {"covered_lines": 34, "num_statements": 42, "percent_covered": 80.95238095238095, "percent_covered_display": "80.95", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 10, 12, 35, 49, 51, 71, 82], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 5, 6, 7, 9, 11, 14, 15, 17, 18, 19, 22, 23, 27, 28, 30, 31, 32, 33, 34, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 53, 54, 56, 57, 58, 59, 63, 64, 66, 67, 68, 73, 74, 76, 77, 78, 79, 80, 81], "summary": {"covered_lines": 34, "num_statements": 42, "percent_covered": 80.95238095238095, "percent_covered_display": "80.95", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 10, 12, 35, 49, 51, 71, 82], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"SessionBase": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionCreate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionRead": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionUpdate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionStatusUpdate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "IncomingSessionData": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 5, 6, 7, 9, 11, 14, 15, 17, 18, 19, 22, 23, 27, 28, 30, 31, 32, 33, 34, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 53, 54, 56, 57, 58, 59, 63, 64, 66, 67, 68, 73, 74, 76, 77, 78, 79, 80, 81], "summary": {"covered_lines": 34, "num_statements": 42, "percent_covered": 80.95238095238095, "percent_covered_display": "80.95", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 10, 12, 35, 49, 51, 71, 82], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\schemas\\tenant.py": {"executed_lines": [1, 5, 6, 7, 9, 11, 14, 15, 17, 18, 25, 28, 29, 33, 34, 36, 37, 48, 49, 51, 52, 55, 56, 57, 59, 60, 70, 71, 73, 74, 75, 76, 79, 80, 82, 83, 101, 102, 104, 105, 109, 110, 112, 113, 114, 115, 116, 118, 119, 144, 145, 147, 148, 150, 151, 157, 158, 160, 161, 162, 163, 164, 165, 167, 168, 181, 182, 184, 186, 189, 190, 192, 193, 195, 196], "summary": {"covered_lines": 43, "num_statements": 55, "percent_covered": 78.18181818181819, "percent_covered_display": "78.18", "missing_lines": 12, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 10, 12, 58, 77, 81, 117, 149, 166, 185, 187, 194], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 5, 6, 7, 9, 11, 14, 15, 17, 18, 25, 28, 29, 33, 34, 36, 37, 48, 49, 51, 52, 55, 56, 57, 59, 60, 70, 71, 73, 74, 75, 76, 79, 80, 82, 83, 101, 102, 104, 105, 109, 110, 112, 113, 114, 115, 116, 118, 119, 144, 145, 147, 148, 150, 151, 157, 158, 160, 161, 162, 163, 164, 165, 167, 168, 181, 182, 184, 186, 189, 190, 192, 193, 195, 196], "summary": {"covered_lines": 43, "num_statements": 55, "percent_covered": 78.18181818181819, "percent_covered_display": "78.18", "missing_lines": 12, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 10, 12, 58, 77, 81, 117, 149, 166, 185, 187, 194], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"TenantBase": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantCreate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantUpdate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantRead": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantReadWithAPIKey": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantListResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantStatusUpdate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantStats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "APIKeyRegenerate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "APIKeyResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 5, 6, 7, 9, 11, 14, 15, 17, 18, 25, 28, 29, 33, 34, 36, 37, 48, 49, 51, 52, 55, 56, 57, 59, 60, 70, 71, 73, 74, 75, 76, 79, 80, 82, 83, 101, 102, 104, 105, 109, 110, 112, 113, 114, 115, 116, 118, 119, 144, 145, 147, 148, 150, 151, 157, 158, 160, 161, 162, 163, 164, 165, 167, 168, 181, 182, 184, 186, 189, 190, 192, 193, 195, 196], "summary": {"covered_lines": 43, "num_statements": 55, "percent_covered": 78.18181818181819, "percent_covered_display": "78.18", "missing_lines": 12, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 10, 12, 58, 77, 81, 117, 149, 166, 185, 187, 194], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\schemas\\user.py": {"executed_lines": [1, 5, 6, 7, 9, 12, 13, 15, 16, 23, 30, 33, 37, 38, 40, 42, 43, 58, 59, 61, 62, 64, 65, 74, 75, 77, 78, 79, 80, 81, 83, 84, 101, 102, 104, 105, 106, 107, 108, 110, 111, 135, 136, 138, 139, 140, 143, 145, 146, 157, 158, 160, 161, 162, 164, 165, 171, 172, 175, 176, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 209, 210, 213, 214, 217, 218, 238, 239, 240, 242, 243, 244, 245, 246, 247], "summary": {"covered_lines": 49, "num_statements": 62, "percent_covered": 74.24242424242425, "percent_covered_display": "74.24", "missing_lines": 13, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [8, 10, 41, 63, 82, 109, 144, 163, 166, 168, 169, 170, 211], "excluded_lines": [], "executed_branches": [], "missing_branches": [[168, 169], [168, 170], [170, 171], [170, 172]], "functions": {"PlatformUserInfo.generate_composite_id": {"executed_lines": [171, 172], "summary": {"covered_lines": 2, "num_statements": 5, "percent_covered": 22.22222222222222, "percent_covered_display": "22.22", "missing_lines": 3, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [168, 169, 170], "excluded_lines": [], "executed_branches": [], "missing_branches": [[168, 169], [168, 170], [170, 171], [170, 172]]}, "": {"executed_lines": [1, 5, 6, 7, 9, 12, 13, 15, 16, 23, 30, 33, 37, 38, 40, 42, 43, 58, 59, 61, 62, 64, 65, 74, 75, 77, 78, 79, 80, 81, 83, 84, 101, 102, 104, 105, 106, 107, 108, 110, 111, 135, 136, 138, 139, 140, 143, 145, 146, 157, 158, 160, 161, 162, 164, 165, 175, 176, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 209, 210, 213, 214, 217, 218, 238, 239, 240, 242, 243, 244, 245, 246, 247], "summary": {"covered_lines": 47, "num_statements": 57, "percent_covered": 82.45614035087719, "percent_covered_display": "82.46", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 10, 41, 63, 82, 109, 144, 163, 166, 211], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"UserBase": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UserCreate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UserUpdate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UserRead": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UserListResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UserSearch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "PlatformUserInfo": {"executed_lines": [171, 172], "summary": {"covered_lines": 2, "num_statements": 5, "percent_covered": 22.22222222222222, "percent_covered_display": "22.22", "missing_lines": 3, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [168, 169, 170], "excluded_lines": [], "executed_branches": [], "missing_branches": [[168, 169], [168, 170], [170, 171], [170, 172]]}, "UserStats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UserBatchCreate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UserBatchCreateResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 5, 6, 7, 9, 12, 13, 15, 16, 23, 30, 33, 37, 38, 40, 42, 43, 58, 59, 61, 62, 64, 65, 74, 75, 77, 78, 79, 80, 81, 83, 84, 101, 102, 104, 105, 106, 107, 108, 110, 111, 135, 136, 138, 139, 140, 143, 145, 146, 157, 158, 160, 161, 162, 164, 165, 175, 176, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 209, 210, 213, 214, 217, 218, 238, 239, 240, 242, 243, 244, 245, 246, 247], "summary": {"covered_lines": 47, "num_statements": 57, "percent_covered": 82.45614035087719, "percent_covered_display": "82.46", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 10, 41, 63, 82, 109, 144, 163, 166, 211], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\agent_suggestion_service.py": {"executed_lines": [1, 6, 7, 8, 9, 11, 13, 14, 15, 16, 17, 18, 21, 24, 25, 42, 43, 45, 63, 64, 218, 219, 292, 349, 358, 455, 461, 514, 566, 569, 615, 621, 679], "summary": {"covered_lines": 16, "num_statements": 183, "percent_covered": 6.694560669456067, "percent_covered_display": "6.69", "missing_lines": 167, "excluded_lines": 0, "num_branches": 56, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 56}, "missing_lines": [10, 12, 19, 22, 46, 53, 54, 55, 56, 59, 62, 89, 91, 92, 96, 98, 122, 123, 140, 143, 144, 147, 150, 153, 163, 170, 175, 182, 199, 208, 210, 211, 217, 232, 234, 235, 236, 239, 246, 249, 254, 255, 256, 257, 258, 260, 283, 284, 287, 289, 302, 303, 306, 319, 320, 322, 325, 338, 339, 340, 372, 374, 376, 393, 395, 398, 409, 415, 417, 420, 421, 423, 424, 425, 434, 435, 443, 445, 446, 447, 473, 476, 478, 479, 480, 483, 484, 485, 486, 488, 489, 490, 491, 492, 493, 494, 496, 497, 500, 502, 513, 523, 526, 527, 531, 562, 564, 580, 582, 583, 584, 587, 588, 595, 596, 598, 599, 601, 602, 607, 612, 613, 632, 634, 635, 636, 637, 640, 641, 642, 643, 644, 645, 646, 649, 650, 651, 652, 653, 656, 657, 658, 659, 662, 663, 664, 665, 666, 667, 670, 673, 674, 676, 678, 682, 684, 685, 687, 688, 690, 691, 692, 694, 695, 696, 698, 703], "excluded_lines": [], "executed_branches": [], "missing_branches": [[96, 98], [96, 140], [143, 144], [143, 147], [235, 236], [235, 239], [255, 256], [255, 260], [374, 376], [374, 395], [423, 424], [423, 434], [424, 423], [424, 425], [434, 435], [434, 443], [483, 484], [483, 500], [489, 490], [489, 491], [491, 492], [491, 493], [493, 494], [493, 496], [496, 483], [496, 497], [526, 527], [526, 531], [587, 588], [587, 595], [595, 596], [595, 598], [598, 599], [598, 601], [601, 602], [601, 607], [640, 641], [640, 644], [644, 645], [644, 649], [649, 650], [649, 652], [652, 653], [652, 656], [656, 657], [656, 662], [662, 663], [662, 665], [665, 666], [665, 670], [673, 674], [673, 676], [687, 688], [687, 690], [691, 692], [691, 694]], "functions": {"AgentSuggestionService.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [53, 54, 55, 56, 59, 62], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AgentSuggestionService.get_reply_suggestions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 22, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [89, 91, 92, 96, 98, 122, 123, 140, 143, 144, 147, 150, 153, 163, 170, 175, 182, 199, 208, 210, 211, 217], "excluded_lines": [], "executed_branches": [], "missing_branches": [[96, 98], [96, 140], [143, 144], [143, 147]]}, "AgentSuggestionService._get_session_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [232, 234, 235, 236, 239, 246, 249, 254, 255, 256, 257, 258, 260, 283, 284, 287], "excluded_lines": [], "executed_branches": [], "missing_branches": [[235, 236], [235, 239], [255, 256], [255, 260]]}, "AgentSuggestionService._analyze_user_message": {"executed_lines": [292], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [302, 303, 306, 319, 320, 322, 325, 338, 339, 340], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AgentSuggestionService._generate_ai_suggestions": {"executed_lines": [358], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 20, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [372, 374, 376, 393, 395, 398, 409, 415, 417, 420, 421, 423, 424, 425, 434, 435, 443, 445, 446, 447], "excluded_lines": [], "executed_branches": [], "missing_branches": [[374, 376], [374, 395], [423, 424], [423, 434], [424, 423], [424, 425], [434, 435], [434, 443]]}, "AgentSuggestionService._get_template_suggestions": {"executed_lines": [461], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 20, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [473, 476, 478, 479, 480, 483, 484, 485, 486, 488, 489, 490, 491, 492, 493, 494, 496, 497, 500, 502], "excluded_lines": [], "executed_branches": [], "missing_branches": [[483, 484], [483, 500], [489, 490], [489, 491], [491, 492], [491, 493], [493, 494], [493, 496], [496, 483], [496, 497]]}, "AgentSuggestionService._get_template_library": {"executed_lines": [514], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [523, 526, 527, 531, 562, 564], "excluded_lines": [], "executed_branches": [], "missing_branches": [[526, 527], [526, 531]]}, "AgentSuggestionService._generate_quick_replies": {"executed_lines": [569], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [580, 582, 583, 584, 587, 588, 595, 596, 598, 599, 601, 602, 607, 612, 613], "excluded_lines": [], "executed_branches": [], "missing_branches": [[587, 588], [587, 595], [595, 596], [595, 598], [598, 599], [598, 601], [601, 602], [601, 607]]}, "AgentSuggestionService._generate_handling_tips": {"executed_lines": [621], "summary": {"covered_lines": 0, "num_statements": 31, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 31, "excluded_lines": 0, "num_branches": 16, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 16}, "missing_lines": [632, 634, 635, 636, 637, 640, 641, 642, 643, 644, 645, 646, 649, 650, 651, 652, 653, 656, 657, 658, 659, 662, 663, 664, 665, 666, 667, 670, 673, 674, 676], "excluded_lines": [], "executed_branches": [], "missing_branches": [[640, 641], [640, 644], [644, 645], [644, 649], [649, 650], [649, 652], [652, 653], [652, 656], [656, 657], [656, 662], [662, 663], [662, 665], [665, 666], [665, 670], [673, 674], [673, 676]]}, "AgentSuggestionService._get_llm_provider": {"executed_lines": [679], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [682, 684, 685, 687, 688, 690, 691, 692, 694, 695, 696], "excluded_lines": [], "executed_branches": [], "missing_branches": [[687, 688], [687, 690], [691, 692], [691, 694]]}, "AgentSuggestionService._get_default_llm_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [703], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 11, 13, 14, 15, 16, 17, 18, 21, 24, 25, 42, 43, 45, 63, 64, 218, 219, 349, 455, 566, 615], "summary": {"covered_lines": 16, "num_statements": 25, "percent_covered": 64.0, "percent_covered_display": "64.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 12, 19, 22, 46, 289, 513, 678, 698], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"AgentSuggestionService": {"executed_lines": [292, 358, 461, 514, 569, 621, 679], "summary": {"covered_lines": 0, "num_statements": 158, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 158, "excluded_lines": 0, "num_branches": 56, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 56}, "missing_lines": [53, 54, 55, 56, 59, 62, 89, 91, 92, 96, 98, 122, 123, 140, 143, 144, 147, 150, 153, 163, 170, 175, 182, 199, 208, 210, 211, 217, 232, 234, 235, 236, 239, 246, 249, 254, 255, 256, 257, 258, 260, 283, 284, 287, 302, 303, 306, 319, 320, 322, 325, 338, 339, 340, 372, 374, 376, 393, 395, 398, 409, 415, 417, 420, 421, 423, 424, 425, 434, 435, 443, 445, 446, 447, 473, 476, 478, 479, 480, 483, 484, 485, 486, 488, 489, 490, 491, 492, 493, 494, 496, 497, 500, 502, 523, 526, 527, 531, 562, 564, 580, 582, 583, 584, 587, 588, 595, 596, 598, 599, 601, 602, 607, 612, 613, 632, 634, 635, 636, 637, 640, 641, 642, 643, 644, 645, 646, 649, 650, 651, 652, 653, 656, 657, 658, 659, 662, 663, 664, 665, 666, 667, 670, 673, 674, 676, 682, 684, 685, 687, 688, 690, 691, 692, 694, 695, 696, 703], "excluded_lines": [], "executed_branches": [], "missing_branches": [[96, 98], [96, 140], [143, 144], [143, 147], [235, 236], [235, 239], [255, 256], [255, 260], [374, 376], [374, 395], [423, 424], [423, 434], [424, 423], [424, 425], [434, 435], [434, 443], [483, 484], [483, 500], [489, 490], [489, 491], [491, 492], [491, 493], [493, 494], [493, 496], [496, 483], [496, 497], [526, 527], [526, 531], [587, 588], [587, 595], [595, 596], [595, 598], [598, 599], [598, 601], [601, 602], [601, 607], [640, 641], [640, 644], [644, 645], [644, 649], [649, 650], [649, 652], [652, 653], [652, 656], [656, 657], [656, 662], [662, 663], [662, 665], [665, 666], [665, 670], [673, 674], [673, 676], [687, 688], [687, 690], [691, 692], [691, 694]]}, "": {"executed_lines": [1, 6, 7, 8, 9, 11, 13, 14, 15, 16, 17, 18, 21, 24, 25, 42, 43, 45, 63, 64, 218, 219, 349, 455, 566, 615], "summary": {"covered_lines": 16, "num_statements": 25, "percent_covered": 64.0, "percent_covered_display": "64.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 12, 19, 22, 46, 289, 513, 678, 698], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\analytics_service.py": {"executed_lines": [1, 10, 11, 12, 14, 15, 17, 18, 19, 20, 30, 33, 34, 36, 45, 46, 132, 210, 274, 346, 366, 404, 461, 479, 511, 537, 559, 570, 571, 572], "summary": {"covered_lines": 19, "num_statements": 139, "percent_covered": 12.258064516129032, "percent_covered_display": "12.26", "missing_lines": 120, "excluded_lines": 0, "num_branches": 16, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 16}, "missing_lines": [13, 16, 28, 31, 37, 44, 59, 61, 64, 65, 66, 67, 70, 71, 74, 77, 99, 123, 125, 139, 140, 141, 143, 156, 158, 161, 162, 163, 164, 167, 168, 171, 174, 194, 199, 202, 204, 211, 219, 220, 221, 223, 233, 234, 235, 238, 245, 254, 263, 267, 282, 283, 286, 288, 299, 300, 301, 304, 305, 310, 313, 318, 321, 326, 329, 334, 338, 354, 355, 358, 360, 364, 389, 394, 395, 396, 398, 402, 427, 432, 433, 434, 436, 440, 442, 481, 500, 502, 503, 504, 506, 510, 550, 551, 552, 554, 558, 569, 573, 575, 579, 580, 590, 592, 593, 594, 596, 598, 599, 601, 603, 607, 608, 623, 626, 627, 629, 631, 632, 633], "excluded_lines": [], "executed_branches": [], "missing_branches": [[64, 65], [64, 66], [66, 67], [66, 70], [70, 71], [70, 74], [161, 162], [161, 163], [163, 164], [163, 167], [167, 168], [167, 171], [598, 599], [598, 601], [626, 627], [626, 629]], "functions": {"AnalyticsService.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [44], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AnalyticsService.get_session_stats": {"executed_lines": [132], "summary": {"covered_lines": 1, "num_statements": 17, "percent_covered": 4.3478260869565215, "percent_covered_display": "4.35", "missing_lines": 16, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [59, 61, 64, 65, 66, 67, 70, 71, 74, 77, 99, 123, 125, 139, 140, 141], "excluded_lines": [], "executed_branches": [], "missing_branches": [[64, 65], [64, 66], [66, 67], [66, 70], [70, 71], [70, 74]]}, "AnalyticsService.get_message_stats": {"executed_lines": [210], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 18, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [156, 158, 161, 162, 163, 164, 167, 168, 171, 174, 194, 199, 202, 204, 211, 219, 220, 221], "excluded_lines": [], "executed_branches": [], "missing_branches": [[161, 162], [161, 163], [163, 164], [163, 167], [167, 168], [167, 171]]}, "AnalyticsService.get_realtime_metrics": {"executed_lines": [274], "summary": {"covered_lines": 1, "num_statements": 12, "percent_covered": 8.333333333333334, "percent_covered_display": "8.33", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [233, 234, 235, 238, 245, 254, 263, 267, 282, 283, 286], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AnalyticsService.get_trend_analysis": {"executed_lines": [346], "summary": {"covered_lines": 1, "num_statements": 17, "percent_covered": 5.882352941176471, "percent_covered_display": "5.88", "missing_lines": 16, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [299, 300, 301, 304, 305, 310, 313, 318, 321, 326, 329, 334, 338, 354, 355, 358], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AnalyticsService._get_sessions_time_series": {"executed_lines": [366], "summary": {"covered_lines": 1, "num_statements": 6, "percent_covered": 16.666666666666668, "percent_covered_display": "16.67", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [364, 389, 394, 395, 396], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AnalyticsService._get_messages_time_series": {"executed_lines": [404], "summary": {"covered_lines": 1, "num_statements": 6, "percent_covered": 16.666666666666668, "percent_covered_display": "16.67", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [402, 427, 432, 433, 434], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AnalyticsService._calculate_avg_response_time": {"executed_lines": [461, 479], "summary": {"covered_lines": 1, "num_statements": 8, "percent_covered": 12.5, "percent_covered_display": "12.50", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [440, 442, 481, 500, 502, 503, 504], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AnalyticsService._get_top_active_sessions": {"executed_lines": [511, 537], "summary": {"covered_lines": 2, "num_statements": 6, "percent_covered": 33.333333333333336, "percent_covered_display": "33.33", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [510, 550, 551, 552], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AnalyticsService._count_sessions_in_period": {"executed_lines": [559, 570, 571, 572], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [558, 569, 573], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AnalyticsService._count_messages_in_period": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [579, 580, 590, 592, 593, 594], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AnalyticsService._calculate_change_rate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [598, 599, 601], "excluded_lines": [], "executed_branches": [], "missing_branches": [[598, 599], [598, 601]]}, "AnalyticsService._get_hourly_activity_distribution": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [607, 608, 623, 626, 627, 629, 631, 632, 633], "excluded_lines": [], "executed_branches": [], "missing_branches": [[626, 627], [626, 629]]}, "": {"executed_lines": [1, 10, 11, 12, 14, 15, 17, 18, 19, 20, 30, 33, 34, 36, 45, 46], "summary": {"covered_lines": 8, "num_statements": 24, "percent_covered": 33.333333333333336, "percent_covered_display": "33.33", "missing_lines": 16, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [13, 16, 28, 31, 37, 143, 223, 288, 360, 398, 436, 506, 554, 575, 596, 603], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"AnalyticsService": {"executed_lines": [132, 210, 274, 346, 366, 404, 461, 479, 511, 537, 559, 570, 571, 572], "summary": {"covered_lines": 11, "num_statements": 115, "percent_covered": 8.396946564885496, "percent_covered_display": "8.40", "missing_lines": 104, "excluded_lines": 0, "num_branches": 16, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 16}, "missing_lines": [44, 59, 61, 64, 65, 66, 67, 70, 71, 74, 77, 99, 123, 125, 139, 140, 141, 156, 158, 161, 162, 163, 164, 167, 168, 171, 174, 194, 199, 202, 204, 211, 219, 220, 221, 233, 234, 235, 238, 245, 254, 263, 267, 282, 283, 286, 299, 300, 301, 304, 305, 310, 313, 318, 321, 326, 329, 334, 338, 354, 355, 358, 364, 389, 394, 395, 396, 402, 427, 432, 433, 434, 440, 442, 481, 500, 502, 503, 504, 510, 550, 551, 552, 558, 569, 573, 579, 580, 590, 592, 593, 594, 598, 599, 601, 607, 608, 623, 626, 627, 629, 631, 632, 633], "excluded_lines": [], "executed_branches": [], "missing_branches": [[64, 65], [64, 66], [66, 67], [66, 70], [70, 71], [70, 74], [161, 162], [161, 163], [163, 164], [163, 167], [167, 168], [167, 171], [598, 599], [598, 601], [626, 627], [626, 629]]}, "": {"executed_lines": [1, 10, 11, 12, 14, 15, 17, 18, 19, 20, 30, 33, 34, 36, 45, 46], "summary": {"covered_lines": 8, "num_statements": 24, "percent_covered": 33.333333333333336, "percent_covered_display": "33.33", "missing_lines": 16, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [13, 16, 28, 31, 37, 143, 223, 288, 360, 398, 436, 506, 554, 575, 596, 603], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\auth_service.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 155, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 155, "excluded_lines": 0, "num_branches": 28, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 28}, "missing_lines": [6, 7, 8, 10, 11, 13, 19, 20, 21, 32, 35, 38, 41, 44, 47, 54, 56, 67, 70, 71, 72, 74, 75, 79, 81, 82, 84, 97, 99, 100, 101, 103, 104, 107, 108, 114, 117, 128, 137, 138, 139, 140, 142, 155, 157, 158, 159, 161, 162, 165, 175, 176, 177, 180, 182, 188, 189, 190, 191, 192, 194, 209, 211, 212, 214, 215, 218, 219, 221, 224, 225, 226, 229, 231, 240, 246, 247, 248, 249, 251, 267, 269, 270, 272, 275, 276, 277, 287, 289, 290, 291, 292, 293, 295, 305, 307, 309, 311, 313, 314, 316, 326, 327, 328, 329, 331, 332, 334, 337, 338, 339, 341, 351, 352, 353, 354, 356, 366, 367, 368, 369, 371, 372, 374, 376, 377, 379, 392, 393, 394, 395, 398, 399, 401, 403, 405, 406, 407, 408, 409, 411, 424, 425, 426, 427, 430, 431, 433, 435, 436, 437, 438, 439, 442, 452], "excluded_lines": [], "executed_branches": [], "missing_branches": [[74, 75], [74, 79], [103, 104], [103, 107], [107, 108], [107, 114], [161, 162], [161, 165], [214, 215], [214, 218], [218, 219], [218, 221], [225, 226], [225, 229], [269, 270], [269, 272], [276, 277], [276, 287], [331, 332], [331, 334], [338, 339], [338, 341], [371, 372], [371, 374], [394, 395], [394, 398], [426, 427], [426, 430]], "functions": {"AuthService.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [54], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AuthService.authenticate_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [67, 70, 71, 72, 74, 75, 79, 81, 82], "excluded_lines": [], "executed_branches": [], "missing_branches": [[74, 75], [74, 79]]}, "AuthService.login": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [97, 99, 100, 101, 103, 104, 107, 108, 114, 117, 128, 137, 138, 139, 140], "excluded_lines": [], "executed_branches": [], "missing_branches": [[103, 104], [103, 107], [107, 108], [107, 114]]}, "AuthService.register": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 17, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [155, 157, 158, 159, 161, 162, 165, 175, 176, 177, 180, 182, 188, 189, 190, 191, 192], "excluded_lines": [], "executed_branches": [], "missing_branches": [[161, 162], [161, 165]]}, "AuthService.refresh_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 18, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [209, 211, 212, 214, 215, 218, 219, 221, 224, 225, 226, 229, 231, 240, 246, 247, 248, 249], "excluded_lines": [], "executed_branches": [], "missing_branches": [[214, 215], [214, 218], [218, 219], [218, 221], [225, 226], [225, 229]]}, "AuthService.change_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [267, 269, 270, 272, 275, 276, 277, 287, 289, 290, 291, 292, 293], "excluded_lines": [], "executed_branches": [], "missing_branches": [[269, 270], [269, 272], [276, 277], [276, 287]]}, "AuthService.logout": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [305, 307, 309, 311, 313, 314], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AuthService.get_user_by_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [326, 327, 328, 329, 331, 332, 334, 337, 338, 339, 341, 351, 352, 353, 354], "excluded_lines": [], "executed_branches": [], "missing_branches": [[331, 332], [331, 334], [338, 339], [338, 341]]}, "AuthService.verify_api_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [366, 367, 368, 369, 371, 372, 374, 376, 377], "excluded_lines": [], "executed_branches": [], "missing_branches": [[371, 372], [371, 374]]}, "AuthService.generate_api_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [392, 393, 394, 395, 398, 399, 401, 403, 405, 406, 407, 408, 409], "excluded_lines": [], "executed_branches": [], "missing_branches": [[394, 395], [394, 398]]}, "AuthService.revoke_api_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [424, 425, 426, 427, 430, 431, 433, 435, 436, 437, 438, 439], "excluded_lines": [], "executed_branches": [], "missing_branches": [[426, 427], [426, 430]]}, "get_auth_service": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [452], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 26, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 26, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [6, 7, 8, 10, 11, 13, 19, 20, 21, 32, 35, 38, 41, 44, 47, 56, 84, 142, 194, 251, 295, 316, 356, 379, 411, 442], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"AuthenticationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "RegistrationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AuthService": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 128, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 128, "excluded_lines": 0, "num_branches": 28, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 28}, "missing_lines": [54, 67, 70, 71, 72, 74, 75, 79, 81, 82, 97, 99, 100, 101, 103, 104, 107, 108, 114, 117, 128, 137, 138, 139, 140, 155, 157, 158, 159, 161, 162, 165, 175, 176, 177, 180, 182, 188, 189, 190, 191, 192, 209, 211, 212, 214, 215, 218, 219, 221, 224, 225, 226, 229, 231, 240, 246, 247, 248, 249, 267, 269, 270, 272, 275, 276, 277, 287, 289, 290, 291, 292, 293, 305, 307, 309, 311, 313, 314, 326, 327, 328, 329, 331, 332, 334, 337, 338, 339, 341, 351, 352, 353, 354, 366, 367, 368, 369, 371, 372, 374, 376, 377, 392, 393, 394, 395, 398, 399, 401, 403, 405, 406, 407, 408, 409, 424, 425, 426, 427, 430, 431, 433, 435, 436, 437, 438, 439], "excluded_lines": [], "executed_branches": [], "missing_branches": [[74, 75], [74, 79], [103, 104], [103, 107], [107, 108], [107, 114], [161, 162], [161, 165], [214, 215], [214, 218], [218, 219], [218, 221], [225, 226], [225, 229], [269, 270], [269, 272], [276, 277], [276, 287], [331, 332], [331, 334], [338, 339], [338, 341], [371, 372], [371, 374], [394, 395], [394, 398], [426, 427], [426, 430]]}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 27, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [6, 7, 8, 10, 11, 13, 19, 20, 21, 32, 35, 38, 41, 44, 47, 56, 84, 142, 194, 251, 295, 316, 356, 379, 411, 442, 452], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\auto_reply_service.py": {"executed_lines": [1, 6, 7, 9, 11, 12, 13, 14, 15, 16, 17, 18, 21, 24, 25, 27, 43, 45, 46, 194, 202, 284, 285, 286, 336, 359, 364, 365, 393, 415, 452, 472, 498, 500, 501, 502, 503, 504], "summary": {"covered_lines": 19, "num_statements": 123, "percent_covered": 11.949685534591195, "percent_covered_display": "11.95", "missing_lines": 104, "excluded_lines": 0, "num_branches": 36, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 36}, "missing_lines": [8, 19, 20, 30, 37, 38, 39, 41, 44, 73, 75, 76, 79, 81, 84, 85, 86, 92, 97, 109, 112, 113, 118, 126, 130, 131, 137, 140, 143, 148, 151, 152, 155, 158, 159, 171, 173, 183, 185, 186, 192, 215, 217, 218, 221, 224, 229, 232, 233, 235, 236, 237, 240, 242, 248, 251, 257, 259, 260, 261, 263, 269, 270, 271, 273, 274, 276, 277, 280, 281, 282, 288, 289, 294, 295, 301, 303, 304, 306, 311, 322, 332, 343, 348, 350, 356, 357, 358, 367, 373, 374, 375, 376, 377, 379, 384, 386, 396, 397, 400, 401, 408, 409, 410], "excluded_lines": [], "executed_branches": [], "missing_branches": [[75, 76], [75, 79], [79, 81], [79, 130], [84, 85], [84, 118], [130, 131], [130, 140], [158, 159], [158, 173], [217, 218], [217, 221], [233, 235], [233, 240], [235, 233], [235, 236], [240, -194], [240, 242], [270, 271], [270, 273], [276, 277], [276, 280], [281, 282], [281, 285], [288, 289], [288, 294], [294, 295], [294, 301], [357, 358], [357, 365], [358, 357], [358, 359], [374, 375], [374, 377], [375, 374], [375, 376]], "functions": {"AutoReplyService.__init__": {"executed_lines": [43], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [37, 38, 39, 41, 44], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AutoReplyService.generate_reply": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 32, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 32, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [73, 75, 76, 79, 81, 84, 85, 86, 92, 97, 109, 112, 113, 118, 126, 130, 131, 137, 140, 143, 148, 151, 152, 155, 158, 159, 171, 173, 183, 185, 186, 192], "excluded_lines": [], "executed_branches": [], "missing_branches": [[75, 76], [75, 79], [79, 81], [79, 130], [84, 85], [84, 118], [130, 131], [130, 140], [158, 159], [158, 173]]}, "AutoReplyService.generate_stream_reply": {"executed_lines": [202], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 19, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [215, 217, 218, 221, 224, 229, 232, 233, 235, 236, 237, 240, 242, 248, 251, 257, 259, 260, 261], "excluded_lines": [], "executed_branches": [], "missing_branches": [[217, 218], [217, 221], [233, 235], [233, 240], [235, 233], [235, 236], [240, -194], [240, 242]]}, "AutoReplyService._get_llm_provider": {"executed_lines": [284, 285, 286], "summary": {"covered_lines": 1, "num_statements": 18, "percent_covered": 3.5714285714285716, "percent_covered_display": "3.57", "missing_lines": 17, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [269, 270, 271, 273, 274, 276, 277, 280, 281, 282, 288, 289, 294, 295, 301, 303, 304], "excluded_lines": [], "executed_branches": [], "missing_branches": [[270, 271], [270, 273], [276, 277], [276, 280], [281, 282], [281, 285], [288, 289], [288, 294], [294, 295], [294, 301]]}, "AutoReplyService._get_default_llm_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [311], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AutoReplyService._build_conversation_context": {"executed_lines": [336], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [332], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AutoReplyService._get_default_system_prompt": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [348], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AutoReplyService._content_safety_check": {"executed_lines": [359, 364, 365], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 30.0, "percent_covered_display": "30.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [356, 357, 358], "excluded_lines": [], "executed_branches": [], "missing_branches": [[357, 358], [357, 365], [358, 357], [358, 359]]}, "AutoReplyService._is_content_safe_chunk": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [373, 374, 375, 376, 377], "excluded_lines": [], "executed_branches": [], "missing_branches": [[374, 375], [374, 377], [375, 374], [375, 376]]}, "AutoReplyService.is_auto_reply_enabled": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [384], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "AutoReplyService.get_reply_suggestions": {"executed_lines": [393], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [396, 397, 400, 401, 408, 409, 410], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 7, 9, 11, 12, 13, 14, 15, 16, 17, 18, 21, 24, 25, 27, 45, 46, 194], "summary": {"covered_lines": 14, "num_statements": 26, "percent_covered": 53.84615384615385, "percent_covered_display": "53.85", "missing_lines": 12, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 19, 20, 30, 263, 306, 322, 343, 350, 367, 379, 386], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"AutoReplyService": {"executed_lines": [43, 202, 284, 285, 286, 336, 359, 364, 365, 393], "summary": {"covered_lines": 5, "num_statements": 97, "percent_covered": 3.7593984962406015, "percent_covered_display": "3.76", "missing_lines": 92, "excluded_lines": 0, "num_branches": 36, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 36}, "missing_lines": [37, 38, 39, 41, 44, 73, 75, 76, 79, 81, 84, 85, 86, 92, 97, 109, 112, 113, 118, 126, 130, 131, 137, 140, 143, 148, 151, 152, 155, 158, 159, 171, 173, 183, 185, 186, 192, 215, 217, 218, 221, 224, 229, 232, 233, 235, 236, 237, 240, 242, 248, 251, 257, 259, 260, 261, 269, 270, 271, 273, 274, 276, 277, 280, 281, 282, 288, 289, 294, 295, 301, 303, 304, 311, 332, 348, 356, 357, 358, 373, 374, 375, 376, 377, 384, 396, 397, 400, 401, 408, 409, 410], "excluded_lines": [], "executed_branches": [], "missing_branches": [[75, 76], [75, 79], [79, 81], [79, 130], [84, 85], [84, 118], [130, 131], [130, 140], [158, 159], [158, 173], [217, 218], [217, 221], [233, 235], [233, 240], [235, 233], [235, 236], [240, -194], [240, 242], [270, 271], [270, 273], [276, 277], [276, 280], [281, 282], [281, 285], [288, 289], [288, 294], [294, 295], [294, 301], [357, 358], [357, 365], [358, 357], [358, 359], [374, 375], [374, 377], [375, 374], [375, 376]]}, "": {"executed_lines": [1, 6, 7, 9, 11, 12, 13, 14, 15, 16, 17, 18, 21, 24, 25, 27, 45, 46, 194], "summary": {"covered_lines": 14, "num_statements": 26, "percent_covered": 53.84615384615385, "percent_covered_display": "53.85", "missing_lines": 12, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 19, 20, 30, 263, 306, 322, 343, 350, 367, 379, 386], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\context_manager.py": {"executed_lines": [1, 6, 7, 8, 10, 12, 13, 14, 15, 18, 21, 22, 24, 26, 33, 35, 36, 38, 39, 124, 125, 150, 196, 220, 285, 312, 345, 372, 386], "summary": {"covered_lines": 10, "num_statements": 131, "percent_covered": 5.847953216374269, "percent_covered_display": "5.85", "missing_lines": 121, "excluded_lines": 0, "num_branches": 40, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 40}, "missing_lines": [9, 11, 16, 19, 25, 32, 34, 37, 62, 64, 69, 72, 73, 78, 79, 80, 81, 86, 87, 88, 101, 105, 114, 116, 117, 123, 140, 148, 163, 165, 166, 167, 170, 173, 176, 179, 180, 183, 184, 185, 187, 189, 190, 193, 195, 206, 208, 212, 214, 215, 217, 230, 231, 234, 235, 238, 239, 241, 243, 248, 251, 252, 254, 255, 257, 258, 259, 262, 265, 266, 267, 270, 272, 280, 282, 295, 296, 299, 300, 302, 303, 306, 307, 309, 322, 323, 325, 326, 328, 329, 332, 333, 335, 336, 338, 340, 344, 355, 358, 361, 364, 367, 369, 371, 381, 383, 397, 398, 400, 401, 404, 406, 408, 409, 411, 412, 414, 416, 418, 419, 422], "excluded_lines": [], "executed_branches": [], "missing_branches": [[72, 73], [72, 78], [78, 79], [78, 86], [80, 81], [80, 86], [86, 87], [86, 101], [166, 167], [166, 170], [179, 180], [179, 183], [184, 185], [184, 187], [230, 231], [230, 234], [241, 243], [241, 251], [254, 255], [254, 270], [257, 258], [257, 262], [265, 266], [265, 267], [295, 296], [295, 299], [302, 303], [302, 306], [322, 323], [322, 325], [328, 329], [328, 332], [335, 336], [335, 338], [400, 401], [400, 404], [406, 408], [406, 416], [411, 412], [411, 414]], "functions": {"ContextManager.__init__": {"executed_lines": [26, 33, 35, 36], "summary": {"covered_lines": 1, "num_statements": 4, "percent_covered": 25.0, "percent_covered_display": "25.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [32, 34, 37], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ContextManager.build_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 18, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [62, 64, 69, 72, 73, 78, 79, 80, 81, 86, 87, 88, 101, 105, 114, 116, 117, 123], "excluded_lines": [], "executed_branches": [], "missing_branches": [[72, 73], [72, 78], [78, 79], [78, 86], [80, 81], [80, 86], [86, 87], [86, 101]]}, "ContextManager._get_session_messages": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [140, 148], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ContextManager._build_user_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [163, 165, 166, 167, 170, 173, 176, 179, 180, 183, 184, 185, 187, 189, 190, 193], "excluded_lines": [], "executed_branches": [], "missing_branches": [[166, 167], [166, 170], [179, 180], [179, 183], [184, 185], [184, 187]]}, "ContextManager._get_user_recent_activity": {"executed_lines": [196], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [206, 208, 212, 214, 215], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ContextManager._optimize_context_by_tokens": {"executed_lines": [220], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 23, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [230, 231, 234, 235, 238, 239, 241, 243, 248, 251, 252, 254, 255, 257, 258, 259, 262, 265, 266, 267, 270, 272, 280], "excluded_lines": [], "executed_branches": [], "missing_branches": [[230, 231], [230, 234], [241, 243], [241, 251], [254, 255], [254, 270], [257, 258], [257, 262], [265, 266], [265, 267]]}, "ContextManager._truncate_system_messages": {"executed_lines": [285], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [295, 296, 299, 300, 302, 303, 306, 307], "excluded_lines": [], "executed_branches": [], "missing_branches": [[295, 296], [295, 299], [302, 303], [302, 306]]}, "ContextManager._truncate_message": {"executed_lines": [312], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [322, 323, 325, 326, 328, 329, 332, 333, 335, 336, 338, 340], "excluded_lines": [], "executed_branches": [], "missing_branches": [[322, 323], [322, 325], [328, 329], [328, 332], [335, 336], [335, 338]]}, "ContextManager._estimate_message_tokens": {"executed_lines": [345], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [355, 358, 361, 364, 367, 369], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ContextManager._estimate_total_tokens": {"executed_lines": [372], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [381], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ContextManager.get_context_summary": {"executed_lines": [386], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [397, 398, 400, 401, 404, 406, 408, 409, 411, 412, 414, 416, 418, 419, 422], "excluded_lines": [], "executed_branches": [], "missing_branches": [[400, 401], [400, 404], [406, 408], [406, 416], [411, 412], [411, 414]]}, "": {"executed_lines": [1, 6, 7, 8, 10, 12, 13, 14, 15, 18, 21, 22, 24, 38, 39, 124, 125, 150], "summary": {"covered_lines": 9, "num_statements": 21, "percent_covered": 42.857142857142854, "percent_covered_display": "42.86", "missing_lines": 12, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 11, 16, 19, 25, 195, 217, 282, 309, 344, 371, 383], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"ContextManager": {"executed_lines": [26, 33, 35, 36, 196, 220, 285, 312, 345, 372, 386], "summary": {"covered_lines": 1, "num_statements": 110, "percent_covered": 0.6666666666666666, "percent_covered_display": "0.67", "missing_lines": 109, "excluded_lines": 0, "num_branches": 40, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 40}, "missing_lines": [32, 34, 37, 62, 64, 69, 72, 73, 78, 79, 80, 81, 86, 87, 88, 101, 105, 114, 116, 117, 123, 140, 148, 163, 165, 166, 167, 170, 173, 176, 179, 180, 183, 184, 185, 187, 189, 190, 193, 206, 208, 212, 214, 215, 230, 231, 234, 235, 238, 239, 241, 243, 248, 251, 252, 254, 255, 257, 258, 259, 262, 265, 266, 267, 270, 272, 280, 295, 296, 299, 300, 302, 303, 306, 307, 322, 323, 325, 326, 328, 329, 332, 333, 335, 336, 338, 340, 355, 358, 361, 364, 367, 369, 381, 397, 398, 400, 401, 404, 406, 408, 409, 411, 412, 414, 416, 418, 419, 422], "excluded_lines": [], "executed_branches": [], "missing_branches": [[72, 73], [72, 78], [78, 79], [78, 86], [80, 81], [80, 86], [86, 87], [86, 101], [166, 167], [166, 170], [179, 180], [179, 183], [184, 185], [184, 187], [230, 231], [230, 234], [241, 243], [241, 251], [254, 255], [254, 270], [257, 258], [257, 262], [265, 266], [265, 267], [295, 296], [295, 299], [302, 303], [302, 306], [322, 323], [322, 325], [328, 329], [328, 332], [335, 336], [335, 338], [400, 401], [400, 404], [406, 408], [406, 416], [411, 412], [411, 414]]}, "": {"executed_lines": [1, 6, 7, 8, 10, 12, 13, 14, 15, 18, 21, 22, 24, 38, 39, 124, 125, 150], "summary": {"covered_lines": 9, "num_statements": 21, "percent_covered": 42.857142857142854, "percent_covered_display": "42.86", "missing_lines": 12, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 11, 16, 19, 25, 195, 217, 282, 309, 344, 371, 383], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\instance_auth_service.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 14, 15, 17, 18, 21, 24, 25, 27, 36, 37, 109, 110, 183, 184, 230, 231, 272, 273, 323, 324, 404, 405, 442, 443, 535, 546, 556, 557, 598, 599], "summary": {"covered_lines": 19, "num_statements": 195, "percent_covered": 7.755102040816326, "percent_covered_display": "7.76", "missing_lines": 176, "excluded_lines": 0, "num_branches": 50, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 50}, "missing_lines": [13, 16, 19, 22, 28, 35, 59, 61, 62, 65, 66, 69, 72, 83, 85, 92, 101, 102, 108, 124, 126, 128, 129, 134, 137, 138, 144, 147, 148, 149, 150, 151, 157, 160, 161, 162, 167, 169, 173, 175, 176, 182, 197, 199, 201, 202, 205, 206, 209, 211, 215, 222, 223, 229, 245, 247, 250, 254, 258, 264, 265, 271, 286, 288, 289, 290, 292, 293, 296, 298, 299, 301, 302, 305, 314, 316, 318, 319, 322, 343, 345, 347, 348, 353, 355, 356, 357, 362, 365, 366, 371, 377, 379, 380, 386, 394, 396, 397, 403, 418, 419, 421, 422, 425, 434, 435, 441, 455, 456, 457, 460, 461, 463, 465, 466, 468, 470, 471, 472, 474, 475, 477, 478, 481, 482, 483, 484, 485, 490, 491, 492, 494, 500, 501, 508, 511, 512, 513, 515, 522, 529, 530, 531, 534, 536, 544, 545, 547, 555, 568, 570, 571, 572, 574, 575, 578, 579, 581, 582, 584, 587, 589, 590, 591, 597, 612, 613, 614, 615, 617, 618, 620, 621, 623, 624, 630], "excluded_lines": [], "executed_branches": [], "missing_branches": [[61, 62], [61, 65], [128, 129], [128, 137], [137, 138], [137, 147], [148, 149], [148, 160], [150, 151], [150, 160], [161, 162], [161, 169], [201, 202], [201, 205], [292, 293], [292, 296], [299, 301], [299, 316], [301, 302], [301, 305], [347, 348], [347, 355], [356, 357], [356, 365], [379, 380], [379, 386], [421, 422], [421, 425], [460, 461], [460, 463], [470, 471], [470, 515], [471, 472], [471, 474], [477, 478], [477, 511], [482, 483], [482, 508], [485, 490], [485, 508], [511, 470], [511, 512], [574, 575], [574, 578], [578, 579], [578, 581], [581, 582], [581, 584], [617, 618], [617, 620]], "functions": {"InstanceAuthService.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [35], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "InstanceAuthService.generate_instance_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [59, 61, 62, 65, 66, 69, 72, 83, 85, 92, 101, 102, 108], "excluded_lines": [], "executed_branches": [], "missing_branches": [[61, 62], [61, 65]]}, "InstanceAuthService.validate_instance_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 23, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [124, 126, 128, 129, 134, 137, 138, 144, 147, 148, 149, 150, 151, 157, 160, 161, 162, 167, 169, 173, 175, 176, 182], "excluded_lines": [], "executed_branches": [], "missing_branches": [[128, 129], [128, 137], [137, 138], [137, 147], [148, 149], [148, 160], [150, 151], [150, 160], [161, 162], [161, 169]]}, "InstanceAuthService.revoke_instance_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [197, 199, 201, 202, 205, 206, 209, 211, 215, 222, 223, 229], "excluded_lines": [], "executed_branches": [], "missing_branches": [[201, 202], [201, 205]]}, "InstanceAuthService.rotate_instance_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [245, 247, 250, 254, 258, 264, 265, 271], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "InstanceAuthService.list_instance_tokens": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 17, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [286, 288, 289, 290, 292, 293, 296, 298, 299, 301, 302, 305, 314, 316, 318, 319, 322], "excluded_lines": [], "executed_branches": [], "missing_branches": [[292, 293], [292, 296], [299, 301], [299, 316], [301, 302], [301, 305]]}, "InstanceAuthService.verify_webhook_signature": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 20, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [343, 345, 347, 348, 353, 355, 356, 357, 362, 365, 366, 371, 377, 379, 380, 386, 394, 396, 397, 403], "excluded_lines": [], "executed_branches": [], "missing_branches": [[347, 348], [347, 355], [356, 357], [356, 365], [379, 380], [379, 386]]}, "InstanceAuthService.get_instance_credentials": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [418, 419, 421, 422, 425, 434, 435, 441], "excluded_lines": [], "executed_branches": [], "missing_branches": [[421, 422], [421, 425]]}, "InstanceAuthService.cleanup_expired_tokens": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 37, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 37, "excluded_lines": 0, "num_branches": 14, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 14}, "missing_lines": [455, 456, 457, 460, 461, 463, 465, 466, 468, 470, 471, 472, 474, 475, 477, 478, 481, 482, 483, 484, 485, 490, 491, 492, 494, 500, 501, 508, 511, 512, 513, 515, 522, 529, 530, 531, 534], "excluded_lines": [], "executed_branches": [], "missing_branches": [[460, 461], [460, 463], [470, 471], [470, 515], [471, 472], [471, 474], [477, 478], [477, 511], [482, 483], [482, 508], [485, 490], [485, 508], [511, 470], [511, 512]]}, "InstanceAuthService._generate_secure_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [544, 545], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "InstanceAuthService._generate_webhook_secret": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [555], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "InstanceAuthService._save_instance_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [568, 570, 571, 572, 574, 575, 578, 579, 581, 582, 584, 587, 589, 590, 591, 597], "excluded_lines": [], "executed_branches": [], "missing_branches": [[574, 575], [574, 578], [578, 579], [578, 581], [581, 582], [581, 584]]}, "InstanceAuthService._get_instance_token_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [612, 613, 614, 615, 617, 618, 620, 621, 623, 624, 630], "excluded_lines": [], "executed_branches": [], "missing_branches": [[617, 618], [617, 620]]}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 14, 15, 17, 18, 21, 24, 25, 27, 36, 37, 109, 110, 183, 184, 230, 231, 272, 273, 323, 324, 404, 405, 442, 443, 535, 546, 556, 557, 598, 599], "summary": {"covered_lines": 19, "num_statements": 26, "percent_covered": 73.07692307692308, "percent_covered_display": "73.08", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [13, 16, 19, 22, 28, 536, 547], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"InstanceAuthService": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 169, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 169, "excluded_lines": 0, "num_branches": 50, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 50}, "missing_lines": [35, 59, 61, 62, 65, 66, 69, 72, 83, 85, 92, 101, 102, 108, 124, 126, 128, 129, 134, 137, 138, 144, 147, 148, 149, 150, 151, 157, 160, 161, 162, 167, 169, 173, 175, 176, 182, 197, 199, 201, 202, 205, 206, 209, 211, 215, 222, 223, 229, 245, 247, 250, 254, 258, 264, 265, 271, 286, 288, 289, 290, 292, 293, 296, 298, 299, 301, 302, 305, 314, 316, 318, 319, 322, 343, 345, 347, 348, 353, 355, 356, 357, 362, 365, 366, 371, 377, 379, 380, 386, 394, 396, 397, 403, 418, 419, 421, 422, 425, 434, 435, 441, 455, 456, 457, 460, 461, 463, 465, 466, 468, 470, 471, 472, 474, 475, 477, 478, 481, 482, 483, 484, 485, 490, 491, 492, 494, 500, 501, 508, 511, 512, 513, 515, 522, 529, 530, 531, 534, 544, 545, 555, 568, 570, 571, 572, 574, 575, 578, 579, 581, 582, 584, 587, 589, 590, 591, 597, 612, 613, 614, 615, 617, 618, 620, 621, 623, 624, 630], "excluded_lines": [], "executed_branches": [], "missing_branches": [[61, 62], [61, 65], [128, 129], [128, 137], [137, 138], [137, 147], [148, 149], [148, 160], [150, 151], [150, 160], [161, 162], [161, 169], [201, 202], [201, 205], [292, 293], [292, 296], [299, 301], [299, 316], [301, 302], [301, 305], [347, 348], [347, 355], [356, 357], [356, 365], [379, 380], [379, 386], [421, 422], [421, 425], [460, 461], [460, 463], [470, 471], [470, 515], [471, 472], [471, 474], [477, 478], [477, 511], [482, 483], [482, 508], [485, 490], [485, 508], [511, 470], [511, 512], [574, 575], [574, 578], [578, 579], [578, 581], [581, 582], [581, 584], [617, 618], [617, 620]]}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 14, 15, 17, 18, 21, 24, 25, 27, 36, 37, 109, 110, 183, 184, 230, 231, 272, 273, 323, 324, 404, 405, 442, 443, 535, 546, 556, 557, 598, 599], "summary": {"covered_lines": 19, "num_statements": 26, "percent_covered": 73.07692307692308, "percent_covered_display": "73.08", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [13, 16, 19, 22, 28, 536, 547], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\instance_config_service.py": {"executed_lines": [1, 6, 7, 8, 10, 11, 12, 14, 15, 18, 21, 22, 24, 34, 35, 145, 146, 231, 232, 273, 274, 324, 325, 348, 349, 382, 395, 396, 442, 486, 487, 519, 520, 572, 608], "summary": {"covered_lines": 17, "num_statements": 188, "percent_covered": 6.746031746031746, "percent_covered_display": "6.75", "missing_lines": 171, "excluded_lines": 0, "num_branches": 64, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 64}, "missing_lines": [9, 13, 16, 19, 25, 32, 33, 58, 60, 61, 64, 65, 69, 70, 73, 76, 78, 83, 84, 87, 96, 101, 102, 103, 110, 118, 119, 126, 128, 129, 135, 137, 138, 144, 163, 165, 167, 168, 169, 176, 177, 179, 180, 181, 187, 195, 196, 199, 207, 218, 226, 228, 229, 230, 245, 247, 250, 255, 263, 265, 266, 272, 288, 290, 293, 295, 303, 304, 308, 310, 317, 319, 320, 323, 342, 347, 362, 364, 365, 366, 368, 369, 370, 372, 374, 375, 381, 383, 394, 413, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 441, 443, 453, 454, 455, 456, 458, 459, 462, 475, 476, 477, 479, 481, 482, 485, 500, 501, 502, 503, 505, 506, 507, 509, 511, 512, 518, 532, 539, 540, 541, 543, 544, 545, 546, 547, 548, 550, 553, 554, 560, 561, 562, 564, 565, 566, 567, 568, 569, 571, 575, 579, 581, 591, 593, 594, 595, 597, 598, 601, 602, 603, 604, 607, 610, 611, 612, 615, 617, 619], "excluded_lines": [], "executed_branches": [], "missing_branches": [[60, 61], [60, 64], [64, 65], [64, 69], [69, 70], [69, 73], [83, 84], [83, 87], [101, 102], [101, 118], [167, 168], [167, 176], [179, 180], [179, 207], [303, 304], [303, 310], [368, 369], [368, 372], [429, 430], [429, 441], [431, 432], [431, 439], [433, 434], [433, 437], [434, 433], [434, 435], [437, 431], [437, 438], [458, 459], [458, 462], [475, 476], [475, 479], [505, 506], [505, 509], [539, 540], [539, 543], [540, 539], [540, 541], [543, 544], [543, 550], [544, 543], [544, 545], [545, 546], [545, 547], [547, 543], [547, 548], [550, 553], [550, 579], [560, 561], [560, 579], [561, 562], [561, 564], [565, 566], [565, 571], [566, 567], [566, 568], [568, 569], [568, 571], [571, 560], [571, 572], [572, 560], [572, 575], [597, 598], [597, 601]], "functions": {"InstanceConfigService.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [32, 33], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "InstanceConfigService.push_config_to_instance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 27, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [58, 60, 61, 64, 65, 69, 70, 73, 76, 78, 83, 84, 87, 96, 101, 102, 103, 110, 118, 119, 126, 128, 129, 135, 137, 138, 144], "excluded_lines": [], "executed_branches": [], "missing_branches": [[60, 61], [60, 64], [64, 65], [64, 69], [69, 70], [69, 73], [83, 84], [83, 87], [101, 102], [101, 118]]}, "InstanceConfigService.broadcast_config_update": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 20, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [163, 165, 167, 168, 169, 176, 177, 179, 180, 181, 187, 195, 196, 199, 207, 218, 226, 228, 229, 230], "excluded_lines": [], "executed_branches": [], "missing_branches": [[167, 168], [167, 176], [179, 180], [179, 207]]}, "InstanceConfigService.get_instance_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [245, 247, 250, 255, 263, 265, 266, 272], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "InstanceConfigService.update_tenant_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [288, 290, 293, 295, 303, 304, 308, 310, 317, 319, 320, 323], "excluded_lines": [], "executed_branches": [], "missing_branches": [[303, 304], [303, 310]]}, "InstanceConfigService._get_instance_endpoint": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [342, 347], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "InstanceConfigService._get_instance_auth_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [362, 364, 365, 366, 368, 369, 370, 372, 374, 375, 381], "excluded_lines": [], "executed_branches": [], "missing_branches": [[368, 369], [368, 372]]}, "InstanceConfigService._get_config_version": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [394], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "InstanceConfigService._get_tenant_instances": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [413, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 441], "excluded_lines": [], "executed_branches": [], "missing_branches": [[429, 430], [429, 441], [431, 432], [431, 439], [433, 434], [433, 437], [434, 433], [434, 435], [437, 431], [437, 438]]}, "InstanceConfigService._get_tenant_base_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [453, 454, 455, 456, 458, 459, 462, 475, 476, 477, 479, 481, 482, 485], "excluded_lines": [], "executed_branches": [], "missing_branches": [[458, 459], [458, 462], [475, 476], [475, 479]]}, "InstanceConfigService._get_instance_specific_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [500, 501, 502, 503, 505, 506, 507, 509, 511, 512, 518], "excluded_lines": [], "executed_branches": [], "missing_branches": [[505, 506], [505, 509]]}, "InstanceConfigService._validate_config_updates": {"executed_lines": [572], "summary": {"covered_lines": 1, "num_statements": 26, "percent_covered": 1.8518518518518519, "percent_covered_display": "1.85", "missing_lines": 25, "excluded_lines": 0, "num_branches": 28, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 28}, "missing_lines": [532, 539, 540, 541, 543, 544, 545, 546, 547, 548, 550, 553, 554, 560, 561, 562, 564, 565, 566, 567, 568, 569, 571, 575, 579], "excluded_lines": [], "executed_branches": [], "missing_branches": [[539, 540], [539, 543], [540, 539], [540, 541], [543, 544], [543, 550], [544, 543], [544, 545], [545, 546], [545, 547], [547, 543], [547, 548], [550, 553], [550, 579], [560, 561], [560, 579], [561, 562], [561, 564], [565, 566], [565, 571], [566, 567], [566, 568], [568, 569], [568, 571], [571, 560], [571, 572], [572, 560], [572, 575]]}, "InstanceConfigService._update_tenant_configuration": {"executed_lines": [608], "summary": {"covered_lines": 1, "num_statements": 16, "percent_covered": 5.555555555555555, "percent_covered_display": "5.56", "missing_lines": 15, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [591, 593, 594, 595, 597, 598, 601, 602, 603, 604, 607, 610, 611, 612, 615], "excluded_lines": [], "executed_branches": [], "missing_branches": [[597, 598], [597, 601]]}, "InstanceConfigService.close": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [619], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 7, 8, 10, 11, 12, 14, 15, 18, 21, 22, 24, 34, 35, 145, 146, 231, 232, 273, 274, 324, 325, 348, 349, 382, 395, 396, 442, 486, 487, 519, 520], "summary": {"covered_lines": 15, "num_statements": 24, "percent_covered": 62.5, "percent_covered_display": "62.50", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 13, 16, 19, 25, 383, 443, 581, 617], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"InstanceConfigService": {"executed_lines": [572, 608], "summary": {"covered_lines": 2, "num_statements": 164, "percent_covered": 0.8771929824561403, "percent_covered_display": "0.88", "missing_lines": 162, "excluded_lines": 0, "num_branches": 64, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 64}, "missing_lines": [32, 33, 58, 60, 61, 64, 65, 69, 70, 73, 76, 78, 83, 84, 87, 96, 101, 102, 103, 110, 118, 119, 126, 128, 129, 135, 137, 138, 144, 163, 165, 167, 168, 169, 176, 177, 179, 180, 181, 187, 195, 196, 199, 207, 218, 226, 228, 229, 230, 245, 247, 250, 255, 263, 265, 266, 272, 288, 290, 293, 295, 303, 304, 308, 310, 317, 319, 320, 323, 342, 347, 362, 364, 365, 366, 368, 369, 370, 372, 374, 375, 381, 394, 413, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 441, 453, 454, 455, 456, 458, 459, 462, 475, 476, 477, 479, 481, 482, 485, 500, 501, 502, 503, 505, 506, 507, 509, 511, 512, 518, 532, 539, 540, 541, 543, 544, 545, 546, 547, 548, 550, 553, 554, 560, 561, 562, 564, 565, 566, 567, 568, 569, 571, 575, 579, 591, 593, 594, 595, 597, 598, 601, 602, 603, 604, 607, 610, 611, 612, 615, 619], "excluded_lines": [], "executed_branches": [], "missing_branches": [[60, 61], [60, 64], [64, 65], [64, 69], [69, 70], [69, 73], [83, 84], [83, 87], [101, 102], [101, 118], [167, 168], [167, 176], [179, 180], [179, 207], [303, 304], [303, 310], [368, 369], [368, 372], [429, 430], [429, 441], [431, 432], [431, 439], [433, 434], [433, 437], [434, 433], [434, 435], [437, 431], [437, 438], [458, 459], [458, 462], [475, 476], [475, 479], [505, 506], [505, 509], [539, 540], [539, 543], [540, 539], [540, 541], [543, 544], [543, 550], [544, 543], [544, 545], [545, 546], [545, 547], [547, 543], [547, 548], [550, 553], [550, 579], [560, 561], [560, 579], [561, 562], [561, 564], [565, 566], [565, 571], [566, 567], [566, 568], [568, 569], [568, 571], [571, 560], [571, 572], [572, 560], [572, 575], [597, 598], [597, 601]]}, "": {"executed_lines": [1, 6, 7, 8, 10, 11, 12, 14, 15, 18, 21, 22, 24, 34, 35, 145, 146, 231, 232, 273, 274, 324, 325, 348, 349, 382, 395, 396, 442, 486, 487, 519, 520], "summary": {"covered_lines": 15, "num_statements": 24, "percent_covered": 62.5, "percent_covered_display": "62.50", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 13, 16, 19, 25, 383, 443, 581, 617], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\llm\\base_provider.py": {"executed_lines": [1, 6, 7, 8, 9, 11, 14, 15, 16, 18, 19, 20, 23, 24, 25, 27, 28, 29, 30, 33, 34, 35, 37, 38, 39, 40, 41, 42, 43, 44, 47, 48, 50, 52, 60, 61, 63, 64, 65, 69, 70, 71, 75, 76, 77, 94, 95, 96, 113, 114, 123, 124, 136, 137, 172, 186, 187, 230, 231, 233, 247, 248, 250, 253, 254, 256, 259, 260, 262], "summary": {"covered_lines": 33, "num_statements": 73, "percent_covered": 37.07865168539326, "percent_covered_display": "37.08", "missing_lines": 40, "excluded_lines": 67, "num_branches": 16, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 16}, "missing_lines": [10, 12, 21, 31, 45, 51, 62, 150, 153, 154, 157, 158, 159, 171, 173, 184, 185, 201, 202, 205, 206, 208, 209, 212, 213, 214, 215, 216, 217, 220, 221, 222, 223, 224, 226, 228, 234, 245, 246, 265], "excluded_lines": [64, 65, 66, 67, 68, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 114, 115, 116, 117, 118, 119, 120, 121, 122, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135], "executed_branches": [], "missing_branches": [[153, 154], [153, 157], [157, 158], [157, 171], [201, 202], [201, 205], [212, 213], [212, 220], [213, 214], [213, 220], [215, 213], [215, 216], [220, 221], [220, 228], [222, 223], [222, 226]], "functions": {"BaseLLMProvider.__init__": {"executed_lines": [52, 60, 61], "summary": {"covered_lines": 2, "num_statements": 3, "percent_covered": 66.66666666666667, "percent_covered_display": "66.67", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [62], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BaseLLMProvider.provider_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 2, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [67, 68], "executed_branches": [], "missing_branches": []}, "BaseLLMProvider.supported_models": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 2, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [73, 74], "executed_branches": [], "missing_branches": []}, "BaseLLMProvider.generate_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 14, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93], "executed_branches": [], "missing_branches": []}, "BaseLLMProvider.generate_stream_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 14, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112], "executed_branches": [], "missing_branches": []}, "BaseLLMProvider.validate_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 7, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [116, 117, 118, 119, 120, 121, 122], "executed_branches": [], "missing_branches": []}, "BaseLLMProvider.get_token_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 10, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [126, 127, 128, 129, 130, 131, 132, 133, 134, 135], "executed_branches": [], "missing_branches": []}, "BaseLLMProvider.messages_from_session_history": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [150, 153, 154, 157, 158, 159, 171], "excluded_lines": [], "executed_branches": [], "missing_branches": [[153, 154], [153, 157], [157, 158], [157, 171]]}, "BaseLLMProvider.estimate_context_tokens": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [184, 185], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BaseLLMProvider.truncate_messages_by_tokens": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 19, "excluded_lines": 0, "num_branches": 12, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 12}, "missing_lines": [201, 202, 205, 206, 208, 209, 212, 213, 214, 215, 216, 217, 220, 221, 222, 223, 224, 226, 228], "excluded_lines": [], "executed_branches": [], "missing_branches": [[201, 202], [201, 205], [212, 213], [212, 220], [213, 214], [213, 220], [215, 213], [215, 216], [220, 221], [220, 228], [222, 223], [222, 226]]}, "LLMProviderError.__init__": {"executed_lines": [247], "summary": {"covered_lines": 1, "num_statements": 3, "percent_covered": 33.333333333333336, "percent_covered_display": "33.33", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [245, 246], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 11, 14, 15, 16, 18, 19, 20, 23, 24, 25, 27, 28, 29, 30, 33, 34, 35, 37, 38, 39, 40, 41, 42, 43, 44, 47, 48, 50, 63, 64, 65, 69, 70, 71, 75, 76, 77, 94, 95, 96, 113, 114, 123, 124, 136, 137, 172, 186, 187, 230, 231, 233, 248, 250, 253, 254, 256, 259, 260, 262], "summary": {"covered_lines": 30, "num_statements": 39, "percent_covered": 76.92307692307692, "percent_covered_display": "76.92", "missing_lines": 9, "excluded_lines": 18, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 12, 21, 31, 45, 51, 173, 234, 265], "excluded_lines": [64, 65, 66, 70, 71, 72, 76, 77, 78, 79, 95, 96, 97, 98, 114, 115, 124, 125], "executed_branches": [], "missing_branches": []}}, "classes": {"LLMMessage": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "LLMResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "LLMConfig": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BaseLLMProvider": {"executed_lines": [52, 60, 61], "summary": {"covered_lines": 2, "num_statements": 31, "percent_covered": 4.25531914893617, "percent_covered_display": "4.26", "missing_lines": 29, "excluded_lines": 49, "num_branches": 16, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 16}, "missing_lines": [62, 150, 153, 154, 157, 158, 159, 171, 184, 185, 201, 202, 205, 206, 208, 209, 212, 213, 214, 215, 216, 217, 220, 221, 222, 223, 224, 226, 228], "excluded_lines": [67, 68, 73, 74, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 116, 117, 118, 119, 120, 121, 122, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135], "executed_branches": [], "missing_branches": [[153, 154], [153, 157], [157, 158], [157, 171], [201, 202], [201, 205], [212, 213], [212, 220], [213, 214], [213, 220], [215, 213], [215, 216], [220, 221], [220, 228], [222, 223], [222, 226]]}, "LLMProviderError": {"executed_lines": [247], "summary": {"covered_lines": 1, "num_statements": 3, "percent_covered": 33.333333333333336, "percent_covered_display": "33.33", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [245, 246], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "LLMRateLimitError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "LLMQuotaExceededError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "LLMInvalidRequestError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 11, 14, 15, 16, 18, 19, 20, 23, 24, 25, 27, 28, 29, 30, 33, 34, 35, 37, 38, 39, 40, 41, 42, 43, 44, 47, 48, 50, 63, 64, 65, 69, 70, 71, 75, 76, 77, 94, 95, 96, 113, 114, 123, 124, 136, 137, 172, 186, 187, 230, 231, 233, 248, 250, 253, 254, 256, 259, 260, 262], "summary": {"covered_lines": 30, "num_statements": 39, "percent_covered": 76.92307692307692, "percent_covered_display": "76.92", "missing_lines": 9, "excluded_lines": 18, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 12, 21, 31, 45, 51, 173, 234, 265], "excluded_lines": [64, 65, 66, 70, 71, 72, 76, 77, 78, 79, 95, 96, 97, 98, 114, 115, 124, 125], "executed_branches": [], "missing_branches": []}}}, "app\\services\\llm\\dify_provider.py": {"executed_lines": [1, 6, 7, 8, 9, 11, 12, 14, 16, 28, 31, 32, 34, 35, 59, 60, 64, 65, 76, 88, 94, 103, 104, 134, 135, 184, 203, 240, 243, 295, 309, 347, 376], "summary": {"covered_lines": 14, "num_statements": 147, "percent_covered": 7.106598984771574, "percent_covered_display": "7.11", "missing_lines": 133, "excluded_lines": 0, "num_branches": 50, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 50}, "missing_lines": [10, 13, 15, 17, 29, 53, 54, 55, 58, 61, 63, 66, 68, 77, 79, 80, 87, 89, 91, 92, 93, 95, 97, 98, 99, 100, 102, 117, 118, 119, 122, 125, 126, 129, 131, 132, 133, 148, 149, 150, 153, 156, 158, 159, 160, 162, 163, 164, 166, 167, 169, 170, 173, 174, 175, 176, 178, 179, 181, 182, 183, 185, 192, 194, 197, 198, 202, 213, 215, 216, 217, 219, 229, 232, 234, 236, 255, 256, 257, 259, 262, 263, 264, 265, 266, 267, 270, 280, 281, 284, 285, 286, 287, 290, 291, 293, 294, 296, 298, 310, 312, 322, 323, 325, 326, 328, 329, 331, 346, 351, 361, 362, 363, 364, 365, 366, 367, 368, 370, 371, 372, 373, 374, 375, 377, 379, 389, 390, 391, 392, 395, 396, 398], "excluded_lines": [], "executed_branches": [], "missing_branches": [[79, 80], [79, 87], [91, -89], [91, 92], [97, -95], [97, 98], [159, 160], [159, 162], [162, -135], [162, 163], [163, 162], [163, 164], [166, 167], [166, 169], [173, 162], [173, 174], [175, 162], [175, 176], [256, 257], [256, 259], [263, 264], [263, 270], [264, 265], [264, 266], [266, 263], [266, 267], [280, 281], [280, 284], [284, 285], [284, 286], [286, 287], [286, 290], [290, 291], [290, 293], [293, 294], [293, 296], [322, 323], [322, 325], [370, 371], [370, 372], [372, 373], [372, 374], [374, 375], [374, 377], [389, 390], [389, 391], [391, 392], [391, 395], [395, 396], [395, 398]], "functions": {"DifyProvider.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [53, 54, 55, 58], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DifyProvider.provider_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [63], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DifyProvider.supported_models": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [68], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DifyProvider._get_client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [79, 80, 87], "excluded_lines": [], "executed_branches": [], "missing_branches": [[79, 80], [79, 87]]}, "DifyProvider._close_client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [91, 92, 93], "excluded_lines": [], "executed_branches": [], "missing_branches": [[91, -89], [91, 92]]}, "DifyProvider.__del__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [97, 98, 99, 100, 102], "excluded_lines": [], "executed_branches": [], "missing_branches": [[97, -95], [97, 98]]}, "DifyProvider.generate_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [117, 118, 119, 122, 125, 126, 129, 131, 132, 133], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DifyProvider.generate_stream_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 24, "excluded_lines": 0, "num_branches": 12, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 12}, "missing_lines": [148, 149, 150, 153, 156, 158, 159, 160, 162, 163, 164, 166, 167, 169, 170, 173, 174, 175, 176, 178, 179, 181, 182, 183], "excluded_lines": [], "executed_branches": [], "missing_branches": [[159, 160], [159, 162], [162, -135], [162, 163], [163, 162], [163, 164], [166, 167], [166, 169], [173, 162], [173, 174], [175, 162], [175, 176]]}, "DifyProvider.validate_config": {"executed_lines": [203], "summary": {"covered_lines": 1, "num_statements": 10, "percent_covered": 10.0, "percent_covered_display": "10.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [192, 194, 197, 198, 202, 213, 215, 216, 217], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DifyProvider.get_token_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [229, 232, 234, 236], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DifyProvider._build_chat_request": {"executed_lines": [243, 295], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 22, "excluded_lines": 0, "num_branches": 18, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 18}, "missing_lines": [255, 256, 257, 259, 262, 263, 264, 265, 266, 267, 270, 280, 281, 284, 285, 286, 287, 290, 291, 293, 294, 296], "excluded_lines": [], "executed_branches": [], "missing_branches": [[256, 257], [256, 259], [263, 264], [263, 270], [264, 265], [264, 266], [266, 263], [266, 267], [280, 281], [280, 284], [284, 285], [284, 286], [286, 287], [286, 290], [290, 291], [290, 293], [293, 294], [293, 296]]}, "DifyProvider._generate_conversation_id": {"executed_lines": [309], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [310], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DifyProvider._parse_chat_response": {"executed_lines": [347], "summary": {"covered_lines": 1, "num_statements": 9, "percent_covered": 9.090909090909092, "percent_covered_display": "9.09", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [322, 323, 325, 326, 328, 329, 331, 346], "excluded_lines": [], "executed_branches": [], "missing_branches": [[322, 323], [322, 325]]}, "DifyProvider._handle_http_error": {"executed_lines": [376], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [361, 362, 363, 364, 365, 366, 367, 368, 370, 371, 372, 373, 374, 375, 377], "excluded_lines": [], "executed_branches": [], "missing_branches": [[370, 371], [370, 372], [372, 373], [372, 374], [374, 375], [374, 377]]}, "DifyProvider._handle_exception": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [389, 390, 391, 392, 395, 396, 398], "excluded_lines": [], "executed_branches": [], "missing_branches": [[389, 390], [389, 391], [391, 392], [391, 395], [395, 396], [395, 398]]}, "": {"executed_lines": [1, 6, 7, 8, 9, 11, 12, 14, 16, 28, 31, 32, 34, 35, 59, 60, 64, 65, 76, 88, 94, 103, 104, 134, 135, 184, 240], "summary": {"covered_lines": 11, "num_statements": 27, "percent_covered": 40.74074074074074, "percent_covered_display": "40.74", "missing_lines": 16, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 13, 15, 17, 29, 61, 66, 77, 89, 95, 185, 219, 298, 312, 351, 379], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"DifyProvider": {"executed_lines": [203, 243, 295, 309, 347, 376], "summary": {"covered_lines": 3, "num_statements": 120, "percent_covered": 1.7647058823529411, "percent_covered_display": "1.76", "missing_lines": 117, "excluded_lines": 0, "num_branches": 50, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 50}, "missing_lines": [53, 54, 55, 58, 63, 68, 79, 80, 87, 91, 92, 93, 97, 98, 99, 100, 102, 117, 118, 119, 122, 125, 126, 129, 131, 132, 133, 148, 149, 150, 153, 156, 158, 159, 160, 162, 163, 164, 166, 167, 169, 170, 173, 174, 175, 176, 178, 179, 181, 182, 183, 192, 194, 197, 198, 202, 213, 215, 216, 217, 229, 232, 234, 236, 255, 256, 257, 259, 262, 263, 264, 265, 266, 267, 270, 280, 281, 284, 285, 286, 287, 290, 291, 293, 294, 296, 310, 322, 323, 325, 326, 328, 329, 331, 346, 361, 362, 363, 364, 365, 366, 367, 368, 370, 371, 372, 373, 374, 375, 377, 389, 390, 391, 392, 395, 396, 398], "excluded_lines": [], "executed_branches": [], "missing_branches": [[79, 80], [79, 87], [91, -89], [91, 92], [97, -95], [97, 98], [159, 160], [159, 162], [162, -135], [162, 163], [163, 162], [163, 164], [166, 167], [166, 169], [173, 162], [173, 174], [175, 162], [175, 176], [256, 257], [256, 259], [263, 264], [263, 270], [264, 265], [264, 266], [266, 263], [266, 267], [280, 281], [280, 284], [284, 285], [284, 286], [286, 287], [286, 290], [290, 291], [290, 293], [293, 294], [293, 296], [322, 323], [322, 325], [370, 371], [370, 372], [372, 373], [372, 374], [374, 375], [374, 377], [389, 390], [389, 391], [391, 392], [391, 395], [395, 396], [395, 398]]}, "": {"executed_lines": [1, 6, 7, 8, 9, 11, 12, 14, 16, 28, 31, 32, 34, 35, 59, 60, 64, 65, 76, 88, 94, 103, 104, 134, 135, 184, 240], "summary": {"covered_lines": 11, "num_statements": 27, "percent_covered": 40.74074074074074, "percent_covered_display": "40.74", "missing_lines": 16, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 13, 15, 17, 29, 61, 66, 77, 89, 95, 185, 219, 298, 312, 351, 379], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\llm\\mock_provider.py": {"executed_lines": [1, 6, 7, 9, 11, 14, 17, 18, 20, 28, 29, 31, 32, 36, 37, 41, 42, 106, 107, 133, 142, 155, 164, 173], "summary": {"covered_lines": 7, "num_statements": 47, "percent_covered": 12.727272727272727, "percent_covered_display": "12.73", "missing_lines": 40, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [8, 10, 12, 15, 21, 30, 33, 35, 38, 40, 56, 57, 60, 61, 68, 69, 74, 75, 82, 87, 88, 90, 121, 122, 125, 126, 127, 128, 130, 132, 134, 141, 143, 154, 156, 163, 165, 172, 174, 181], "excluded_lines": [], "executed_branches": [], "missing_branches": [[60, 61], [60, 68], [68, 69], [68, 74], [74, 75], [74, 82], [126, -107], [126, 127]], "functions": {"MockLLMProvider.__init__": {"executed_lines": [28, 29], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [30], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MockLLMProvider.provider_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [35], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MockLLMProvider.supported_models": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [40], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MockLLMProvider.generate_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [56, 57, 60, 61, 68, 69, 74, 75, 82, 87, 88, 90], "excluded_lines": [], "executed_branches": [], "missing_branches": [[60, 61], [60, 68], [68, 69], [68, 74], [74, 75], [74, 82]]}, "MockLLMProvider.generate_stream_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [121, 122, 125, 126, 127, 128, 130, 132], "excluded_lines": [], "executed_branches": [], "missing_branches": [[126, -107], [126, 127]]}, "MockLLMProvider.validate_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [141], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MockLLMProvider.get_token_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [154], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MockLLMProvider.validate_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [163], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MockLLMProvider.get_available_models": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [172], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MockLLMProvider.get_cost_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [181], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 7, 9, 11, 14, 17, 18, 20, 31, 32, 36, 37, 41, 42, 106, 107, 133, 142, 155, 164, 173], "summary": {"covered_lines": 6, "num_statements": 18, "percent_covered": 33.333333333333336, "percent_covered_display": "33.33", "missing_lines": 12, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 10, 12, 15, 21, 33, 38, 134, 143, 156, 165, 174], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"MockLLMProvider": {"executed_lines": [28, 29], "summary": {"covered_lines": 1, "num_statements": 29, "percent_covered": 2.7027027027027026, "percent_covered_display": "2.70", "missing_lines": 28, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [30, 35, 40, 56, 57, 60, 61, 68, 69, 74, 75, 82, 87, 88, 90, 121, 122, 125, 126, 127, 128, 130, 132, 141, 154, 163, 172, 181], "excluded_lines": [], "executed_branches": [], "missing_branches": [[60, 61], [60, 68], [68, 69], [68, 74], [74, 75], [74, 82], [126, -107], [126, 127]]}, "": {"executed_lines": [1, 6, 7, 9, 11, 14, 17, 18, 20, 31, 32, 36, 37, 41, 42, 106, 107, 133, 142, 155, 164, 173], "summary": {"covered_lines": 6, "num_statements": 18, "percent_covered": 33.333333333333336, "percent_covered_display": "33.33", "missing_lines": 12, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 10, 12, 15, 21, 33, 38, 134, 143, 156, 165, 174], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\llm\\openai_provider.py": {"executed_lines": [1, 6, 7, 8, 9, 11, 12, 14, 16, 28, 31, 32, 34, 35, 59, 60, 64, 65, 78, 90, 96, 107, 110, 139, 140, 141, 191, 214, 254, 255, 256, 297, 336, 372], "summary": {"covered_lines": 18, "num_statements": 153, "percent_covered": 9.137055837563452, "percent_covered_display": "9.14", "missing_lines": 135, "excluded_lines": 0, "num_branches": 44, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 44}, "missing_lines": [10, 13, 15, 17, 29, 53, 54, 55, 58, 61, 63, 66, 68, 79, 81, 82, 89, 91, 93, 94, 95, 97, 99, 100, 101, 102, 105, 112, 125, 126, 127, 130, 133, 134, 137, 143, 156, 157, 158, 161, 164, 166, 167, 168, 170, 171, 172, 174, 175, 177, 178, 181, 182, 183, 184, 185, 186, 188, 189, 192, 193, 195, 202, 204, 207, 208, 211, 213, 224, 226, 227, 228, 230, 240, 242, 243, 246, 247, 249, 251, 253, 257, 259, 261, 263, 267, 282, 283, 284, 287, 298, 299, 301, 302, 305, 306, 308, 310, 320, 321, 323, 324, 326, 327, 328, 330, 331, 332, 334, 351, 352, 356, 366, 367, 368, 369, 370, 371, 373, 374, 376, 377, 378, 379, 380, 381, 383, 385, 395, 396, 397, 398, 401, 402, 404], "excluded_lines": [], "executed_branches": [], "missing_branches": [[81, 82], [81, 89], [93, -91], [93, 94], [99, -97], [99, 100], [167, 168], [167, 170], [170, -143], [170, 171], [171, 170], [171, 172], [174, 175], [174, 177], [182, 170], [182, 183], [185, 170], [185, 186], [246, 247], [246, 249], [283, 284], [283, 287], [298, 299], [298, 301], [301, 302], [301, 305], [305, 306], [305, 308], [320, 321], [320, 323], [327, 328], [327, 330], [376, 377], [376, 378], [378, 379], [378, 380], [380, 381], [380, 383], [395, 396], [395, 397], [397, 398], [397, 401], [401, 402], [401, 404]], "functions": {"OpenAIProvider.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [53, 54, 55, 58], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "OpenAIProvider.provider_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [63], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "OpenAIProvider.supported_models": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [68], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "OpenAIProvider._get_client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [81, 82, 89], "excluded_lines": [], "executed_branches": [], "missing_branches": [[81, 82], [81, 89]]}, "OpenAIProvider._close_client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [93, 94, 95], "excluded_lines": [], "executed_branches": [], "missing_branches": [[93, -91], [93, 94]]}, "OpenAIProvider.__del__": {"executed_lines": [107, 110], "summary": {"covered_lines": 2, "num_statements": 7, "percent_covered": 22.22222222222222, "percent_covered_display": "22.22", "missing_lines": 5, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [99, 100, 101, 102, 105], "excluded_lines": [], "executed_branches": [], "missing_branches": [[99, -97], [99, 100]]}, "OpenAIProvider.generate_response": {"executed_lines": [139, 140, 141], "summary": {"covered_lines": 3, "num_statements": 10, "percent_covered": 30.0, "percent_covered_display": "30.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [125, 126, 127, 130, 133, 134, 137], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "OpenAIProvider.generate_stream_response": {"executed_lines": [191], "summary": {"covered_lines": 1, "num_statements": 26, "percent_covered": 2.6315789473684212, "percent_covered_display": "2.63", "missing_lines": 25, "excluded_lines": 0, "num_branches": 12, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 12}, "missing_lines": [156, 157, 158, 161, 164, 166, 167, 168, 170, 171, 172, 174, 175, 177, 178, 181, 182, 183, 184, 185, 186, 188, 189, 192, 193], "excluded_lines": [], "executed_branches": [], "missing_branches": [[167, 168], [167, 170], [170, -143], [170, 171], [171, 170], [171, 172], [174, 175], [174, 177], [182, 170], [182, 183], [185, 170], [185, 186]]}, "OpenAIProvider.validate_config": {"executed_lines": [214], "summary": {"covered_lines": 1, "num_statements": 11, "percent_covered": 9.090909090909092, "percent_covered_display": "9.09", "missing_lines": 10, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [202, 204, 207, 208, 211, 213, 224, 226, 227, 228], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "OpenAIProvider.get_token_count": {"executed_lines": [254, 255, 256], "summary": {"covered_lines": 1, "num_statements": 13, "percent_covered": 6.666666666666667, "percent_covered_display": "6.67", "missing_lines": 12, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [240, 242, 243, 246, 247, 249, 251, 253, 257, 259, 261, 263], "excluded_lines": [], "executed_branches": [], "missing_branches": [[246, 247], [246, 249]]}, "OpenAIProvider._build_chat_request": {"executed_lines": [297], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [282, 283, 284, 287, 298, 299, 301, 302, 305, 306, 308], "excluded_lines": [], "executed_branches": [], "missing_branches": [[283, 284], [283, 287], [298, 299], [298, 301], [301, 302], [301, 305], [305, 306], [305, 308]]}, "OpenAIProvider._parse_chat_response": {"executed_lines": [336], "summary": {"covered_lines": 1, "num_statements": 14, "percent_covered": 5.555555555555555, "percent_covered_display": "5.56", "missing_lines": 13, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [320, 321, 323, 324, 326, 327, 328, 330, 331, 332, 334, 351, 352], "excluded_lines": [], "executed_branches": [], "missing_branches": [[320, 321], [320, 323], [327, 328], [327, 330]]}, "OpenAIProvider._handle_http_error": {"executed_lines": [372], "summary": {"covered_lines": 1, "num_statements": 16, "percent_covered": 4.545454545454546, "percent_covered_display": "4.55", "missing_lines": 15, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [366, 367, 368, 369, 370, 371, 373, 374, 376, 377, 378, 379, 380, 381, 383], "excluded_lines": [], "executed_branches": [], "missing_branches": [[376, 377], [376, 378], [378, 379], [378, 380], [380, 381], [380, 383]]}, "OpenAIProvider._handle_exception": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [395, 396, 397, 398, 401, 402, 404], "excluded_lines": [], "executed_branches": [], "missing_branches": [[395, 396], [395, 397], [397, 398], [397, 401], [401, 402], [401, 404]]}, "": {"executed_lines": [1, 6, 7, 8, 9, 11, 12, 14, 16, 28, 31, 32, 34, 35, 59, 60, 64, 65, 78, 90, 96], "summary": {"covered_lines": 8, "num_statements": 26, "percent_covered": 30.76923076923077, "percent_covered_display": "30.77", "missing_lines": 18, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 13, 15, 17, 29, 61, 66, 79, 91, 97, 112, 143, 195, 230, 267, 310, 356, 385], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"OpenAIProvider": {"executed_lines": [107, 110, 139, 140, 141, 191, 214, 254, 255, 256, 297, 336, 372], "summary": {"covered_lines": 10, "num_statements": 127, "percent_covered": 5.847953216374269, "percent_covered_display": "5.85", "missing_lines": 117, "excluded_lines": 0, "num_branches": 44, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 44}, "missing_lines": [53, 54, 55, 58, 63, 68, 81, 82, 89, 93, 94, 95, 99, 100, 101, 102, 105, 125, 126, 127, 130, 133, 134, 137, 156, 157, 158, 161, 164, 166, 167, 168, 170, 171, 172, 174, 175, 177, 178, 181, 182, 183, 184, 185, 186, 188, 189, 192, 193, 202, 204, 207, 208, 211, 213, 224, 226, 227, 228, 240, 242, 243, 246, 247, 249, 251, 253, 257, 259, 261, 263, 282, 283, 284, 287, 298, 299, 301, 302, 305, 306, 308, 320, 321, 323, 324, 326, 327, 328, 330, 331, 332, 334, 351, 352, 366, 367, 368, 369, 370, 371, 373, 374, 376, 377, 378, 379, 380, 381, 383, 395, 396, 397, 398, 401, 402, 404], "excluded_lines": [], "executed_branches": [], "missing_branches": [[81, 82], [81, 89], [93, -91], [93, 94], [99, -97], [99, 100], [167, 168], [167, 170], [170, -143], [170, 171], [171, 170], [171, 172], [174, 175], [174, 177], [182, 170], [182, 183], [185, 170], [185, 186], [246, 247], [246, 249], [283, 284], [283, 287], [298, 299], [298, 301], [301, 302], [301, 305], [305, 306], [305, 308], [320, 321], [320, 323], [327, 328], [327, 330], [376, 377], [376, 378], [378, 379], [378, 380], [380, 381], [380, 383], [395, 396], [395, 397], [397, 398], [397, 401], [401, 402], [401, 404]]}, "": {"executed_lines": [1, 6, 7, 8, 9, 11, 12, 14, 16, 28, 31, 32, 34, 35, 59, 60, 64, 65, 78, 90, 96], "summary": {"covered_lines": 8, "num_statements": 26, "percent_covered": 30.76923076923077, "percent_covered_display": "30.77", "missing_lines": 18, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [10, 13, 15, 17, 29, 61, 66, 79, 91, 97, 112, 143, 195, 230, 267, 310, 356, 385], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\message_service.py": {"executed_lines": [1, 7, 8, 9, 10, 12, 13, 14, 16, 17, 18, 19, 20, 22, 25, 26, 28, 30, 36, 38, 39, 247, 248, 368, 369, 454, 461, 465, 466, 536, 542, 594, 600, 604, 672], "summary": {"covered_lines": 25, "num_statements": 188, "percent_covered": 11.363636363636363, "percent_covered_display": "11.36", "missing_lines": 163, "excluded_lines": 0, "num_branches": 32, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 32}, "missing_lines": [21, 23, 29, 37, 56, 58, 61, 62, 68, 69, 74, 76, 78, 81, 82, 83, 84, 85, 87, 89, 90, 93, 112, 115, 116, 121, 122, 124, 125, 126, 134, 137, 138, 139, 140, 141, 142, 149, 150, 156, 157, 158, 159, 160, 161, 171, 173, 190, 200, 201, 202, 203, 204, 205, 206, 213, 231, 232, 233, 234, 235, 236, 243, 274, 276, 277, 278, 284, 289, 290, 292, 293, 295, 296, 299, 307, 308, 310, 320, 321, 322, 324, 325, 326, 327, 334, 351, 353, 355, 356, 357, 358, 364, 387, 388, 407, 412, 420, 432, 441, 449, 451, 452, 453, 494, 496, 499, 500, 502, 503, 505, 506, 508, 509, 511, 512, 515, 523, 524, 526, 533, 535, 546, 559, 561, 564, 565, 567, 568, 574, 575, 577, 578, 580, 587, 589, 590, 591, 592, 593, 605, 621, 622, 624, 625, 626, 627, 630, 631, 632, 635, 640, 641, 644, 649, 650, 652, 662, 668, 670, 671, 678, 687], "excluded_lines": [], "executed_branches": [], "missing_branches": [[61, 62], [61, 68], [74, 76], [74, 93], [121, 122], [121, 124], [277, 278], [277, 284], [289, 290], [289, 292], [292, 293], [292, 295], [295, 296], [295, 299], [321, 322], [321, 353], [499, 500], [499, 502], [502, 503], [502, 505], [505, 506], [505, 508], [508, 509], [508, 511], [511, 512], [511, 515], [567, 568], [567, 574], [624, 625], [624, 626], [626, 627], [626, 630]], "functions": {"MessageService.__init__": {"executed_lines": [30, 36], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [37], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageService.store_message": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 59, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 59, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [56, 58, 61, 62, 68, 69, 74, 76, 78, 81, 82, 83, 84, 85, 87, 89, 90, 93, 112, 115, 116, 121, 122, 124, 125, 126, 134, 137, 138, 139, 140, 141, 142, 149, 150, 156, 157, 158, 159, 160, 161, 171, 173, 190, 200, 201, 202, 203, 204, 205, 206, 213, 231, 232, 233, 234, 235, 236, 243], "excluded_lines": [], "executed_branches": [], "missing_branches": [[61, 62], [61, 68], [74, 76], [74, 93], [121, 122], [121, 124]]}, "MessageService.get_session_messages": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 30, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [274, 276, 277, 278, 284, 289, 290, 292, 293, 295, 296, 299, 307, 308, 310, 320, 321, 322, 324, 325, 326, 327, 334, 351, 353, 355, 356, 357, 358, 364], "excluded_lines": [], "executed_branches": [], "missing_branches": [[277, 278], [277, 284], [289, 290], [289, 292], [292, 293], [292, 295], [295, 296], [295, 299], [321, 322], [321, 353]]}, "MessageService.process_incoming_message": {"executed_lines": [454, 461], "summary": {"covered_lines": 2, "num_statements": 13, "percent_covered": 15.384615384615385, "percent_covered_display": "15.38", "missing_lines": 11, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [387, 388, 407, 412, 420, 432, 441, 449, 451, 452, 453], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MessageService.search_messages": {"executed_lines": [536, 542], "summary": {"covered_lines": 2, "num_statements": 20, "percent_covered": 6.666666666666667, "percent_covered_display": "6.67", "missing_lines": 18, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [494, 496, 499, 500, 502, 503, 505, 506, 508, 509, 511, 512, 515, 523, 524, 526, 533, 535], "excluded_lines": [], "executed_branches": [], "missing_branches": [[499, 500], [499, 502], [502, 503], [502, 505], [505, 506], [505, 508], [508, 509], [508, 511], [511, 512], [511, 515]]}, "MessageService.update_message_status": {"executed_lines": [594, 600], "summary": {"covered_lines": 2, "num_statements": 19, "percent_covered": 9.523809523809524, "percent_covered_display": "9.52", "missing_lines": 17, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [559, 561, 564, 565, 567, 568, 574, 575, 577, 578, 580, 587, 589, 590, 591, 592, 593], "excluded_lines": [], "executed_branches": [], "missing_branches": [[567, 568], [567, 574]]}, "MessageService.get_message_statistics": {"executed_lines": [672], "summary": {"covered_lines": 1, "num_statements": 21, "percent_covered": 4.0, "percent_covered_display": "4.00", "missing_lines": 20, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [621, 622, 624, 625, 626, 627, 630, 631, 632, 635, 640, 641, 644, 649, 650, 652, 662, 668, 670, 671], "excluded_lines": [], "executed_branches": [], "missing_branches": [[624, 625], [624, 626], [626, 627], [626, 630]]}, "get_message_service": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [687], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 12, 13, 14, 16, 17, 18, 19, 20, 22, 25, 26, 28, 38, 39, 247, 248, 368, 369, 465, 466, 604], "summary": {"covered_lines": 17, "num_statements": 23, "percent_covered": 73.91304347826087, "percent_covered_display": "73.91", "missing_lines": 6, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [21, 23, 29, 546, 605, 678], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"MessageService": {"executed_lines": [30, 36, 454, 461, 536, 542, 594, 600, 672], "summary": {"covered_lines": 8, "num_statements": 164, "percent_covered": 4.081632653061225, "percent_covered_display": "4.08", "missing_lines": 156, "excluded_lines": 0, "num_branches": 32, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 32}, "missing_lines": [37, 56, 58, 61, 62, 68, 69, 74, 76, 78, 81, 82, 83, 84, 85, 87, 89, 90, 93, 112, 115, 116, 121, 122, 124, 125, 126, 134, 137, 138, 139, 140, 141, 142, 149, 150, 156, 157, 158, 159, 160, 161, 171, 173, 190, 200, 201, 202, 203, 204, 205, 206, 213, 231, 232, 233, 234, 235, 236, 243, 274, 276, 277, 278, 284, 289, 290, 292, 293, 295, 296, 299, 307, 308, 310, 320, 321, 322, 324, 325, 326, 327, 334, 351, 353, 355, 356, 357, 358, 364, 387, 388, 407, 412, 420, 432, 441, 449, 451, 452, 453, 494, 496, 499, 500, 502, 503, 505, 506, 508, 509, 511, 512, 515, 523, 524, 526, 533, 535, 559, 561, 564, 565, 567, 568, 574, 575, 577, 578, 580, 587, 589, 590, 591, 592, 593, 621, 622, 624, 625, 626, 627, 630, 631, 632, 635, 640, 641, 644, 649, 650, 652, 662, 668, 670, 671], "excluded_lines": [], "executed_branches": [], "missing_branches": [[61, 62], [61, 68], [74, 76], [74, 93], [121, 122], [121, 124], [277, 278], [277, 284], [289, 290], [289, 292], [292, 293], [292, 295], [295, 296], [295, 299], [321, 322], [321, 353], [499, 500], [499, 502], [502, 503], [502, 505], [505, 506], [505, 508], [508, 509], [508, 511], [511, 512], [511, 515], [567, 568], [567, 574], [624, 625], [624, 626], [626, 627], [626, 630]]}, "": {"executed_lines": [1, 7, 8, 9, 10, 12, 13, 14, 16, 17, 18, 19, 20, 22, 25, 26, 28, 38, 39, 247, 248, 368, 369, 465, 466, 604], "summary": {"covered_lines": 17, "num_statements": 24, "percent_covered": 70.83333333333333, "percent_covered_display": "70.83", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [21, 23, 29, 546, 605, 678, 687], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\rbac_service.py": {"executed_lines": [1, 6, 7, 9, 10, 11, 13, 14, 15, 18, 21, 22, 24, 34, 35, 83, 104, 105, 148, 149, 218, 244, 245, 280, 281, 333, 334, 400, 401, 459, 460, 493, 494, 501, 507, 508, 519], "summary": {"covered_lines": 16, "num_statements": 187, "percent_covered": 7.048458149779735, "percent_covered_display": "7.05", "missing_lines": 171, "excluded_lines": 0, "num_branches": 40, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 40}, "missing_lines": [8, 12, 16, 19, 25, 32, 53, 55, 56, 57, 58, 61, 65, 66, 67, 69, 77, 79, 80, 81, 82, 84, 94, 95, 96, 97, 99, 100, 103, 122, 123, 126, 127, 128, 129, 130, 131, 132, 134, 135, 137, 139, 140, 142, 143, 146, 175, 177, 180, 181, 182, 185, 194, 195, 196, 198, 199, 200, 202, 210, 212, 213, 214, 217, 219, 230, 231, 236, 237, 239, 240, 243, 259, 260, 267, 268, 269, 270, 272, 274, 275, 277, 278, 279, 298, 300, 301, 302, 305, 308, 309, 311, 312, 314, 321, 323, 324, 325, 331, 356, 358, 359, 360, 362, 363, 364, 367, 368, 374, 377, 378, 380, 388, 390, 391, 392, 399, 415, 417, 418, 419, 422, 423, 424, 425, 426, 428, 429, 430, 432, 438, 440, 446, 448, 449, 450, 457, 475, 476, 477, 478, 480, 482, 483, 491, 498, 499, 500, 502, 504, 505, 506, 512, 517, 518, 520, 527, 561, 563, 564, 566, 567, 568, 570, 571, 574, 576, 578, 579, 583, 589], "excluded_lines": [], "executed_branches": [], "missing_branches": [[57, 58], [57, 61], [127, 128], [127, 129], [129, 130], [129, 131], [131, 132], [131, 134], [134, 135], [134, 137], [181, 182], [181, 185], [194, 195], [194, 198], [267, 268], [267, 269], [269, 270], [269, 272], [301, 302], [301, 305], [359, 360], [359, 362], [363, 364], [363, 367], [367, 368], [367, 377], [418, 419], [418, 422], [423, 424], [423, 428], [424, 423], [424, 425], [428, 429], [428, 440], [477, 478], [477, 480], [563, 564], [563, 583], [570, 571], [570, 576]], "functions": {"RBACService.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [32], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "RBACService.create_permission": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [53, 55, 56, 57, 58, 61, 65, 66, 67, 69, 77, 79, 80, 81, 82], "excluded_lines": [], "executed_branches": [], "missing_branches": [[57, 58], [57, 61]]}, "RBACService.get_permission": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [94, 95, 96, 97, 99, 100, 103], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "RBACService.list_permissions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 17, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [122, 123, 126, 127, 128, 129, 130, 131, 132, 134, 135, 137, 139, 140, 142, 143, 146], "excluded_lines": [], "executed_branches": [], "missing_branches": [[127, 128], [127, 129], [129, 130], [129, 131], [131, 132], [131, 134], [134, 135], [134, 137]]}, "RBACService.create_role": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 18, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [175, 177, 180, 181, 182, 185, 194, 195, 196, 198, 199, 200, 202, 210, 212, 213, 214, 217], "excluded_lines": [], "executed_branches": [], "missing_branches": [[181, 182], [181, 185], [194, 195], [194, 198]]}, "RBACService.get_role": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [230, 231, 236, 237, 239, 240, 243], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "RBACService.list_roles": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [259, 260, 267, 268, 269, 270, 272, 274, 275, 277, 278, 279], "excluded_lines": [], "executed_branches": [], "missing_branches": [[267, 268], [267, 269], [269, 270], [269, 272]]}, "RBACService.update_role_permissions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [298, 300, 301, 302, 305, 308, 309, 311, 312, 314, 321, 323, 324, 325, 331], "excluded_lines": [], "executed_branches": [], "missing_branches": [[301, 302], [301, 305]]}, "RBACService.assign_role_to_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 18, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [356, 358, 359, 360, 362, 363, 364, 367, 368, 374, 377, 378, 380, 388, 390, 391, 392, 399], "excluded_lines": [], "executed_branches": [], "missing_branches": [[359, 360], [359, 362], [363, 364], [363, 367], [367, 368], [367, 377]]}, "RBACService.remove_role_from_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 20, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [415, 417, 418, 419, 422, 423, 424, 425, 426, 428, 429, 430, 432, 438, 440, 446, 448, 449, 450, 457], "excluded_lines": [], "executed_branches": [], "missing_branches": [[418, 419], [418, 422], [423, 424], [423, 428], [424, 423], [424, 425], [428, 429], [428, 440]]}, "RBACService.check_user_permission": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [475, 476, 477, 478, 480, 482, 483, 491], "excluded_lines": [], "executed_branches": [], "missing_branches": [[477, 478], [477, 480]]}, "RBACService._get_permissions_by_ids": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [498, 499, 500], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "RBACService._get_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [504, 505, 506], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "RBACService._get_user_with_roles": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [512, 517, 518], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "RBACService.initialize_default_permissions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [527, 561, 563, 564, 566, 567, 568, 570, 571, 574, 576, 578, 579, 583, 589], "excluded_lines": [], "executed_branches": [], "missing_branches": [[563, 564], [563, 583], [570, 571], [570, 576]]}, "": {"executed_lines": [1, 6, 7, 9, 10, 11, 13, 14, 15, 18, 21, 22, 24, 34, 35, 83, 104, 105, 148, 149, 218, 244, 245, 280, 281, 333, 334, 400, 401, 459, 460, 493, 494, 501, 507, 508, 519], "summary": {"covered_lines": 16, "num_statements": 25, "percent_covered": 64.0, "percent_covered_display": "64.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 12, 16, 19, 25, 84, 219, 502, 520], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"RBACService": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 162, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 162, "excluded_lines": 0, "num_branches": 40, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 40}, "missing_lines": [32, 53, 55, 56, 57, 58, 61, 65, 66, 67, 69, 77, 79, 80, 81, 82, 94, 95, 96, 97, 99, 100, 103, 122, 123, 126, 127, 128, 129, 130, 131, 132, 134, 135, 137, 139, 140, 142, 143, 146, 175, 177, 180, 181, 182, 185, 194, 195, 196, 198, 199, 200, 202, 210, 212, 213, 214, 217, 230, 231, 236, 237, 239, 240, 243, 259, 260, 267, 268, 269, 270, 272, 274, 275, 277, 278, 279, 298, 300, 301, 302, 305, 308, 309, 311, 312, 314, 321, 323, 324, 325, 331, 356, 358, 359, 360, 362, 363, 364, 367, 368, 374, 377, 378, 380, 388, 390, 391, 392, 399, 415, 417, 418, 419, 422, 423, 424, 425, 426, 428, 429, 430, 432, 438, 440, 446, 448, 449, 450, 457, 475, 476, 477, 478, 480, 482, 483, 491, 498, 499, 500, 504, 505, 506, 512, 517, 518, 527, 561, 563, 564, 566, 567, 568, 570, 571, 574, 576, 578, 579, 583, 589], "excluded_lines": [], "executed_branches": [], "missing_branches": [[57, 58], [57, 61], [127, 128], [127, 129], [129, 130], [129, 131], [131, 132], [131, 134], [134, 135], [134, 137], [181, 182], [181, 185], [194, 195], [194, 198], [267, 268], [267, 269], [269, 270], [269, 272], [301, 302], [301, 305], [359, 360], [359, 362], [363, 364], [363, 367], [367, 368], [367, 377], [418, 419], [418, 422], [423, 424], [423, 428], [424, 423], [424, 425], [428, 429], [428, 440], [477, 478], [477, 480], [563, 564], [563, 583], [570, 571], [570, 576]]}, "": {"executed_lines": [1, 6, 7, 9, 10, 11, 13, 14, 15, 18, 21, 22, 24, 34, 35, 83, 104, 105, 148, 149, 218, 244, 245, 280, 281, 333, 334, 400, 401, 459, 460, 493, 494, 501, 507, 508, 519], "summary": {"covered_lines": 16, "num_statements": 25, "percent_covered": 64.0, "percent_covered_display": "64.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [8, 12, 16, 19, 25, 84, 219, 502, 520], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\session_service.py": {"executed_lines": [1, 7, 8, 9, 11, 12, 13, 15, 16, 17, 18, 20, 23, 24, 26, 32, 34, 124, 206, 244, 250, 254, 255, 311, 407, 424, 449, 450, 451, 465], "summary": {"covered_lines": 25, "num_statements": 132, "percent_covered": 15.625, "percent_covered_display": "15.62", "missing_lines": 107, "excluded_lines": 0, "num_branches": 28, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 28}, "missing_lines": [57, 59, 63, 64, 71, 74, 107, 108, 109, 111, 120, 122, 123, 133, 138, 156, 158, 161, 162, 164, 165, 171, 172, 175, 176, 178, 179, 180, 181, 184, 185, 187, 188, 190, 199, 201, 202, 203, 204, 205, 212, 217, 229, 230, 235, 236, 238, 239, 241, 243, 277, 279, 281, 282, 284, 285, 287, 288, 291, 299, 300, 302, 310, 312, 313, 314, 319, 333, 335, 337, 339, 343, 344, 346, 347, 352, 354, 359, 362, 364, 372, 376, 377, 379, 380, 385, 388, 389, 392, 394, 401, 403, 404, 406, 417, 420, 437, 438, 440, 444, 447, 448, 455, 457, 472, 475, 483], "excluded_lines": [], "executed_branches": [], "missing_branches": [[63, 64], [63, 74], [164, 165], [164, 171], [175, 176], [175, 178], [178, 179], [178, 184], [179, 180], [179, 181], [184, 185], [184, 187], [238, 239], [238, 241], [281, 282], [281, 284], [284, 285], [284, 287], [287, 288], [287, 291], [337, 339], [337, 362], [346, 347], [346, 354], [379, 380], [379, 388], [450, 451], [450, 455]], "functions": {"SessionService.__init__": {"executed_lines": [32], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionService.create_or_get_session": {"executed_lines": [124], "summary": {"covered_lines": 1, "num_statements": 15, "percent_covered": 5.882352941176471, "percent_covered_display": "5.88", "missing_lines": 14, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [57, 59, 63, 64, 71, 74, 107, 108, 109, 111, 120, 122, 123, 133], "excluded_lines": [], "executed_branches": [], "missing_branches": [[63, 64], [63, 74]]}, "SessionService.update_session_status": {"executed_lines": [206], "summary": {"covered_lines": 1, "num_statements": 27, "percent_covered": 2.7027027027027026, "percent_covered_display": "2.70", "missing_lines": 26, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [156, 158, 161, 162, 164, 165, 171, 172, 175, 176, 178, 179, 180, 181, 184, 185, 187, 188, 190, 199, 201, 202, 203, 204, 205, 212], "excluded_lines": [], "executed_branches": [], "missing_branches": [[164, 165], [164, 171], [175, 176], [175, 178], [178, 179], [178, 184], [179, 180], [179, 181], [184, 185], [184, 187]]}, "SessionService.get_session": {"executed_lines": [244, 250], "summary": {"covered_lines": 2, "num_statements": 10, "percent_covered": 16.666666666666668, "percent_covered_display": "16.67", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [229, 230, 235, 236, 238, 239, 241, 243], "excluded_lines": [], "executed_branches": [], "missing_branches": [[238, 239], [238, 241]]}, "SessionService.list_tenant_sessions": {"executed_lines": [311], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [277, 279, 281, 282, 284, 285, 287, 288, 291, 299, 300, 302, 310, 312, 313, 314], "excluded_lines": [], "executed_branches": [], "missing_branches": [[281, 282], [281, 284], [284, 285], [284, 287], [287, 288], [287, 291]]}, "SessionService.update_last_message_time": {"executed_lines": [407], "summary": {"covered_lines": 1, "num_statements": 29, "percent_covered": 2.857142857142857, "percent_covered_display": "2.86", "missing_lines": 28, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [333, 335, 337, 339, 343, 344, 346, 347, 352, 354, 359, 362, 364, 372, 376, 377, 379, 380, 385, 388, 389, 392, 394, 401, 403, 404, 406, 417], "excluded_lines": [], "executed_branches": [], "missing_branches": [[337, 339], [337, 362], [346, 347], [346, 354], [379, 380], [379, 388]]}, "SessionService._get_active_session": {"executed_lines": [424], "summary": {"covered_lines": 1, "num_statements": 3, "percent_covered": 33.333333333333336, "percent_covered_display": "33.33", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [437, 438], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionService._get_session_with_tenant_check": {"executed_lines": [449, 450, 451], "summary": {"covered_lines": 2, "num_statements": 6, "percent_covered": 25.0, "percent_covered_display": "25.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [444, 447, 448, 455], "excluded_lines": [], "executed_branches": [], "missing_branches": [[450, 451], [450, 455]]}, "SessionService._is_valid_status_transition": {"executed_lines": [465], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [472], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_session_service": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [483], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 7, 8, 9, 11, 12, 13, 15, 16, 17, 18, 20, 23, 24, 26, 34, 254, 255], "summary": {"covered_lines": 15, "num_statements": 22, "percent_covered": 68.18181818181819, "percent_covered_display": "68.18", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [138, 217, 319, 420, 440, 457, 475], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"SessionService": {"executed_lines": [32, 124, 206, 244, 250, 311, 407, 424, 449, 450, 451, 465], "summary": {"covered_lines": 10, "num_statements": 109, "percent_covered": 7.299270072992701, "percent_covered_display": "7.30", "missing_lines": 99, "excluded_lines": 0, "num_branches": 28, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 28}, "missing_lines": [57, 59, 63, 64, 71, 74, 107, 108, 109, 111, 120, 122, 123, 133, 156, 158, 161, 162, 164, 165, 171, 172, 175, 176, 178, 179, 180, 181, 184, 185, 187, 188, 190, 199, 201, 202, 203, 204, 205, 212, 229, 230, 235, 236, 238, 239, 241, 243, 277, 279, 281, 282, 284, 285, 287, 288, 291, 299, 300, 302, 310, 312, 313, 314, 333, 335, 337, 339, 343, 344, 346, 347, 352, 354, 359, 362, 364, 372, 376, 377, 379, 380, 385, 388, 389, 392, 394, 401, 403, 404, 406, 417, 437, 438, 444, 447, 448, 455, 472], "excluded_lines": [], "executed_branches": [], "missing_branches": [[63, 64], [63, 74], [164, 165], [164, 171], [175, 176], [175, 178], [178, 179], [178, 184], [179, 180], [179, 181], [184, 185], [184, 187], [238, 239], [238, 241], [281, 282], [281, 284], [284, 285], [284, 287], [287, 288], [287, 291], [337, 339], [337, 362], [346, 347], [346, 354], [379, 380], [379, 388], [450, 451], [450, 455]]}, "": {"executed_lines": [1, 7, 8, 9, 11, 12, 13, 15, 16, 17, 18, 20, 23, 24, 26, 34, 254, 255], "summary": {"covered_lines": 15, "num_statements": 23, "percent_covered": 65.21739130434783, "percent_covered_display": "65.22", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [138, 217, 319, 420, 440, 457, 475, 483], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\session_summary_service.py": {"executed_lines": [1, 6, 7, 8, 10, 12, 13, 14, 15, 21, 22, 23, 26, 29, 30, 32, 36, 44, 47, 50, 52, 53, 54, 58, 73, 74, 80, 81, 84, 85, 86, 89, 92, 93, 94, 96, 100, 104, 108, 111, 114, 115, 116, 117, 135, 136, 143, 144, 145, 146, 147, 154, 155, 178, 179, 180, 183, 195, 196, 197, 198, 199, 202, 204, 205, 206, 218, 227, 228, 229, 232, 238, 240, 241, 242, 245, 255, 258, 259, 260, 264, 272, 273, 279, 280, 286, 287, 288, 291, 301, 303, 304, 305, 309, 320, 321, 327, 328, 334, 335, 336, 339, 349, 351, 352, 353, 357, 368, 369, 375, 376, 382, 383, 384, 387, 397, 398, 415, 436, 437, 438, 467, 492, 510, 529, 530, 531, 534, 544, 545, 563, 584, 585, 586, 609, 610, 611, 635, 665, 686, 701, 702, 704, 705, 706, 708, 711, 712, 722, 734, 735, 736, 751, 752, 755, 756, 759, 763, 769, 770, 773, 774, 775, 777], "summary": {"covered_lines": 96, "num_statements": 222, "percent_covered": 33.91608391608391, "percent_covered_display": "33.92", "missing_lines": 126, "excluded_lines": 0, "num_branches": 64, "num_partial_branches": 5, "covered_branches": 1, "missing_branches": 63}, "missing_lines": [9, 11, 24, 27, 33, 43, 45, 55, 82, 83, 87, 88, 95, 99, 103, 156, 170, 193, 194, 200, 201, 217, 230, 231, 234, 235, 257, 281, 282, 284, 285, 329, 330, 332, 333, 377, 378, 380, 381, 399, 400, 403, 417, 427, 429, 430, 431, 432, 433, 434, 452, 455, 456, 457, 459, 462, 465, 476, 487, 488, 489, 490, 494, 505, 506, 507, 508, 512, 522, 524, 525, 526, 527, 546, 547, 550, 552, 565, 567, 577, 578, 579, 580, 581, 582, 583, 588, 600, 604, 605, 606, 607, 608, 613, 627, 629, 630, 634, 644, 646, 657, 658, 660, 661, 663, 664, 667, 669, 679, 680, 681, 683, 696, 697, 699, 700, 703, 707, 709, 746, 747, 750, 754, 758, 762, 772], "excluded_lines": [], "executed_branches": [[198, 199]], "missing_branches": [[83, 84], [83, 87], [88, 89], [88, 92], [95, 96], [95, 99], [99, 100], [99, 103], [103, 104], [103, 108], [198, 204], [229, 230], [229, 240], [234, 229], [234, 235], [399, 400], [399, 403], [429, 430], [429, 431], [431, 432], [431, 433], [433, 434], [433, 436], [487, 488], [487, 489], [489, 490], [489, 492], [505, 506], [505, 507], [507, 508], [507, 510], [524, 525], [524, 526], [526, 527], [526, 529], [546, 547], [546, 550], [577, 578], [577, 579], [579, 580], [579, 581], [581, 582], [581, 583], [583, 584], [583, 586], [604, 605], [604, 606], [606, 607], [606, 608], [608, 609], [608, 611], [657, 658], [657, 660], [664, 665], [664, 667], [699, 700], [699, 709], [703, 704], [703, 706], [746, 747], [746, 750], [751, 754], [751, 775]], "functions": {"SessionSummaryService.__init__": {"executed_lines": [36, 44, 47, 50, 52, 53], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "66.67", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [43, 45], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionSummaryService.generate_session_summary": {"executed_lines": [58, 73, 74, 80, 81, 84, 85, 86, 89, 92, 93, 94, 96, 100, 104, 108, 111, 114, 115, 116, 117, 135, 136, 143, 144, 145, 146, 147, 154], "summary": {"covered_lines": 17, "num_statements": 24, "percent_covered": 50.0, "percent_covered_display": "50.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [82, 83, 87, 88, 95, 99, 103], "excluded_lines": [], "executed_branches": [], "missing_branches": [[83, 84], [83, 87], [88, 89], [88, 92], [95, 96], [95, 99], [99, 100], [99, 103], [103, 104], [103, 108]]}, "SessionSummaryService._get_session_messages": {"executed_lines": [178], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [170], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionSummaryService._calculate_basic_stats": {"executed_lines": [183, 195, 196, 197, 198, 199, 202, 204, 205, 206], "summary": {"covered_lines": 5, "num_statements": 9, "percent_covered": 54.54545454545455, "percent_covered_display": "54.55", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 1}, "missing_lines": [193, 194, 200, 201], "excluded_lines": [], "executed_branches": [[198, 199]], "missing_branches": [[198, 204]]}, "SessionSummaryService._calculate_avg_response_time": {"executed_lines": [218, 227, 228, 229, 232, 238, 240], "summary": {"covered_lines": 4, "num_statements": 8, "percent_covered": 33.333333333333336, "percent_covered_display": "33.33", "missing_lines": 4, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [230, 231, 234, 235], "excluded_lines": [], "executed_branches": [], "missing_branches": [[229, 230], [229, 240], [234, 229], [234, 235]]}, "SessionSummaryService._generate_brief_summary": {"executed_lines": [245, 255, 258, 259, 260, 264, 272, 273, 279, 280, 286], "summary": {"covered_lines": 6, "num_statements": 11, "percent_covered": 54.54545454545455, "percent_covered_display": "54.55", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [257, 281, 282, 284, 285], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionSummaryService._generate_detailed_summary": {"executed_lines": [291, 301, 303, 304, 305, 309, 320, 321, 327, 328, 334], "summary": {"covered_lines": 7, "num_statements": 11, "percent_covered": 63.63636363636363, "percent_covered_display": "63.64", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [329, 330, 332, 333], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionSummaryService._generate_analysis_summary": {"executed_lines": [339, 349, 351, 352, 353, 357, 368, 369, 375, 376, 382], "summary": {"covered_lines": 7, "num_statements": 11, "percent_covered": 63.63636363636363, "percent_covered_display": "63.64", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [377, 378, 380, 381], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionSummaryService._analyze_user_behavior": {"executed_lines": [387, 397, 398, 415], "summary": {"covered_lines": 2, "num_statements": 5, "percent_covered": 28.571428571428573, "percent_covered_display": "28.57", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [399, 400, 403], "excluded_lines": [], "executed_branches": [], "missing_branches": [[399, 400], [399, 403]]}, "SessionSummaryService._analyze_response_pattern": {"executed_lines": [436], "summary": {"covered_lines": 1, "num_statements": 8, "percent_covered": 7.142857142857143, "percent_covered_display": "7.14", "missing_lines": 7, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [427, 429, 430, 431, 432, 433, 434], "excluded_lines": [], "executed_branches": [], "missing_branches": [[429, 430], [429, 431], [431, 432], [431, 433], [433, 434], [433, 436]]}, "SessionSummaryService._detect_emotion_indicators": {"executed_lines": [467], "summary": {"covered_lines": 1, "num_statements": 8, "percent_covered": 12.5, "percent_covered_display": "12.50", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [452, 455, 456, 457, 459, 462, 465], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionSummaryService._determine_overall_tone": {"executed_lines": [492], "summary": {"covered_lines": 1, "num_statements": 5, "percent_covered": 11.11111111111111, "percent_covered_display": "11.11", "missing_lines": 4, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [487, 488, 489, 490], "excluded_lines": [], "executed_branches": [], "missing_branches": [[487, 488], [487, 489], [489, 490], [489, 492]]}, "SessionSummaryService._assess_urgency_level": {"executed_lines": [510], "summary": {"covered_lines": 1, "num_statements": 5, "percent_covered": 11.11111111111111, "percent_covered_display": "11.11", "missing_lines": 4, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [505, 506, 507, 508], "excluded_lines": [], "executed_branches": [], "missing_branches": [[505, 506], [505, 507], [507, 508], [507, 510]]}, "SessionSummaryService._determine_interaction_style": {"executed_lines": [529], "summary": {"covered_lines": 1, "num_statements": 6, "percent_covered": 10.0, "percent_covered_display": "10.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [522, 524, 525, 526, 527], "excluded_lines": [], "executed_branches": [], "missing_branches": [[524, 525], [524, 526], [526, 527], [526, 529]]}, "SessionSummaryService._assess_service_quality": {"executed_lines": [534, 544, 545, 563], "summary": {"covered_lines": 2, "num_statements": 7, "percent_covered": 22.22222222222222, "percent_covered_display": "22.22", "missing_lines": 5, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [546, 547, 550, 552, 565], "excluded_lines": [], "executed_branches": [], "missing_branches": [[546, 547], [546, 550]]}, "SessionSummaryService._rate_response_timeliness": {"executed_lines": [584, 585, 586], "summary": {"covered_lines": 2, "num_statements": 9, "percent_covered": 11.764705882352942, "percent_covered_display": "11.76", "missing_lines": 7, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [577, 578, 579, 580, 581, 582, 583], "excluded_lines": [], "executed_branches": [], "missing_branches": [[577, 578], [577, 579], [579, 580], [579, 581], [581, 582], [581, 583], [583, 584], [583, 586]]}, "SessionSummaryService._assess_response_completeness": {"executed_lines": [609, 610, 611], "summary": {"covered_lines": 2, "num_statements": 8, "percent_covered": 14.285714285714286, "percent_covered_display": "14.29", "missing_lines": 6, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [600, 604, 605, 606, 607, 608], "excluded_lines": [], "executed_branches": [], "missing_branches": [[604, 605], [604, 606], [606, 607], [606, 608], [608, 609], [608, 611]]}, "SessionSummaryService._assess_professional_tone": {"executed_lines": [635], "summary": {"covered_lines": 1, "num_statements": 6, "percent_covered": 16.666666666666668, "percent_covered_display": "16.67", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [627, 629, 630, 634, 644], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionSummaryService._assess_problem_resolution": {"executed_lines": [665], "summary": {"covered_lines": 1, "num_statements": 8, "percent_covered": 8.333333333333334, "percent_covered_display": "8.33", "missing_lines": 7, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [657, 658, 660, 661, 663, 664, 667], "excluded_lines": [], "executed_branches": [], "missing_branches": [[657, 658], [657, 660], [664, 665], [664, 667]]}, "SessionSummaryService._calculate_overall_rating": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [679, 680, 681], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionSummaryService._format_messages_for_summary": {"executed_lines": [686, 701, 702, 704, 705, 706, 708], "summary": {"covered_lines": 3, "num_statements": 10, "percent_covered": 21.428571428571427, "percent_covered_display": "21.43", "missing_lines": 7, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [696, 697, 699, 700, 703, 707, 709], "excluded_lines": [], "executed_branches": [], "missing_branches": [[699, 700], [699, 709], [703, 704], [703, 706]]}, "SessionSummaryService._create_empty_summary": {"executed_lines": [712, 722], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SessionSummaryService._get_llm_provider": {"executed_lines": [736, 751, 752, 755, 756, 759, 763, 769, 770, 773, 774, 775], "summary": {"covered_lines": 8, "num_statements": 15, "percent_covered": 42.10526315789474, "percent_covered_display": "42.11", "missing_lines": 7, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 2, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [746, 747, 750, 754, 758, 762, 772], "excluded_lines": [], "executed_branches": [], "missing_branches": [[746, 747], [746, 750], [751, 754], [751, 775]]}, "": {"executed_lines": [1, 6, 7, 8, 10, 12, 13, 14, 15, 21, 22, 23, 26, 29, 30, 32, 54, 155, 179, 180, 241, 242, 287, 288, 335, 336, 383, 384, 437, 438, 530, 531, 711, 734, 735], "summary": {"covered_lines": 18, "num_statements": 36, "percent_covered": 50.0, "percent_covered_display": "50.00", "missing_lines": 18, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 11, 24, 27, 33, 55, 156, 217, 417, 476, 494, 512, 567, 588, 613, 646, 669, 683], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"SessionSummaryService": {"executed_lines": [36, 44, 47, 50, 52, 53, 58, 73, 74, 80, 81, 84, 85, 86, 89, 92, 93, 94, 96, 100, 104, 108, 111, 114, 115, 116, 117, 135, 136, 143, 144, 145, 146, 147, 154, 178, 183, 195, 196, 197, 198, 199, 202, 204, 205, 206, 218, 227, 228, 229, 232, 238, 240, 245, 255, 258, 259, 260, 264, 272, 273, 279, 280, 286, 291, 301, 303, 304, 305, 309, 320, 321, 327, 328, 334, 339, 349, 351, 352, 353, 357, 368, 369, 375, 376, 382, 387, 397, 398, 415, 436, 467, 492, 510, 529, 534, 544, 545, 563, 584, 585, 586, 609, 610, 611, 635, 665, 686, 701, 702, 704, 705, 706, 708, 712, 722, 736, 751, 752, 755, 756, 759, 763, 769, 770, 773, 774, 775], "summary": {"covered_lines": 78, "num_statements": 186, "percent_covered": 31.6, "percent_covered_display": "31.60", "missing_lines": 108, "excluded_lines": 0, "num_branches": 64, "num_partial_branches": 5, "covered_branches": 1, "missing_branches": 63}, "missing_lines": [43, 45, 82, 83, 87, 88, 95, 99, 103, 170, 193, 194, 200, 201, 230, 231, 234, 235, 257, 281, 282, 284, 285, 329, 330, 332, 333, 377, 378, 380, 381, 399, 400, 403, 427, 429, 430, 431, 432, 433, 434, 452, 455, 456, 457, 459, 462, 465, 487, 488, 489, 490, 505, 506, 507, 508, 522, 524, 525, 526, 527, 546, 547, 550, 552, 565, 577, 578, 579, 580, 581, 582, 583, 600, 604, 605, 606, 607, 608, 627, 629, 630, 634, 644, 657, 658, 660, 661, 663, 664, 667, 679, 680, 681, 696, 697, 699, 700, 703, 707, 709, 746, 747, 750, 754, 758, 762, 772], "excluded_lines": [], "executed_branches": [[198, 199]], "missing_branches": [[83, 84], [83, 87], [88, 89], [88, 92], [95, 96], [95, 99], [99, 100], [99, 103], [103, 104], [103, 108], [198, 204], [229, 230], [229, 240], [234, 229], [234, 235], [399, 400], [399, 403], [429, 430], [429, 431], [431, 432], [431, 433], [433, 434], [433, 436], [487, 488], [487, 489], [489, 490], [489, 492], [505, 506], [505, 507], [507, 508], [507, 510], [524, 525], [524, 526], [526, 527], [526, 529], [546, 547], [546, 550], [577, 578], [577, 579], [579, 580], [579, 581], [581, 582], [581, 583], [583, 584], [583, 586], [604, 605], [604, 606], [606, 607], [606, 608], [608, 609], [608, 611], [657, 658], [657, 660], [664, 665], [664, 667], [699, 700], [699, 709], [703, 704], [703, 706], [746, 747], [746, 750], [751, 754], [751, 775]]}, "": {"executed_lines": [1, 6, 7, 8, 10, 12, 13, 14, 15, 21, 22, 23, 26, 29, 30, 32, 54, 155, 179, 180, 241, 242, 287, 288, 335, 336, 383, 384, 437, 438, 530, 531, 711, 734, 735], "summary": {"covered_lines": 18, "num_statements": 36, "percent_covered": 50.0, "percent_covered_display": "50.00", "missing_lines": 18, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [9, 11, 24, 27, 33, 55, 156, 217, 417, 476, 494, 512, 567, 588, 613, 646, 669, 683], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\tenant_service.py": {"executed_lines": [1, 6, 7, 9, 10, 11, 13, 14, 15, 16, 18, 21, 22, 24, 32, 97, 123, 142, 203, 257, 311, 351, 359, 365, 373, 374, 375, 423, 478], "summary": {"covered_lines": 27, "num_statements": 180, "percent_covered": 12.735849056603774, "percent_covered_display": "12.74", "missing_lines": 153, "excluded_lines": 0, "num_branches": 32, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 32}, "missing_lines": [30, 44, 46, 47, 48, 49, 55, 56, 57, 62, 71, 72, 73, 75, 82, 84, 85, 86, 87, 88, 89, 92, 106, 107, 108, 109, 111, 112, 114, 116, 117, 118, 135, 136, 137, 140, 157, 159, 160, 161, 163, 164, 169, 170, 171, 172, 178, 179, 180, 182, 183, 185, 191, 193, 194, 195, 196, 197, 198, 199, 215, 217, 218, 219, 221, 222, 227, 228, 230, 231, 233, 240, 241, 243, 245, 247, 248, 249, 250, 251, 252, 253, 275, 276, 279, 280, 281, 289, 290, 291, 293, 300, 302, 303, 305, 307, 308, 316, 325, 327, 330, 338, 347, 348, 349, 350, 357, 360, 361, 363, 366, 367, 369, 371, 377, 392, 394, 395, 396, 398, 399, 402, 403, 405, 409, 410, 412, 418, 420, 421, 422, 428, 440, 442, 443, 444, 446, 447, 450, 451, 454, 457, 460, 463, 464, 466, 468, 471, 473, 474, 475, 485, 487], "excluded_lines": [], "executed_branches": [], "missing_branches": [[47, 48], [47, 55], [56, 57], [56, 62], [111, 112], [111, 114], [136, 137], [136, 140], [163, 164], [163, 169], [169, 170], [169, 178], [171, 172], [171, 178], [179, 180], [179, 182], [221, 222], [221, 227], [228, 230], [228, 240], [279, 280], [279, 289], [289, 290], [289, 300], [290, 291], [290, 293], [398, 399], [398, 402], [402, 403], [402, 405], [446, 447], [446, 450]], "functions": {"TenantService.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [30], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantService.create_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 21, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [44, 46, 47, 48, 49, 55, 56, 57, 62, 71, 72, 73, 75, 82, 84, 85, 86, 87, 88, 89, 92], "excluded_lines": [], "executed_branches": [], "missing_branches": [[47, 48], [47, 55], [56, 57], [56, 62]]}, "TenantService.get_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [106, 107, 108, 109, 111, 112, 114, 116, 117, 118], "excluded_lines": [], "executed_branches": [], "missing_branches": [[111, 112], [111, 114]]}, "TenantService.get_tenant_by_id_with_verification": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [135, 136, 137, 140], "excluded_lines": [], "executed_branches": [], "missing_branches": [[136, 137], [136, 140]]}, "TenantService.update_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 24, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [157, 159, 160, 161, 163, 164, 169, 170, 171, 172, 178, 179, 180, 182, 183, 185, 191, 193, 194, 195, 196, 197, 198, 199], "excluded_lines": [], "executed_branches": [], "missing_branches": [[163, 164], [163, 169], [169, 170], [169, 178], [171, 172], [171, 178], [179, 180], [179, 182]]}, "TenantService.delete_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 22, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [215, 217, 218, 219, 221, 222, 227, 228, 230, 231, 233, 240, 241, 243, 245, 247, 248, 249, 250, 251, 252, 253], "excluded_lines": [], "executed_branches": [], "missing_branches": [[221, 222], [221, 227], [228, 230], [228, 240]]}, "TenantService.list_tenants": {"executed_lines": [311], "summary": {"covered_lines": 1, "num_statements": 16, "percent_covered": 4.545454545454546, "percent_covered_display": "4.55", "missing_lines": 15, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [275, 276, 279, 280, 281, 289, 290, 291, 293, 300, 302, 303, 305, 307, 308], "excluded_lines": [], "executed_branches": [], "missing_branches": [[279, 280], [279, 289], [289, 290], [289, 300], [290, 291], [290, 293]]}, "TenantService.get_tenant_statistics": {"executed_lines": [351], "summary": {"covered_lines": 1, "num_statements": 9, "percent_covered": 11.11111111111111, "percent_covered_display": "11.11", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [325, 327, 330, 338, 347, 348, 349, 350], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantService._get_tenant_by_email": {"executed_lines": [359], "summary": {"covered_lines": 1, "num_statements": 3, "percent_covered": 33.333333333333336, "percent_covered_display": "33.33", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [360, 361], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantService._get_tenant_by_name": {"executed_lines": [365], "summary": {"covered_lines": 1, "num_statements": 3, "percent_covered": 33.333333333333336, "percent_covered_display": "33.33", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [366, 367], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantService._count_tenant_users": {"executed_lines": [373, 374, 375], "summary": {"covered_lines": 3, "num_statements": 4, "percent_covered": 75.0, "percent_covered_display": "75.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [371], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TenantService.update_tenant_status": {"executed_lines": [423], "summary": {"covered_lines": 1, "num_statements": 17, "percent_covered": 4.761904761904762, "percent_covered_display": "4.76", "missing_lines": 16, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [392, 394, 395, 396, 398, 399, 402, 403, 405, 409, 410, 412, 418, 420, 421, 422], "excluded_lines": [], "executed_branches": [], "missing_branches": [[398, 399], [398, 402], [402, 403], [402, 405]]}, "TenantService.regenerate_api_key": {"executed_lines": [478], "summary": {"covered_lines": 1, "num_statements": 20, "percent_covered": 4.545454545454546, "percent_covered_display": "4.55", "missing_lines": 19, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [440, 442, 443, 444, 446, 447, 450, 451, 454, 457, 460, 463, 464, 466, 468, 471, 473, 474, 475], "excluded_lines": [], "executed_branches": [], "missing_branches": [[446, 447], [446, 450]]}, "get_tenant_service": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [487], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 7, 9, 10, 11, 13, 14, 15, 16, 18, 21, 22, 24, 32, 97, 123, 142, 203, 257], "summary": {"covered_lines": 18, "num_statements": 25, "percent_covered": 72.0, "percent_covered_display": "72.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [316, 357, 363, 369, 377, 428, 485], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"TenantService": {"executed_lines": [311, 351, 359, 365, 373, 374, 375, 423, 478], "summary": {"covered_lines": 9, "num_statements": 154, "percent_covered": 4.838709677419355, "percent_covered_display": "4.84", "missing_lines": 145, "excluded_lines": 0, "num_branches": 32, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 32}, "missing_lines": [30, 44, 46, 47, 48, 49, 55, 56, 57, 62, 71, 72, 73, 75, 82, 84, 85, 86, 87, 88, 89, 92, 106, 107, 108, 109, 111, 112, 114, 116, 117, 118, 135, 136, 137, 140, 157, 159, 160, 161, 163, 164, 169, 170, 171, 172, 178, 179, 180, 182, 183, 185, 191, 193, 194, 195, 196, 197, 198, 199, 215, 217, 218, 219, 221, 222, 227, 228, 230, 231, 233, 240, 241, 243, 245, 247, 248, 249, 250, 251, 252, 253, 275, 276, 279, 280, 281, 289, 290, 291, 293, 300, 302, 303, 305, 307, 308, 325, 327, 330, 338, 347, 348, 349, 350, 360, 361, 366, 367, 371, 392, 394, 395, 396, 398, 399, 402, 403, 405, 409, 410, 412, 418, 420, 421, 422, 440, 442, 443, 444, 446, 447, 450, 451, 454, 457, 460, 463, 464, 466, 468, 471, 473, 474, 475], "excluded_lines": [], "executed_branches": [], "missing_branches": [[47, 48], [47, 55], [56, 57], [56, 62], [111, 112], [111, 114], [136, 137], [136, 140], [163, 164], [163, 169], [169, 170], [169, 178], [171, 172], [171, 178], [179, 180], [179, 182], [221, 222], [221, 227], [228, 230], [228, 240], [279, 280], [279, 289], [289, 290], [289, 300], [290, 291], [290, 293], [398, 399], [398, 402], [402, 403], [402, 405], [446, 447], [446, 450]]}, "": {"executed_lines": [1, 6, 7, 9, 10, 11, 13, 14, 15, 16, 18, 21, 22, 24, 32, 97, 123, 142, 203, 257], "summary": {"covered_lines": 18, "num_statements": 26, "percent_covered": 69.23076923076923, "percent_covered_display": "69.23", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [316, 357, 363, 369, 377, 428, 485, 487], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\services\\webhook_service.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 14, 15, 17, 18, 19, 20, 21, 24, 27, 28, 30, 36, 43, 44, 45, 46, 137, 143, 193, 194, 195, 259, 262, 311, 312, 313, 351, 352, 353, 390, 391, 392, 417, 418, 419, 450, 451, 452, 507, 532, 582, 583, 585], "summary": {"covered_lines": 25, "num_statements": 124, "percent_covered": 16.025641025641026, "percent_covered_display": "16.03", "missing_lines": 99, "excluded_lines": 0, "num_branches": 32, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 32}, "missing_lines": [22, 23, 25, 31, 32, 33, 34, 42, 48, 55, 56, 58, 61, 63, 70, 77, 83, 84, 86, 87, 88, 90, 91, 93, 100, 107, 114, 117, 118, 119, 120, 121, 123, 124, 126, 127, 129, 131, 133, 144, 151, 153, 159, 162, 163, 164, 165, 167, 168, 170, 171, 173, 175, 182, 184, 190, 196, 198, 199, 201, 202, 204, 205, 207, 213, 219, 225, 228, 229, 231, 232, 234, 235, 236, 240, 247, 250, 251, 257, 270, 273, 274, 275, 278, 280, 281, 285, 286, 287, 289, 290, 291, 293, 297, 298, 302, 303, 305, 308], "excluded_lines": [], "executed_branches": [], "missing_branches": [[42, 43], [42, 45], [55, 56], [55, 58], [83, 84], [83, 86], [90, 91], [90, 93], [123, 124], [123, 126], [126, 127], [126, 129], [167, 168], [167, 170], [170, 171], [170, 173], [198, 199], [198, 201], [201, 202], [201, 204], [204, 205], [204, 207], [231, 232], [231, 234], [274, 275], [274, 280], [285, -270], [285, 286], [297, 298], [297, 302], [302, 303], [302, 305]], "functions": {"WebhookService.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [32, 33, 34], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "WebhookService.process_message_webhook": {"executed_lines": [43, 44, 45, 46], "summary": {"covered_lines": 3, "num_statements": 11, "percent_covered": 20.0, "percent_covered_display": "20.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [42, 48, 55, 56, 58, 61, 63, 70], "excluded_lines": [], "executed_branches": [], "missing_branches": [[42, 43], [42, 45], [55, 56], [55, 58]]}, "WebhookService.process_status_webhook": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [83, 84, 86, 87, 88, 90, 91, 93, 100, 107], "excluded_lines": [], "executed_branches": [], "missing_branches": [[83, 84], [83, 86], [90, 91], [90, 93]]}, "WebhookService._handle_message_received": {"executed_lines": [137, 143], "summary": {"covered_lines": 1, "num_statements": 16, "percent_covered": 5.0, "percent_covered_display": "5.00", "missing_lines": 15, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [117, 118, 119, 120, 121, 123, 124, 126, 127, 129, 131, 133, 144, 151, 153], "excluded_lines": [], "executed_branches": [], "missing_branches": [[123, 124], [123, 126], [126, 127], [126, 129]]}, "WebhookService._handle_message_sent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [162, 163, 164, 165, 167, 168, 170, 171, 173, 175, 182, 184], "excluded_lines": [], "executed_branches": [], "missing_branches": [[167, 168], [167, 170], [170, 171], [170, 173]]}, "WebhookService._handle_session_created": {"executed_lines": [193, 194, 195], "summary": {"covered_lines": 3, "num_statements": 13, "percent_covered": 15.789473684210526, "percent_covered_display": "15.79", "missing_lines": 10, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [196, 198, 199, 201, 202, 204, 205, 207, 213, 219], "excluded_lines": [], "executed_branches": [], "missing_branches": [[198, 199], [198, 201], [201, 202], [201, 204], [204, 205], [204, 207]]}, "WebhookService._handle_session_closed": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [228, 229, 231, 232, 234, 235, 236, 240], "excluded_lines": [], "executed_branches": [], "missing_branches": [[231, 232], [231, 234]]}, "WebhookService._handle_unknown_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [250, 251, 257], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "WebhookService._update_instance_status": {"executed_lines": [262], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "WebhookService._verify_webhook_signature": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [273, 274, 275, 278, 280, 281, 285, 286, 287], "excluded_lines": [], "executed_branches": [], "missing_branches": [[274, 275], [274, 280], [285, -270], [285, 286]]}, "WebhookService._get_tenant_webhook_secret": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [290, 291], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "WebhookService.validate_webhook_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [297, 298, 302, 303, 305], "excluded_lines": [], "executed_branches": [], "missing_branches": [[297, 298], [297, 302], [302, 303], [302, 305]]}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 14, 15, 17, 18, 19, 20, 21, 24, 27, 28, 30, 36, 259, 311], "summary": {"covered_lines": 17, "num_statements": 31, "percent_covered": 54.83870967741935, "percent_covered_display": "54.84", "missing_lines": 14, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [22, 23, 25, 31, 77, 114, 159, 190, 225, 247, 270, 289, 293, 308], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"WebhookService": {"executed_lines": [43, 44, 45, 46, 137, 143, 193, 194, 195, 262], "summary": {"covered_lines": 8, "num_statements": 93, "percent_covered": 6.4, "percent_covered_display": "6.40", "missing_lines": 85, "excluded_lines": 0, "num_branches": 32, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 32}, "missing_lines": [32, 33, 34, 42, 48, 55, 56, 58, 61, 63, 70, 83, 84, 86, 87, 88, 90, 91, 93, 100, 107, 117, 118, 119, 120, 121, 123, 124, 126, 127, 129, 131, 133, 144, 151, 153, 162, 163, 164, 165, 167, 168, 170, 171, 173, 175, 182, 184, 196, 198, 199, 201, 202, 204, 205, 207, 213, 219, 228, 229, 231, 232, 234, 235, 236, 240, 250, 251, 257, 273, 274, 275, 278, 280, 281, 285, 286, 287, 290, 291, 297, 298, 302, 303, 305], "excluded_lines": [], "executed_branches": [], "missing_branches": [[42, 43], [42, 45], [55, 56], [55, 58], [83, 84], [83, 86], [90, 91], [90, 93], [123, 124], [123, 126], [126, 127], [126, 129], [167, 168], [167, 170], [170, 171], [170, 173], [198, 199], [198, 201], [201, 202], [201, 204], [204, 205], [204, 207], [231, 232], [231, 234], [274, 275], [274, 280], [285, -270], [285, 286], [297, 298], [297, 302], [302, 303], [302, 305]]}, "SecurityError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 14, 15, 17, 18, 19, 20, 21, 24, 27, 28, 30, 36, 259, 311], "summary": {"covered_lines": 17, "num_statements": 31, "percent_covered": 54.83870967741935, "percent_covered_display": "54.84", "missing_lines": 14, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [22, 23, 25, 31, 77, 114, 159, 190, 225, 247, 270, 289, 293, 308], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "app\\utils\\logging.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 16, 17, 19, 50, 54, 55, 58, 61, 62, 65, 67, 74, 75, 78, 79, 80, 83, 84, 86, 87, 88, 90, 94, 98, 100, 103, 108, 109, 111, 113, 117, 119, 121, 123, 125, 127, 129, 135, 138, 148, 149, 151, 155], "summary": {"covered_lines": 47, "num_statements": 64, "percent_covered": 63.75, "percent_covered_display": "63.75", "missing_lines": 17, "excluded_lines": 0, "num_branches": 16, "num_partial_branches": 2, "covered_branches": 4, "missing_branches": 12}, "missing_lines": [23, 34, 35, 38, 39, 40, 41, 42, 43, 44, 45, 47, 72, 92, 96, 115, 131], "excluded_lines": [], "executed_branches": [[65, 67], [108, 109], [108, 111], [148, 149]], "missing_branches": [[34, 35], [34, 38], [38, 39], [38, 40], [40, 41], [40, 42], [42, 43], [42, 44], [44, 45], [44, 47], [65, 72], [148, 151]], "functions": {"StructuredFormatter.format": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [23, 34, 35, 38, 39, 40, 41, 42, 43, 44, 45, 47], "excluded_lines": [], "executed_branches": [], "missing_branches": [[34, 35], [34, 38], [38, 39], [38, 40], [40, 41], [40, 42], [42, 43], [42, 44], [44, 45], [44, 47]]}, "setup_logging": {"executed_lines": [54, 55, 58, 61, 62, 65, 67, 74, 75, 78, 79, 80], "summary": {"covered_lines": 12, "num_statements": 13, "percent_covered": 86.66666666666667, "percent_covered_display": "86.67", "missing_lines": 1, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 1}, "missing_lines": [72], "excluded_lines": [], "executed_branches": [[65, 67]], "missing_branches": [[65, 72]]}, "ContextLogger.__init__": {"executed_lines": [87, 88], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ContextLogger.set_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [92], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ContextLogger.clear_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [96], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ContextLogger._log": {"executed_lines": [100, 103, 108, 109, 111], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 2, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [[108, 109], [108, 111]], "missing_branches": []}, "ContextLogger.debug": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [115], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ContextLogger.info": {"executed_lines": [119], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ContextLogger.warning": {"executed_lines": [123], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ContextLogger.error": {"executed_lines": [127], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ContextLogger.critical": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [131], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_logger": {"executed_lines": [148, 149, 151], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 80.0, "percent_covered_display": "80.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 1, "covered_branches": 1, "missing_branches": 1}, "missing_lines": [], "excluded_lines": [], "executed_branches": [[148, 149]], "missing_branches": [[148, 151]]}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 16, 17, 19, 50, 83, 84, 86, 90, 94, 98, 113, 117, 121, 125, 129, 135, 138, 155], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"StructuredFormatter": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0, "num_branches": 10, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 10}, "missing_lines": [23, 34, 35, 38, 39, 40, 41, 42, 43, 44, 45, 47], "excluded_lines": [], "executed_branches": [], "missing_branches": [[34, 35], [34, 38], [38, 39], [38, 40], [40, 41], [40, 42], [42, 43], [42, 44], [44, 45], [44, 47]]}, "ContextLogger": {"executed_lines": [87, 88, 100, 103, 108, 109, 111, 119, 123, 127], "summary": {"covered_lines": 10, "num_statements": 14, "percent_covered": 75.0, "percent_covered_display": "75.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 2, "missing_branches": 0}, "missing_lines": [92, 96, 115, 131], "excluded_lines": [], "executed_branches": [[108, 109], [108, 111]], "missing_branches": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 16, 17, 19, 50, 54, 55, 58, 61, 62, 65, 67, 74, 75, 78, 79, 80, 83, 84, 86, 90, 94, 98, 113, 117, 121, 125, 129, 135, 138, 148, 149, 151, 155], "summary": {"covered_lines": 37, "num_statements": 38, "percent_covered": 92.85714285714286, "percent_covered_display": "92.86", "missing_lines": 1, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 2, "covered_branches": 2, "missing_branches": 2}, "missing_lines": [72], "excluded_lines": [], "executed_branches": [[65, 67], [148, 149]], "missing_branches": [[65, 72], [148, 151]]}}}}, "totals": {"covered_lines": 1361, "num_statements": 5549, "percent_covered": 20.941140868512633, "percent_covered_display": "20.94", "missing_lines": 4188, "excluded_lines": 86, "num_branches": 1060, "num_partial_branches": 37, "covered_branches": 23, "missing_branches": 1037}}