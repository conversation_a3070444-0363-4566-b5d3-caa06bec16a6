# AstrBot SaaS Platform

🚀 **企业级多租户智能客服平台** - 基于FastAPI和Vue3构建的现代化SaaS解决方案

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue.svg)](https://postgresql.org)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📖 项目简介

AstrBot SaaS Platform 是一个功能完整的企业级多租户智能客服平台，提供：

- 🏢 **多租户架构** - 完整的租户隔离和管理
- 🤖 **智能对话** - 集成多种LLM提供商 (OpenAI, Dify等)
- 📊 **数据分析** - 实时会话统计和业务分析
- 🔐 **企业安全** - JWT认证、RBAC权限控制
- 📱 **API优先** - RESTful API设计，易于集成
- ⚡ **高性能** - 异步处理，支持高并发
- 🐳 **容器化** - Docker部署，云原生架构

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (Vue3)        │◄──►│   (FastAPI)     │◄──►│   (PostgreSQL)  │
│                 │    │                 │    │                 │
│ - 租户管理界面   │    │ - API Gateway   │    │ - 租户数据      │
│ - 会话监控面板   │    │ - 业务逻辑层    │    │ - 会话记录      │
│ - 统计分析图表   │    │ - 权限控制      │    │ - 用户信息      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   AstrBot       │
                    │   Instances     │
                    │                 │
                    │ - 智能对话      │
                    │ - 多模态交互    │
                    │ - 插件系统      │
                    └─────────────────┘
```

## 🚀 快速开始

### 前置要求

- Python 3.9+
- PostgreSQL 15+
- Redis 6+
- Docker & Docker Compose (推荐)

### 方式一：Docker部署 (推荐)

```bash
# 1. 克隆项目
git clone https://github.com/your-org/AstrBot.git
cd AstrBot/saas-platform

# 2. 配置环境变量
cp .env.template .env
# 编辑 .env 文件，填入真实配置

# 3. 启动服务
docker-compose up -d

# 4. 初始化数据库
docker-compose exec app alembic upgrade head

# 5. 访问应用
# API文档: http://localhost:8000/docs
# 管理界面: http://localhost:3000
```

### 方式二：本地开发

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置数据库
# 创建PostgreSQL数据库: astrbot_saas

# 3. 配置环境变量
cp .env.template .env
# 编辑配置文件

# 4. 运行数据库迁移
alembic upgrade head

# 5. 启动应用
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### PostgreSQL快速安装

我们提供了Windows环境下的PostgreSQL自动安装工具：

```bash
# 运行PostgreSQL安装器
python install_postgresql_windows.py

# 运行数据库连接测试
python quick_db_test.py

# 运行完整数据库集成测试
python integration_db_test_windows.py
```

## 📚 API文档

### 认证端点

```http
POST /api/v1/auth/login
POST /api/v1/auth/register
POST /api/v1/auth/refresh
```

### 租户管理

```http
GET    /api/v1/tenants              # 获取租户列表
POST   /api/v1/tenants              # 创建租户
GET    /api/v1/tenants/{id}         # 获取租户详情
PUT    /api/v1/tenants/{id}         # 更新租户
DELETE /api/v1/tenants/{id}         # 删除租户
```

### 会话管理

```http
GET    /api/v1/sessions             # 获取会话列表
POST   /api/v1/sessions             # 创建会话
GET    /api/v1/sessions/{id}        # 获取会话详情
DELETE /api/v1/sessions/{id}        # 删除会话
```

### 消息处理

```http
GET    /api/v1/messages             # 获取消息列表
POST   /api/v1/messages             # 发送消息
GET    /api/v1/messages/{id}        # 获取消息详情
```

### 实例管理

```http
GET    /api/v1/instances            # 获取实例列表
POST   /api/v1/instances            # 创建实例
PUT    /api/v1/instances/{id}       # 更新实例配置
DELETE /api/v1/instances/{id}       # 删除实例
```

### 分析统计

```http
GET    /api/v1/analytics/overview   # 总览统计
GET    /api/v1/analytics/sessions   # 会话统计
GET    /api/v1/analytics/messages   # 消息统计
```

## 🔧 配置说明

### 环境变量配置

```bash
# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/astrbot_saas
DATABASE_PASSWORD=your_secure_password

# 安全配置
SECRET_KEY=your-super-secret-key
JWT_SECRET_KEY=your-jwt-secret-key

# LLM提供商配置
OPENAI_API_KEY=your-openai-api-key
DIFY_API_KEY=your-dify-api-key

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 应用配置
ENVIRONMENT=development
DEBUG=false
LOG_LEVEL=INFO
```

### 数据库迁移

```bash
# 创建新迁移
alembic revision --autogenerate -m "描述变更"

# 执行迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1
```

## 🧪 测试

### 运行测试套件

```bash
# 运行所有测试
python run_tests.py

# 运行单元测试
pytest tests/unit/ -v

# 运行集成测试
pytest tests/integration/ -v

# 运行端到端测试
pytest tests/e2e/ -v

# 运行性能测试
pytest tests/performance/ -v
```

### 测试覆盖率

```bash
# 生成覆盖率报告
pytest --cov=app --cov-report=html
# 查看报告: htmlcov/index.html
```

### 质量检查

```bash
# 运行代码质量检查
python quality_check.py

# 自动修复质量问题
python quality_fix.py
```

## 🔐 安全

### 认证授权

- **JWT Token认证** - 支持访问令牌和刷新令牌
- **RBAC权限控制** - 基于角色的访问控制
- **多租户隔离** - 数据和权限完全隔离

### 安全最佳实践

- 所有密码使用bcrypt加密
- 敏感配置通过环境变量管理
- API接口支持CORS和CSRF保护
- 定期安全扫描和依赖更新

### 安全配置

详细的安全配置请参考：[SECURITY_GUIDE.md](SECURITY_GUIDE.md)

## 📊 监控

### 应用监控

```bash
# 查看应用日志
docker-compose logs -f app

# 查看数据库日志
docker-compose logs -f postgres

# 查看Redis日志
docker-compose logs -f redis
```

### 性能监控

- Prometheus + Grafana监控栈
- 自定义业务指标收集
- 实时性能分析面板

## 🛠️ 开发指南

### 项目结构

```
saas-platform/
├── app/                    # 应用主目录
│   ├── api/               # API路由
│   ├── core/              # 核心配置
│   ├── models/            # 数据模型
│   ├── schemas/           # Pydantic模式
│   ├── services/          # 业务逻辑层
│   └── utils/             # 工具函数
├── tests/                 # 测试目录
│   ├── unit/              # 单元测试
│   ├── integration/       # 集成测试
│   ├── e2e/               # 端到端测试
│   └── performance/       # 性能测试
├── alembic/               # 数据库迁移
├── docs/                  # 文档
├── k8s/                   # Kubernetes配置
├── monitoring/            # 监控配置
└── scripts/               # 工具脚本
```

### 开发规范

1. **代码风格** - 遵循PEP 8标准
2. **类型注解** - 使用Python类型提示
3. **文档字符串** - 完整的函数和类文档
4. **测试驱动** - 测试覆盖率 > 70%
5. **异步优先** - 使用async/await模式

### 提交规范

```bash
# 提交格式
<type>(<scope>): <description>

# 示例
feat(auth): 添加JWT刷新令牌功能
fix(database): 修复连接池配置问题
docs(api): 更新API文档
test(services): 增加租户服务测试用例
```

### 发布流程

1. 功能开发完成
2. 通过所有测试
3. 代码审查通过
4. 合并到主分支
5. 自动化部署

## 🚀 部署

### Docker生产部署

```bash
# 1. 构建生产镜像
docker build -t astrbot-saas:latest .

# 2. 使用生产配置
cp docker-compose.prod.yml docker-compose.yml

# 3. 启动生产服务
docker-compose up -d

# 4. 配置反向代理 (Nginx)
# 5. 设置SSL证书
# 6. 配置监控告警
```

### Kubernetes部署

```bash
# 1. 应用Kubernetes配置
kubectl apply -f k8s/

# 2. 配置Ingress
kubectl apply -f k8s/ingress.yaml

# 3. 配置Secret
kubectl create secret generic app-secrets --from-env-file=.env

# 4. 检查部署状态
kubectl get pods -l app=astrbot-saas
```

## 📈 性能

### 基准测试结果

- **并发用户**: 1000+ 
- **响应时间**: < 100ms (P95)
- **吞吐量**: 5000+ 请求/秒
- **可用性**: 99.9%

### 性能优化

- 数据库连接池优化
- Redis缓存加速
- 异步任务处理
- CDN静态资源
- 负载均衡

## 🤝 贡献指南

### 如何贡献

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 贡献者

感谢所有为项目做出贡献的开发者！

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

### 文档

- [API文档](http://localhost:8000/docs) - 交互式API文档
- [开发指南](docs/development.md) - 详细开发说明
- [部署指南](docs/deployment.md) - 生产环境部署

### 社区

- [GitHub Issues](https://github.com/your-org/AstrBot/issues) - 问题报告
- [讨论论坛](https://github.com/your-org/AstrBot/discussions) - 社区讨论
- [技术博客](https://blog.astrbot.com) - 技术分享

### 联系我们

- 📧 Email: <EMAIL>
- 💬 QQ群: 123456789
- 🐦 微信群: 添加微信号 astrbot_official

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！

🎉 **感谢使用 AstrBot SaaS Platform！** 