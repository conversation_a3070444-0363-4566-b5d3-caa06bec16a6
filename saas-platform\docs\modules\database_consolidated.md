# Database 综合文档

*合并自: database_comprehensive_docs.md, models_comprehensive_docs.md*

*合并时间: 2025-06-20 16:08:22*

---

## 来源: database_comprehensive_docs.md

# AstrBot SaaS Platform 数据库核心模块技术文档

## 📋 模块概述

`app/core/database.py` 是 AstrBot SaaS Platform 的数据库基础设施核心模块，基于 SQLAlchemy 2.0 异步架构，提供数据库连接管理、会话控制、环境适配等关键功能。该模块是整个应用数据持久化的基石。

### 核心价值
- **异步高性能：** 基于 SQLAlchemy 2.0 异步引擎，支持高并发访问
- **环境自适应：** 自动适配开发、测试、生产不同环境的数据库配置
- **连接池优化：** 智能连接池管理，提升数据库访问效率
- **健康监控：** 内置连接状态监控和健康检查机制

## 🏗️ 架构设计

### 整体架构图
```mermaid
graph TD
    A[FastAPI Application] --> B[Database Module]
    B --> C[Environment Detection]
    B --> D[Engine Creation]
    B --> E[Session Management]
    B --> F[Health Monitoring]
    
    C --> C1[Test Environment]
    C --> C2[Production Environment]
    
    D --> D1[SQLite Engine]
    D --> D2[PostgreSQL Engine]
    
    E --> E1[Session Factory]
    E --> E2[Dependency Injection]
    E --> E3[Transaction Management]
    
    F --> F1[Connection Check]
    F --> F2[Response Time Monitor]
    F --> F3[Health Endpoint]
```

### 数据流设计
```mermaid
sequenceDiagram
    participant API as API层
    participant DI as 依赖注入
    participant Session as 数据库会话
    participant Engine as 数据库引擎
    participant DB as 数据库

    API->>DI: 请求数据库会话
    DI->>Session: 创建新会话
    Session->>Engine: 获取连接
    Engine->>DB: 建立数据库连接
    DB->>Engine: 连接确认
    Engine->>Session: 返回连接
    Session->>DI: 会话就绪
    DI->>API: 注入会话对象
    
    Note over API,DB: 业务操作执行
    
    API->>Session: 事务提交/回滚
    Session->>Engine: 释放连接
    Engine->>DB: 关闭连接
```

### 依赖关系图
```mermaid
graph LR
    A[Database Module] --> B[SQLAlchemy 2.0]
    A --> C[App Settings]
    A --> D[Logging Utils]
    
    B --> E[AsyncSession]
    B --> F[Create Async Engine]
    B --> G[DeclarativeBase]
    
    C --> H[Environment Variables]
    C --> I[Database URLs]
    C --> J[Debug Settings]
    
    D --> K[Structured Logging]
    D --> L[Error Tracking]
```

## 🔧 核心功能模块

### 1. 环境检测与配置

#### 智能环境识别
```python
def is_test_environment() -> bool:
    """多重检测机制确保准确识别测试环境"""
    return (
        "pytest" in os.environ.get("PYTEST_CURRENT_TEST", "") or    # pytest 检测
        "test" in os.environ.get("DATABASE_URL", "") or             # URL 包含 test
        os.environ.get("TESTING", "").lower() == "true"             # 显式测试标志
    )
```

#### 数据库URL动态选择
```python
def get_database_url() -> str:
    """根据环境动态选择最适合的数据库配置"""
    if is_test_environment():
        # 测试环境：快速、隔离的SQLite
        return "sqlite+aiosqlite:///./test.db"
    else:
        # 生产环境：从配置读取PostgreSQL URL
        return settings.DATABASE_URL
```

**环境配置策略：**
- **测试环境：** SQLite 内存数据库，快速且隔离
- **开发环境：** 本地 PostgreSQL 或 SQLite 文件
- **生产环境：** 生产级 PostgreSQL 集群

### 2. 数据库引擎配置

#### 高性能引擎创建
```python
engine = create_async_engine(
    database_url,
    echo=settings.DEBUG,                                    # 开发模式SQL日志
    connect_args=connect_args,                             # 数据库特定参数
    pool_pre_ping=True,                                    # 连接健康检查
    pool_recycle=300 if "postgresql" in database_url else -1,  # 连接回收策略
    pool_size=20 if "postgresql" in database_url else 5,      # 基础连接池大小
    max_overflow=30 if "postgresql" in database_url else 10,  # 最大溢出连接
    future=True,                                           # SQLAlchemy 2.0 语法
)
```

#### 数据库特定优化
```python
# SQLite 特殊配置
if "sqlite" in database_url:
    connect_args = {"check_same_thread": False}  # 支持多线程访问

# PostgreSQL 连接池优化
if "postgresql" in database_url:
    pool_size = 20          # 基础连接数
    max_overflow = 30       # 峰值额外连接
    pool_recycle = 300      # 5分钟连接回收
else:
    # SQLite 轻量级配置
    pool_size = 5
    max_overflow = 10
    pool_recycle = -1       # 不回收连接
```

**配置原理：**
- **pool_pre_ping：** 连接使用前健康检查，避免死连接
- **pool_recycle：** 定期回收长时间连接，防止连接超时
- **pool_size/max_overflow：** 平衡性能与资源消耗

### 3. 会话管理系统

#### 异步会话工厂
```python
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,  # 提交后对象仍然可用
    autoflush=True,          # 自动刷新变更到数据库
    autocommit=False,        # 手动控制事务提交
)
```

#### 依赖注入实现
```python
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI 依赖注入函数，提供数据库会话
    
    特性：
    - 自动会话管理
    - 异常时自动回滚
    - 保证资源释放
    """
    async with AsyncSessionLocal() as session:
        try:
            logger.debug("创建数据库会话")
            yield session
        except Exception as e:
            logger.error(f"数据库会话错误: {str(e)}")
            await session.rollback()  # 异常时回滚事务
            raise
        finally:
            await session.close()     # 确保会话关闭
            logger.debug("关闭数据库会话")
```

#### 会话生命周期管理
```mermaid
stateDiagram-v2
    [*] --> SessionCreated: 创建会话
    SessionCreated --> Active: 开始事务
    Active --> Committed: 成功提交
    Active --> RolledBack: 异常回滚
    Committed --> Closed: 关闭会话
    RolledBack --> Closed: 关闭会话
    Closed --> [*]: 释放资源
```

### 4. 数据模型基类

#### DeclarativeBase 设计
```python
class Base(DeclarativeBase):
    """
    所有 SQLAlchemy 模型的基类
    
    特性：
    - SQLAlchemy 2.0 声明式语法
    - 自动表名生成
    - 通用字段和方法扩展点
    """
    pass
```

#### 模型继承示例
```python
# 使用基类创建数据模型
class Tenant(Base):
    __tablename__ = "tenants"
    
    id: Mapped[UUID] = mapped_column(UUID(as_uuid=True), primary_key=True)
    name: Mapped[str] = mapped_column(String(100), unique=True)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True))
```

### 5. 数据库初始化

#### 开发环境初始化
```python
async def init_db() -> None:
    """
    开发环境数据库初始化
    
    注意：生产环境应使用 Alembic 迁移
    """
    try:
        logger.info("开始初始化数据库")
        async with engine.begin() as conn:
            # 导入所有模型确保注册
            from app.models import tenant, user, session, message
            
            # 创建所有表
            await conn.run_sync(Base.metadata.create_all)
            logger.info("数据库表创建完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        raise
```

#### 生产环境迁移策略
```python
# 生产环境使用 Alembic 进行版本控制
# alembic revision --autogenerate -m "Add new table"
# alembic upgrade head
```

### 6. 健康监控系统

#### 连接状态检查
```python
async def check_db_connection() -> bool:
    """
    数据库连接健康检查
    
    返回：
    - True: 连接正常
    - False: 连接异常
    """
    try:
        async with AsyncSessionLocal() as session:
            # 执行简单查询测试连接
            result = await session.execute("SELECT 1")
            result.scalar()
            logger.info("数据库连接正常")
            return True
    except Exception as e:
        logger.error(f"数据库连接检查失败: {str(e)}")
        return False
```

#### 完整健康报告
```python
async def health_check() -> dict:
    """
    生成详细的数据库健康报告
    
    包含：
    - 连接状态
    - 响应时间
    - 数据库URL（脱敏）
    """
    import time
    
    start_time = time.time()
    is_healthy = await check_db_connection()
    end_time = time.time()
    
    return {
        "database": {
            "status": "healthy" if is_healthy else "unhealthy",
            "response_time_ms": round((end_time - start_time) * 1000, 2),
            "url": database_url.replace(settings.DB_PASSWORD, "***") 
                   if settings.DB_PASSWORD else database_url,
        }
    }
```

## 📊 配置参数详解

### 数据库URL配置

#### PostgreSQL 配置示例
```bash
# 生产环境
DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/astrbot_prod

# 开发环境
DATABASE_URL=postgresql+asyncpg://dev_user:dev_pass@localhost:5432/astrbot_dev

# 测试环境（自动检测，无需配置）
# 自动使用: sqlite+aiosqlite:///./test.db
```

#### 连接参数说明
| 参数 | PostgreSQL | SQLite | 说明 |
|------|------------|--------|------|
| `pool_size` | 20 | 5 | 基础连接池大小 |
| `max_overflow` | 30 | 10 | 峰值时额外连接数 |
| `pool_recycle` | 300秒 | -1 | 连接回收时间 |
| `pool_pre_ping` | True | True | 使用前检查连接 |
| `echo` | Debug模式 | Debug模式 | 是否打印SQL |

### 环境变量配置

#### 必需环境变量
```bash
# 生产数据库URL
DATABASE_URL=postgresql+asyncpg://user:pass@host:port/dbname

# 调试模式（可选）
DEBUG=false

# 测试模式标识（可选）
TESTING=false
```

#### 可选优化参数
```bash
# 数据库连接池配置
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_RECYCLE=300

# 连接超时配置
DB_CONNECT_TIMEOUT=30
DB_COMMAND_TIMEOUT=60
```

## 🔧 集成使用指南

### 1. FastAPI 依赖注入

#### 基本使用模式
```python
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db

@router.get("/tenants")
async def list_tenants(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100
):
    # 数据库操作
    query = select(Tenant).offset(skip).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()
```

#### 事务管理模式
```python
@router.post("/tenants")
async def create_tenant(
    tenant_data: TenantCreate,
    db: AsyncSession = Depends(get_db)
):
    try:
        # 创建租户
        tenant = Tenant(**tenant_data.dict())
        db.add(tenant)
        
        # 创建默认用户
        admin_user = User(tenant_id=tenant.id, role="admin")
        db.add(admin_user)
        
        # 提交事务
        await db.commit()
        
        # 刷新对象获取生成的ID
        await db.refresh(tenant)
        return tenant
        
    except Exception as e:
        # 异常时自动回滚（由get_db处理）
        logger.error(f"创建租户失败: {str(e)}")
        raise HTTPException(500, "创建失败")
```

### 2. 服务层数据访问

#### 服务类集成模式
```python
from app.core.database import get_db

class TenantService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_tenant(self, tenant_data: TenantCreate) -> Tenant:
        # 检查重复
        existing = await self._get_by_email(tenant_data.email)
        if existing:
            raise ValueError("邮箱已存在")
        
        # 创建租户
        tenant = Tenant(**tenant_data.dict())
        self.db.add(tenant)
        await self.db.commit()
        await self.db.refresh(tenant)
        
        return tenant
    
    async def _get_by_email(self, email: str) -> Optional[Tenant]:
        query = select(Tenant).where(Tenant.email == email)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

# 依赖注入工厂
def get_tenant_service(
    db: AsyncSession = Depends(get_db)
) -> TenantService:
    return TenantService(db)
```

### 3. 应用生命周期集成

#### 启动事件处理
```python
from fastapi import FastAPI
from app.core.database import init_db, close_db

app = FastAPI()

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    if settings.ENVIRONMENT == "development":
        await init_db()  # 开发环境自动创建表
    
    # 检查数据库连接
    is_healthy = await check_db_connection()
    if not is_healthy:
        raise RuntimeError("数据库连接失败")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理"""
    await close_db()
```

#### 健康检查端点
```python
@app.get("/health/database")
async def database_health():
    """数据库健康检查端点"""
    return await health_check()
```

## 🔍 最佳实践

### 1. 连接池优化

#### 连接池大小计算
```python
# 推荐配置公式
import os

cpu_count = os.cpu_count() or 4
expected_concurrent_requests = 100

# 基础连接池大小
pool_size = min(cpu_count * 2, 20)

# 最大连接数
max_connections = min(expected_concurrent_requests, 50)
max_overflow = max_connections - pool_size

engine = create_async_engine(
    database_url,
    pool_size=pool_size,
    max_overflow=max_overflow,
    pool_recycle=300,
    pool_pre_ping=True
)
```

#### 连接池监控
```python
from sqlalchemy import event

@event.listens_for(engine.sync_engine, "connect")
def receive_connect(dbapi_connection, connection_record):
    logger.info("新数据库连接建立")

@event.listens_for(engine.sync_engine, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    # 监控连接获取
    pass

@event.listens_for(engine.sync_engine, "checkin")
def receive_checkin(dbapi_connection, connection_record):
    # 监控连接归还
    pass
```

### 2. 事务管理模式

#### 显式事务控制
```python
async def complex_business_operation(db: AsyncSession):
    """复杂业务操作的事务管理"""
    async with db.begin():  # 显式开启事务
        try:
            # 步骤1：创建租户
            tenant = Tenant(name="Test Corp")
            db.add(tenant)
            await db.flush()  # 获取ID但不提交
            
            # 步骤2：创建用户
            user = User(tenant_id=tenant.id, email="<EMAIL>")
            db.add(user)
            
            # 步骤3：发送欢迎邮件
            await send_welcome_email(user.email)
            
            # 事务自动提交
            logger.info("业务操作完成")
            
        except Exception as e:
            # 异常时自动回滚
            logger.error(f"业务操作失败: {str(e)}")
            raise
```

#### 嵌套事务（保存点）
```python
async def nested_transaction_example(db: AsyncSession):
    """使用保存点实现嵌套事务"""
    try:
        # 主事务
        tenant = Tenant(name="Main Tenant")
        db.add(tenant)
        
        # 嵌套事务（保存点）
        async with db.begin_nested():
            user = User(tenant_id=tenant.id)
            db.add(user)
            
            if some_condition:
                raise ValueError("条件不满足")
            # 保存点自动提交
        
        # 主事务提交
        await db.commit()
        
    except ValueError:
        # 只回滚到保存点，主事务继续
        logger.warning("嵌套操作失败，但主事务继续")
        await db.commit()
```

### 3. 查询优化策略

#### 预加载关联数据
```python
from sqlalchemy.orm import selectinload, joinedload

async def get_tenant_with_users(db: AsyncSession, tenant_id: UUID):
    """预加载关联数据，避免N+1查询"""
    query = (
        select(Tenant)
        .options(selectinload(Tenant.users))  # 预加载用户
        .where(Tenant.id == tenant_id)
    )
    result = await db.execute(query)
    return result.scalar_one_or_none()

async def get_tenants_with_stats(db: AsyncSession):
    """使用JOIN优化复杂查询"""
    query = (
        select(Tenant, func.count(User.id).label("user_count"))
        .join(User, Tenant.id == User.tenant_id, isouter=True)
        .group_by(Tenant.id)
        .options(joinedload(Tenant.users))
    )
    result = await db.execute(query)
    return result.all()
```

#### 批量操作优化
```python
async def bulk_create_users(db: AsyncSession, users_data: List[dict]):
    """批量创建用户的高效方法"""
    # 方法1：bulk_insert_mappings（最快）
    await db.execute(
        insert(User),
        users_data
    )
    
    # 方法2：add_all（支持关系）
    users = [User(**data) for data in users_data]
    db.add_all(users)
    
    await db.commit()

async def bulk_update_status(db: AsyncSession, tenant_ids: List[UUID], status: str):
    """批量更新状态"""
    await db.execute(
        update(Tenant)
        .where(Tenant.id.in_(tenant_ids))
        .values(status=status)
    )
    await db.commit()
```

## 🐛 故障排除

### 常见问题诊断

#### 1. 连接池耗尽
**问题表现：**
```
sqlalchemy.exc.TimeoutError: QueuePool limit of size 20 overflow 30 reached
```

**诊断方法：**
```python
# 检查连接池状态
def check_pool_status():
    pool = engine.pool
    logger.info(f"连接池状态:")
    logger.info(f"  总大小: {pool.size()}")
    logger.info(f"  已检出: {pool.checkedout()}")
    logger.info(f"  溢出: {pool.overflow()}")
    logger.info(f"  无效: {pool.invalidated()}")

# 监控长时间运行的会话
import time
from contextvars import ContextVar

session_start_time: ContextVar[float] = ContextVar('session_start_time')

@event.listens_for(AsyncSession, "after_begin")
def track_session_start(session, transaction, connection):
    session_start_time.set(time.time())

@event.listens_for(AsyncSession, "after_commit")
def track_session_end(session):
    start = session_start_time.get(0)
    duration = time.time() - start
    if duration > 10:  # 超过10秒的会话
        logger.warning(f"长时间运行的会话: {duration:.2f}秒")
```

**解决方案：**
```python
# 1. 增加连接池大小
engine = create_async_engine(
    database_url,
    pool_size=30,      # 增加基础连接数
    max_overflow=50,   # 增加溢出连接数
    pool_timeout=30,   # 设置获取连接超时
)

# 2. 实现连接池监控和自动回收
async def cleanup_connections():
    """定期清理空闲连接"""
    try:
        # 回收空闲连接
        await engine.dispose()
        logger.info("连接池已清理")
    except Exception as e:
        logger.error(f"连接池清理失败: {str(e)}")

# 3. 使用连接上下文管理器
class DatabaseManager:
    async def __aenter__(self):
        self.session = AsyncSessionLocal()
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        try:
            if exc_type:
                await self.session.rollback()
            else:
                await self.session.commit()
        finally:
            await self.session.close()

# 使用示例
async def safe_database_operation():
    async with DatabaseManager() as db:
        # 数据库操作
        tenant = Tenant(name="Test")
        db.add(tenant)
        # 自动提交和关闭
```

#### 2. 死锁检测和处理
**问题表现：**
```
sqlalchemy.exc.OperationalError: (psycopg2.OperationalError) deadlock detected
```

**解决方案：**
```python
import asyncio
from sqlalchemy.exc import OperationalError

async def retry_on_deadlock(operation, max_retries=3):
    """死锁重试机制"""
    for attempt in range(max_retries):
        try:
            return await operation()
        except OperationalError as e:
            if "deadlock detected" in str(e).lower() and attempt < max_retries - 1:
                # 随机延迟后重试
                delay = 0.1 * (2 ** attempt) + random.uniform(0, 0.1)
                await asyncio.sleep(delay)
                logger.warning(f"检测到死锁，重试第 {attempt + 1} 次")
                continue
            raise

# 使用示例
async def safe_tenant_update(tenant_id: UUID, data: dict):
    async def update_operation():
        async with AsyncSessionLocal() as db:
            # 按固定顺序获取锁，减少死锁概率
            tenant = await db.get(Tenant, tenant_id, with_for_update=True)
            for key, value in data.items():
                setattr(tenant, key, value)
            await db.commit()
            return tenant
    
    return await retry_on_deadlock(update_operation)
```

#### 3. 内存泄漏排查
**诊断工具：**
```python
import psutil
import gc
from weakref import WeakSet

# 会话跟踪器
active_sessions = WeakSet()

class TrackedAsyncSession(AsyncSession):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        active_sessions.add(self)
        self._created_at = time.time()

async def memory_monitor():
    """内存使用监控"""
    while True:
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        
        logger.info(f"内存使用: {memory_mb:.1f}MB")
        logger.info(f"活跃会话数: {len(active_sessions)}")
        
        # 检查长时间运行的会话
        current_time = time.time()
        old_sessions = [
            s for s in active_sessions 
            if hasattr(s, '_created_at') and current_time - s._created_at > 300
        ]
        
        if old_sessions:
            logger.warning(f"发现 {len(old_sessions)} 个长时间运行的会话")
        
        await asyncio.sleep(60)  # 每分钟检查一次
```

## 🔮 扩展和优化

### 1. 读写分离架构

```python
# 多数据库引擎配置
class DatabaseRouter:
    def __init__(self):
        self.write_engine = create_async_engine(settings.WRITE_DATABASE_URL)
        self.read_engines = [
            create_async_engine(url) for url in settings.READ_DATABASE_URLS
        ]
        self.read_engine_index = 0
    
    def get_read_engine(self):
        """轮询选择读库"""
        engine = self.read_engines[self.read_engine_index]
        self.read_engine_index = (self.read_engine_index + 1) % len(self.read_engines)
        return engine
    
    def get_write_engine(self):
        """获取写库"""
        return self.write_engine

# 读写会话工厂
router = DatabaseRouter()

ReadSessionLocal = async_sessionmaker(
    router.get_read_engine(),
    class_=AsyncSession,
    expire_on_commit=False,
)

WriteSessionLocal = async_sessionmaker(
    router.get_write_engine(),
    class_=AsyncSession,
    expire_on_commit=False,
)

# 智能会话选择
async def get_db_session(read_only: bool = False) -> AsyncSession:
    if read_only:
        return ReadSessionLocal()
    else:
        return WriteSessionLocal()
```

### 2. 缓存集成

```python
import redis.asyncio as redis
from typing import Optional, Any

class CachedDatabaseService:
    def __init__(self, db: AsyncSession, cache: redis.Redis):
        self.db = db
        self.cache = cache
        self.cache_ttl = 3600  # 1小时
    
    async def get_with_cache(
        self, 
        model_class, 
        model_id: Any, 
        cache_key: Optional[str] = None
    ):
        """带缓存的数据查询"""
        if not cache_key:
            cache_key = f"{model_class.__tablename__}:{model_id}"
        
        # 先查缓存
        cached_data = await self.cache.get(cache_key)
        if cached_data:
            return model_class.model_validate_json(cached_data)
        
        # 缓存未命中，查询数据库
        result = await self.db.get(model_class, model_id)
        if result:
            # 写入缓存
            await self.cache.setex(
                cache_key,
                self.cache_ttl,
                result.model_dump_json()
            )
        
        return result
    
    async def invalidate_cache(self, cache_pattern: str):
        """缓存失效"""
        keys = await self.cache.keys(cache_pattern)
        if keys:
            await self.cache.delete(*keys)
```

### 3. 分库分表支持

```python
class ShardedDatabase:
    def __init__(self, shard_configs: List[dict]):
        self.shards = {}
        for config in shard_configs:
            shard_id = config['shard_id']
            engine = create_async_engine(config['url'])
            self.shards[shard_id] = async_sessionmaker(engine)
    
    def get_shard_id(self, tenant_id: UUID) -> str:
        """根据租户ID计算分片"""
        return f"shard_{hash(str(tenant_id)) % len(self.shards)}"
    
    async def get_session(self, tenant_id: UUID) -> AsyncSession:
        """获取指定租户的数据库会话"""
        shard_id = self.get_shard_id(tenant_id)
        session_maker = self.shards[shard_id]
        return session_maker()
```

---

**文档维护：** 本文档应与数据库架构变更同步更新，确保配置和最佳实践的准确性。
**更新日期：** 2024年12月17日
**版本：** v1.0.0
**维护者：** 技术文档专家 

---

