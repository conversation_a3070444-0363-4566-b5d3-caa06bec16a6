#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AstrBot SaaS Platform 全局质量检查工具
多维度质量评估：代码质量、安全性、文档完整性、测试覆盖率等
"""

import os
import sys
import subprocess
import json
import logging
import ast
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import hashlib

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('quality_check.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class QualityChecker:
    """全局质量检查器"""
    
    def __init__(self):
        """初始化质量检查器"""
        self.project_root = Path.cwd()
        self.results = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'code_quality': {},
            'security': {},
            'documentation': {},
            'testing': {},
            'performance': {},
            'overall_score': 0
        }
        
        # 质量标准配置
        self.quality_standards = {
            'min_doc_coverage': 80,
            'min_test_coverage': 70,
            'max_complexity': 10,
            'max_file_lines': 500,
            'min_function_doc': 60
        }

    def check_code_quality(self) -> Dict:
        """检查代码质量"""
        logger.info("[代码质量] 开始代码质量检查...")
        
        quality_results = {
            'file_count': 0,
            'total_lines': 0,
            'python_files': [],
            'complexity_issues': [],
            'style_issues': [],
            'import_issues': [],
            'score': 0
        }
        
        # 遍历Python文件
        python_files = list(self.project_root.rglob('*.py'))
        quality_results['file_count'] = len(python_files)
        
        for py_file in python_files:
            if self._should_skip_file(py_file):
                continue
                
            file_info = self._analyze_python_file(py_file)
            quality_results['python_files'].append(file_info)
            quality_results['total_lines'] += file_info['lines']
            
            # 检查复杂度
            if file_info['complexity'] > self.quality_standards['max_complexity']:
                quality_results['complexity_issues'].append({
                    'file': str(py_file.relative_to(self.project_root)),
                    'complexity': file_info['complexity']
                })
            
            # 检查文件长度
            if file_info['lines'] > self.quality_standards['max_file_lines']:
                quality_results['style_issues'].append({
                    'file': str(py_file.relative_to(self.project_root)),
                    'issue': f'文件过长 ({file_info["lines"]} 行)'
                })
        
        # 计算代码质量分数
        quality_results['score'] = self._calculate_code_score(quality_results)
        
        logger.info(f"  [统计] Python文件数: {quality_results['file_count']}")
        logger.info(f"  [统计] 代码总行数: {quality_results['total_lines']}")
        logger.info(f"  [评分] 代码质量分数: {quality_results['score']}/100")
        
        return quality_results

    def check_security(self) -> Dict:
        """检查安全性"""
        logger.info("[安全检查] 开始安全性检查...")
        
        security_results = {
            'secret_leaks': [],
            'insecure_patterns': [],
            'dependency_issues': [],
            'permission_issues': [],
            'score': 0
        }
        
        # 检查敏感信息泄露
        self._check_secret_leaks(security_results)
        
        # 检查不安全的代码模式
        self._check_insecure_patterns(security_results)
        
        # 检查依赖安全性
        self._check_dependencies(security_results)
        
        # 检查文件权限
        self._check_file_permissions(security_results)
        
        # 计算安全分数
        security_results['score'] = self._calculate_security_score(security_results)
        
        logger.info(f"  [检查] 发现敏感信息泄露: {len(security_results['secret_leaks'])}个")
        logger.info(f"  [检查] 发现不安全模式: {len(security_results['insecure_patterns'])}个")
        logger.info(f"  [评分] 安全性分数: {security_results['score']}/100")
        
        return security_results

    def check_documentation(self) -> Dict:
        """检查文档完整性"""
        logger.info("[文档检查] 开始文档完整性检查...")
        
        doc_results = {
            'readme_quality': {},
            'api_docs': {},
            'code_comments': {},
            'docstring_coverage': 0,
            'score': 0
        }
        
        # 检查README质量
        doc_results['readme_quality'] = self._check_readme_quality()
        
        # 检查API文档
        doc_results['api_docs'] = self._check_api_docs()
        
        # 检查代码注释和文档字符串
        doc_results['code_comments'] = self._check_code_comments()
        doc_results['docstring_coverage'] = self._calculate_docstring_coverage()
        
        # 计算文档分数
        doc_results['score'] = self._calculate_doc_score(doc_results)
        
        logger.info(f"  [统计] 文档字符串覆盖率: {doc_results['docstring_coverage']:.1f}%")
        logger.info(f"  [评分] 文档质量分数: {doc_results['score']}/100")
        
        return doc_results

    def check_testing(self) -> Dict:
        """检查测试覆盖率"""
        logger.info("[测试检查] 开始测试覆盖率检查...")
        
        test_results = {
            'test_files': [],
            'test_coverage': 0,
            'test_types': {},
            'score': 0
        }
        
        # 查找测试文件
        test_files = list(self.project_root.rglob('test_*.py')) + \
                    list(self.project_root.rglob('*_test.py')) + \
                    list((self.project_root / 'tests').rglob('*.py')) if (self.project_root / 'tests').exists() else []
        
        test_results['test_files'] = [str(f.relative_to(self.project_root)) for f in test_files]
        
        # 分析测试类型
        test_results['test_types'] = self._analyze_test_types(test_files)
        
        # 估算测试覆盖率
        test_results['test_coverage'] = self._estimate_test_coverage()
        
        # 计算测试分数
        test_results['score'] = self._calculate_test_score(test_results)
        
        logger.info(f"  [统计] 测试文件数: {len(test_results['test_files'])}")
        logger.info(f"  [统计] 测试覆盖率: {test_results['test_coverage']:.1f}%")
        logger.info(f"  [评分] 测试质量分数: {test_results['score']}/100")
        
        return test_results

    def check_performance(self) -> Dict:
        """检查性能指标"""
        logger.info("[性能检查] 开始性能指标检查...")
        
        perf_results = {
            'large_files': [],
            'complex_functions': [],
            'import_cycles': [],
            'optimization_suggestions': [],
            'score': 0
        }
        
        # 检查大文件
        self._check_large_files(perf_results)
        
        # 检查复杂函数
        self._check_complex_functions(perf_results)
        
        # 检查导入循环
        self._check_import_cycles(perf_results)
        
        # 生成优化建议
        self._generate_optimization_suggestions(perf_results)
        
        # 计算性能分数
        perf_results['score'] = self._calculate_performance_score(perf_results)
        
        logger.info(f"  [检查] 大文件数量: {len(perf_results['large_files'])}")
        logger.info(f"  [检查] 复杂函数数量: {len(perf_results['complex_functions'])}")
        logger.info(f"  [评分] 性能指标分数: {perf_results['score']}/100")
        
        return perf_results

    def _should_skip_file(self, file_path: Path) -> bool:
        """判断是否应该跳过文件"""
        skip_patterns = [
            '__pycache__',
            '.git',
            'venv',
            'env',
            'node_modules',
            '.pytest_cache',
            'htmlcov',
            'migrations'
        ]
        
        return any(pattern in str(file_path) for pattern in skip_patterns)

    def _analyze_python_file(self, file_path: Path) -> Dict:
        """分析Python文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            return {
                'path': str(file_path.relative_to(self.project_root)),
                'lines': len(content.splitlines()),
                'functions': len([n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef)]),
                'classes': len([n for n in ast.walk(tree) if isinstance(n, ast.ClassDef)]),
                'complexity': self._calculate_complexity(tree),
                'has_docstring': ast.get_docstring(tree) is not None,
                'imports': len([n for n in ast.walk(tree) if isinstance(n, (ast.Import, ast.ImportFrom))])
            }
        except Exception as e:
            logger.warning(f"  [警告] 无法分析文件 {file_path}: {e}")
            return {
                'path': str(file_path.relative_to(self.project_root)),
                'lines': 0,
                'functions': 0,
                'classes': 0,
                'complexity': 0,
                'has_docstring': False,
                'imports': 0
            }

    def _calculate_complexity(self, tree: ast.AST) -> int:
        """计算代码复杂度"""
        complexity = 1  # 基础复杂度
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.For, ast.While, ast.Try, ast.With)):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1
        
        return complexity

    def _check_secret_leaks(self, results: Dict) -> None:
        """检查敏感信息泄露"""
        secret_patterns = [
            (r'password\s*=\s*["\'][^"\']{8,}["\']', '硬编码密码'),
            (r'api_key\s*=\s*["\'][^"\']+["\']', 'API密钥'),
            (r'secret_key\s*=\s*["\'][^"\']+["\']', '密钥'),
            (r'token\s*=\s*["\'][^"\']+["\']', '令牌'),
            (r'aws_access_key_id\s*=\s*["\'][^"\']+["\']', 'AWS访问密钥'),
        ]
        
        for file_path in self.project_root.rglob('*'):
            if file_path.is_file() and not self._should_skip_file(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    for pattern, desc in secret_patterns:
                        matches = re.finditer(pattern, content, re.IGNORECASE)
                        for match in matches:
                            results['secret_leaks'].append({
                                'file': str(file_path.relative_to(self.project_root)),
                                'type': desc,
                                'line': content[:match.start()].count('\n') + 1
                            })
                except:
                    continue

    def _check_insecure_patterns(self, results: Dict) -> None:
        """检查不安全的代码模式"""
        insecure_patterns = [
            (r'eval\s*\(', '使用eval()函数'),
            (r'exec\s*\(', '使用exec()函数'),
            (r'shell\s*=\s*True', '使用shell=True'),
            (r'input\s*\([^)]*\)', '使用input()可能不安全'),
            (r'pickle\.loads?\s*\(', '使用pickle可能不安全'),
        ]
        
        python_files = list(self.project_root.rglob('*.py'))
        
        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern, desc in insecure_patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        results['insecure_patterns'].append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'pattern': desc,
                            'line': content[:match.start()].count('\n') + 1
                        })
            except:
                continue

    def _check_dependencies(self, results: Dict) -> None:
        """检查依赖安全性"""
        # 检查requirements.txt和pyproject.toml
        req_files = ['requirements.txt', 'pyproject.toml', 'Pipfile']
        
        for req_file in req_files:
            file_path = self.project_root / req_file
            if file_path.exists():
                results['dependency_issues'].append({
                    'file': req_file,
                    'suggestion': '建议定期更新依赖并检查安全漏洞'
                })

    def _check_file_permissions(self, results: Dict) -> None:
        """检查文件权限"""
        sensitive_files = ['.env', 'config.py', 'settings.py']
        
        for file_name in sensitive_files:
            for file_path in self.project_root.rglob(file_name):
                if file_path.exists():
                    results['permission_issues'].append({
                        'file': str(file_path.relative_to(self.project_root)),
                        'suggestion': '敏感配置文件应设置适当权限'
                    })

    def _check_readme_quality(self) -> Dict:
        """检查README质量"""
        readme_files = ['README.md', 'README.rst', 'README.txt']
        readme_info = {'exists': False, 'quality_score': 0, 'sections': []}
        
        for readme_name in readme_files:
            readme_path = self.project_root / readme_name
            if readme_path.exists():
                readme_info['exists'] = True
                
                try:
                    with open(readme_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查关键章节
                    required_sections = [
                        '安装', '使用', '配置', '贡献', 'API', '示例'
                    ]
                    
                    for section in required_sections:
                        if section.lower() in content.lower():
                            readme_info['sections'].append(section)
                    
                    readme_info['quality_score'] = min(100, len(readme_info['sections']) * 20)
                    
                except:
                    readme_info['quality_score'] = 30
                break
        
        return readme_info

    def _check_api_docs(self) -> Dict:
        """检查API文档"""
        api_doc_info = {'swagger_exists': False, 'doc_files': []}
        
        # 检查Swagger/OpenAPI文档
        swagger_files = ['openapi.json', 'swagger.json', 'api.yaml']
        for swagger_file in swagger_files:
            if (self.project_root / swagger_file).exists():
                api_doc_info['swagger_exists'] = True
                api_doc_info['doc_files'].append(swagger_file)
        
        # 检查docs目录
        docs_dir = self.project_root / 'docs'
        if docs_dir.exists():
            doc_files = list(docs_dir.rglob('*.md')) + list(docs_dir.rglob('*.rst'))
            api_doc_info['doc_files'].extend([str(f.relative_to(self.project_root)) for f in doc_files])
        
        return api_doc_info

    def _check_code_comments(self) -> Dict:
        """检查代码注释"""
        comment_info = {'total_lines': 0, 'comment_lines': 0, 'ratio': 0}
        
        python_files = list(self.project_root.rglob('*.py'))
        
        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                comment_info['total_lines'] += len(lines)
                
                for line in lines:
                    stripped = line.strip()
                    if stripped.startswith('#') or '"""' in stripped or "'''" in stripped:
                        comment_info['comment_lines'] += 1
                        
            except:
                continue
        
        if comment_info['total_lines'] > 0:
            comment_info['ratio'] = (comment_info['comment_lines'] / comment_info['total_lines']) * 100
        
        return comment_info

    def _calculate_docstring_coverage(self) -> float:
        """计算文档字符串覆盖率"""
        total_functions = 0
        documented_functions = 0
        
        python_files = list(self.project_root.rglob('*.py'))
        
        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                        total_functions += 1
                        if ast.get_docstring(node):
                            documented_functions += 1
                            
            except:
                continue
        
        return (documented_functions / total_functions * 100) if total_functions > 0 else 0

    def _analyze_test_types(self, test_files: List[Path]) -> Dict:
        """分析测试类型"""
        test_types = {'unit': 0, 'integration': 0, 'e2e': 0, 'performance': 0}
        
        for test_file in test_files:
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                
                if 'unit' in str(test_file).lower() or 'test_unit' in content:
                    test_types['unit'] += 1
                elif 'integration' in str(test_file).lower() or 'test_integration' in content:
                    test_types['integration'] += 1
                elif 'e2e' in str(test_file).lower() or 'test_e2e' in content:
                    test_types['e2e'] += 1
                elif 'performance' in str(test_file).lower() or 'test_performance' in content:
                    test_types['performance'] += 1
                    
            except:
                continue
        
        return test_types

    def _estimate_test_coverage(self) -> float:
        """估算测试覆盖率"""
        # 基于测试文件数量和主要代码文件数量的简单估算
        python_files = [f for f in self.project_root.rglob('*.py') if not self._should_skip_file(f)]
        test_files = [f for f in python_files if 'test' in str(f).lower()]
        
        main_files = [f for f in python_files if 'test' not in str(f).lower()]
        
        if len(main_files) == 0:
            return 0
        
        # 简单的估算公式
        coverage = min(100, (len(test_files) / len(main_files)) * 50)
        return coverage

    def _check_large_files(self, results: Dict) -> None:
        """检查大文件"""
        for file_path in self.project_root.rglob('*.py'):
            if self._should_skip_file(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                
                if lines > self.quality_standards['max_file_lines']:
                    results['large_files'].append({
                        'file': str(file_path.relative_to(self.project_root)),
                        'lines': lines
                    })
            except:
                continue

    def _check_complex_functions(self, results: Dict) -> None:
        """检查复杂函数"""
        for file_path in self.project_root.rglob('*.py'):
            if self._should_skip_file(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                        complexity = self._calculate_complexity(node)
                        if complexity > self.quality_standards['max_complexity']:
                            results['complex_functions'].append({
                                'file': str(file_path.relative_to(self.project_root)),
                                'function': node.name,
                                'complexity': complexity
                            })
            except:
                continue

    def _check_import_cycles(self, results: Dict) -> None:
        """检查导入循环"""
        # 简化的导入循环检查
        import_graph = {}
        
        for file_path in self.project_root.rglob('*.py'):
            if self._should_skip_file(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                imports = []
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    elif isinstance(node, ast.ImportFrom):
                        if node.module:
                            imports.append(node.module)
                
                import_graph[str(file_path.relative_to(self.project_root))] = imports
                
            except:
                continue
        
        # 这里可以添加更复杂的循环检测算法
        results['import_cycles'] = []  # 简化实现

    def _generate_optimization_suggestions(self, results: Dict) -> None:
        """生成优化建议"""
        suggestions = []
        
        if results['large_files']:
            suggestions.append("建议将大文件拆分为更小的模块")
        
        if results['complex_functions']:
            suggestions.append("建议重构复杂函数，提高可读性")
        
        suggestions.append("定期进行代码评审和重构")
        suggestions.append("使用代码分析工具持续监控代码质量")
        
        results['optimization_suggestions'] = suggestions

    def _calculate_code_score(self, results: Dict) -> int:
        """计算代码质量分数"""
        score = 100
        
        # 复杂度问题扣分
        score -= len(results['complexity_issues']) * 5
        
        # 样式问题扣分
        score -= len(results['style_issues']) * 3
        
        # 导入问题扣分
        score -= len(results['import_issues']) * 2
        
        return max(0, score)

    def _calculate_security_score(self, results: Dict) -> int:
        """计算安全性分数"""
        score = 100
        
        # 敏感信息泄露严重扣分
        score -= len(results['secret_leaks']) * 20
        
        # 不安全模式扣分
        score -= len(results['insecure_patterns']) * 10
        
        # 权限问题扣分
        score -= len(results['permission_issues']) * 5
        
        return max(0, score)

    def _calculate_doc_score(self, results: Dict) -> int:
        """计算文档质量分数"""
        score = 0
        
        # README质量
        score += results['readme_quality']['quality_score'] * 0.3
        
        # 文档字符串覆盖率
        score += results['docstring_coverage'] * 0.5
        
        # API文档
        if results['api_docs']['swagger_exists']:
            score += 20
        
        return min(100, int(score))

    def _calculate_test_score(self, results: Dict) -> int:
        """计算测试质量分数"""
        score = 0
        
        # 测试覆盖率
        score += results['test_coverage']
        
        # 测试类型多样性
        test_types = len([v for v in results['test_types'].values() if v > 0])
        score += test_types * 10
        
        return min(100, int(score))

    def _calculate_performance_score(self, results: Dict) -> int:
        """计算性能指标分数"""
        score = 100
        
        # 大文件扣分
        score -= len(results['large_files']) * 10
        
        # 复杂函数扣分
        score -= len(results['complex_functions']) * 5
        
        # 导入循环扣分
        score -= len(results['import_cycles']) * 15
        
        return max(0, score)

    def calculate_overall_score(self) -> int:
        """计算总体质量分数"""
        weights = {
            'code_quality': 0.25,
            'security': 0.25,
            'documentation': 0.20,
            'testing': 0.20,
            'performance': 0.10
        }
        
        total_score = 0
        for category, weight in weights.items():
            if category in self.results and 'score' in self.results[category]:
                total_score += self.results[category]['score'] * weight
        
        return int(total_score)

    def generate_report(self) -> None:
        """生成质量检查报告"""
        logger.info("\n" + "="*70)
        logger.info("AstrBot SaaS Platform 全局质量检查报告")
        logger.info("="*70)
        
        # 各项分数
        logger.info(f"\n[分数] 质量评分:")
        logger.info(f"  代码质量: {self.results['code_quality']['score']}/100")
        logger.info(f"  安全性: {self.results['security']['score']}/100")
        logger.info(f"  文档质量: {self.results['documentation']['score']}/100")
        logger.info(f"  测试覆盖: {self.results['testing']['score']}/100")
        logger.info(f"  性能指标: {self.results['performance']['score']}/100")
        
        overall_score = self.calculate_overall_score()
        self.results['overall_score'] = overall_score
        
        logger.info(f"\n[总分] 综合质量分数: {overall_score}/100")
        
        # 质量等级
        if overall_score >= 90:
            grade = "优秀 (A)"
        elif overall_score >= 80:
            grade = "良好 (B)"
        elif overall_score >= 70:
            grade = "中等 (C)"
        elif overall_score >= 60:
            grade = "及格 (D)"
        else:
            grade = "需要改进 (F)"
        
        logger.info(f"[等级] 质量等级: {grade}")
        
        # 主要问题
        issues = []
        if self.results['security']['secret_leaks']:
            issues.append("发现敏感信息泄露")
        if self.results['code_quality']['complexity_issues']:
            issues.append("存在高复杂度代码")
        if self.results['testing']['test_coverage'] < 70:
            issues.append("测试覆盖率不足")
        
        if issues:
            logger.info(f"\n[问题] 主要问题:")
            for issue in issues:
                logger.info(f"  - {issue}")
        
        # 改进建议
        logger.info(f"\n[建议] 改进建议:")
        logger.info("  1. 定期进行代码审查")
        logger.info("  2. 增加单元测试和集成测试")
        logger.info("  3. 完善API文档和用户手册")
        logger.info("  4. 使用自动化工具监控代码质量")
        
        logger.info("="*70)

    def save_report(self) -> None:
        """保存质量检查报告"""
        report_file = self.project_root / 'quality_report.json'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"[保存] 详细报告已保存到: {report_file}")

    def run_full_check(self) -> bool:
        """运行完整质量检查"""
        logger.info("[开始] AstrBot SaaS Platform 全局质量检查...")
        logger.info(f"[时间] 检查时间: {self.results['timestamp']}")
        
        try:
            # 执行各项检查
            self.results['code_quality'] = self.check_code_quality()
            self.results['security'] = self.check_security()
            self.results['documentation'] = self.check_documentation()
            self.results['testing'] = self.check_testing()
            self.results['performance'] = self.check_performance()
            
            # 生成报告
            self.generate_report()
            self.save_report()
            
            return True
            
        except Exception as e:
            logger.error(f"[错误] 质量检查过程出错: {e}")
            return False

def main():
    """主函数"""
    print("AstrBot SaaS Platform 全局质量检查工具")
    print("="*50)
    
    checker = QualityChecker()
    
    try:
        success = checker.run_full_check()
        
        if success:
            print(f"\n[成功] 质量检查完成! 总体分数: {checker.results['overall_score']}/100")
        else:
            print("\n[失败] 质量检查遇到问题")
            
    except KeyboardInterrupt:
        print("\n[中断] 用户中断检查")
    except Exception as e:
        logger.error(f"[错误] 检查过程出错: {e}")

if __name__ == "__main__":
    main() 