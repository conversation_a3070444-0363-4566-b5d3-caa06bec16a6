#!/usr/bin/env python3
"""
文档分析和优化工具
分析项目中的markdown文档，识别重复内容、冗余文档，并提供优化建议
"""

import os
import re
import hashlib
from pathlib import Path
from typing import Dict, List, Tuple, Set
from collections import defaultdict
import difflib


class DocumentAnalyzer:
    """文档分析器"""
    
    def __init__(self, root_path: str):
        self.root_path = Path(root_path)
        self.documents = {}  # 文件路径 -> 内容
        self.document_stats = {}  # 文件路径 -> 统计信息
        self.duplicates = defaultdict(list)  # 内容哈希 -> 文件列表
        self.similar_docs = []  # 相似文档对
        
    def scan_documents(self) -> None:
        """扫描所有markdown文档"""
        print("🔍 扫描markdown文档...")
        
        md_files = list(self.root_path.rglob("*.md"))
        print(f"📁 发现 {len(md_files)} 个markdown文件")
        
        for file_path in md_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                relative_path = str(file_path.relative_to(self.root_path))
                self.documents[relative_path] = content
                
                # 统计信息
                lines = content.count('\n') + 1
                words = len(content.split())
                chars = len(content)
                
                self.document_stats[relative_path] = {
                    'lines': lines,
                    'words': words,
                    'chars': chars,
                    'size_kb': chars / 1024
                }
                
            except Exception as e:
                print(f"⚠️ 读取文件失败: {file_path} - {e}")
    
    def find_duplicate_content(self) -> None:
        """查找重复内容"""
        print("\n🔍 查找重复内容...")
        
        content_hashes = {}
        
        for file_path, content in self.documents.items():
            # 规范化内容（移除多余空白）
            normalized = re.sub(r'\s+', ' ', content.strip())
            content_hash = hashlib.md5(normalized.encode()).hexdigest()
            
            if content_hash in content_hashes:
                self.duplicates[content_hash].append(file_path)
            else:
                content_hashes[content_hash] = file_path
                self.duplicates[content_hash] = [file_path]
        
        # 只保留有重复的
        self.duplicates = {k: v for k, v in self.duplicates.items() if len(v) > 1}
        
        if self.duplicates:
            print(f"❌ 发现 {len(self.duplicates)} 组重复内容")
        else:
            print("✅ 未发现完全重复的内容")
    
    def find_similar_content(self, threshold: float = 0.8) -> None:
        """查找相似内容"""
        print(f"\n🔍 查找相似内容（相似度阈值: {threshold*100}%）...")
        
        file_list = list(self.documents.keys())
        
        for i in range(len(file_list)):
            for j in range(i + 1, len(file_list)):
                file1, file2 = file_list[i], file_list[j]
                content1 = self.documents[file1]
                content2 = self.documents[file2]
                
                # 计算相似度
                similarity = self._calculate_similarity(content1, content2)
                
                if similarity >= threshold:
                    self.similar_docs.append({
                        'file1': file1,
                        'file2': file2,
                        'similarity': similarity
                    })
        
        print(f"⚠️ 发现 {len(self.similar_docs)} 对相似文档")
    
    def _calculate_similarity(self, content1: str, content2: str) -> float:
        """计算两个文档的相似度"""
        lines1 = content1.splitlines()
        lines2 = content2.splitlines()
        
        matcher = difflib.SequenceMatcher(None, lines1, lines2)
        return matcher.ratio()
    
    def analyze_document_patterns(self) -> Dict:
        """分析文档模式"""
        print("\n🔍 分析文档模式...")
        
        patterns = {
            'comprehensive_docs': [],
            'reports': [],
            'guides': [],
            'summaries': [],
            'test_docs': [],
            'security_docs': [],
            'api_docs': [],
            'others': []
        }
        
        for file_path in self.documents.keys():
            file_name = Path(file_path).name.lower()
            
            if 'comprehensive' in file_name:
                patterns['comprehensive_docs'].append(file_path)
            elif 'report' in file_name or '报告' in file_name:
                patterns['reports'].append(file_path)
            elif 'guide' in file_name or '指南' in file_name or 'manual' in file_name:
                patterns['guides'].append(file_path)
            elif 'summary' in file_name or '总结' in file_name:
                patterns['summaries'].append(file_path)
            elif 'test' in file_name or '测试' in file_name:
                patterns['test_docs'].append(file_path)
            elif 'security' in file_name or '安全' in file_name:
                patterns['security_docs'].append(file_path)
            elif 'api' in file_name or 'docs.md' in file_name:
                patterns['api_docs'].append(file_path)
            else:
                patterns['others'].append(file_path)
        
        return patterns
    
    def identify_redundant_docs(self) -> List[Dict]:
        """识别冗余文档"""
        print("\n🔍 识别冗余文档...")
        
        redundant = []
        patterns = self.analyze_document_patterns()
        
        # 检查comprehensive系列文档
        if len(patterns['comprehensive_docs']) > 5:
            total_lines = sum(self.document_stats[doc]['lines'] 
                            for doc in patterns['comprehensive_docs'])
            redundant.append({
                'type': 'comprehensive_overload',
                'files': patterns['comprehensive_docs'],
                'count': len(patterns['comprehensive_docs']),
                'total_lines': total_lines,
                'suggestion': '考虑合并为单一综合文档或按模块重新组织'
            })
        
        # 检查重复的报告
        if len(patterns['reports']) > 10:
            redundant.append({
                'type': 'excessive_reports',
                'files': patterns['reports'],
                'count': len(patterns['reports']),
                'suggestion': '保留最新和最重要的报告，归档其他报告'
            })
        
        # 检查测试文档过多
        if len(patterns['test_docs']) > 15:
            redundant.append({
                'type': 'excessive_test_docs',
                'files': patterns['test_docs'],
                'count': len(patterns['test_docs']),
                'suggestion': '合并测试文档，建立统一的测试文档结构'
            })
        
        return redundant
    
    def generate_optimization_plan(self) -> Dict:
        """生成优化计划"""
        print("\n📋 生成优化计划...")
        
        patterns = self.analyze_document_patterns()
        redundant = self.identify_redundant_docs()
        
        # 计算当前文档统计
        total_files = len(self.documents)
        total_lines = sum(stats['lines'] for stats in self.document_stats.values())
        total_size_kb = sum(stats['size_kb'] for stats in self.document_stats.values())
        
        # 优化建议
        optimizations = []
        
        # 1. 合并comprehensive文档
        if patterns['comprehensive_docs']:
            estimated_reduction = len(patterns['comprehensive_docs']) * 0.7
            optimizations.append({
                'action': 'merge_comprehensive_docs',
                'description': '合并所有comprehensive文档为模块化文档',
                'files_to_merge': patterns['comprehensive_docs'],
                'estimated_file_reduction': int(estimated_reduction),
                'new_structure': [
                    'docs/modules/api_comprehensive.md',
                    'docs/modules/services_comprehensive.md', 
                    'docs/modules/security_comprehensive.md'
                ]
            })
        
        # 2. 清理重复报告
        if len(patterns['reports']) > 5:
            keep_reports = sorted(patterns['reports'], 
                                key=lambda x: self.document_stats[x]['lines'], 
                                reverse=True)[:3]
            remove_reports = [r for r in patterns['reports'] if r not in keep_reports]
            
            optimizations.append({
                'action': 'cleanup_reports',
                'description': '保留最重要的3个报告，归档其他报告',
                'files_to_keep': keep_reports,
                'files_to_archive': remove_reports,
                'estimated_file_reduction': len(remove_reports)
            })
        
        # 3. 整理测试文档
        if len(patterns['test_docs']) > 10:
            optimizations.append({
                'action': 'consolidate_test_docs',
                'description': '整合测试文档为统一结构',
                'files_to_merge': patterns['test_docs'],
                'new_structure': [
                    'docs/testing/test_strategy.md',
                    'docs/testing/test_reports.md',
                    'docs/testing/coverage_reports.md'
                ],
                'estimated_file_reduction': len(patterns['test_docs']) - 3
            })
        
        # 4. 处理相似文档
        if self.similar_docs:
            similar_groups = defaultdict(set)
            for doc_pair in self.similar_docs:
                similar_groups[doc_pair['file1']].add(doc_pair['file2'])
            
            optimizations.append({
                'action': 'merge_similar_docs',
                'description': '合并相似度高的文档',
                'similar_groups': dict(similar_groups),
                'estimated_file_reduction': len(self.similar_docs)
            })
        
        plan = {
            'current_stats': {
                'total_files': total_files,
                'total_lines': total_lines,
                'total_size_kb': round(total_size_kb, 2)
            },
            'problems_identified': {
                'duplicate_groups': len(self.duplicates),
                'similar_pairs': len(self.similar_docs),
                'redundant_categories': len(redundant)
            },
            'optimizations': optimizations,
            'estimated_reduction': {
                'files': sum(opt.get('estimated_file_reduction', 0) for opt in optimizations),
                'lines_percentage': 60  # 预估减少60%的行数
            }
        }
        
        return plan
    
    def print_analysis_report(self) -> None:
        """打印分析报告"""
        print("\n" + "="*80)
        print("📊 文档分析报告")
        print("="*80)
        
        # 基本统计
        total_files = len(self.documents)
        total_lines = sum(stats['lines'] for stats in self.document_stats.values())
        total_size_kb = sum(stats['size_kb'] for stats in self.document_stats.values())
        
        print(f"\n📈 基本统计:")
        print(f"  文档总数: {total_files}")
        print(f"  总行数: {total_lines:,}")
        print(f"  总大小: {total_size_kb:.1f} KB")
        
        # 最大的文档
        largest_docs = sorted(self.document_stats.items(), 
                            key=lambda x: x[1]['lines'], reverse=True)[:10]
        
        print(f"\n📋 最大的10个文档:")
        for i, (file_path, stats) in enumerate(largest_docs, 1):
            print(f"  {i:2d}. {Path(file_path).name:<40} {stats['lines']:>6} 行")
        
        # 重复内容
        if self.duplicates:
            print(f"\n❌ 重复内容 ({len(self.duplicates)} 组):")
            for i, (hash_key, files) in enumerate(self.duplicates.items(), 1):
                print(f"  {i}. 重复组 ({len(files)} 个文件):")
                for file_path in files:
                    print(f"     - {file_path}")
        
        # 相似文档
        if self.similar_docs:
            print(f"\n⚠️ 相似文档 ({len(self.similar_docs)} 对):")
            for i, doc_pair in enumerate(self.similar_docs[:5], 1):
                print(f"  {i}. {doc_pair['file1']} ↔ {doc_pair['file2']} "
                      f"(相似度: {doc_pair['similarity']:.1%})")
            if len(self.similar_docs) > 5:
                print(f"     ... 还有 {len(self.similar_docs) - 5} 对相似文档")
        
        # 文档模式分析
        patterns = self.analyze_document_patterns()
        print(f"\n📂 文档分类:")
        for category, files in patterns.items():
            if files:
                total_lines_cat = sum(self.document_stats[f]['lines'] for f in files)
                print(f"  {category:<20}: {len(files):>3} 文件, {total_lines_cat:>6} 行")


def main():
    """主函数"""
    print("📚 AstrBot SaaS Platform - 文档分析工具")
    print("="*60)
    
    # 分析当前目录
    analyzer = DocumentAnalyzer(".")
    
    # 扫描文档
    analyzer.scan_documents()
    
    # 查找重复和相似内容
    analyzer.find_duplicate_content()
    analyzer.find_similar_content(threshold=0.8)
    
    # 生成分析报告
    analyzer.print_analysis_report()
    
    # 生成优化计划
    optimization_plan = analyzer.generate_optimization_plan()
    
    print("\n" + "="*80)
    print("🚀 优化建议")
    print("="*80)
    
    current = optimization_plan['current_stats']
    reduction = optimization_plan['estimated_reduction']
    
    print(f"\n📊 优化潜力:")
    print(f"  当前文档数: {current['total_files']}")
    print(f"  当前总行数: {current['total_lines']:,}")
    print(f"  预计减少文档: {reduction['files']} 个")
    print(f"  预计减少行数: {reduction['lines_percentage']}%")
    print(f"  优化后行数: {int(current['total_lines'] * (1 - reduction['lines_percentage']/100)):,}")
    
    print(f"\n🎯 具体优化建议:")
    for i, opt in enumerate(optimization_plan['optimizations'], 1):
        print(f"\n  {i}. {opt['description']}")
        print(f"     预计减少: {opt.get('estimated_file_reduction', 0)} 个文件")
        if 'new_structure' in opt:
            print(f"     新结构:")
            for new_file in opt['new_structure']:
                print(f"       - {new_file}")
    
    return optimization_plan


if __name__ == "__main__":
    plan = main() 