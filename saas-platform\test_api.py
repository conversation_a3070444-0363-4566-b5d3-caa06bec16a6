#!/usr/bin/env python3
"""
AstrBot SaaS Platform API 功能测试脚本
"""

import requests
import json

def test_api():
    print("🎯 AstrBot SaaS Platform API 测试")
    print("=" * 50)
    
    base_url = 'http://localhost:8000'
    tests = []
    
    # 测试1: 根路径
    print("测试1: 根路径 API")
    try:
        response = requests.get(f'{base_url}/', timeout=5)
        print(f"  状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  响应: {data}")
            tests.append(True)
        else:
            tests.append(False)
    except Exception as e:
        print(f"  错误: {e}")
        tests.append(False)
    
    # 测试2: 健康检查
    print("\n测试2: 健康检查端点")
    try:
        response = requests.get(f'{base_url}/health', timeout=5)
        print(f"  状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  健康状态: {data}")
            tests.append(True)
        else:
            tests.append(False)
    except Exception as e:
        print(f"  错误: {e}")
        tests.append(False)
    
    # 测试3: API文档
    print("\n测试3: API文档访问")
    try:
        response = requests.get(f'{base_url}/docs', timeout=5)
        print(f"  状态码: {response.status_code}")
        print(f"  内容类型: {response.headers.get('Content-Type', '未知')}")
        tests.append(response.status_code == 200)
    except Exception as e:
        print(f"  错误: {e}")
        tests.append(False)
    
    # 测试4: OpenAPI规范
    print("\n测试4: OpenAPI规范")
    try:
        response = requests.get(f'{base_url}/api/v1/openapi.json', timeout=5)
        print(f"  状态码: {response.status_code}")
        if response.status_code == 200:
            openapi_data = response.json()
            print(f"  API版本: {openapi_data.get('openapi', '未知')}")
            print(f"  API标题: {openapi_data.get('info', {}).get('title', '未知')}")
        tests.append(response.status_code == 200)
    except Exception as e:
        print(f"  错误: {e}")
        tests.append(False)
    
    # 结果总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = sum(tests)
    total = len(tests)
    
    test_names = ["根路径API", "健康检查", "API文档", "OpenAPI规范"]
    for i, (name, result) in enumerate(zip(test_names, tests)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed >= 3:
        print("\n🎉 AstrBot SaaS Platform 部署成功！")
        print("🌟 核心功能正常，应用可以使用")
        print(f"\n🚀 访问地址:")
        print(f"   📱 应用主页: {base_url}/")
        print(f"   📚 API文档: {base_url}/docs")  
        print(f"   🔍 健康检查: {base_url}/health")
        return True
    else:
        print(f"\n⚠️ 部分功能存在问题，通过率: {passed/total*100:.1f}%")
        return False

if __name__ == "__main__":
    test_api() 