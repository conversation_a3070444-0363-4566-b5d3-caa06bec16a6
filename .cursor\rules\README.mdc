---
description:
globs: **/.cursor/**,**/rules/**
alwaysApply: false
---
# 📋 Cursor Rules 使用说明

## 🎯 设计原则

### 简洁性与引用机制
- **核心原则**: Cursor Rules保持简洁，避免过长内容
- **引用机制**: 通过 `@cursor doc/` 引用详细设计文档
- **AI友好**: 确保AI能够有效"消化"的长度和复杂度

## 📁 规则文件结构

| 文件 | 大小 | 描述 | 用途 |
|------|------|------|------|
| **[core_project_rules.md](mdc:core_project_rules.md)** | ~90行 | 🔗 **核心规则** - 项目核心信息和文档引用体系 | 所有AI任务的起点 |
| **[coding_conventions.mdc](mdc:coding_conventions.mdc)** | ~85行 | 📝 编码规范和约定 | 代码生成时参考 |
| **[multi_tenancy_guards.mdc](mdc:multi_tenancy_guards.mdc)** | ~130行 | 🛡️ 多租户隔离安全规则 | 关键安全约束 |

## 🔗 详细文档引用

### 当规则文件引用详细文档时:
```markdown
# 规则文件中的引用示例
- **完整编码规范**: `@cursor doc/开发规范.md`
- **API设计规范**: `@cursor doc/api_contracts/README.md`
- **数据模型定义**: `@cursor doc/api_contracts/models/common_models.yaml`
```

### AI使用流程:
1. **先读取**: 核心规则文件 (简洁概览)
2. **再引用**: 相关的详细设计文档
3. **最后应用**: 具体的实现约束和标准

## ✅ 最佳实践

- ✅ 规则文件总长度 < 200行
- ✅ 突出最关键的约束和原则
- ✅ 大量使用 `@cursor doc/` 引用
- ✅ 提供具体的代码示例
- ❌ 避免在规则中重复详细设计
- ❌ 避免长篇大论的解释

---

> **维护原则**: 规则文件应该是"指南针"而不是"百科全书"。详细内容放在docs/目录中。
