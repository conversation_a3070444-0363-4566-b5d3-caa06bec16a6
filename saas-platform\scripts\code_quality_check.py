#!/usr/bin/env python3
"""
AstrBot SaaS - 系统性代码质量检查工具
===================================

按照项目最佳实践标准，系统性检查所有功能模块的代码质量。

检查内容:
1. 多租户安全隔离
2. 类型注解完整性  
3. 错误处理规范
4. 异步编程实践
5. 代码风格和结构

使用方法:
    python scripts/code_quality_check.py
    python scripts/code_quality_check.py --module api
    python scripts/code_quality_check.py --fix
"""

import ast
import sys
import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum
import argparse
from datetime import datetime

# 项目路径配置
SCRIPT_DIR = Path(__file__).parent
PROJECT_ROOT = SCRIPT_DIR.parent
APP_DIR = PROJECT_ROOT / "app"


class Severity(Enum):
    """问题严重程度"""

    CRITICAL = "critical"  # 安全漏洞
    ERROR = "error"  # 代码错误
    WARNING = "warning"  # 最佳实践违规
    INFO = "info"  # 改进建议


@dataclass
class Issue:
    """代码问题"""

    file_path: Path
    line_number: int
    severity: Severity
    category: str
    description: str
    suggestion: str = ""
    code_snippet: str = ""


@dataclass
class CheckResult:
    """检查结果"""

    module: str
    total_files: int = 0
    total_lines: int = 0
    issues: List[Issue] = field(default_factory=list)

    @property
    def critical_count(self) -> int:
        return len([i for i in self.issues if i.severity == Severity.CRITICAL])

    @property
    def error_count(self) -> int:
        return len([i for i in self.issues if i.severity == Severity.ERROR])

    @property
    def warning_count(self) -> int:
        return len([i for i in self.issues if i.severity == Severity.WARNING])

    @property
    def score(self) -> float:
        """计算代码质量评分 (0-100)"""
        if self.total_lines == 0:
            return 100.0

        # 计算问题密度
        total_issues = len(self.issues)
        critical_weight = 20
        error_weight = 10
        warning_weight = 5

        penalty = (
            self.critical_count * critical_weight
            + self.error_count * error_weight
            + self.warning_count * warning_weight
        )

        # 基于代码行数和问题数量计算分数
        penalty_per_100_lines = (penalty / self.total_lines) * 100
        score = max(0, 100 - penalty_per_100_lines)

        return round(score, 2)


class CodeQualityChecker:
    """代码质量检查器"""

    def __init__(self):
        """初始化检查器"""
        self.results: Dict[str, CheckResult] = {}
        self.tenant_isolation_patterns = [
            # 危险模式 - 缺少租户隔离
            r"\.all\(\)",
            r"select\([^)]+\)\.where\([^tenant_id][^)]*\)",
            r"query\([^)]+\)\.filter\([^tenant_id][^)]*\)\.all\(\)",
        ]

        # 安全的API依赖模式
        self.safe_auth_patterns = [
            "get_current_tenant",
            "get_tenant_from_mixed_auth",
            "get_admin_user",
            "get_current_user",
        ]

    def check_module(self, module_name: str) -> CheckResult:
        """检查指定模块"""
        module_path = APP_DIR / module_name
        if not module_path.exists():
            print(f"❌ 模块不存在: {module_path}")
            return CheckResult(module_name)

        print(f"🔍 检查模块: {module_name}")
        result = CheckResult(module_name)

        # 获取所有Python文件
        py_files = list(module_path.rglob("*.py"))
        result.total_files = len(py_files)

        for py_file in py_files:
            if py_file.name.startswith("__"):
                continue

            print(f"  📄 {py_file.relative_to(PROJECT_ROOT)}")
            self._check_file(py_file, result)

        self.results[module_name] = result
        return result

    def _check_file(self, file_path: Path, result: CheckResult) -> None:
        """检查单个文件"""
        try:
            content = file_path.read_text(encoding="utf-8")
            lines = content.split("\n")
            result.total_lines += len(lines)

            # 解析AST
            try:
                tree = ast.parse(content)
                self._check_ast(tree, file_path, result, lines)
            except SyntaxError as e:
                result.issues.append(
                    Issue(
                        file_path=file_path,
                        line_number=e.lineno or 1,
                        severity=Severity.ERROR,
                        category="语法错误",
                        description=f"Python语法错误: {e.msg}",
                        suggestion="修复语法错误",
                    )
                )

            # 文本模式检查
            self._check_text_patterns(content, lines, file_path, result)

        except Exception as e:
            result.issues.append(
                Issue(
                    file_path=file_path,
                    line_number=1,
                    severity=Severity.ERROR,
                    category="文件读取",
                    description=f"无法读取文件: {e}",
                    suggestion="检查文件编码和权限",
                )
            )

    def _check_ast(
        self, tree: ast.AST, file_path: Path, result: CheckResult, lines: List[str]
    ) -> None:
        """AST级别检查"""
        for node in ast.walk(tree):
            # 检查函数定义
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                self._check_function(node, file_path, result, lines)

            # 检查API路由定义
            elif isinstance(node, ast.Call):
                self._check_api_call(node, file_path, result, lines)

            # 检查导入语句
            elif isinstance(node, (ast.Import, ast.ImportFrom)):
                self._check_imports(node, file_path, result, lines)

    def _check_function(
        self,
        node: ast.FunctionDef,
        file_path: Path,
        result: CheckResult,
        lines: List[str],
    ) -> None:
        """检查函数定义"""
        func_name = node.name
        line_num = node.lineno

        # 检查API端点函数的认证依赖
        if file_path.name.endswith(".py") and "api/v1" in str(file_path):
            self._check_api_endpoint_auth(node, file_path, result, lines)

        # 检查类型注解
        self._check_type_annotations(node, file_path, result, lines)

        # 检查异步函数
        if isinstance(node, ast.AsyncFunctionDef):
            self._check_async_function(node, file_path, result, lines)

        # 检查错误处理
        self._check_error_handling(node, file_path, result, lines)

    def _check_api_endpoint_auth(
        self,
        node: ast.FunctionDef,
        file_path: Path,
        result: CheckResult,
        lines: List[str],
    ) -> None:
        """检查API端点的认证依赖"""
        # 健康检查等公共端点可以跳过
        public_endpoints = ["health_check", "root", "health"]
        if node.name in public_endpoints:
            return

        # 检查是否有认证相关的依赖注入
        has_auth_dep = False

        # 检查函数参数中的依赖注入
        for default in node.args.defaults:
            if (
                isinstance(default, ast.Call)
                and hasattr(default.func, "id")
                and default.func.id == "Depends"
            ):
                if (
                    hasattr(default.args[0], "id")
                    and default.args[0].id in self.safe_auth_patterns
                ):
                    has_auth_dep = True
                    break

        if not has_auth_dep:
            result.issues.append(
                Issue(
                    file_path=file_path,
                    line_number=node.lineno,
                    severity=Severity.CRITICAL,
                    category="多租户安全",
                    description=f"API端点 {node.name} 缺少租户认证依赖",
                    suggestion=f"添加认证依赖，如: current_tenant: Tenant = Depends(get_current_tenant)",
                    code_snippet=(
                        lines[node.lineno - 1] if node.lineno <= len(lines) else ""
                    ),
                )
            )

    def _check_type_annotations(
        self,
        node: ast.FunctionDef,
        file_path: Path,
        result: CheckResult,
        lines: List[str],
    ) -> None:
        """检查类型注解"""
        # 检查返回类型注解
        if not node.returns and node.name != "__init__":
            result.issues.append(
                Issue(
                    file_path=file_path,
                    line_number=node.lineno,
                    severity=Severity.WARNING,
                    category="类型注解",
                    description=f"函数 {node.name} 缺少返回类型注解",
                    suggestion="添加返回类型注解，如: -> Optional[str]",
                    code_snippet=(
                        lines[node.lineno - 1] if node.lineno <= len(lines) else ""
                    ),
                )
            )

        # 检查参数类型注解
        for arg in node.args.args:
            if not arg.annotation and arg.arg not in ["self", "cls"]:
                result.issues.append(
                    Issue(
                        file_path=file_path,
                        line_number=node.lineno,
                        severity=Severity.INFO,
                        category="类型注解",
                        description=f"参数 {arg.arg} 缺少类型注解",
                        suggestion="添加类型注解，如: name: str",
                        code_snippet=(
                            lines[node.lineno - 1] if node.lineno <= len(lines) else ""
                        ),
                    )
                )

    def _check_async_function(
        self,
        node: ast.AsyncFunctionDef,
        file_path: Path,
        result: CheckResult,
        lines: List[str],
    ) -> None:
        """检查异步函数实践"""
        # 检查是否有await调用
        has_await = False
        for child in ast.walk(node):
            if isinstance(child, ast.Await):
                has_await = True
                break

        if not has_await:
            result.issues.append(
                Issue(
                    file_path=file_path,
                    line_number=node.lineno,
                    severity=Severity.WARNING,
                    category="异步实践",
                    description=f"异步函数 {node.name} 没有await调用",
                    suggestion="确认是否需要async，或添加适当的await调用",
                    code_snippet=(
                        lines[node.lineno - 1] if node.lineno <= len(lines) else ""
                    ),
                )
            )

    def _check_error_handling(
        self,
        node: ast.FunctionDef,
        file_path: Path,
        result: CheckResult,
        lines: List[str],
    ) -> None:
        """检查错误处理"""
        # 查找try-except块
        has_try_except = False
        has_bare_except = False

        for child in ast.walk(node):
            if isinstance(child, ast.Try):
                has_try_except = True
                # 检查是否有裸露的except
                for handler in child.handlers:
                    if handler.type is None:
                        has_bare_except = True
                        break

        if has_bare_except:
            result.issues.append(
                Issue(
                    file_path=file_path,
                    line_number=node.lineno,
                    severity=Severity.ERROR,
                    category="错误处理",
                    description="使用了裸露的except语句",
                    suggestion="指定具体的异常类型",
                    code_snippet=(
                        lines[node.lineno - 1] if node.lineno <= len(lines) else ""
                    ),
                )
            )

    def _check_api_call(
        self, node: ast.Call, file_path: Path, result: CheckResult, lines: List[str]
    ) -> None:
        """检查API调用"""
        # 检查路由装饰器
        if (
            hasattr(node.func, "attr")
            and hasattr(node.func, "value")
            and hasattr(node.func.value, "id")
            and node.func.value.id == "router"
            and node.func.attr in ["get", "post", "put", "patch", "delete"]
        ):

            # 检查路由配置
            self._check_route_config(node, file_path, result, lines)

    def _check_route_config(
        self, node: ast.Call, file_path: Path, result: CheckResult, lines: List[str]
    ) -> None:
        """检查路由配置"""
        # 检查是否有response_model
        has_response_model = False
        has_summary = False

        for keyword in node.keywords:
            if keyword.arg == "response_model":
                has_response_model = True
            elif keyword.arg == "summary":
                has_summary = True

        if not has_response_model:
            result.issues.append(
                Issue(
                    file_path=file_path,
                    line_number=node.lineno,
                    severity=Severity.INFO,
                    category="API文档",
                    description="路由缺少response_model配置",
                    suggestion="添加response_model以改善API文档",
                    code_snippet=(
                        lines[node.lineno - 1] if node.lineno <= len(lines) else ""
                    ),
                )
            )

        if not has_summary:
            result.issues.append(
                Issue(
                    file_path=file_path,
                    line_number=node.lineno,
                    severity=Severity.INFO,
                    category="API文档",
                    description="路由缺少summary配置",
                    suggestion="添加summary以改善API文档",
                    code_snippet=(
                        lines[node.lineno - 1] if node.lineno <= len(lines) else ""
                    ),
                )
            )

    def _check_imports(
        self, node: ast.Import, file_path: Path, result: CheckResult, lines: List[str]
    ) -> None:
        """检查导入语句"""
        # 检查未使用的导入（简化版）
        # 这需要更复杂的分析，这里只做基本检查
        pass

    def _check_text_patterns(
        self, content: str, lines: List[str], file_path: Path, result: CheckResult
    ) -> None:
        """文本模式检查"""
        # 检查多租户隔离模式
        self._check_tenant_isolation(content, lines, file_path, result)

        # 检查密码和密钥硬编码
        self._check_hardcoded_secrets(content, lines, file_path, result)

        # 检查日志记录
        self._check_logging_practices(content, lines, file_path, result)

    def _check_tenant_isolation(
        self, content: str, lines: List[str], file_path: Path, result: CheckResult
    ) -> None:
        """检查多租户隔离"""
        dangerous_patterns = [
            # 只检查真正危险的模式
            (
                r"\.query\([^)]+\)\.all\(\)",
                "直接使用.query().all()可能造成跨租户数据泄露",
            ),
            (
                r"db\.execute\([^)]*select.*\*.*from.*(?!where).*\)",
                "原生SQL查询缺少WHERE条件",
            ),
            (
                r"\.filter\([^)]*\)\.delete\(\)",
                "使用.filter().delete()可能缺少租户隔离",
            ),
        ]

        for i, line in enumerate(lines, 1):
            # 跳过空行、注释和文档字符串
            stripped_line = line.strip()
            if (
                not stripped_line
                or stripped_line.startswith("#")
                or '"""' in line
                or "'''" in line
                or stripped_line.startswith("*")  # 文档注释
                or "example" in line.lower()
                or "test" in line.lower()
            ):
                continue

            for pattern, description in dangerous_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    # 额外检查：如果在API层并且有依赖注入，可能是安全的
                    if "api/v1" in str(file_path) and (
                        "tenant" in line.lower() or "Depends(" in line
                    ):
                        continue

                    result.issues.append(
                        Issue(
                            file_path=file_path,
                            line_number=i,
                            severity=Severity.WARNING,  # 降级为警告而非严重
                            category="多租户安全",
                            description=description,
                            suggestion="确保所有数据操作包含tenant_id过滤或通过安全的服务层处理",
                            code_snippet=line.strip(),
                        )
                    )

    def _check_hardcoded_secrets(
        self, content: str, lines: List[str], file_path: Path, result: CheckResult
    ) -> None:
        """检查硬编码密钥"""
        secret_patterns = [
            (r'password\s*=\s*["\'][^"\']{3,}["\']', "密码硬编码"),
            (r'secret\s*=\s*["\'][^"\']{10,}["\']', "密钥硬编码"),
            (r'api[_-]?key\s*=\s*["\'][^"\']{10,}["\']', "API密钥硬编码"),
        ]

        for i, line in enumerate(lines, 1):
            for pattern, description in secret_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    # 排除示例和文档
                    if any(
                        word in line.lower()
                        for word in ["example", "test", "demo", "sample"]
                    ):
                        continue

                    result.issues.append(
                        Issue(
                            file_path=file_path,
                            line_number=i,
                            severity=Severity.ERROR,
                            category="安全",
                            description=description,
                            suggestion="使用环境变量或配置文件",
                            code_snippet=line.strip(),
                        )
                    )

    def _check_logging_practices(
        self, content: str, lines: List[str], file_path: Path, result: CheckResult
    ) -> None:
        """检查日志记录实践"""
        has_logger = "logger" in content
        has_logging_import = any(
            re.search(r"from.*logging|import.*logging", line) for line in lines
        )

        # 服务层和API层应该有日志记录
        if (
            ("services/" in str(file_path) or "api/" in str(file_path))
            and not has_logger
            and file_path.name != "__init__.py"
        ):
            result.issues.append(
                Issue(
                    file_path=file_path,
                    line_number=1,
                    severity=Severity.INFO,
                    category="日志记录",
                    description="缺少日志记录器",
                    suggestion="添加logger = get_logger(__name__)",
                    code_snippet="",
                )
            )

    def check_all_modules(self) -> Dict[str, CheckResult]:
        """检查所有模块"""
        modules = ["api", "services", "models", "schemas", "core", "utils"]

        print("🚀 开始系统性代码质量检查")
        print("=" * 50)

        for module in modules:
            if (APP_DIR / module).exists():
                self.check_module(module)
            else:
                print(f"⚠️  模块不存在: {module}")

        return self.results

    def generate_report(self) -> str:
        """生成检查报告"""
        report = []
        report.append("📊 AstrBot SaaS - 代码质量检查报告")
        report.append("=" * 60)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # 总体统计
        total_files = sum(r.total_files for r in self.results.values())
        total_lines = sum(r.total_lines for r in self.results.values())
        total_critical = sum(r.critical_count for r in self.results.values())
        total_errors = sum(r.error_count for r in self.results.values())
        total_warnings = sum(r.warning_count for r in self.results.values())

        overall_score = (
            sum(r.score * r.total_lines for r in self.results.values()) / total_lines
            if total_lines > 0
            else 100
        )

        report.append("📈 总体质量评估")
        report.append("-" * 30)
        report.append(f"检查文件数: {total_files}")
        report.append(f"代码总行数: {total_lines}")
        report.append(f"质量评分: {overall_score:.1f}/100")
        report.append("")
        report.append(f"🚨 严重问题: {total_critical}")
        report.append(f"❌ 错误: {total_errors}")
        report.append(f"⚠️  警告: {total_warnings}")
        report.append("")

        # 各模块详情
        for module_name, result in self.results.items():
            report.append(f"📦 模块: {module_name}")
            report.append("-" * 20)
            report.append(f"文件数: {result.total_files}")
            report.append(f"代码行数: {result.total_lines}")
            report.append(f"质量评分: {result.score}/100")
            report.append(
                f"问题统计: 🚨{result.critical_count} ❌{result.error_count} ⚠️{result.warning_count}"
            )
            report.append("")

            # 显示严重问题
            critical_issues = [
                i for i in result.issues if i.severity == Severity.CRITICAL
            ]
            if critical_issues:
                report.append("  🚨 严重问题:")
                for issue in critical_issues[:5]:  # 只显示前5个
                    report.append(f"    📍 {issue.file_path.name}:{issue.line_number}")
                    report.append(f"       {issue.description}")
                    report.append(f"       💡 {issue.suggestion}")
                if len(critical_issues) > 5:
                    report.append(f"    ... 还有 {len(critical_issues) - 5} 个严重问题")
                report.append("")

            # 显示错误详情
            error_issues = [i for i in result.issues if i.severity == Severity.ERROR]
            if error_issues:
                report.append("  ❌ 错误详情:")
                for issue in error_issues:
                    report.append(f"    📍 {issue.file_path.name}:{issue.line_number}")
                    report.append(f"       类别: {issue.category}")
                    report.append(f"       问题: {issue.description}")
                    report.append(f"       建议: {issue.suggestion}")
                    if issue.code_snippet:
                        report.append(f"       代码: {issue.code_snippet}")
                    report.append("")
                report.append("")

        # 建议改进
        report.append("🎯 改进建议")
        report.append("-" * 20)
        if total_critical > 0:
            report.append("1. 🚨 优先修复所有严重安全问题")
        if total_errors > 0:
            report.append("2. ❌ 修复代码错误")
        if total_warnings > 10:
            report.append("3. ⚠️  逐步改善最佳实践违规")
        report.append("4. 📚 参考项目开发规范文档")
        report.append("")

        # 质量等级
        if overall_score >= 90:
            grade = "🏆 优秀"
        elif overall_score >= 80:
            grade = "✅ 良好"
        elif overall_score >= 70:
            grade = "⚠️  一般"
        elif overall_score >= 60:
            grade = "❌ 需要改进"
        else:
            grade = "🚨 严重问题"

        report.append(f"🎖️  代码质量等级: {grade}")
        report.append("")

        return "\n".join(report)

    def save_report(self, output_file: str = None) -> Path:
        """保存检查报告"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"code_quality_report_{timestamp}.txt"

        report_path = PROJECT_ROOT / output_file
        report_content = self.generate_report()

        report_path.write_text(report_content, encoding="utf-8")
        return report_path


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AstrBot SaaS 代码质量检查工具")
    parser.add_argument(
        "--module", help="指定检查的模块 (api, services, models, etc.)", default=None
    )
    parser.add_argument("--output", help="报告输出文件名", default=None)
    parser.add_argument("--json", action="store_true", help="输出JSON格式报告")

    args = parser.parse_args()

    checker = CodeQualityChecker()

    if args.module:
        # 检查指定模块
        result = checker.check_module(args.module)
        results = {args.module: result}
    else:
        # 检查所有模块
        results = checker.check_all_modules()

    # 生成报告
    if args.json:
        # JSON格式报告
        json_data = {}
        for module, result in results.items():
            json_data[module] = {
                "total_files": result.total_files,
                "total_lines": result.total_lines,
                "score": result.score,
                "issues": [
                    {
                        "file": str(issue.file_path.relative_to(PROJECT_ROOT)),
                        "line": issue.line_number,
                        "severity": issue.severity.value,
                        "category": issue.category,
                        "description": issue.description,
                        "suggestion": issue.suggestion,
                    }
                    for issue in result.issues
                ],
            }

        output_file = args.output or "code_quality_report.json"
        output_path = PROJECT_ROOT / output_file
        output_path.write_text(
            json.dumps(json_data, indent=2, ensure_ascii=False), encoding="utf-8"
        )
        print(f"📄 JSON报告已保存: {output_path}")
    else:
        # 文本格式报告
        print(checker.generate_report())

        if args.output:
            report_path = checker.save_report(args.output)
            print(f"📄 报告已保存: {report_path}")

    # 返回退出码
    total_critical = sum(r.critical_count for r in results.values())
    total_errors = sum(r.error_count for r in results.values())

    if total_critical > 0:
        return 2  # 严重问题
    elif total_errors > 0:
        return 1  # 一般错误
    else:
        return 0  # 通过检查


if __name__ == "__main__":
    sys.exit(main())
