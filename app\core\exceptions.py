"""
AstrBot SaaS Platform 统一异常管理系统

此模块定义了整个应用程序的异常层次结构，提供统一的错误处理机制。
按照业务层次和HTTP状态码进行分类组织。
"""

from fastapi import HTTPException, status
from typing import Optional, Dict, Any


class AstrBotBaseException(Exception):
    """
    AstrBot平台基础异常类
    
    所有自定义异常的基类，提供统一的错误信息格式。
    """
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        super().__init__(self.message)


class AstrBotHTTPException(HTTPException):
    """
    AstrBot平台HTTP异常基类
    
    继承自FastAPI的HTTPException，提供标准化的HTTP错误响应。
    """
    
    def __init__(
        self, 
        status_code: int, 
        detail: str, 
        error_code: str = None,
        headers: Optional[Dict[str, str]] = None
    ):
        self.error_code = error_code or self.__class__.__name__
        super().__init__(status_code=status_code, detail=detail, headers=headers)


# ==================== 认证与授权异常 ====================

class AuthenticationError(AstrBotHTTPException):
    """认证失败异常"""
    
    def __init__(self, detail: str = "Could not validate credentials"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            error_code="AUTHENTICATION_FAILED",
            headers={"WWW-Authenticate": "Bearer"}
        )


class AuthorizationError(AstrBotHTTPException):
    """授权失败异常"""
    
    def __init__(self, detail: str = "Not enough permissions"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            error_code="AUTHORIZATION_FAILED"
        )


class PermissionError(AstrBotHTTPException):
    """权限不足异常"""
    
    def __init__(self, detail: str = "Permission denied"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            error_code="PERMISSION_DENIED"
        )


# ==================== 安全相关异常 ====================

class SecurityError(AstrBotBaseException):
    """安全相关异常基类"""
    pass


class TokenExpiredError(SecurityError):
    """Token过期异常"""
    
    def __init__(self, message: str = "Token has expired"):
        super().__init__(message, "TOKEN_EXPIRED")


class InvalidTokenError(SecurityError):
    """无效Token异常"""
    
    def __init__(self, message: str = "Invalid token"):
        super().__init__(message, "INVALID_TOKEN")


# ==================== 租户相关异常 ====================

class TenantAccessError(AstrBotHTTPException):
    """租户访问异常"""
    
    def __init__(self, detail: str = "Tenant access denied"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            error_code="TENANT_ACCESS_DENIED"
        )


class TenantNotFoundError(AstrBotHTTPException):
    """租户不存在异常"""
    
    def __init__(self, tenant_id: str = None):
        detail = f"Tenant {tenant_id} not found" if tenant_id else "Tenant not found"
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            error_code="TENANT_NOT_FOUND"
        )


# ==================== 业务逻辑异常 ====================

class BusinessLogicError(AstrBotHTTPException):
    """业务逻辑异常基类"""
    
    def __init__(self, detail: str, error_code: str = None):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail,
            error_code=error_code or "BUSINESS_LOGIC_ERROR"
        )


class RegistrationError(BusinessLogicError):
    """用户注册异常"""
    
    def __init__(self, detail: str = "Registration failed"):
        super().__init__(detail, "REGISTRATION_FAILED")


# ==================== 资源相关异常 ====================

class ResourceNotFoundError(AstrBotHTTPException):
    """资源不存在异常"""
    
    def __init__(self, resource_type: str, resource_id: str = None):
        detail = f"{resource_type}"
        if resource_id:
            detail += f" {resource_id}"
        detail += " not found"
        
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            error_code="RESOURCE_NOT_FOUND"
        )


class ResourceConflictError(AstrBotHTTPException):
    """资源冲突异常"""
    
    def __init__(self, detail: str = "Resource already exists"):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=detail,
            error_code="RESOURCE_CONFLICT"
        )


# ==================== 外部服务异常 ====================

class LLMProviderError(AstrBotBaseException):
    """LLM提供商异常基类"""
    pass


class LLMRateLimitError(LLMProviderError):
    """LLM服务频率限制异常"""
    
    def __init__(self, message: str = "LLM service rate limit exceeded"):
        super().__init__(message, "LLM_RATE_LIMIT")


class LLMQuotaExceededError(LLMProviderError):
    """LLM服务配额超限异常"""
    
    def __init__(self, message: str = "LLM service quota exceeded"):
        super().__init__(message, "LLM_QUOTA_EXCEEDED")


class LLMInvalidRequestError(LLMProviderError):
    """LLM服务请求无效异常"""
    
    def __init__(self, message: str = "Invalid request to LLM service"):
        super().__init__(message, "LLM_INVALID_REQUEST")


# ==================== 验证相关异常 ====================

class ValidationError(AstrBotHTTPException):
    """数据验证异常"""
    
    def __init__(self, detail: str = "Validation failed", field: str = None):
        if field:
            detail = f"Validation failed for field '{field}': {detail}"
        
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail,
            error_code="VALIDATION_ERROR"
        )


# ==================== 异常工具函数 ====================

def create_http_exception_from_base(
    base_exception: AstrBotBaseException,
    status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR
) -> AstrBotHTTPException:
    """
    将基础异常转换为HTTP异常
    
    Args:
        base_exception: 基础异常实例
        status_code: HTTP状态码，默认500
        
    Returns:
        HTTP异常实例
    """
    return AstrBotHTTPException(
        status_code=status_code,
        detail=base_exception.message,
        error_code=base_exception.error_code
    )


def get_exception_details(exception: Exception) -> Dict[str, Any]:
    """
    获取异常详细信息
    
    Args:
        exception: 异常实例
        
    Returns:
        包含异常详细信息的字典
    """
    details = {
        "type": exception.__class__.__name__,
        "message": str(exception)
    }
    
    if isinstance(exception, AstrBotBaseException):
        details.update({
            "error_code": exception.error_code,
            "details": exception.details
        })
    elif isinstance(exception, AstrBotHTTPException):
        details.update({
            "error_code": exception.error_code,
            "status_code": exception.status_code
        })
    
    return details 