"""
工具类测试
"""

import pytest
import logging
import json
import sys
from unittest.mock import patch, Mock
from datetime import datetime

from app.utils.logging import (
    StructuredFormatter,
    ContextLogger,
    get_logger,
    setup_logging,
)


class TestStructuredFormatter:
    """测试结构化日志格式化器"""

    def test_format_basic_record(self):
        """测试基础日志记录格式化"""
        # Arrange
        formatter = StructuredFormatter()
        record = logging.LogRecord(
            name="test.logger",
            level=logging.INFO,
            pathname="test.py",
            lineno=10,
            msg="Test message",
            args=(),
            exc_info=None,
        )
        record.module = "test"
        record.funcName = "test_function"

        # Act
        formatted = formatter.format(record)
        log_data = json.loads(formatted)

        # Assert
        assert log_data["level"] == "INFO"
        assert log_data["logger"] == "test.logger"
        assert log_data["message"] == "Test message"
        assert log_data["module"] == "test"
        assert log_data["function"] == "test_function"
        assert log_data["line"] == 10
        assert "timestamp" in log_data

    def test_format_with_extra_fields(self):
        """测试带额外字段的日志记录"""
        # Arrange
        formatter = StructuredFormatter()
        record = logging.LogRecord(
            name="test.logger",
            level=logging.INFO,
            pathname="test.py",
            lineno=10,
            msg="Test message",
            args=(),
            exc_info=None,
        )
        record.module = "test"
        record.funcName = "test_function"
        record.tenant_id = "tenant-123"
        record.user_id = "user-456"
        record.session_id = "session-789"

        # Act
        formatted = formatter.format(record)
        log_data = json.loads(formatted)

        # Assert
        assert log_data["tenant_id"] == "tenant-123"
        assert log_data["user_id"] == "user-456"
        assert log_data["session_id"] == "session-789"

    def test_format_with_exception(self):
        """测试带异常信息的日志记录"""
        # Arrange
        formatter = StructuredFormatter()

        try:
            raise ValueError("Test error")
        except ValueError:
            record = logging.LogRecord(
                name="test.logger",
                level=logging.ERROR,
                pathname="test.py",
                lineno=10,
                msg="Error occurred",
                args=(),
                exc_info=sys.exc_info(),
            )
            record.module = "test"
            record.funcName = "test_function"

        # Act
        formatted = formatter.format(record)
        log_data = json.loads(formatted)

        # Assert
        assert log_data["level"] == "ERROR"
        assert log_data["message"] == "Error occurred"


class TestContextLogger:
    """测试上下文日志记录器"""

    def test_context_logger_creation(self):
        """测试上下文日志记录器创建"""
        # Act
        logger = ContextLogger("test.context")

        # Assert
        assert logger.logger.name == "test.context"
        assert logger.context == {}

    def test_set_context(self):
        """测试设置上下文"""
        # Arrange
        logger = ContextLogger("test.context")

        # Act
        logger.set_context(tenant_id="tenant-123", user_id="user-456")

        # Assert
        assert logger.context["tenant_id"] == "tenant-123"
        assert logger.context["user_id"] == "user-456"

    def test_clear_context(self):
        """测试清除上下文"""
        # Arrange
        logger = ContextLogger("test.context")
        logger.set_context(tenant_id="tenant-123")

        # Act
        logger.clear_context()

        # Assert
        assert logger.context == {}

    @patch("app.utils.logging.logging.getLogger")
    def test_log_methods(self, mock_get_logger):
        """测试日志记录方法"""
        # Arrange
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        logger = ContextLogger("test.context")
        logger.set_context(tenant_id="tenant-123")

        # Act
        logger.info("Test message", extra_field="extra_value")

        # Assert
        mock_logger.handle.assert_called_once()


class TestLoggerUtilities:
    """测试日志工具函数"""

    def test_get_logger_creates_new(self):
        """测试获取新的日志记录器"""
        # Act
        logger = get_logger("test.new.logger")

        # Assert
        assert isinstance(logger, ContextLogger)
        assert logger.logger.name == "test.new.logger"

    def test_get_logger_returns_cached(self):
        """测试获取缓存的日志记录器"""
        # Arrange
        logger1 = get_logger("test.cached.logger")

        # Act
        logger2 = get_logger("test.cached.logger")

        # Assert
        assert logger1 is logger2

    @patch("app.utils.logging.logging.getLogger")
    @patch("app.utils.logging.settings")
    def test_setup_logging_development(self, mock_settings, mock_get_logger):
        """测试开发环境日志设置"""
        # Arrange
        mock_settings.LOG_LEVEL = "DEBUG"
        mock_settings.is_development = True
        mock_root_logger = Mock()
        mock_get_logger.return_value = mock_root_logger

        # Act
        setup_logging()

        # Assert
        mock_root_logger.setLevel.assert_called()
        mock_root_logger.handlers.clear.assert_called()
        mock_root_logger.addHandler.assert_called()

    @patch("app.utils.logging.logging.getLogger")
    @patch("app.utils.logging.settings")
    def test_setup_logging_production(self, mock_settings, mock_get_logger):
        """测试生产环境日志设置"""
        # Arrange
        mock_settings.LOG_LEVEL = "INFO"
        mock_settings.is_development = False
        mock_root_logger = Mock()
        mock_get_logger.return_value = mock_root_logger

        # Act
        setup_logging()

        # Assert
        mock_root_logger.setLevel.assert_called()
        mock_root_logger.addHandler.assert_called()


class TestLoggerIntegration:
    """测试日志记录器集成"""

    def test_full_logging_workflow(self):
        """测试完整的日志记录工作流"""
        # Arrange
        logger = get_logger("test.integration")

        # Act - 设置上下文并记录日志
        logger.set_context(tenant_id="tenant-123", operation="test")
        logger.info("Integration test", session_id="session-456")

        # Assert - 验证上下文已设置
        assert logger.context["tenant_id"] == "tenant-123"
        assert logger.context["operation"] == "test"
