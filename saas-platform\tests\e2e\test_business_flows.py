"""
AstrBot SaaS Platform - 业务流程端到端测试
============================================

测试完整的业务流程，包括：
1. 租户注册和配置
2. 用户管理和认证
3. 会话创建和消息处理
4. AI功能集成
5. 实例配置和通信
6. 多租户隔离验证

这些测试确保整个系统的业务逻辑正确运行。
"""

import pytest
import asyncio
from httpx import AsyncClient
from uuid import uuid4
from typing import Dict, List, Any

from app.main import app
from app.core.database import get_db
from app.models.tenant import Tenant
from app.models.user import User
from app.models.session import Session
from app.models.message import Message
from app.schemas.tenant import TenantCreate
from app.schemas.user import UserCreate
from app.schemas.session import SessionCreate
from app.schemas.message import MessageCreate

# 测试配置
TEST_BASE_URL = "http://testserver"
TEST_TENANT_DATA = {
    "name": "E2E Test Corp",
    "email": "<EMAIL>",
    "plan": "enterprise",
    "extra_data": {"test_mode": True},
}


class E2ETestContext:
    """端到端测试上下文管理器"""

    def __init__(self):
        self.client: AsyncClient = None
        self.tenant_data: Dict[str, Any] = {}
        self.user_data: Dict[str, Any] = {}
        self.session_data: Dict[str, Any] = {}
        self.auth_headers: Dict[str, str] = {}

    async def setup(self):
        """设置测试环境"""
        self.client = AsyncClient(app=app, base_url=TEST_BASE_URL)

    async def cleanup(self):
        """清理测试环境"""
        if self.client:
            await self.client.aclose()


@pytest.fixture
async def e2e_context():
    """E2E测试上下文fixture"""
    context = E2ETestContext()
    await context.setup()
    try:
        yield context
    finally:
        await context.cleanup()


@pytest.mark.asyncio
async def test_complete_tenant_onboarding_flow(e2e_context: E2ETestContext):
    """
    测试完整的租户入驻流程

    流程：
    1. 创建租户
    2. 获取API密钥
    3. 验证租户状态
    4. 配置租户设置
    """
    client = e2e_context.client

    # 步骤1: 创建租户
    response = await client.post("/api/v1/tenants", json=TEST_TENANT_DATA)
    assert response.status_code == 201
    tenant_response = response.json()
    assert tenant_response["success"] is True

    tenant_data = tenant_response["data"]
    tenant_id = tenant_data["id"]
    api_key = tenant_data["api_key"]

    # 保存租户信息到上下文
    e2e_context.tenant_data = tenant_data
    e2e_context.auth_headers = {"X-API-Key": api_key}

    # 步骤2: 验证租户信息获取
    response = await client.get(
        f"/api/v1/tenants/{tenant_id}", headers=e2e_context.auth_headers
    )
    assert response.status_code == 200
    tenant_info = response.json()
    assert tenant_info["data"]["name"] == TEST_TENANT_DATA["name"]
    assert tenant_info["data"]["email"] == TEST_TENANT_DATA["email"]

    # 步骤3: 验证租户统计信息
    response = await client.get(
        f"/api/v1/tenants/{tenant_id}/stats", headers=e2e_context.auth_headers
    )
    assert response.status_code == 200
    stats = response.json()
    assert "data" in stats

    # 步骤4: 测试API密钥重新生成
    response = await client.post(
        f"/api/v1/tenants/{tenant_id}/regenerate-api-key",
        headers=e2e_context.auth_headers,
    )
    assert response.status_code == 200
    new_api_key_response = response.json()
    new_api_key = new_api_key_response["data"]["api_key"]
    assert new_api_key != api_key

    # 更新认证头
    e2e_context.auth_headers = {"X-API-Key": new_api_key}

    print("✅ 租户入驻流程测试通过")


@pytest.mark.asyncio
async def test_user_management_flow(e2e_context: E2ETestContext):
    """
    测试用户管理流程

    前置条件：需要先运行租户创建测试
    流程：
    1. 创建用户
    2. 用户认证
    3. 获取用户信息
    4. 更新用户信息
    """
    client = e2e_context.client

    # 确保有租户上下文
    if not e2e_context.tenant_data:
        await test_complete_tenant_onboarding_flow(e2e_context)

    tenant_id = e2e_context.tenant_data["id"]

    # 步骤1: 创建用户
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "TestPassword123!",
        "tenant_id": tenant_id,
    }

    response = await client.post(
        "/api/v1/users", json=user_data, headers=e2e_context.auth_headers
    )
    assert response.status_code == 201
    user_response = response.json()
    assert user_response["success"] is True

    user_info = user_response["data"]
    user_id = user_info["id"]
    e2e_context.user_data = user_info

    # 步骤2: 用户登录获取JWT
    login_data = {"email": user_data["email"], "password": user_data["password"]}

    response = await client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 200
    auth_response = response.json()
    access_token = auth_response["data"]["access_token"]

    # 更新认证头包含JWT
    jwt_headers = {"Authorization": f"Bearer {access_token}"}

    # 步骤3: 获取用户信息
    response = await client.get(f"/api/v1/users/{user_id}", headers=jwt_headers)
    assert response.status_code == 200
    user_profile = response.json()
    assert user_profile["data"]["email"] == user_data["email"]

    # 步骤4: 更新用户信息
    update_data = {
        "username": "updated-testuser",
        "extra_data": {"preferences": {"theme": "dark"}},
    }

    response = await client.put(
        f"/api/v1/users/{user_id}", json=update_data, headers=jwt_headers
    )
    assert response.status_code == 200
    updated_user = response.json()
    assert updated_user["data"]["username"] == update_data["username"]

    print("✅ 用户管理流程测试通过")


@pytest.mark.asyncio
async def test_session_message_flow(e2e_context: E2ETestContext):
    """
    测试会话和消息处理流程

    流程：
    1. 创建会话
    2. 发送消息
    3. 获取会话历史
    4. 消息状态更新
    """
    client = e2e_context.client

    # 确保有用户上下文
    if not e2e_context.user_data:
        await test_user_management_flow(e2e_context)

    tenant_id = e2e_context.tenant_data["id"]
    user_id = e2e_context.user_data["id"]

    # 步骤1: 创建会话
    session_data = {
        "title": "E2E Test Session",
        "customer_info": {"name": "Test Customer", "email": "<EMAIL>"},
        "metadata": {"source": "web", "test_session": True},
    }

    response = await client.post(
        "/api/v1/sessions", json=session_data, headers=e2e_context.auth_headers
    )
    assert response.status_code == 201
    session_response = response.json()
    assert session_response["success"] is True

    session_info = session_response["data"]
    session_id = session_info["id"]
    e2e_context.session_data = session_info

    # 步骤2: 发送用户消息
    message_data = {
        "content": "Hello, I need help with my account",
        "sender_type": "customer",
        "sender_id": "customer123",
        "message_type": "text",
        "metadata": {"timestamp": "2024-01-01T10:00:00Z"},
    }

    response = await client.post(
        f"/api/v1/sessions/{session_id}/messages",
        json=message_data,
        headers=e2e_context.auth_headers,
    )
    assert response.status_code == 201
    message_response = response.json()
    assert message_response["success"] is True

    customer_message = message_response["data"]
    customer_message_id = customer_message["id"]

    # 步骤3: 发送AI回复消息
    ai_message_data = {
        "content": "Hello! I'd be happy to help you with your account. What specific issue are you experiencing?",
        "sender_type": "assistant",
        "sender_id": "ai_assistant",
        "message_type": "text",
        "metadata": {"ai_model": "gpt-3.5-turbo", "confidence": 0.95},
    }

    response = await client.post(
        f"/api/v1/sessions/{session_id}/messages",
        json=ai_message_data,
        headers=e2e_context.auth_headers,
    )
    assert response.status_code == 201
    ai_message_response = response.json()
    ai_message = ai_message_response["data"]

    # 步骤4: 获取会话消息历史
    response = await client.get(
        f"/api/v1/sessions/{session_id}/messages", headers=e2e_context.auth_headers
    )
    assert response.status_code == 200
    messages_response = response.json()
    messages = messages_response["data"]

    # 验证消息顺序和内容
    assert len(messages) == 2
    assert messages[0]["content"] == message_data["content"]
    assert messages[1]["content"] == ai_message_data["content"]

    # 步骤5: 更新会话状态
    session_update = {
        "status": "resolved",
        "resolution_summary": "Customer account issue resolved",
    }

    response = await client.patch(
        f"/api/v1/sessions/{session_id}",
        json=session_update,
        headers=e2e_context.auth_headers,
    )
    assert response.status_code == 200
    updated_session = response.json()
    assert updated_session["data"]["status"] == "resolved"

    print("✅ 会话消息流程测试通过")


@pytest.mark.asyncio
async def test_ai_features_integration(e2e_context: E2ETestContext):
    """
    测试AI功能集成流程

    流程：
    1. 智能回复生成
    2. 情感分析
    3. 会话摘要
    4. 知识库查询
    """
    client = e2e_context.client

    # 确保有会话上下文
    if not e2e_context.session_data:
        await test_session_message_flow(e2e_context)

    session_id = e2e_context.session_data["id"]

    # 步骤1: 智能回复建议
    response = await client.post(
        f"/api/v1/ai/smart-reply",
        json={
            "session_id": session_id,
            "last_message": "I can't access my account dashboard",
            "context": "customer_support",
        },
        headers=e2e_context.auth_headers,
    )
    # AI服务可能未配置，检查响应
    if response.status_code == 200:
        smart_reply = response.json()
        assert "suggestions" in smart_reply["data"]
        print("✅ 智能回复功能正常")
    else:
        print("⏭️ 智能回复服务未配置，跳过测试")

    # 步骤2: 情感分析
    response = await client.post(
        f"/api/v1/ai/sentiment",
        json={"text": "I'm really frustrated with this service!", "language": "en"},
        headers=e2e_context.auth_headers,
    )
    if response.status_code == 200:
        sentiment = response.json()
        assert "sentiment" in sentiment["data"]
        print("✅ 情感分析功能正常")
    else:
        print("⏭️ 情感分析服务未配置，跳过测试")

    # 步骤3: 会话摘要生成
    response = await client.post(
        f"/api/v1/ai/session-summary",
        json={"session_id": session_id, "summary_type": "brief"},
        headers=e2e_context.auth_headers,
    )
    if response.status_code == 200:
        summary = response.json()
        assert "summary" in summary["data"]
        print("✅ 会话摘要功能正常")
    else:
        print("⏭️ 会话摘要服务未配置，跳过测试")

    print("✅ AI功能集成测试完成")


@pytest.mark.asyncio
async def test_multi_tenant_isolation(e2e_context: E2ETestContext):
    """
    测试多租户隔离

    流程：
    1. 创建第二个租户
    2. 验证数据隔离
    3. 验证跨租户访问被拒绝
    """
    client = e2e_context.client

    # 确保有第一个租户的上下文
    if not e2e_context.tenant_data:
        await test_complete_tenant_onboarding_flow(e2e_context)

    first_tenant_id = e2e_context.tenant_data["id"]
    first_tenant_headers = e2e_context.auth_headers

    # 步骤1: 创建第二个租户
    second_tenant_data = {
        "name": "Second E2E Test Corp",
        "email": "<EMAIL>",
        "plan": "professional",
        "extra_data": {"test_mode": True},
    }

    response = await client.post("/api/v1/tenants", json=second_tenant_data)
    assert response.status_code == 201
    second_tenant_response = response.json()

    second_tenant_info = second_tenant_response["data"]
    second_tenant_id = second_tenant_info["id"]
    second_api_key = second_tenant_info["api_key"]
    second_tenant_headers = {"X-API-Key": second_api_key}

    # 步骤2: 验证第二个租户无法访问第一个租户的数据
    response = await client.get(
        f"/api/v1/tenants/{first_tenant_id}", headers=second_tenant_headers
    )
    assert response.status_code == 403  # 禁止访问

    # 步骤3: 验证第一个租户无法访问第二个租户的数据
    response = await client.get(
        f"/api/v1/tenants/{second_tenant_id}", headers=first_tenant_headers
    )
    assert response.status_code == 403  # 禁止访问

    # 步骤4: 验证各自可以访问自己的数据
    response = await client.get(
        f"/api/v1/tenants/{first_tenant_id}", headers=first_tenant_headers
    )
    assert response.status_code == 200

    response = await client.get(
        f"/api/v1/tenants/{second_tenant_id}", headers=second_tenant_headers
    )
    assert response.status_code == 200

    print("✅ 多租户隔离测试通过")


@pytest.mark.asyncio
async def test_webhook_integration_flow(e2e_context: E2ETestContext):
    """
    测试Webhook集成流程

    流程：
    1. 配置Webhook
    2. 模拟AstrBot实例回调
    3. 验证消息处理
    """
    client = e2e_context.client

    # 确保有租户上下文
    if not e2e_context.tenant_data:
        await test_complete_tenant_onboarding_flow(e2e_context)

    tenant_id = e2e_context.tenant_data["id"]

    # 步骤1: 配置实例和Webhook
    instance_config = {
        "name": "Test AstrBot Instance",
        "endpoint_url": "http://test-astrbot:8080",
        "webhook_secret": "test-webhook-secret-123",
        "is_active": True,
        "config": {"max_sessions": 100, "timeout": 30},
    }

    response = await client.post(
        "/api/v1/instances", json=instance_config, headers=e2e_context.auth_headers
    )
    if response.status_code == 201:
        instance_response = response.json()
        instance_id = instance_response["data"]["id"]

        # 步骤2: 模拟Webhook回调
        webhook_payload = {
            "event_type": "message_received",
            "session_id": str(uuid4()),
            "message": {
                "content": "Hello from AstrBot instance",
                "sender_type": "customer",
                "timestamp": "2024-01-01T12:00:00Z",
            },
            "instance_id": instance_id,
            "tenant_id": tenant_id,
        }

        webhook_headers = {
            "X-Webhook-Signature": "test-signature",
            "Content-Type": "application/json",
        }

        response = await client.post(
            "/api/v1/webhooks/astrbot-callback",
            json=webhook_payload,
            headers=webhook_headers,
        )

        # Webhook可能需要特殊配置，检查响应
        if response.status_code in [200, 201]:
            print("✅ Webhook集成测试通过")
        else:
            print("⏭️ Webhook集成需要进一步配置")
    else:
        print("⏭️ 实例管理服务未完全配置，跳过Webhook测试")


@pytest.mark.asyncio
async def test_performance_under_load(e2e_context: E2ETestContext):
    """
    测试系统在负载下的性能

    创建多个并发会话和消息，验证系统稳定性
    """
    client = e2e_context.client

    # 确保有租户上下文
    if not e2e_context.tenant_data:
        await test_complete_tenant_onboarding_flow(e2e_context)

    # 创建多个并发会话
    concurrent_sessions = 5
    messages_per_session = 3

    async def create_session_with_messages(session_index: int):
        """创建会话并发送消息"""
        try:
            # 创建会话
            session_data = {
                "title": f"Load Test Session {session_index}",
                "customer_info": {
                    "name": f"Test Customer {session_index}",
                    "email": f"customer{session_index}@example.com",
                },
            }

            response = await client.post(
                "/api/v1/sessions", json=session_data, headers=e2e_context.auth_headers
            )

            if response.status_code != 201:
                return False

            session_info = response.json()["data"]
            session_id = session_info["id"]

            # 发送多条消息
            for msg_index in range(messages_per_session):
                message_data = {
                    "content": f"Load test message {msg_index} in session {session_index}",
                    "sender_type": "customer",
                    "sender_id": f"customer{session_index}",
                    "message_type": "text",
                }

                response = await client.post(
                    f"/api/v1/sessions/{session_id}/messages",
                    json=message_data,
                    headers=e2e_context.auth_headers,
                )

                if response.status_code != 201:
                    return False

            return True

        except Exception as e:
            print(f"❌ 负载测试会话 {session_index} 失败: {e}")
            return False

    # 并发执行会话创建
    tasks = [create_session_with_messages(i) for i in range(concurrent_sessions)]

    results = await asyncio.gather(*tasks, return_exceptions=True)

    # 统计成功率
    successful_sessions = sum(1 for result in results if result is True)
    success_rate = successful_sessions / concurrent_sessions

    assert success_rate >= 0.8, f"负载测试成功率过低: {success_rate:.2%}"

    print(
        f"✅ 负载测试完成: {successful_sessions}/{concurrent_sessions} 会话成功 ({success_rate:.2%})"
    )


@pytest.mark.asyncio
async def test_complete_business_flow_integration():
    """
    完整业务流程集成测试

    按顺序运行所有业务流程测试，模拟真实的用户使用场景
    """
    print("🚀 开始完整业务流程集成测试...")
    print("=" * 60)

    # 创建共享的测试上下文
    context = E2ETestContext()
    await context.setup()

    try:
        # 按依赖顺序执行测试
        test_functions = [
            test_complete_tenant_onboarding_flow,
            test_user_management_flow,
            test_session_message_flow,
            test_ai_features_integration,
            test_multi_tenant_isolation,
            test_webhook_integration_flow,
            test_performance_under_load,
        ]

        for i, test_func in enumerate(test_functions, 1):
            print(f"\n📋 执行测试 {i}/{len(test_functions)}: {test_func.__name__}")
            try:
                await test_func(context)
                print(f"✅ 测试 {i} 通过")
            except Exception as e:
                print(f"❌ 测试 {i} 失败: {e}")
                raise

        print("\n" + "=" * 60)
        print("🎉 所有业务流程测试通过!")
        print("🏢 多租户SaaS平台核心功能验证完成")

    finally:
        await context.cleanup()


if __name__ == "__main__":
    # 直接运行完整集成测试
    asyncio.run(test_complete_business_flow_integration())
