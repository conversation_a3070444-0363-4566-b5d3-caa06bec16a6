# 📖 技术文档：AI功能API (ai_features.py)

## 🎯 1. 模块概述

**功能**：提供自动回复、会话总结、客服话术推荐等AI智能功能的RESTful接口。

**核心职责**：
- **自动回复**：提供生成和流式生成自动回复的端点。
- **会话总结**：提供获取会话总结的端点。
- **回复建议**：提供获取客服回复建议的端点。
- **健康检查**：提供AI功能健康检查的端点。

## 🚀 2. 快速使用

### 2.1 依赖注入

```python
from app.services.auto_reply_service import AutoReplyService
from app.services.session_summary_service import SessionSummaryService

@router.post("/auto-reply")
async def generate_auto_reply(
    # ...
    db: AsyncSession = Depends(get_db),
):
    auto_reply_service = AutoReplyService(db)
    # ...
```

### 2.2 核心端点

- `POST /auto-reply` - 生成自动回复
- `POST /auto-reply/stream` - 生成流式自动回复
- `GET /{session_id}/summary` - 获取会话总结
- `GET /{session_id}/suggestions` - 获取客服回复建议

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    A[API Endpoints] --> B(AutoReplyService)
    A --> C(SessionSummaryService)
    A --> D(AgentSuggestionService)
```

### 3.2 数据流

**自动回复流程**：
1. **API接收**：接收自动回复请求。
2. **服务调用**：调用`AutoReplyService.generate_reply`。
3. **响应返回**：返回生成的回复。

## 🔧 4. API参考

| HTTP动词 | 端点 | 描述 |
|---|---|---|
| `POST` | `/auto-reply` | 生成自动回复 |
| `POST` | `/auto-reply/stream` | 生成流式自动回复 |
| `GET` | `/{session_id}/summary` | 获取会话总结 |
| `GET`|`/{session_id}/suggestions`|获取客服回复建议|
| `GET` | `/health` | AI功能健康检查 |

## 🧪 5. 测试策略

- **集成测试**：`tests/integration/test_ai_features_api.py`

## 💡 6. 维护与扩展

- **模型配置**：可以添加API来动态配置不同AI功能使用的LLM模型。
- **A/B测试**：可以添加A/B测试框架来比较不同模型或提示词的效果。
- **功能开关**：可以为每个AI功能添加独立的功能开关。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 