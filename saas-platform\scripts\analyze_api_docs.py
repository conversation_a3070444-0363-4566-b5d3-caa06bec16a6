#!/usr/bin/env python3
"""
API文档完整性分析脚本

检查FastAPI路由的文档完整性，包括：
- response_model 定义
- summary 描述
- description 详细说明
- 标签（tags）定义
- 响应状态码文档
"""

import ast
import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Set


class APIDocAnalyzer:
    """API文档分析器"""

    def __init__(self, base_path: str = "app"):
        self.base_path = Path(base_path)
        self.api_issues: Dict[str, List[Dict[str, Any]]] = {
            "missing_response_model": [],
            "missing_summary": [],
            "missing_description": [],
            "missing_tags": [],
            "missing_status_codes": [],
            "poor_naming": [],
        }

    def analyze_api_files(self) -> Dict[str, Any]:
        """分析API文件"""
        api_path = self.base_path / "api" / "v1"

        if not api_path.exists():
            print(f"❌ API路径不存在: {api_path}")
            return {}

        for py_file in api_path.glob("*.py"):
            if py_file.name == "__init__.py":
                continue

            print(f"🔍 分析文件: {py_file}")
            self._analyze_file(py_file)

        return self._generate_report()

    def _analyze_file(self, file_path: Path) -> None:
        """分析单个文件"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 解析AST
            tree = ast.parse(content)

            # 查找路由装饰器
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    self._analyze_route_function(node, file_path)

        except Exception as e:
            print(f"❌ 分析文件失败 {file_path}: {e}")

    def _analyze_route_function(self, node: ast.FunctionDef, file_path: Path) -> None:
        """分析路由函数"""
        # 检查是否有路由装饰器
        route_decorators = []

        for decorator in node.decorator_list:
            if isinstance(decorator, ast.Call):
                if isinstance(decorator.func, ast.Attribute):
                    # router.get, router.post 等
                    if decorator.func.attr in ["get", "post", "put", "delete", "patch"]:
                        route_decorators.append(decorator)
                elif isinstance(decorator.func, ast.Name):
                    # 直接的装饰器函数
                    if decorator.func.id in ["get", "post", "put", "delete", "patch"]:
                        route_decorators.append(decorator)

        if not route_decorators:
            return

        # 分析每个路由装饰器
        for decorator in route_decorators:
            self._analyze_route_decorator(decorator, node, file_path)

    def _analyze_route_decorator(
        self, decorator: ast.Call, func_node: ast.FunctionDef, file_path: Path
    ) -> None:
        """分析路由装饰器"""
        route_info = {
            "file": str(file_path.relative_to(self.base_path.parent)),
            "function": func_node.name,
            "line": func_node.lineno,
        }

        # 获取HTTP方法
        if isinstance(decorator.func, ast.Attribute):
            method = decorator.func.attr.upper()
        else:
            method = decorator.func.id.upper()

        route_info["method"] = method

        # 获取路径
        if decorator.args:
            if isinstance(decorator.args[0], ast.Constant):
                route_info["path"] = decorator.args[0].value

        # 检查关键字参数
        has_response_model = False
        has_summary = False
        has_description = False
        has_tags = False
        has_status_code = False

        for keyword in decorator.keywords:
            if keyword.arg == "response_model":
                has_response_model = True
            elif keyword.arg == "summary":
                has_summary = True
            elif keyword.arg == "description":
                has_description = True
            elif keyword.arg == "tags":
                has_tags = True
            elif keyword.arg == "status_code":
                has_status_code = True

        # 检查函数文档字符串
        has_docstring = ast.get_docstring(func_node) is not None

        # 记录缺失的文档元素
        if not has_response_model and method in ["GET", "POST", "PUT"]:
            self.api_issues["missing_response_model"].append(
                {**route_info, "issue": "缺少response_model定义"}
            )

        if not has_summary:
            self.api_issues["missing_summary"].append(
                {**route_info, "issue": "缺少summary描述"}
            )

        if not has_description and not has_docstring:
            self.api_issues["missing_description"].append(
                {**route_info, "issue": "缺少description或文档字符串"}
            )

        if not has_tags:
            self.api_issues["missing_tags"].append(
                {**route_info, "issue": "缺少tags标签"}
            )

        # 检查函数命名
        if not func_node.name.startswith(
            (
                "get_",
                "post_",
                "put_",
                "delete_",
                "patch_",
                "create_",
                "update_",
                "list_",
            )
        ):
            self.api_issues["poor_naming"].append(
                {**route_info, "issue": f"函数命名不规范: {func_node.name}"}
            )

    def _generate_report(self) -> Dict[str, Any]:
        """生成报告"""
        total_issues = sum(len(issues) for issues in self.api_issues.values())

        report = {
            "summary": {
                "total_issues": total_issues,
                "categories": {
                    category: len(issues)
                    for category, issues in self.api_issues.items()
                },
            },
            "details": self.api_issues,
            "recommendations": self._generate_recommendations(),
        }

        return report

    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []

        if self.api_issues["missing_response_model"]:
            recommendations.append("为GET/POST/PUT端点添加response_model")

        if self.api_issues["missing_summary"]:
            recommendations.append("为所有路由添加summary描述")

        if self.api_issues["missing_description"]:
            recommendations.append("添加详细的description或函数文档字符串")

        if self.api_issues["missing_tags"]:
            recommendations.append("为路由添加tags以便API文档分组")

        if self.api_issues["poor_naming"]:
            recommendations.append("改进函数命名规范")

        return recommendations

    def save_report(
        self, report: Dict[str, Any], output_file: str = "api_docs_analysis.json"
    ) -> None:
        """保存报告"""
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"📊 报告已保存: {output_file}")


def main():
    """主函数"""
    print("🔍 开始API文档完整性分析...")

    analyzer = APIDocAnalyzer()
    report = analyzer.analyze_api_files()

    if not report:
        print("❌ 分析失败")
        return

    # 显示摘要
    print("\n📊 API文档分析报告")
    print("=" * 50)

    summary = report["summary"]
    print(f"📈 总问题数: {summary['total_issues']}")
    print()

    print("📋 问题分布:")
    for category, count in summary["categories"].items():
        if count > 0:
            category_name = {
                "missing_response_model": "缺少响应模型",
                "missing_summary": "缺少摘要描述",
                "missing_description": "缺少详细描述",
                "missing_tags": "缺少标签",
                "missing_status_codes": "缺少状态码",
                "poor_naming": "命名不规范",
            }.get(category, category)
            print(f"  - {category_name}: {count}个")

    if report["recommendations"]:
        print("\n🎯 改进建议:")
        for i, rec in enumerate(report["recommendations"], 1):
            print(f"  {i}. {rec}")

    # 保存详细报告
    analyzer.save_report(report)

    print(f"\n✅ 分析完成，发现 {summary['total_issues']} 个文档问题")


if __name__ == "__main__":
    main()
