# AstrBot SaaS Platform - 端到端测试配置
# 定义完整的业务流程测试场景、环境配置和质量标准

# 全局配置
global:
  # 测试环境配置
  environment: "e2e_test"
  base_url: "http://localhost:8000"
  
  # 超时配置（秒）
  default_timeout: 30
  api_timeout: 10
  ui_timeout: 15
  database_timeout: 5
  
  # 重试配置
  max_retries: 3
  retry_interval: 2
  
  # 并发配置
  max_concurrent_scenarios: 5
  max_concurrent_steps: 3
  
  # 数据管理
  test_data_cleanup: true
  preserve_test_data_on_failure: true
  
  # 报告配置
  generate_html_report: true
  generate_json_report: true
  capture_screenshots: true
  record_api_calls: true

# 质量标准配置
quality_standards:
  # 功能质量标准
  functional_quality:
    scenario_pass_rate: 95.0      # 场景通过率 >= 95%
    step_pass_rate: 98.0          # 步骤通过率 >= 98%
    business_flow_coverage: 90.0   # 业务流程覆盖率 >= 90%
    critical_path_coverage: 100.0  # 关键路径覆盖率 = 100%
  
  # 性能质量标准
  performance_quality:
    avg_scenario_duration: 60.0   # 平均场景执行时间 <= 60秒
    max_scenario_duration: 300.0  # 最大场景执行时间 <= 5分钟
    api_response_time: 2.0        # API响应时间 <= 2秒
    ui_interaction_time: 3.0      # UI交互响应时间 <= 3秒
  
  # 稳定性质量标准
  stability_quality:
    flaky_test_rate: 2.0          # 不稳定测试率 <= 2%
    execution_consistency: 95.0    # 执行一致性 >= 95%
    error_recovery_rate: 100.0     # 错误恢复率 = 100%
  
  # 数据质量标准
  data_quality:
    data_integrity_check: true    # 数据完整性检查
    multi_tenant_isolation: true  # 多租户隔离验证
    data_consistency: true        # 数据一致性验证
    transaction_atomicity: true   # 事务原子性验证

# 测试场景分类配置
scenario_categories:
  
  # 核心业务流程
  core_business:
    description: "核心业务功能的端到端测试"
    priority: 10
    required_pass_rate: 100.0
    scenarios:
      - tenant_onboarding
      - user_session_management
      - message_ai_processing
    tags: ["core", "critical", "smoke"]
  
  # 系统集成测试
  system_integration:
    description: "系统内部和外部集成的端到端测试"
    priority: 9
    required_pass_rate: 95.0
    scenarios:
      - api_integration
      - database_integration
      - third_party_service_integration
    tags: ["integration", "api", "database"]
  
  # 安全与权限测试
  security_authorization:
    description: "安全性和权限控制的端到端测试"
    priority: 9
    required_pass_rate: 100.0
    scenarios:
      - multi_tenant_isolation
      - authentication_authorization
      - data_privacy_protection
    tags: ["security", "auth", "privacy"]
  
  # 异常处理测试
  exception_handling:
    description: "异常情况和错误恢复的端到端测试"
    priority: 8
    required_pass_rate: 90.0
    scenarios:
      - business_exception_handling
      - system_failure_recovery
      - network_error_handling
    tags: ["exception", "recovery", "robustness"]
  
  # 性能压力测试
  performance_stress:
    description: "性能和压力场景的端到端测试"
    priority: 7
    required_pass_rate: 85.0
    scenarios:
      - high_load_processing
      - concurrent_user_simulation
      - resource_limitation_test
    tags: ["performance", "stress", "load"]

# 具体测试场景配置
test_scenarios:
  
  # ========== 核心业务流程场景 ==========
  
  tenant_onboarding:
    name: "租户入驻完整流程"
    description: "从租户注册到配置完成的完整业务流程"
    category: "core_business"
    priority: 10
    estimated_duration: 60
    prerequisites: []
    
    steps:
      - name: "租户注册"
        action: "POST /api/v1/tenants"
        expected_status: 201
        validation:
          - field: "id"
            type: "uuid"
            required: true
          - field: "api_key"
            type: "string"
            required: true
          - field: "status"
            value: "pending"
        
      - name: "租户激活"
        action: "PATCH /api/v1/tenants/{tenant_id}/activate"
        expected_status: 200
        validation:
          - field: "status"
            value: "active"
        
      - name: "租户配置验证"
        action: "GET /api/v1/tenants/{tenant_id}"
        expected_status: 200
        validation:
          - field: "name"
            type: "string"
            required: true
          - field: "email"
            type: "email"
            required: true
          - field: "plan"
            type: "string"
            required: true
        
      - name: "API权限测试"
        action: "GET /api/v1/users"
        headers:
          X-API-Key: "{api_key}"
        expected_status: [200, 404]
    
    cleanup:
      - action: "DELETE /api/v1/tenants/{tenant_id}"
        condition: "test_passed"
  
  user_session_management:
    name: "用户会话管理流程"
    description: "完整的用户会话创建、管理和关闭流程"
    category: "core_business"
    priority: 9
    estimated_duration: 45
    prerequisites: ["tenant_onboarding"]
    
    steps:
      - name: "用户创建"
        action: "POST /api/v1/users"
        payload:
          platform: "e2e_test"
          user_id: "e2e_user_{timestamp}"
          nickname: "E2E测试用户"
        expected_status: 201
        validation:
          - field: "id"
            type: "string"
            required: true
        
      - name: "会话创建"
        action: "POST /api/v1/sessions"
        payload:
          user_id: "{user_id}"
          platform: "e2e_test"
          channel_type: "direct"
          priority: 5
        expected_status: 201
        validation:
          - field: "id"
            type: "uuid"
            required: true
          - field: "status"
            value: "active"
        
      - name: "会话状态管理"
        action: "PATCH /api/v1/sessions/{session_id}/status"
        payload:
          status: "active"
        expected_status: 200
        
      - name: "会话数据查询"
        action: "GET /api/v1/sessions/{session_id}"
        expected_status: 200
        validation:
          - field: "user_id"
            value: "{user_id}"
          - field: "status"
            value: "active"
        
      - name: "会话关闭"
        action: "PATCH /api/v1/sessions/{session_id}/status"
        payload:
          status: "closed"
        expected_status: 200
        validation:
          - field: "status"
            value: "closed"
  
  message_ai_processing:
    name: "消息AI处理完整链路"
    description: "从用户消息发送到AI响应的完整处理链路"
    category: "core_business"
    priority: 10
    estimated_duration: 90
    prerequisites: ["user_session_management"]
    
    steps:
      - name: "用户消息发送"
        action: "POST /api/v1/messages"
        payload:
          session_id: "{session_id}"
          content: "这是一条端到端测试消息"
          message_type: "text"
          sender_type: "user"
          sender_id: "{user_id}"
        expected_status: 201
        validation:
          - field: "id"
            type: "integer"
            required: true
          - field: "content"
            value: "这是一条端到端测试消息"
        
      - name: "消息处理验证"
        action: "GET /api/v1/messages/{message_id}"
        expected_status: 200
        validation:
          - field: "status"
            value: "processed"
        
      - name: "AI响应生成"
        action: "POST /api/v1/messages/ai-response"
        payload:
          message_id: "{message_id}"
        expected_status: 201
        timeout: 30
        validation:
          - field: "response_message_id"
            type: "integer"
            required: true
        
      - name: "响应质量验证"
        action: "GET /api/v1/messages/{response_message_id}"
        expected_status: 200
        validation:
          - field: "sender_type"
            value: "assistant"
          - field: "content"
            type: "string"
            min_length: 10
        
      - name: "消息历史验证"
        action: "GET /api/v1/messages?session_id={session_id}"
        expected_status: 200
        validation:
          - field: "total"
            min_value: 2  # 至少包含用户消息和AI响应
  
  # ========== 安全与权限测试场景 ==========
  
  multi_tenant_isolation:
    name: "多租户隔离安全验证"
    description: "验证多租户架构的数据和权限隔离"
    category: "security_authorization"
    priority: 9
    estimated_duration: 120
    prerequisites: []
    
    setup:
      - action: "create_multiple_tenants"
        count: 3
        
    steps:
      - name: "创建租户A数据"
        tenant: "tenant_a"
        action: "POST /api/v1/users"
        payload:
          platform: "test"
          user_id: "user_a"
          nickname: "租户A用户"
        expected_status: 201
        
      - name: "创建租户B数据"
        tenant: "tenant_b"
        action: "POST /api/v1/users"
        payload:
          platform: "test"
          user_id: "user_b"
          nickname: "租户B用户"
        expected_status: 201
        
      - name: "租户A访问自己数据"
        tenant: "tenant_a"
        action: "GET /api/v1/users"
        expected_status: 200
        validation:
          - field: "items"
            contains: "user_a"
          - field: "items"
            not_contains: "user_b"
        
      - name: "租户B访问自己数据"
        tenant: "tenant_b"
        action: "GET /api/v1/users"
        expected_status: 200
        validation:
          - field: "items"
            contains: "user_b"
          - field: "items"
            not_contains: "user_a"
        
      - name: "租户A尝试访问租户B数据"
        tenant: "tenant_a"
        action: "GET /api/v1/users/{tenant_b_user_id}"
        expected_status: [403, 404]
        
      - name: "并发访问隔离测试"
        action: "concurrent_access_test"
        concurrent_requests: 10
        tenants: ["tenant_a", "tenant_b", "tenant_c"]
        validation:
          - field: "isolation_maintained"
            value: true
  
  # ========== 异常处理测试场景 ==========
  
  business_exception_handling:
    name: "业务异常处理验证"
    description: "验证系统对各种异常情况的处理能力"
    category: "exception_handling"
    priority: 8
    estimated_duration: 75
    prerequisites: ["tenant_onboarding"]
    
    steps:
      - name: "无效输入处理"
        action: "POST /api/v1/users"
        payload:
          platform: ""  # 无效的空平台
          user_id: "test_user"
          nickname: "测试用户"
        expected_status: 422
        validation:
          - field: "detail"
            type: "array"
            required: true
        
      - name: "重复数据处理"
        action: "POST /api/v1/users"
        payload:
          platform: "test"
          user_id: "duplicate_user"
          nickname: "重复用户"
        steps:
          - execute_twice: true
          - second_expected_status: 409
        
      - name: "资源不存在处理"
        action: "GET /api/v1/users/non_existent_user"
        expected_status: 404
        validation:
          - field: "detail"
            contains: "not found"
        
      - name: "权限不足处理"
        action: "DELETE /api/v1/tenants/{tenant_id}"
        headers:
          X-API-Key: "invalid_key"
        expected_status: 401
        
      - name: "请求超限处理"
        action: "rate_limit_test"
        requests_per_second: 100
        duration: 10
        expected_status: 429
        validation:
          - field: "detail"
            contains: "rate limit"

# 测试数据配置
test_data:
  
  # 租户测试数据模板
  tenant_templates:
    basic_tenant:
      name: "E2E测试租户_{timestamp}"
      email: "e2e_test_{timestamp}@example.com"
      plan: "basic"
      
    premium_tenant:
      name: "E2E高级租户_{timestamp}"
      email: "e2e_premium_{timestamp}@example.com"
      plan: "premium"
  
  # 用户测试数据模板
  user_templates:
    basic_user:
      platform: "e2e_test"
      user_id: "e2e_user_{timestamp}"
      nickname: "E2E测试用户"
      extra_data:
        test: true
        created_by: "e2e_framework"
        
    agent_user:
      platform: "e2e_test"
      user_id: "e2e_agent_{timestamp}"
      nickname: "E2E测试客服"
      extra_data:
        role: "agent"
        department: "customer_service"
  
  # 会话测试数据模板
  session_templates:
    direct_session:
      platform: "e2e_test"
      channel_type: "direct"
      priority: 5
      
    group_session:
      platform: "e2e_test"
      channel_type: "group"
      priority: 3
  
  # 消息测试数据模板
  message_templates:
    text_message:
      content: "这是一条端到端测试消息"
      message_type: "text"
      sender_type: "user"
      
    system_message:
      content: "系统消息：测试开始"
      message_type: "system"
      sender_type: "system"

# 环境特定配置
environments:
  
  # 本地开发环境
  local:
    base_url: "http://localhost:8000"
    database_url: "postgresql://test:test@localhost:5432/testdb"
    parallel_execution: false
    debug_mode: true
    
  # Docker环境
  docker:
    base_url: "http://app:8000"
    database_url: "******************************/testdb"
    parallel_execution: true
    debug_mode: false
    
  # CI/CD环境
  ci:
    base_url: "http://test-api:8000"
    database_url: "***********************************/testdb"
    parallel_execution: true
    timeout_multiplier: 1.5
    max_retries: 5
    
  # 预生产环境
  staging:
    base_url: "https://staging-api.example.com"
    database_url: "**************************************/stagingdb"
    parallel_execution: true
    data_cleanup_disabled: true

# 报告和监控配置
reporting:
  
  # 报告格式
  formats:
    - html
    - json
    - junit_xml
    
  # 报告内容
  include:
    - execution_summary
    - scenario_details
    - step_breakdown
    - performance_metrics
    - failure_analysis
    - data_artifacts
    
  # 实时监控
  real_time_monitoring:
    enabled: true
    websocket_endpoint: "/ws/test-monitoring"
    update_interval: 5  # 秒
    
  # 通知配置
  notifications:
    enabled: true
    channels:
      - email
      - slack
      - webhook
    
    triggers:
      - test_completion
      - critical_failure
      - quality_threshold_breach

# 质量门禁配置
quality_gates:
  
  # 发布门禁
  release_gate:
    scenario_pass_rate: 98.0
    critical_scenario_pass_rate: 100.0
    performance_regression_threshold: 10.0
    
  # 部署门禁
  deployment_gate:
    scenario_pass_rate: 95.0
    security_test_pass_rate: 100.0
    integration_test_pass_rate: 95.0
    
  # 回归门禁
  regression_gate:
    scenario_pass_rate: 90.0
    flaky_test_rate: 5.0
    execution_time_increase: 20.0 