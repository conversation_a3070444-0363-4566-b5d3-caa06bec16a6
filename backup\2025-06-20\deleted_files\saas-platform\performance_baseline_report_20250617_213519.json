{"test_summary": {"total_tests": 62, "successful_tests": 60, "success_rate": 96.7741935483871, "test_duration_seconds": 2.04069}, "performance_metrics": {"avg_response_time_ms": 175.47886945950935, "min_response_time_ms": 7.161726511640427, "max_response_time_ms": 724.3423114778168, "median_response_time_ms": 103.38128348114637}, "test_results": [{"endpoint": "/health", "method": "GET", "response_time_ms": 8.061462809511474, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.3881638}, {"endpoint": "/health", "method": "GET", "response_time_ms": 7.161726511640427, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.4406338}, {"endpoint": "/health", "method": "GET", "response_time_ms": 10.00977845658235, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.4914734}, {"endpoint": "/health", "method": "GET", "response_time_ms": 13.866204237858582, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.5417082}, {"endpoint": "/health", "method": "GET", "response_time_ms": 10.508940151641557, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.5920322}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 47.3417225135568, "expected_time_ms": 15, "success": false, "status_code": 500, "timestamp": **********.6422231}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 41.97451833229319, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.6994822}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 10.54822139458709, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.7507834}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 12.085835083845723, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.8015265}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 11.220915056835372, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.851677}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 106.49312828980243, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.9020872}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 71.28528604112753, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.9554038}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 99.80756948851521, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.0063245}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 75.27125519953277, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.0569956}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 100.26943867249032, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.108263}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 439.053749637625, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.1588287}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 270.4463981674079, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.209406}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 269.41160106666393, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.259748}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 152.78098305020882, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.3100677}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 209.13333172268636, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.360598}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 134.70918324427248, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.4109452}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 210.61044300629914, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.468021}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 150.89157244879644, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.518361}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 578.2677721231547, "expected_time_ms": 150, "success": false, "status_code": 500, "timestamp": **********.5687883}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 180.42065277304738, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.6194074}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 84.75954427662316, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.670224}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 76.86968216188063, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.720742}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 113.98885359726783, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.771321}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 114.45362724724615, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.821849}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 281.9902430330193, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.8721998}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 724.3423114778168, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": **********.9226875}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 706.056215685075, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": **********.97357}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 383.0885600811259, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750167319.0245755}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 616.9157148836616, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750167319.0748181}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 470.5613857981254, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750167319.124892}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 381.2733190208111, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167319.1751173}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 231.73503988577093, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167319.2254813}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 351.2505490311187, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167319.275841}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 350.962802522566, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167319.327113}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 445.3638346241303, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167319.3783817}, {"operation": "SELECT租户列表", "response_time_ms": 49.6761214155482, "expected_time_ms": 50, "success": true}, {"operation": "SELECT租户列表", "response_time_ms": 40.766643346647015, "expected_time_ms": 50, "success": true}, {"operation": "SELECT租户列表", "response_time_ms": 87.87769647024098, "expected_time_ms": 50, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 97.48264826801115, "expected_time_ms": 80, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 132.48603905767607, "expected_time_ms": 80, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 123.95753566118954, "expected_time_ms": 80, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 95.24945692998578, "expected_time_ms": 60, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 89.02630881717124, "expected_time_ms": 60, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 75.86396032420365, "expected_time_ms": 60, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 51.555137316138826, "expected_time_ms": 40, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 50.92140591983825, "expected_time_ms": 40, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 46.64764578812185, "expected_time_ms": 40, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 52.95640049522301, "expected_time_ms": 30, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 43.939915324250805, "expected_time_ms": 30, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 53.95146025672546, "expected_time_ms": 30, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 264.98307133159847, "expected_time_ms": 150, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 141.16914560136513, "expected_time_ms": 150, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 124.02046352138616, "expected_time_ms": 150, "success": true}, {"scenario": "5个并发用户", "concurrent_users": 5, "avg_response_time_ms": 160.34154240119003, "p95_response_time_ms": 179.04334128999773, "max_response_time_ms": 177.36896778883556, "expected_max_ms": 200, "success": true}, {"scenario": "10个并发用户", "concurrent_users": 10, "avg_response_time_ms": 204.5364190655729, "p95_response_time_ms": 242.79213973766156, "max_response_time_ms": 238.55284028249656, "expected_max_ms": 350, "success": true}, {"scenario": "20个并发用户", "concurrent_users": 20, "avg_response_time_ms": 313.0566039835362, "p95_response_time_ms": 338.58419166132387, "max_response_time_ms": 338.7709284918586, "expected_max_ms": 600, "success": true}, {"scenario": "50个并发用户", "concurrent_users": 50, "avg_response_time_ms": 612.7062404435784, "p95_response_time_ms": 638.7980116767761, "max_response_time_ms": 639.6592552419023, "expected_max_ms": 1200, "success": true}], "recommendations": ["🔧 数据库连接池优化: 确保数据库连接池大小适合并发负载", "📊 API响应时间监控: 建立API响应时间监控和告警机制", "🚀 缓存策略: 对频繁查询的数据实施Redis缓存", "⚡ 异步处理: AI功能采用异步处理减少响应时间", "🔍 SQL查询优化: 审查慢查询并添加适当索引", "📈 负载均衡: 考虑在高并发场景下实施负载均衡", "💾 数据库分片: 大量数据时考虑数据库分片策略", "🛡️ 限流保护: 实施API限流保护系统稳定性"]}