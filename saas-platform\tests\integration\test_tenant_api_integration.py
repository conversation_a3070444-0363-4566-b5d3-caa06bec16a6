"""
租户API集成测试
测试租户API端点与服务层、数据库的完整集成
"""

from uuid import UUID, uuid4

import pytest
from httpx import AsyncClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.tenant import Tenant


class TestTenantAPIIntegration:
    """租户API集成测试类"""

    @pytest.mark.asyncio
    async def test_create_tenant_full_integration(
        self, client: AsyncClient, db_session: AsyncSession
    ):
        """
        测试创建租户的完整集成流程
        验证：API -> Service -> Database 的完整数据流
        """
        # Given: 准备租户创建数据
        tenant_data = {
            "name": "集成测试租户",
            "email": f"integration-test-{uuid4().hex[:8]}@test.com",
            "plan": "basic",
            "metadata": {"test_type": "integration", "created_by": "automated_test"},
        }

        # When: 调用API创建租户
        response = await client.post("/api/v1/tenants", json=tenant_data)

        # Then: 验证API响应
        assert response.status_code == 201
        response_data = response.json()
        assert response_data["success"] is True

        tenant_response = response_data["data"]
        assert tenant_response["name"] == tenant_data["name"]
        assert tenant_response["email"] == tenant_data["email"]
        assert tenant_response["plan"] == tenant_data["plan"]
        assert tenant_response["status"] == "active"
        assert tenant_response["metadata"] == tenant_data["metadata"]

        # Then: 验证数据库中的实际数据
        tenant_id = tenant_response["id"]
        tenant_uuid = UUID(tenant_id)  # 将字符串转换为UUID对象
        stmt = select(Tenant).where(Tenant.id == tenant_uuid)
        result = await db_session.execute(stmt)
        db_tenant = result.scalar_one_or_none()

        assert db_tenant is not None
        assert str(db_tenant.id) == tenant_id
        assert db_tenant.name == tenant_data["name"]
        assert db_tenant.email == tenant_data["email"]
        assert db_tenant.plan.value == tenant_data["plan"]
        assert db_tenant.status.value == "active"
        assert db_tenant.extra_data == tenant_data["metadata"]
        assert db_tenant.api_key is not None  # 应该自动生成API密钥

        # Then: 验证计算属性
        assert db_tenant.is_active is True
        assert db_tenant.display_name == tenant_data["name"]

    @pytest.mark.asyncio
    async def test_get_tenant_with_database_verification(
        self, client: AsyncClient, db_session: AsyncSession
    ):
        """
        测试获取租户信息的集成流程
        验证API响应与数据库数据的一致性

        需要先创建一个测试租户
        """
        # Given: 创建测试租户
        from app.models.tenant import TenantPlan, TenantStatus

        sample_tenant = Tenant(
            name="测试租户",
            email=f"test-{uuid4().hex[:8]}@test.com",
            plan=TenantPlan.BASIC,
            status=TenantStatus.ACTIVE,
            API_KEY = "test_api_key_for_testing",  # 设置已知的API密钥
        )
        db_session.add(sample_tenant)
        await db_session.commit()
        await db_session.refresh(sample_tenant)

        # When: 通过API获取租户信息（使用API密钥认证）
        response = await client.get(
            f"/api/v1/tenants/{sample_tenant.id}",
            headers={"X-API-Key": "test_api_key_12345678"},
        )

        # Then: 验证API响应
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True

        tenant_response = response_data["data"]

        # Then: 对比API响应与数据库实际数据
        assert tenant_response["id"] == str(sample_tenant.id)
        assert tenant_response["name"] == sample_tenant.name
        assert tenant_response["email"] == sample_tenant.email
        assert tenant_response["status"] == sample_tenant.status.value
        assert tenant_response["plan"] == sample_tenant.plan.value
        assert tenant_response["metadata"] == (sample_tenant.extra_data or {})
        assert tenant_response["is_active"] == sample_tenant.is_active
        assert tenant_response["display_name"] == sample_tenant.display_name

        # Then: 验证敏感信息不泄露
        assert "api_key" not in tenant_response

    @pytest.mark.asyncio
    async def test_update_tenant_integration(
        self, client: AsyncClient, db_session: AsyncSession
    ):
        """
        测试更新租户的完整集成流程
        使用JWT认证（符合系统分层安全策略）
        """
        # Given: 创建测试租户
        from app.models.tenant import TenantPlan, TenantStatus

        sample_tenant = Tenant(
            name="更新测试租户",
            email=f"update-test-{uuid4().hex[:8]}@test.com",
            plan=TenantPlan.BASIC,
            status=TenantStatus.ACTIVE,
            API_KEY = "test_api_key_for_testing",  # 设置已知的API密钥
        )
        db_session.add(sample_tenant)
        await db_session.commit()
        await db_session.refresh(sample_tenant)

        # Given: 创建测试用户并生成JWT Token
        from app.core.security import create_access_token
        from app.models.user import User

        # 使用复合主键格式创建用户
        platform = "test"
        user_id = f"test_user_{uuid4().hex[:8]}"
        composite_id = User.create_user_id(platform, user_id)

        test_user = User(
            id=composite_id,
            platform=platform,
            user_id=user_id,
            nickname="update_test_user",
            tenant_id=sample_tenant.id,
        )
        db_session.add(test_user)
        await db_session.commit()
        await db_session.refresh(test_user)

        # 生成JWT Token
        access_token = create_access_token(
            subject=str(test_user.id),
            extra_data={
                "tenant_id": str(sample_tenant.id),
                "email": f"user-{uuid4().hex[:8]}@test.com",
                "role": "user",
            },
        )

        # Given: 准备更新数据
        update_data = {
            "name": f"更新后的租户名称-{uuid4().hex[:4]}",
            "plan": "pro",
            "metadata": {
                "updated_by": "integration_test",
                "update_reason": "plan_upgrade",
            },
        }

        # When: 调用API更新租户（使用JWT认证 - 修改操作需要JWT）
        response = await client.put(
            f"/api/v1/tenants/{sample_tenant.id}",
            json=update_data,
            headers={"Authorization": f"Bearer {access_token}"},
        )

        # Then: 验证API响应
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True

        # Then: 验证数据库更新
        await db_session.refresh(sample_tenant)
        assert sample_tenant.name == update_data["name"]
        assert sample_tenant.plan.value == update_data["plan"]
        assert sample_tenant.extra_data == update_data["metadata"]
        assert sample_tenant.email == sample_tenant.email  # 未更新的字段保持原值

    @pytest.mark.asyncio
    async def test_list_tenants_with_pagination(
        self, client: AsyncClient, db_session: AsyncSession
    ):
        """
        测试租户列表API的分页集成功能
        使用管理员权限（符合系统分层安全策略）
        """
        # Given: 创建系统租户用于管理员用户
        system_tenant = Tenant(
            name="系统管理租户",
            email=f"system-{uuid4().hex[:8]}@admin.internal",
            plan="enterprise",
            API_KEY = "test_api_key_for_testing",
        )
        db_session.add(system_tenant)
        await db_session.commit()
        await db_session.refresh(system_tenant)

        # Given: 创建管理员用户（关联到系统租户）
        from app.core.security import create_access_token
        from app.models.user import User

        # 使用复合主键格式创建管理员用户
        platform = "{REPLACE_WITH_ENV_VAR}"
        user_id = f"admin_user_{uuid4().hex[:8]}"
        composite_id = User.create_user_id(platform, user_id)

        admin_user = User(
            id=composite_id,
            platform=platform,
            user_id=user_id,
            nickname="admin_test_user",
            tenant_id=system_tenant.id,  # 关联到系统租户而不是None
        )
        db_session.add(admin_user)

        # Given: 为admin用户创建管理员角色和权限
        from app.models.role import Permission, Role

        # 创建admin权限
        admin_permission = Permission(
            name="admin_access",
            description="管理员访问权限",
            resource="{REPLACE_WITH_ENV_VAR}",
            action="access",
            is_active=True,
        )
        db_session.add(admin_permission)

        # 创建系统管理员角色
        admin_role = Role(
            name="system_admin",
            display_name="系统管理员",
            description="系统管理员角色",
            tenant_id=system_tenant.id,
            is_system_role=True,
            is_active=True,
        )
        db_session.add(admin_role)
        await db_session.commit()
        await db_session.refresh(admin_permission)
        await db_session.refresh(admin_role)

        # 创建角色权限关联
        from app.models.role import role_permissions

        role_permission_stmt = role_permissions.insert().values(
            role_id=admin_role.id, permission_id=admin_permission.id
        )
        await db_session.execute(role_permission_stmt)

        # 创建用户角色关联
        from app.models.role import user_roles

        user_role_stmt = user_roles.insert().values(
            user_id=admin_user.id, role_id=admin_role.id, tenant_id=system_tenant.id
        )
        await db_session.execute(user_role_stmt)

        # Given: 创建多个测试租户
        test_tenants = []
        for i in range(5):
            tenant = Tenant(
                name=f"分页测试租户-{i}",
                email=f"page-test-{i}-{uuid4().hex[:6]}@test.com",
                plan="basic",
            )
            db_session.add(tenant)
            test_tenants.append(tenant)

        await db_session.commit()
        await db_session.refresh(admin_user)

        # 生成管理员JWT Token
        admin_token = create_access_token(
            subject=str(admin_user.id),
            extra_data={"email": f"admin-{uuid4().hex[:8]}@test.com", "role": "{REPLACE_WITH_ENV_VAR}"},
        )

        # When: 请求第一页（每页2条，使用管理员JWT认证）
        response = await client.get(
            "/api/v1/tenants",
            params={"skip": 0, "limit": 2},
            headers={"Authorization": f"Bearer {admin_token}"},
        )

        # Then: 验证分页响应
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True

        # 根据实际API响应结构调整
        tenants_list = response_data["data"]
        assert len(tenants_list) >= 2  # 至少包含我们创建的一些租户

    @pytest.mark.asyncio
    async def test_tenant_status_update_integration(
        self, client: AsyncClient, db_session: AsyncSession
    ):
        """
        测试租户状态更新的集成流程
        使用JWT认证（符合系统分层安全策略）
        """
        # Given: 创建测试租户
        from app.models.tenant import TenantPlan, TenantStatus

        sample_tenant = Tenant(
            name="状态测试租户",
            email=f"status-test-{uuid4().hex[:8]}@test.com",
            plan=TenantPlan.BASIC,
            status=TenantStatus.ACTIVE,
            API_KEY = "test_api_key_for_testing",  # 设置已知的API密钥
        )
        db_session.add(sample_tenant)
        await db_session.commit()
        await db_session.refresh(sample_tenant)

        # Given: 创建测试用户并生成JWT Token
        from app.core.security import create_access_token
        from app.models.user import User

        # 使用复合主键格式创建用户
        platform = "test"
        user_id = f"status_test_user_{uuid4().hex[:8]}"
        composite_id = User.create_user_id(platform, user_id)

        test_user = User(
            id=composite_id,
            platform=platform,
            user_id=user_id,
            nickname="status_test_user",
            tenant_id=sample_tenant.id,
        )
        db_session.add(test_user)
        await db_session.commit()
        await db_session.refresh(test_user)

        # 生成JWT Token
        access_token = create_access_token(
            subject=str(test_user.id),
            extra_data={
                "tenant_id": str(sample_tenant.id),
                "email": f"status-user-{uuid4().hex[:8]}@test.com",
                "role": "user",
            },
        )

        # Given: 确认初始状态
        assert sample_tenant.status.value == "active"
        assert sample_tenant.is_active is True

        # When: 暂停租户（使用JWT认证 - 修改操作需要JWT）
        response = await client.patch(
            f"/api/v1/tenants/{sample_tenant.id}/status",
            json={"is_active": False},
            headers={"Authorization": f"Bearer {access_token}"},
        )

        # Then: 验证API响应
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True

        # Then: 验证数据库状态变化
        await db_session.refresh(sample_tenant)
        assert sample_tenant.status.value == "suspended"
        assert sample_tenant.is_active is False

    @pytest.mark.asyncio
    async def test_tenant_deletion_cascade_integration(
        self, client: AsyncClient, db_session: AsyncSession
    ):
        """
        测试租户删除的级联集成效果
        使用JWT认证（符合系统分层安全策略）
        """
        # Given: 创建测试租户
        tenant = Tenant(
            name="删除测试租户",
            email=f"delete-test-{uuid4().hex[:8]}@test.com",
            plan="basic",
            API_KEY = "test_api_key_for_testing",  # 设置已知的API密钥
        )
        db_session.add(tenant)
        await db_session.commit()
        await db_session.refresh(tenant)

        # Given: 创建测试用户并生成JWT Token
        from app.core.security import create_access_token
        from app.models.user import User

        # 使用复合主键格式创建用户
        platform = "test"
        user_id = f"delete_test_user_{uuid4().hex[:8]}"
        composite_id = User.create_user_id(platform, user_id)

        test_user = User(
            id=composite_id,
            platform=platform,
            user_id=user_id,
            nickname="delete_test_user",
            tenant_id=tenant.id,
        )
        db_session.add(test_user)
        await db_session.commit()
        await db_session.refresh(test_user)

        # 生成JWT Token
        access_token = create_access_token(
            subject=str(test_user.id),
            extra_data={
                "tenant_id": str(tenant.id),
                "email": f"delete-user-{uuid4().hex[:8]}@test.com",
                "role": "user",
            },
        )

        tenant_id = tenant.id

        # When: 删除租户（使用JWT认证 - 删除操作需要JWT）
        response = await client.delete(
            f"/api/v1/tenants/{tenant_id}",
            headers={"Authorization": f"Bearer {access_token}"},
        )

        # Then: 验证API响应
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True

        # Then: 验证数据库中租户状态（软删除）
        # 需要重新开启新的事务来获取最新数据，避免事务隔离问题
        await db_session.commit()  # 确保当前事务状态清晰
        stmt = select(Tenant).where(Tenant.id == tenant_id)
        result = await db_session.execute(stmt)
        deleted_tenant = result.scalar_one_or_none()

        if deleted_tenant:
            # 刷新对象以获取最新的数据库状态
            await db_session.refresh(deleted_tenant)
            # 根据实际删除逻辑调整：软删除应该是 status = DEACTIVATED
            assert deleted_tenant.status.value == "deactivated"  # 检查status字段
            assert deleted_tenant.is_active is False  # 检查计算属性
        else:
            # 如果租户被硬删除，这也是可接受的结果
            assert True  # 硬删除成功

    @pytest.mark.asyncio
    async def test_error_handling_integration(self, client: AsyncClient):
        """
        测试错误处理的集成行为
        """
        # When: 尝试创建邮箱重复的租户
        tenant_data = {
            "name": "错误测试租户",
            "email": "<EMAIL>",
            "plan": "basic",
        }

        # 第一次创建应该成功
        response1 = await client.post("/api/v1/tenants", json=tenant_data)
        assert response1.status_code == 201

        # 第二次创建应该失败（邮箱重复）
        response2 = await client.post("/api/v1/tenants", json=tenant_data)
        assert response2.status_code == 409  # Conflict
        error_data = response2.json()

        # 适配实际的错误响应格式 - 可能没有success字段或格式不同
        if "success" in error_data:
            assert error_data["success"] is False

        # 检查错误详情
        if "detail" in error_data:
            detail = error_data["detail"]
        elif "message" in error_data:
            detail = error_data["message"]
        else:
            detail = str(error_data)

        assert (
            "duplicate" in detail.lower()
            or "exists" in detail.lower()
            or "已存在" in detail
            or "已被" in detail
            or "使用" in detail
        )  # 支持中英文错误信息

    @pytest.mark.asyncio
    async def test_api_key_generation_integration(
        self, client: AsyncClient, db_session: AsyncSession
    ):
        """
        测试API密钥重新生成的集成流程
        使用JWT认证（符合系统分层安全策略）
        """
        # Given: 创建测试租户
        from app.models.tenant import TenantPlan, TenantStatus

        sample_tenant = Tenant(
            name="API密钥测试租户",
            email=f"apikey-test-{uuid4().hex[:8]}@test.com",
            plan=TenantPlan.BASIC,
            status=TenantStatus.ACTIVE,
            API_KEY = "test_api_key_for_testing",  # 设置已知的API密钥
        )
        db_session.add(sample_tenant)
        await db_session.commit()
        await db_session.refresh(sample_tenant)

        # Given: 创建测试用户并生成JWT Token
        from app.core.security import create_access_token
        from app.models.user import User

        # 使用复合主键格式创建用户
        platform = "test"
        user_id = f"apikey_test_user_{uuid4().hex[:8]}"
        composite_id = User.create_user_id(platform, user_id)

        test_user = User(
            id=composite_id,
            platform=platform,
            user_id=user_id,
            nickname="apikey_test_user",
            tenant_id=sample_tenant.id,
        )
        db_session.add(test_user)
        await db_session.commit()
        await db_session.refresh(test_user)

        # 生成JWT Token
        access_token = create_access_token(
            subject=str(test_user.id),
            extra_data={
                "tenant_id": str(sample_tenant.id),
                "email": f"apikey-user-{uuid4().hex[:8]}@test.com",
                "role": "user",
            },
        )

        # Given: 获取原始API密钥
        original_api_key = sample_tenant.api_key
        assert original_api_key is not None

        # When: 重新生成API密钥（使用JWT认证 - 修改操作需要JWT）
        response = await client.post(
            f"/api/v1/tenants/{sample_tenant.id}/regenerate-api-key",
            headers={"Authorization": f"Bearer {access_token}"},
        )

        # Then: 验证API响应
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True

        new_api_key = response_data["data"]["api_key"]
        assert new_api_key != original_api_key
        assert new_api_key.startswith("astr_")  # 确认密钥格式

        # Then: 验证数据库更新
        await db_session.refresh(sample_tenant)
        # API密钥在数据库中存储为哈希值，所以不能直接比较
        assert sample_tenant.api_key != original_api_key

    @pytest.mark.asyncio
    async def test_get_tenant_statistics_integration(
        self, client: AsyncClient, db_session: AsyncSession
    ):
        """测试获取租户统计信息的集成流程"""
        # Given: 创建一个测试租户
        test_tenant = Tenant(name="Stats Tenant", email=f"stats-{uuid4().hex[:8]}@test.com")
        db_session.add(test_tenant)
        await db_session.commit()
        await db_session.refresh(test_tenant)

        # Given: 为该租户创建一些关联数据（例如用户和会话）
        from app.models.user import User
        from app.models.session import Session
        
        # 创建用户
        for i in range(3):
            user = User(
                id=f"stats_platform:user_{i}",
                tenant_id=test_tenant.id,
                platform="stats_platform",
                user_id=f"user_{i}",
            )
            db_session.add(user)
        
        # 创建会话
        for i in range(5):
            session = Session(
                tenant_id=test_tenant.id,
                user_id=f"stats_platform:user_{i % 3}",
                platform="stats_platform",
            )
            db_session.add(session)
            
        await db_session.commit()
        
        # When: 调用API获取统计信息 (需要JWT认证)
        from app.core.security import create_access_token
        access_token = create_access_token(
            subject=f"tenant:{test_tenant.id}", # 简化处理，实际应有用户
            extra_data={"tenant_id": str(test_tenant.id)}
        )
        
        response = await client.get(
            f"/api/v1/tenants/{test_tenant.id}/stats",
            headers={"Authorization": f"Bearer {access_token}"}
        )

        # Then: 验证API响应
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True
        
        stats = response_data["data"]
        assert stats["user_count"] == 3
        assert stats["session_count"] == 5
        assert "message_count" in stats # 检查字段存在性
