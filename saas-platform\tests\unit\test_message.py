"""
测试 Message 模型
"""

import pytest
import uuid
from datetime import datetime
from unittest.mock import Mock, patch

from app.models.message import Message, MessageType, MessageStatus, SenderType


class TestMessageModel:
    """测试Message模型"""

    def test_message_creation(self):
        """测试消息创建"""
        # Given
        tenant_id = uuid.uuid4()
        session_id = uuid.uuid4()

        # When
        message = Message(
            tenant_id=tenant_id,
            session_id=session_id,
            content="Hello, world!",
            message_type=MessageType.TEXT,
            sender_type=SenderType.USER,
            sender_id="user123",
            timestamp=datetime.now(),
        )

        # Then
        assert message.tenant_id == tenant_id
        assert message.session_id == session_id
        assert message.content == "Hello, world!"
        assert message.message_type == MessageType.TEXT
        assert message.sender_type == SenderType.USER
        assert message.sender_id == "user123"
        assert message.attachments is None or message.attachments == []
        assert message.extra_data is None or message.extra_data == {}

    def test_message_enums(self):
        """测试消息枚举值"""
        # MessageType 枚举
        assert MessageType.TEXT == "text"
        assert MessageType.IMAGE == "image"
        assert MessageType.FILE == "file"
        assert MessageType.VOICE == "voice"
        assert MessageType.VIDEO == "video"
        assert MessageType.LOCATION == "location"
        assert MessageType.SYSTEM == "system"

        # SenderType 枚举
        assert SenderType.USER == "user"
        assert SenderType.STAFF == "staff"
        assert SenderType.BOT == "bot"
        assert SenderType.SYSTEM == "system"

        # MessageStatus 枚举
        assert MessageStatus.SENT == "sent"
        assert MessageStatus.DELIVERED == "delivered"
        assert MessageStatus.READ == "read"
        assert MessageStatus.FAILED == "failed"

    def test_message_properties(self):
        """测试消息属性方法"""
        # Given
        user_message = Message(
            tenant_id=uuid.uuid4(),
            session_id=uuid.uuid4(),
            content="User message",
            sender_type=SenderType.USER,
            sender_id="user123",
            timestamp=datetime.now(),
        )

        staff_message = Message(
            tenant_id=uuid.uuid4(),
            session_id=uuid.uuid4(),
            content="Staff message",
            sender_type=SenderType.STAFF,
            sender_id="staff123",
            timestamp=datetime.now(),
        )

        system_message = Message(
            tenant_id=uuid.uuid4(),
            session_id=uuid.uuid4(),
            content="System message",
            sender_type=SenderType.SYSTEM,
            sender_id="system",
            timestamp=datetime.now(),
        )

        # When & Then
        assert user_message.is_from_user is True
        assert user_message.is_from_staff is False
        assert user_message.is_system_message is False

        assert staff_message.is_from_user is False
        assert staff_message.is_from_staff is True
        assert staff_message.is_system_message is False

        assert system_message.is_from_user is False
        assert system_message.is_from_staff is False
        assert system_message.is_system_message is True

    def test_has_attachments_property(self):
        """测试附件检查属性"""
        # Given - 无附件消息
        message_no_attachments = Message(
            tenant_id=uuid.uuid4(),
            session_id=uuid.uuid4(),
            content="No attachments",
            sender_type=SenderType.USER,
            sender_id="user123",
            timestamp=datetime.now(),
            attachments=[],
        )

        # Given - 有附件消息
        message_with_attachments = Message(
            tenant_id=uuid.uuid4(),
            session_id=uuid.uuid4(),
            content="With attachments",
            sender_type=SenderType.USER,
            sender_id="user123",
            timestamp=datetime.now(),
            attachments=[{"type": "image", "url": "http://example.com/image.jpg"}],
        )

        # When & Then
        assert message_no_attachments.has_attachments is False
        assert message_no_attachments.attachment_count == 0

        assert message_with_attachments.has_attachments is True
        assert message_with_attachments.attachment_count == 1

    def test_to_dict_method(self):
        """测试to_dict方法"""
        # Given
        message = Message(
            id=123,
            tenant_id=uuid.uuid4(),
            session_id=uuid.uuid4(),
            content="Test message",
            message_type=MessageType.TEXT,
            sender_type=SenderType.USER,
            sender_id="user123",
            timestamp=datetime(2023, 1, 1, 12, 0, 0),
            attachments=[{"type": "image", "url": "test.jpg"}],
            extra_data={"key": "value"},
        )

        # When
        result = message.to_dict()

        # Then
        assert "id" in result
        assert "tenant_id" in result
        assert "session_id" in result
        assert "content" in result
        assert "message_type" in result
        assert "sender_type" in result
        assert "sender_id" in result
        assert "timestamp" in result
        assert "attachments" in result
        assert "extra_data" in result

        assert result["content"] == "Test message"
        assert result["message_type"] == "text"
        assert result["sender_type"] == "user"
        assert result["sender_id"] == "user123"

    def test_add_attachment_method(self):
        """测试添加附件方法"""
        # Given
        message = Message(
            tenant_id=uuid.uuid4(),
            session_id=uuid.uuid4(),
            content="Test message",
            sender_type=SenderType.USER,
            sender_id="user123",
            timestamp=datetime.now(),
            attachments=[],
        )

        attachment = {
            "type": "image",
            "url": "http://example.com/image.jpg",
            "filename": "image.jpg",
            "size": 1024,
        }

        # When
        message.add_attachment(attachment)

        # Then
        assert message.has_attachments is True
        assert message.attachment_count == 1
        assert message.attachments[0] == attachment

    def test_metadata_operations(self):
        """测试元数据操作方法"""
        # Given
        message = Message(
            tenant_id=uuid.uuid4(),
            session_id=uuid.uuid4(),
            content="Test message",
            sender_type=SenderType.USER,
            sender_id="user123",
            timestamp=datetime.now(),
            extra_data={},
        )

        # When & Then
        # 更新元数据
        message.update_metadata("test_key", "test_value")
        assert message.get_metadata("test_key") == "test_value"

        # 获取不存在的键
        assert message.get_metadata("nonexistent_key") is None
        assert message.get_metadata("nonexistent_key", "default") == "default"

    def test_mark_as_read_and_is_read_by(self):
        """测试标记已读和检查已读状态"""
        # Given
        message = Message(
            tenant_id=uuid.uuid4(),
            session_id=uuid.uuid4(),
            content="Test message",
            sender_type=SenderType.USER,
            sender_id="user123",
            timestamp=datetime.now(),
            extra_data={},
        )

        reader_id = "staff123"

        # When
        assert message.is_read_by(reader_id) is False

        message.mark_as_read(reader_id)

        # Then
        assert message.is_read_by(reader_id) is True

    def test_create_user_message_class_method(self):
        """测试创建用户消息的类方法"""
        # Given
        tenant_id = uuid.uuid4()
        session_id = uuid.uuid4()
        sender_id = "user123"
        content = "Hello from user"
        platform_message_id = "platform_123"
        timestamp = datetime.now()
        attachments = [{"type": "image", "url": "test.jpg"}]

        # When
        message = Message.create_user_message(
            tenant_id=tenant_id,
            session_id=session_id,
            sender_id=sender_id,
            content=content,
            message_type=MessageType.TEXT,
            platform_message_id=platform_message_id,
            timestamp=timestamp,
            attachments=attachments,
        )

        # Then
        assert message.tenant_id == tenant_id
        assert message.session_id == session_id
        assert message.sender_id == sender_id
        assert message.content == content
        assert message.sender_type == SenderType.USER
        assert message.message_type == MessageType.TEXT
        assert message.platform_message_id == platform_message_id
        assert message.timestamp == timestamp
        assert message.attachments == attachments

    def test_create_staff_message_class_method(self):
        """测试创建客服消息的类方法"""
        # Given
        tenant_id = uuid.uuid4()
        session_id = uuid.uuid4()
        staff_id = "staff123"
        content = "Hello from staff"
        reply_to_id = 456
        attachments = [{"type": "file", "url": "document.pdf"}]

        # When
        message = Message.create_staff_message(
            tenant_id=tenant_id,
            session_id=session_id,
            staff_id=staff_id,
            content=content,
            message_type=MessageType.FILE,
            reply_to_id=reply_to_id,
            attachments=attachments,
        )

        # Then
        assert message.tenant_id == tenant_id
        assert message.session_id == session_id
        assert message.sender_id == staff_id
        assert message.content == content
        assert message.sender_type == SenderType.STAFF
        assert message.message_type == MessageType.FILE
        assert message.reply_to_id == reply_to_id
        assert message.attachments == attachments

    def test_create_system_message_class_method(self):
        """测试创建系统消息的类方法"""
        # Given
        tenant_id = uuid.uuid4()
        session_id = uuid.uuid4()
        content = "Session started"
        system_type = "session_start"

        # When
        message = Message.create_system_message(
            tenant_id=tenant_id,
            session_id=session_id,
            content=content,
            system_type=system_type,
        )

        # Then
        assert message.tenant_id == tenant_id
        assert message.session_id == session_id
        assert message.content == content
        assert message.sender_type == SenderType.SYSTEM
        assert message.message_type == MessageType.SYSTEM
        assert message.sender_id == system_type
        assert isinstance(message.timestamp, datetime)

    def test_string_representations(self):
        """测试字符串表示方法"""
        # Given
        message = Message(
            id=123,
            session_id=uuid.uuid4(),
            content="This is a very long message content that should be truncated in the representation",
            message_type=MessageType.TEXT,
            sender_type=SenderType.USER,
            sender_id="user123",
            timestamp=datetime.now(),
        )

        # When & Then
        repr_str = repr(message)
        str_str = str(message)

        # 基本检查，不依赖精确格式
        assert "Message" in repr_str
        assert "123" in repr_str  # ID应该在repr中
        assert isinstance(repr_str, str)

        assert "Message 123" in str_str
        assert "SenderType.USER:" in str_str  # sender_type是枚举格式
        assert len(str_str) < 100  # 字符串表示应该简洁

    def test_message_with_all_fields(self):
        """测试包含所有字段的消息创建"""
        # Given
        tenant_id = uuid.uuid4()
        session_id = uuid.uuid4()
        reply_to_id = 999

        # When
        message = Message(
            tenant_id=tenant_id,
            session_id=session_id,
            content="Complete message",
            message_type=MessageType.VIDEO,
            sender_type=SenderType.BOT,
            sender_id="bot123",
            platform_message_id="platform_999",
            reply_to_id=reply_to_id,
            timestamp=datetime.now(),
            attachments=[{"type": "video", "url": "video.mp4", "duration": 120}],
            extra_data={
                "ai_confidence": 0.95,
                "processing_time": 1.5,
                "metadata": {"emotion": "positive"},
            },
        )

        # Then
        assert message.tenant_id == tenant_id
        assert message.session_id == session_id
        assert message.content == "Complete message"
        assert message.message_type == MessageType.VIDEO
        assert message.sender_type == SenderType.BOT
        assert message.sender_id == "bot123"
        assert message.platform_message_id == "platform_999"
        assert message.reply_to_id == reply_to_id
        assert message.has_attachments is True
        assert message.get_metadata("ai_confidence") == 0.95
