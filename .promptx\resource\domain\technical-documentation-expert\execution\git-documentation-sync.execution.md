<execution>
  <constraint>
    ## Git和GitHub技术限制
    - **Git文件大小限制**：单个文件不超过100MB，仓库总大小建议不超过1GB
    - **GitHub API限制**：请求频率限制、文件数量限制、并发操作限制
    - **分支保护规则**：主分支可能有保护规则，需要通过PR合并
    - **权限约束**：用户可能没有直接推送到主分支的权限
    - **网络连接依赖**：需要稳定的网络连接进行同步操作
  </constraint>

  <rule>
    ## Git文档同步强制规则
    - **提交信息规范**：使用约定式提交格式，如"docs: 更新README文档"
    - **分支命名规范**：文档更新使用"docs/"前缀，如"docs/update-readme"
    - **文件组织规则**：文档文件必须放在合适的目录结构中
    - **同步完整性**：确保本地和远程仓库的文档版本一致
    - **备份安全性**：重要变更前必须创建备份或标签
  </rule>

  <guideline>
    ## Git文档同步指导原则
    - **渐进式提交**：将大的文档更新拆分为多个逻辑提交
    - **清晰的提交历史**：每个提交都有明确的目的和描述
    - **协作友好**：考虑团队成员的工作流程和习惯
    - **自动化优先**：尽可能使用自动化工具减少手动操作
    - **质量保证**：提交前进行基本的格式和链接检查
  </guideline>

  <process>
    ## Git文档同步执行流程

    ### Phase 1: 环境准备和状态检查 (5分钟)
    1. **Git环境检查**
       ```bash
       # 检查Git配置
       git config --list | grep user
       
       # 检查当前分支和状态
       git status
       git branch -a
       
       # 检查远程仓库连接
       git remote -v
       ```

    2. **工作区清理**
       ```bash
       # 查看未跟踪的文件
       git status --porcelain
       
       # 暂存当前工作（如需要）
       git stash push -m "临时保存：文档更新前的工作状态"
       
       # 确保工作区干净
       git status
       ```

    3. **同步最新代码**
       ```bash
       # 切换到主分支
       git checkout main
       
       # 拉取最新代码
       git pull origin main
       
       # 检查是否有冲突
       git status
       ```

    ### Phase 2: 分支管理和文档更新 (10分钟)
    1. **创建文档更新分支**
       ```bash
       # 创建并切换到文档分支
       git checkout -b docs/update-project-documentation
       
       # 确认分支创建成功
       git branch --show-current
       ```

    2. **文档文件更新**
       - 按照documentation-workflow执行文档更新
       - 确保所有更改都在正确的文件中
       - 验证Markdown格式和链接有效性

    3. **文档质量检查**
       ```bash
       # 检查文档格式（如果有工具）
       markdownlint README.md docs/
       
       # 检查链接有效性（如果有工具）
       markdown-link-check README.md
       
       # 预览文档效果
       # 可以使用VS Code预览或其他Markdown预览工具
       ```

    ### Phase 3: 提交和推送 (5分钟)
    1. **分阶段提交**
       ```bash
       # 查看所有更改
       git diff --name-only
       
       # 分别添加不同类型的文件
       git add README.md
       git commit -m "docs: 更新项目README主要内容和结构"
       
       git add docs/
       git commit -m "docs: 补充详细技术文档和API说明"
       
       # 如果有配置文件更改
       git add .github/
       git commit -m "docs: 添加GitHub模板和工作流配置"
       ```

    2. **推送到远程仓库**
       ```bash
       # 推送分支到远程
       git push -u origin docs/update-project-documentation
       
       # 检查推送结果
       git log --oneline -n 5
       ```

    ### Phase 4: Pull Request创建和管理 (10分钟)
    1. **创建Pull Request**
       ```bash
       # 使用GitHub CLI创建PR（推荐）
       gh pr create \
         --title "📝 更新项目文档结构和内容" \
         --body "## 📋 更新内容

       ### ✨ 主要改进
       - 重新设计README结构，提升可读性
       - 补充详细的安装和使用指南
       - 添加API文档和示例代码
       - 优化项目介绍和特性说明

       ### 🔍 检查清单
       - [x] 所有链接有效
       - [x] 代码示例可执行
       - [x] Markdown格式规范
       - [x] 信息准确完整

       ### 📖 预览
       请查看更新后的文档效果，欢迎提供反馈意见。" \
         --assignee @me \
         --label documentation
       ```

    2. **PR状态监控**
       ```bash
       # 查看PR状态
       gh pr status
       
       # 查看PR详情
       gh pr view
       
       # 如果有CI检查，等待完成
       gh pr checks
       ```

    ### Phase 5: 合并和后续处理 (5分钟)
    1. **PR审查和合并**
       ```bash
       # 如果有权限直接合并
       gh pr merge --squash --delete-branch
       
       # 或者等待团队审查后合并
       # 监控PR状态直到合并完成
       ```

    2. **本地分支清理**
       ```bash
       # 切换回主分支
       git checkout main
       
       # 拉取最新的合并结果
       git pull origin main
       
       # 删除本地文档分支
       git branch -d docs/update-project-documentation
       
       # 清理远程跟踪分支
       git remote prune origin
       ```

    3. **验证同步结果**
       ```bash
       # 检查文档更新是否生效
       git log --oneline --grep="docs:" -n 5
       
       # 访问GitHub页面验证显示效果
       # 检查GitHub Pages更新（如果启用）
       ```

    ### Phase 6: 自动化配置（可选） (10分钟)
    1. **GitHub Actions配置**
       ```yaml
       # .github/workflows/docs.yml
       name: Documentation
       
       on:
         push:
           paths:
             - '**.md'
             - 'docs/**'
         pull_request:
           paths:
             - '**.md'
             - 'docs/**'
       
       jobs:
         docs-check:
           runs-on: ubuntu-latest
           steps:
             - uses: actions/checkout@v3
             - name: Lint Markdown
               uses: articulate/actions-markdownlint@v1
             - name: Check Links
               uses: gaurav-nelson/github-action-markdown-link-check@v1
       ```

    2. **Issue和PR模板**
       ```markdown
       <!-- .github/ISSUE_TEMPLATE/documentation.md -->
       ---
       name: 📝 文档改进
       about: 建议文档改进或报告文档问题
       title: '[DOCS] '
       labels: documentation
       ---

       ## 📋 问题描述
       <!-- 描述文档中的问题或改进建议 -->

       ## 💡 建议方案
       <!-- 提供具体的改进建议 -->

       ## 📍 相关位置
       <!-- 指出具体的文件或章节 -->
       ```

    3. **自动化部署设置**
       ```bash
       # 如果使用GitHub Pages
       # 在仓库设置中启用Pages
       # 选择source分支和目录
       
       # 或者配置自动部署到其他平台
       # 如Netlify、Vercel等
       ```
  </process>

  <criteria>
    ## Git文档同步质量标准

    ### 版本控制质量
    - ✅ 提交信息清晰规范：遵循约定式提交格式
    - ✅ 提交粒度合理：每个提交有明确的逻辑边界
    - ✅ 分支管理规范：使用适当的分支命名和策略
    - ✅ 合并历史清晰：避免不必要的合并提交

    ### 同步完整性
    - ✅ 文件完整性：所有文档文件都正确同步
    - ✅ 权限正确性：文件权限设置合理
    - ✅ 结构一致性：本地和远程目录结构一致
    - ✅ 内容准确性：同步后内容与预期一致

    ### 协作友好性
    - ✅ PR描述详细：包含更改说明和检查清单
    - ✅ 审查流程：遵循团队的代码审查流程
    - ✅ 冲突处理：妥善处理可能的合并冲突
    - ✅ 沟通及时：重要更改及时通知相关人员

    ### 自动化程度
    - ✅ CI集成：文档检查自动化
    - ✅ 部署自动化：文档自动发布
    - ✅ 质量检查：自动格式和链接检查
    - ✅ 通知机制：自动通知相关人员
  </criteria>
</execution> 