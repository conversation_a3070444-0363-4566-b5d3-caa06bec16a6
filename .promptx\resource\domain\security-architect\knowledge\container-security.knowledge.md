# 容器安全专业知识

## 🐳 Docker安全基础

### 1. 容器隔离机制
- **Namespace隔离**：PID、Network、Mount、UTS、IPC、User namespace
- **Control Groups (cgroups)**：资源限制、CPU、内存、I/O控制
- **Capabilities**：Linux能力集、特权分离、最小权限原则
- **Seccomp**：系统调用过滤、攻击面减少、沙箱机制

### 2. 镜像安全管理
- **基础镜像选择**：官方镜像、最小化发行版、安全更新
- **Dockerfile最佳实践**：多阶段构建、非root用户、最小化层
- **镜像扫描**：CVE检测、依赖分析、许可证合规
- **镜像签名验证**：Content Trust、Notary、供应链完整性

### 3. 运行时安全配置
- **安全上下文**：用户映射、文件系统权限、特权模式控制
- **资源限制**：内存限制、CPU限制、文件描述符限制
- **网络安全**：网络模式、端口映射、容器间通信控制
- **文件系统安全**：只读挂载、临时文件系统、敏感路径保护

## 🔒 容器运行时安全

### 1. 容器运行时对比
- **Docker Engine**：默认运行时、containerd集成、安全特性
- **containerd**：CNCF项目、轻量级、CRI兼容
- **CRI-O**：Kubernetes专用、OCI兼容、安全focus
- **Podman**：无守护进程、用户命名空间、根less容器

### 2. 高级安全特性
- **gVisor**：用户空间内核、系统调用拦截、强隔离
- **Kata Containers**：轻量级虚拟机、硬件级隔离
- **Firecracker**：微虚拟机、快速启动、安全隔离
- **User Namespace**：用户ID映射、特权分离、容器隔离

### 3. 安全策略执行
- **AppArmor**：强制访问控制、配置文件管理、应用限制
- **SELinux**：标签访问控制、上下文标记、策略执行
- **Seccomp-bpf**：系统调用过滤、BPF程序、细粒度控制
- **LSM (Linux Security Modules)**：内核安全框架、多重安全

## 🛡️ 容器镜像安全

### 1. 安全的Dockerfile编写
```dockerfile
# 使用最小化基础镜像
FROM alpine:3.18

# 创建非特权用户
RUN addgroup -g 1001 appgroup && \
    adduser -u 1001 -G appgroup -s /bin/sh -D appuser

# 安装必要依赖并清理
RUN apk add --no-cache ca-certificates && \
    rm -rf /var/cache/apk/*

# 设置工作目录
WORKDIR /app

# 复制应用文件
COPY --chown=appuser:appgroup app /app/

# 切换到非特权用户
USER appuser

# 暴露端口
EXPOSE 8080

# 设置只读根文件系统
VOLUME ["/tmp"]

# 运行应用
CMD ["./app"]
```

### 2. 镜像扫描工具
- **Trivy**：开源漏洞扫描器、多格式支持、CI/CD集成
- **Clair**：静态分析、API驱动、分层扫描
- **Anchore**：企业级、策略引擎、合规检查
- **Snyk**：开发者友好、IDE集成、修复建议

### 3. 镜像仓库安全
- **Harbor**：企业级仓库、RBAC、漏洞扫描集成
- **Docker Trusted Registry**：商业解决方案、企业级特性
- **AWS ECR**：云原生、IAM集成、自动扫描
- **访问控制**：认证、授权、网络策略、审计日志

## 🚨 威胁检测与防护

### 1. 常见容器威胁
- **容器逃逸**：内核漏洞、配置错误、特权提升
- **恶意镜像**：后门、恶意软件、供应链攻击
- **资源滥用**：挖矿软件、DoS攻击、资源耗尽
- **数据泄露**：敏感信息暴露、配置泄露、日志泄露

### 2. 运行时监控
- **Falco**：运行时安全监控、规则引擎、异常检测
- **Sysdig**：系统调用跟踪、性能监控、安全告警
- **Aqua Security**：全生命周期保护、运行时防护
- **Twistlock**：企业级平台、合规管理、威胁情报

### 3. 异常行为检测
```yaml
# Falco规则示例
- rule: Shell in Container
  desc: Notice shell activity within a container
  condition: >
    spawned_process and container and
    shell_procs and proc.tty != 0 and container_entrypoint
  output: >
    Shell spawned in container (user=%user.name shell=%proc.name 
    parent=%proc.pname cmdline=%proc.cmdline container=%container.id)
  priority: WARNING

- rule: Write below root
  desc: Write below root directory
  condition: >
    open_write and container and
    fd.name startswith / and 
    not fd.name startswith /proc and
    not fd.name startswith /dev and
    not fd.name startswith /tmp
  output: >
    Write below root (user=%user.name command=%proc.cmdline 
    file=%fd.name container=%container.id)
  priority: ERROR
```

## 🔧 安全工具与技术

### 1. 静态分析工具
- **Dockerfile Linter**：hadolint、dockerfile_lint、最佳实践检查
- **依赖分析**：dependency-check、safety、已知漏洞检测
- **秘密扫描**：truffleHog、git-secrets、敏感信息检测
- **许可证检查**：FOSSA、BlackDuck、开源合规

### 2. 动态安全测试
- **Runtime Testing**：容器行为分析、异常检测
- **Penetration Testing**：容器渗透测试、漏洞验证
- **Fuzzing**：输入模糊测试、崩溃检测
- **Interactive Testing**：IAST、运行时漏洞检测

### 3. 合规与基准测试
- **CIS Docker Benchmark**：配置基准、安全评估
- **NIST Container Security**：框架指导、最佳实践
- **Docker Bench Security**：自动化检查、合规报告
- **OpenSCAP**：安全合规、配置管理

## 📋 安全配置实践

### 1. Docker守护进程安全
```json
// /etc/docker/daemon.json
{
  "icc": false,
  "userland-proxy": false,
  "no-new-privileges": true,
  "seccomp-profile": "/etc/docker/seccomp.json",
  "userns-remap": "default",
  "log-driver": "syslog",
  "log-opts": {
    "syslog-address": "tcp://localhost:514"
  },
  "storage-driver": "overlay2",
  "storage-opts": [
    "overlay2.override_kernel_check=true"
  ]
}
```

### 2. 安全运行参数
```bash
# 安全容器启动示例
docker run -d \
  --name secure-app \
  --user 1001:1001 \
  --read-only \
  --tmpfs /tmp:rw,nosuid,nodev,noexec,relatime,size=100m \
  --cap-drop ALL \
  --cap-add CHOWN \
  --cap-add SETUID \
  --cap-add SETGID \
  --security-opt=no-new-privileges:true \
  --security-opt=seccomp:unconfined \
  --security-opt=apparmor:docker-default \
  --ulimit nproc=1024:2048 \
  --ulimit nofile=1024:2048 \
  --memory=256m \
  --memory-swap=256m \
  --cpu-shares=512 \
  --pids-limit=100 \
  --restart=unless-stopped \
  myapp:latest
```

### 3. 网络安全配置
```bash
# 创建自定义网络
docker network create \
  --driver bridge \
  --subnet=**********/16 \
  --ip-range=************/20 \
  --gateway=********** \
  --opt com.docker.network.bridge.enable_icc=false \
  --opt com.docker.network.bridge.enable_ip_masquerade=true \
  secure-network

# 使用网络别名
docker run -d \
  --name web-server \
  --network secure-network \
  --network-alias web \
  --publish 8080:80 \
  nginx:alpine
```

## 🔐 密钥和敏感数据管理

### 1. Docker Secrets
```bash
# 创建secret
echo "mysecretpassword" | docker secret create db_password -

# 在服务中使用secret
docker service create \
  --name myservice \
  --secret db_password \
  --env DB_PASSWORD_FILE=/run/secrets/db_password \
  myapp:latest
```

### 2. 外部密钥管理集成
- **HashiCorp Vault**：动态秘密、细粒度访问控制
- **AWS Secrets Manager**：自动轮换、IAM集成
- **Azure Key Vault**：硬件安全模块、证书管理
- **Kubernetes Secrets**：集群内秘密管理、自动挂载

### 3. 最佳实践
- **避免硬编码**：环境变量、配置文件、外部存储
- **最小权限**：按需访问、定期轮换、审计跟踪
- **加密传输**：TLS/SSL、服务间通信、数据传输
- **安全存储**：加密存储、访问控制、备份安全

## 🛠️ DevSecOps集成

### 1. CI/CD管道安全
```yaml
# GitLab CI示例
stages:
  - build
  - security-scan
  - deploy

security_scan:
  stage: security-scan
  script:
    - trivy image --exit-code 1 --severity HIGH,CRITICAL $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - hadolint Dockerfile
    - docker run --rm -v "$PWD":/src returntocorp/semgrep --config=auto /src
  only:
    - master
    - merge_requests
```

### 2. 策略即代码
- **Open Policy Agent (OPA)**：通用策略引擎、Rego语言
- **Conftest**：配置测试、策略验证、CI/CD集成
- **Gatekeeper**：Kubernetes准入控制、策略执行
- **Falco Rules**：运行时策略、异常检测、告警管理

### 3. 自动化合规
- **合规扫描**：定期检查、自动报告、趋势分析
- **修复建议**：自动生成、优先级排序、影响评估
- **持续监控**：实时检测、异常告警、事件响应
- **文档生成**：合规报告、审计证据、管理dashboard 