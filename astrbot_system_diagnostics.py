#!/usr/bin/env python3
"""
🔍 AstrBot SaaS平台系统诊断工具
专业DevOps诊断工具，用于分析AstrBot SaaS平台和AstrBot核心的连接问题

功能：
- 端口状态检查
- 服务进程分析
- HTTP健康检查
- 配置文件验证
- 数据库连接测试
- 日志分析
- 问题诊断和修复建议
"""

import asyncio
import json
import os
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

import httpx
import psutil


class AstrBotSystemDiagnostics:
    """AstrBot系统诊断器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.saas_platform_dir = self.project_root / "saas-platform"
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "diagnostics": {},
            "issues": [],
            "recommendations": []
        }
        self.issues = []
        
        # 关键端口配置
        self.ports = {
            "saas_platform": 8000,
            "astrbot_web": 6185,
            "astrbot_admin": 6195,
            "astrbot_api": 6199,
            "postgresql": 5432,
            "redis": 6379
        }
        
        # HTTP客户端
        self.http_client = httpx.AsyncClient(timeout=10.0)
    
    async def run_full_diagnostics(self) -> Dict[str, Any]:
        """运行完整的系统诊断"""
        print("🔍 AstrBot SaaS平台系统诊断开始")
        print("=" * 60)
        
        try:
            # 1. 端口状态检查
            await self.check_port_status()
            
            # 2. 进程状态检查
            await self.check_process_status()
            
            # 3. HTTP健康检查
            await self.check_http_endpoints()
            
            # 4. 配置文件验证
            await self.check_configuration_files()
            
            # 5. 数据库连接测试
            await self.check_database_connection()
            
            # 6. 日志分析
            await self.analyze_logs()
            
            # 7. 生成诊断报告
            await self.generate_diagnostic_report()
            
            return self.results
            
        except Exception as e:
            print(f"❌ 诊断过程出错: {e}")
            self.results["error"] = str(e)
            return self.results
        finally:
            await self.http_client.aclose()
    
    async def check_port_status(self):
        """检查端口状态"""
        print("\n🔌 检查端口状态...")
        port_status = {}
        
        for service, port in self.ports.items():
            try:
                # 使用netstat检查端口
                if os.name == 'nt':  # Windows
                    cmd = f'netstat -an | findstr ":{port}"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                else:  # Linux/Mac
                    cmd = f'netstat -an | grep ":{port}"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                
                is_listening = "LISTENING" in result.stdout or "LISTEN" in result.stdout
                port_status[service] = {
                    "port": port,
                    "listening": is_listening,
                    "details": result.stdout.strip() if result.stdout else "No output"
                }
                
                status_icon = "✅" if is_listening else "❌"
                print(f"  {status_icon} {service} (:{port}) - {'监听中' if is_listening else '未监听'}")
                
            except Exception as e:
                port_status[service] = {
                    "port": port,
                    "listening": False,
                    "error": str(e)
                }
                print(f"  ❌ {service} (:{port}) - 检查失败: {e}")
        
        self.results["diagnostics"]["port_status"] = port_status
    
    async def check_process_status(self):
        """检查进程状态"""
        print("\n🔄 检查进程状态...")
        processes = []
        
        # 查找Python进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if any(keyword in cmdline.lower() for keyword in ['astrbot', 'uvicorn', 'fastapi']):
                        processes.append({
                            "pid": proc.info['pid'],
                            "name": proc.info['name'],
                            "cmdline": cmdline,
                            "status": proc.info['status']
                        })
                        print(f"  ✅ 发现相关进程: PID {proc.info['pid']} - {cmdline[:80]}...")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if not processes:
            print("  ⚠️ 未发现相关Python进程")
            self.issues.append("未发现运行中的AstrBot或SaaS平台进程")
        
        self.results["diagnostics"]["processes"] = processes
    
    async def check_http_endpoints(self):
        """检查HTTP端点健康状态"""
        print("\n🌐 检查HTTP端点...")
        endpoints = {
            "SaaS平台根路径": "http://localhost:8000/",
            "SaaS平台健康检查": "http://localhost:8000/health",
            "SaaS平台API文档": "http://localhost:8000/docs",
            "AstrBot Web界面": "http://localhost:6185/",
            "AstrBot管理接口": "http://localhost:6195/",
            "AstrBot API接口": "http://localhost:6199/"
        }
        
        endpoint_status = {}
        
        for name, url in endpoints.items():
            try:
                response = await self.http_client.get(url)
                endpoint_status[name] = {
                    "url": url,
                    "status_code": response.status_code,
                    "accessible": response.status_code < 400,
                    "response_time": response.elapsed.total_seconds()
                }
                
                status_icon = "✅" if response.status_code < 400 else "❌"
                print(f"  {status_icon} {name} - {response.status_code} ({response.elapsed.total_seconds():.2f}s)")
                
                if response.status_code >= 400:
                    self.issues.append(f"{name} 返回错误状态码: {response.status_code}")
                
            except Exception as e:
                endpoint_status[name] = {
                    "url": url,
                    "accessible": False,
                    "error": str(e)
                }
                print(f"  ❌ {name} - 连接失败: {e}")
                self.issues.append(f"{name} 无法访问: {str(e)}")
        
        self.results["diagnostics"]["endpoints"] = endpoint_status
    
    async def check_configuration_files(self):
        """检查配置文件"""
        print("\n⚙️ 检查配置文件...")
        config_files = {}
        
        # 检查SaaS平台配置
        saas_env_file = self.saas_platform_dir / ".env"
        if saas_env_file.exists():
            try:
                with open(saas_env_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                config_files["saas_platform_env"] = {
                    "path": str(saas_env_file),
                    "exists": True,
                    "size": len(content),
                    "has_database_url": "DATABASE_URL" in content,
                    "has_secret_key": "SECRET_KEY" in content
                }
                print(f"  ✅ SaaS平台 .env 文件存在 ({len(content)} 字符)")
            except Exception as e:
                config_files["saas_platform_env"] = {
                    "path": str(saas_env_file),
                    "exists": True,
                    "error": str(e)
                }
                print(f"  ❌ SaaS平台 .env 文件读取失败: {e}")
        else:
            config_files["saas_platform_env"] = {
                "path": str(saas_env_file),
                "exists": False
            }
            print(f"  ❌ SaaS平台 .env 文件不存在")
            self.issues.append("SaaS平台 .env 配置文件缺失")
        
        # 检查AstrBot配置
        astrbot_config_paths = [
            self.project_root / "config.json",
            self.project_root / "astrbot" / "config.json",
            self.project_root / ".env"
        ]
        
        for config_path in astrbot_config_paths:
            if config_path.exists():
                config_files[f"astrbot_config_{config_path.name}"] = {
                    "path": str(config_path),
                    "exists": True
                }
                print(f"  ✅ AstrBot配置文件存在: {config_path.name}")
                break
        else:
            print(f"  ⚠️ 未找到AstrBot配置文件")
        
        self.results["diagnostics"]["configuration"] = config_files
    
    async def check_database_connection(self):
        """检查数据库连接"""
        print("\n🗄️ 检查数据库连接...")
        
        try:
            # 尝试导入并测试数据库连接
            sys.path.append(str(self.saas_platform_dir))
            
            # 设置环境变量
            env_file = self.saas_platform_dir / ".env"
            if env_file.exists():
                with open(env_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if '=' in line and not line.strip().startswith('#'):
                            key, value = line.strip().split('=', 1)
                            os.environ[key] = value
            
            # 测试数据库连接
            from app.core.database import engine
            
            async with engine.begin() as conn:
                result = await conn.execute("SELECT 1")
                row = result.fetchone()
                
            self.results["diagnostics"]["database"] = {
                "connection": "successful",
                "test_query": "passed"
            }
            print("  ✅ 数据库连接成功")
            
        except Exception as e:
            self.results["diagnostics"]["database"] = {
                "connection": "failed",
                "error": str(e)
            }
            print(f"  ❌ 数据库连接失败: {e}")
            self.issues.append(f"数据库连接问题: {str(e)}")
    
    async def analyze_logs(self):
        """分析日志文件"""
        print("\n📋 分析日志文件...")
        
        log_analysis = {}
        
        # 查找可能的日志文件
        log_patterns = ["*.log", "logs/*.log", "*.out", "*.err"]
        log_files = []
        
        for pattern in log_patterns:
            log_files.extend(self.project_root.glob(pattern))
            log_files.extend(self.saas_platform_dir.glob(pattern))
        
        if log_files:
            for log_file in log_files[:5]:  # 限制检查前5个日志文件
                try:
                    with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()[-2000:]  # 读取最后2000字符
                    
                    error_count = content.lower().count('error')
                    warning_count = content.lower().count('warning')
                    
                    log_analysis[str(log_file)] = {
                        "size": log_file.stat().st_size,
                        "error_count": error_count,
                        "warning_count": warning_count,
                        "recent_content": content[-500:] if content else ""
                    }
                    
                    print(f"  📄 {log_file.name}: {error_count} 错误, {warning_count} 警告")
                    
                except Exception as e:
                    log_analysis[str(log_file)] = {"error": str(e)}
        else:
            print("  ℹ️ 未找到日志文件")
        
        self.results["diagnostics"]["logs"] = log_analysis
    
    async def generate_diagnostic_report(self):
        """生成诊断报告"""
        print("\n📊 生成诊断报告...")
        
        # 分析问题严重程度
        critical_issues = []
        warnings = []
        
        # 检查关键服务状态
        port_status = self.results["diagnostics"].get("port_status", {})
        if not port_status.get("astrbot_admin", {}).get("listening", False):
            critical_issues.append("AstrBot管理接口(6195)未运行 - 这是502错误的主要原因")
        if not port_status.get("astrbot_api", {}).get("listening", False):
            critical_issues.append("AstrBot API接口(6199)未运行 - 这是502错误的主要原因")
        if not port_status.get("saas_platform", {}).get("listening", False):
            critical_issues.append("SaaS平台(8000)未运行")
        
        # 生成修复建议
        recommendations = []
        if critical_issues:
            recommendations.append("1. 检查AstrBot服务是否正确启动")
            recommendations.append("2. 验证AstrBot配置文件是否正确")
            recommendations.append("3. 检查端口冲突和防火墙设置")
            recommendations.append("4. 查看AstrBot启动日志获取详细错误信息")
        
        if not self.results["diagnostics"].get("database", {}).get("connection") == "successful":
            recommendations.append("5. 修复数据库连接问题")
        
        self.results["critical_issues"] = critical_issues
        self.results["warnings"] = warnings
        self.results["recommendations"] = recommendations
        
        # 保存诊断报告
        report_file = self.project_root / "diagnostic_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📋 诊断报告已保存到: {report_file}")
        print(f"🔍 发现 {len(critical_issues)} 个关键问题")
        print(f"⚠️ 发现 {len(warnings)} 个警告")
        print(f"💡 生成 {len(recommendations)} 条修复建议")


async def main():
    """主函数"""
    diagnostics = AstrBotSystemDiagnostics()
    results = await diagnostics.run_full_diagnostics()
    
    print("\n" + "=" * 60)
    print("🎯 诊断总结:")
    
    if results.get("critical_issues"):
        print("\n❌ 关键问题:")
        for issue in results["critical_issues"]:
            print(f"  • {issue}")
    
    if results.get("recommendations"):
        print("\n💡 修复建议:")
        for rec in results["recommendations"]:
            print(f"  {rec}")
    
    print(f"\n📊 详细报告: diagnostic_report.json")
    return results


if __name__ == "__main__":
    asyncio.run(main())
