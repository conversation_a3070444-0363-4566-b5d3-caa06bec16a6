[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "astrbot-saas-platform"
version = "0.1.0"
description = "AstrBot多租户智能客服SaaS平台"
readme = "README.md"
requires-python = ">=3.11"
license = {file = "LICENSE"}
authors = [
    {name = "AstrBot Team", email = "<EMAIL>"},
]
keywords = [
    "chatbot",
    "saas",
    "multi-tenant",
    "fastapi",
    "customer-service"
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
]

dependencies = [
    # FastAPI核心
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "gunicorn>=21.2.0",

    # 数据库相关
    "sqlalchemy[asyncio]>=2.0.20",
    "asyncpg>=0.29.0",
    "alembic>=1.12.0",

    # 认证和安全
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",

    # HTTP客户端和工具
    "httpx>=0.25.0",
    "pydantic>=2.4.0",
    "pydantic-settings>=2.0.0",

    # 日志和监控
    "structlog>=23.2.0",
    "sentry-sdk>=1.38.0",

    # 实用工具
    "python-dotenv>=1.0.0",
    "email-validator>=2.1.0",
    "celery>=5.3.0",
    "redis>=5.0.0",
]

[project.optional-dependencies]
dev = [
    # 开发工具
    "black>=23.9.0",
    "ruff>=0.1.0",
    "mypy>=1.6.0",
    "pre-commit>=3.5.0",

    # 测试工具
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.0",  # 用于测试API

    # 文档工具
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.0",
]

docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
]

[project.urls]
Homepage = "https://github.com/astrbot/saas-platform"
Documentation = "https://docs.astrbot.com"
Repository = "https://github.com/astrbot/saas-platform.git"
"Bug Tracker" = "https://github.com/astrbot/saas-platform/issues"

[project.scripts]
astrbot-saas = "app.main:app"

# =============================================================================
# 代码质量工具配置
# =============================================================================

[tool.black]
# Black代码格式化配置
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # 排除目录
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | alembic/versions
)/
'''

[tool.ruff]
# Ruff linter配置
line-length = 88
target-version = "py311"
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "N",   # pep8-naming
    "B",   # flake8-bugbear
    "A",   # flake8-builtins
    "C4",  # flake8-comprehensions
    "DJ",  # flake8-django
    "UP",  # pyupgrade
    "YTT", # flake8-2020
    "S",   # bandit (安全检查)
    "BLE", # flake8-blind-except
    "C90", # mccabe复杂度
    "T20", # flake8-print
]
ignore = [
    "S101",  # 允许assert语句 (测试中需要)
    "S311",  # 允许random模块 (非加密用途)
    "B008",  # 允许函数参数中的函数调用 (FastAPI Depends)
]
exclude = [
    "alembic/",
    "scripts/",
]

[tool.ruff.per-file-ignores]
# 测试文件允许更宽松的规则
"tests/**/*.py" = ["S101", "S106", "S108", "T20"]
# 配置文件允许星号导入
"app/core/config/__init__.py" = ["F401"]

[tool.ruff.mccabe]
# 复杂度检查
max-complexity = 10

[tool.isort]
# Import排序配置
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88
known_first_party = ["app"]
known_third_party = ["fastapi", "pydantic", "sqlalchemy", "alembic"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[tool.mypy]
# MyPy类型检查配置
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

# 插件配置
plugins = [
    "pydantic.mypy",
    "sqlalchemy.ext.mypy.plugin"
]

# 排除文件
exclude = [
    "alembic/",
    "scripts/",
]

# 第三方库类型忽略
[[tool.mypy.overrides]]
module = [
    "uvicorn.*",
    "gunicorn.*",
    "httpx.*",
]
ignore_missing_imports = true

[tool.pydantic-mypy]
# Pydantic MyPy插件配置
init_forbid_extra = true
init_typed = true
warn_required_dynamic_aliases = true
warn_untyped_fields = true

[tool.pytest.ini_options]
# pytest测试配置
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
    "--cov-fail-under=20",  # 临时降低覆盖率要求，专注于性能测试
]
testpaths = ["tests"]
markers = [
    "unit: 单元测试",
    "integration: 集成测试",
    "e2e: 端到端测试",
    "performance: 性能测试",  # 添加性能测试标记
    "slow: 慢速测试",
]
asyncio_mode = "auto"
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]
pythonpath = [
  "."
]

[tool.coverage.run]
# 代码覆盖率配置
source = ["app"]
omit = [
    "*/tests/*",
    "*/alembic/*",
    "*/scripts/*",
    "*/__init__.py",
]
branch = true

[tool.coverage.report]
# 覆盖率报告配置
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.bandit]
# Bandit安全检查配置
exclude_dirs = ["tests", "alembic", "scripts"]
skips = ["B101", "B601"]  # 跳过assert和shell=True检查

[tool.hatch.build.targets.wheel]
packages = ["app"]
