"""
LLM相关的Pydantic模型
定义LLM服务的请求和响应数据结构
"""

from enum import Enum
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, validator


class LLMRole(str, Enum):
    """LLM消息角色枚举"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    FUNCTION = "function"


class LLMMessage(BaseModel):
    """LLM消息模型"""
    role: LLMRole = Field(..., description="消息角色")
    content: str = Field(..., description="消息内容")
    name: Optional[str] = Field(None, description="消息发送者名称")
    function_call: Optional[Dict[str, Any]] = Field(None, description="函数调用信息")
    
    class Config:
        use_enum_values = True


class LLMProvider(str, Enum):
    """LLM提供商枚举"""
    OPENAI = "openai"
    DIFY = "dify"
    MOCK = "mock"


class LLMModelType(str, Enum):
    """LLM模型类型枚举"""
    GPT_3_5_TURBO = "gpt-3.5-turbo"
    GPT_4 = "gpt-4"
    GPT_4_TURBO = "gpt-4-turbo"
    GPT_4O = "gpt-4o"
    DIFY_CHAT = "dify-chat"
    MOCK_MODEL = "mock-model"


class LLMModelInfo(BaseModel):
    """LLM模型信息"""
    name: str = Field(..., description="模型名称")
    provider: LLMProvider = Field(..., description="提供商")
    max_tokens: int = Field(..., description="最大令牌数")
    cost_per_1k_tokens: float = Field(..., description="每1K令牌成本")
    supports_streaming: bool = Field(True, description="是否支持流式响应")
    supports_functions: bool = Field(False, description="是否支持函数调用")

    class Config:
        use_enum_values = True


class LLMRequest(BaseModel):
    """LLM请求模型"""
    messages: List[LLMMessage] = Field(..., description="消息列表")
    model: str = Field(..., description="模型名称")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="温度参数")
    max_tokens: Optional[int] = Field(None, gt=0, description="最大令牌数")
    top_p: Optional[float] = Field(1.0, ge=0.0, le=1.0, description="Top-p参数")
    frequency_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="频率惩罚")
    presence_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="存在惩罚")
    stop: Optional[Union[str, List[str]]] = Field(None, description="停止序列")
    stream: Optional[bool] = Field(False, description="是否流式响应")
    
    @validator("messages")
    def validate_messages(cls, v):
        """验证消息列表"""
        if not v:
            raise ValueError("消息列表不能为空")
        return v


class LLMUsage(BaseModel):
    """LLM使用统计模型"""
    prompt_tokens: int = Field(..., description="提示令牌数")
    completion_tokens: int = Field(..., description="完成令牌数")
    total_tokens: int = Field(..., description="总令牌数")


class LLMChoice(BaseModel):
    """LLM选择模型"""
    index: int = Field(..., description="选择索引")
    message: LLMMessage = Field(..., description="响应消息")
    finish_reason: Optional[str] = Field(None, description="完成原因")


class LLMResponse(BaseModel):
    """LLM响应模型"""
    id: str = Field(..., description="响应ID")
    object: str = Field(..., description="对象类型")
    created: int = Field(..., description="创建时间戳")
    model: str = Field(..., description="模型名称")
    choices: List[LLMChoice] = Field(..., description="选择列表")
    usage: Optional[LLMUsage] = Field(None, description="使用统计")
    
    @property
    def content(self) -> str:
        """获取响应内容"""
        if self.choices:
            return self.choices[0].message.content
        return ""


class LLMStreamChunk(BaseModel):
    """LLM流式响应块模型"""
    id: str = Field(..., description="响应ID")
    object: str = Field(..., description="对象类型")
    created: int = Field(..., description="创建时间戳")
    model: str = Field(..., description="模型名称")
    choices: List[Dict[str, Any]] = Field(..., description="选择列表")


class LLMError(BaseModel):
    """LLM错误模型"""
    message: str = Field(..., description="错误消息")
    type: str = Field(..., description="错误类型")
    param: Optional[str] = Field(None, description="错误参数")
    code: Optional[str] = Field(None, description="错误代码")


class LLMProviderConfig(BaseModel):
    """LLM提供商配置模型"""
    provider: LLMProvider = Field(..., description="提供商类型")
    api_key: Optional[str] = Field(None, description="API密钥")
    api_base: Optional[str] = Field(None, description="API基础URL")
    organization: Optional[str] = Field(None, description="组织ID")
    timeout: Optional[int] = Field(30, description="超时时间（秒）")
    max_retries: Optional[int] = Field(3, description="最大重试次数")
    
    class Config:
        use_enum_values = True


class OpenAIConfig(LLMProviderConfig):
    """OpenAI配置模型"""
    provider: LLMProvider = LLMProvider.OPENAI
    api_base: str = Field("https://api.openai.com/v1", description="OpenAI API基础URL")


class DifyConfig(LLMProviderConfig):
    """Dify配置模型"""
    provider: LLMProvider = LLMProvider.DIFY
    app_id: Optional[str] = Field(None, description="Dify应用ID")
    conversation_id: Optional[str] = Field(None, description="会话ID")


class MockConfig(LLMProviderConfig):
    """Mock配置模型"""
    provider: LLMProvider = LLMProvider.MOCK
    response_delay: Optional[float] = Field(0.1, description="响应延迟（秒）")
    mock_response: Optional[str] = Field("这是一个模拟响应", description="模拟响应内容")


class LLMStats(BaseModel):
    """LLM统计模型"""
    total_requests: int = Field(0, description="总请求数")
    total_tokens: int = Field(0, description="总令牌数")
    total_cost: float = Field(0.0, description="总成本")
    average_response_time: float = Field(0.0, description="平均响应时间")
    success_rate: float = Field(0.0, description="成功率")
    error_count: int = Field(0, description="错误数量")


class LLMUsageStats(BaseModel):
    """LLM使用统计模型（别名）"""
    total_requests: int = Field(0, description="总请求数")
    total_tokens: int = Field(0, description="总令牌数")
    total_cost: float = Field(0.0, description="总成本")
    average_response_time: float = Field(0.0, description="平均响应时间")
    success_rate: float = Field(0.0, description="成功率")
    error_count: int = Field(0, description="错误数量")


class LLMHealthCheck(BaseModel):
    """LLM健康检查模型"""
    provider: LLMProvider = Field(..., description="提供商类型")
    status: str = Field(..., description="状态")
    response_time: float = Field(..., description="响应时间")
    last_check: int = Field(..., description="最后检查时间戳")
    error_message: Optional[str] = Field(None, description="错误消息")
    
    class Config:
        use_enum_values = True


class LLMQuota(BaseModel):
    """LLM配额模型"""
    provider: LLMProvider = Field(..., description="提供商类型")
    total_quota: int = Field(..., description="总配额")
    used_quota: int = Field(..., description="已使用配额")
    remaining_quota: int = Field(..., description="剩余配额")
    reset_time: Optional[int] = Field(None, description="重置时间戳")
    
    class Config:
        use_enum_values = True
    
    @property
    def usage_percentage(self) -> float:
        """使用百分比"""
        if self.total_quota == 0:
            return 0.0
        return (self.used_quota / self.total_quota) * 100


class LLMConversation(BaseModel):
    """LLM会话模型"""
    id: str = Field(..., description="会话ID")
    messages: List[LLMMessage] = Field(default_factory=list, description="消息列表")
    created_at: int = Field(..., description="创建时间戳")
    updated_at: int = Field(..., description="更新时间戳")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    def add_message(self, message: LLMMessage) -> None:
        """添加消息"""
        self.messages.append(message)
    
    def get_last_message(self) -> Optional[LLMMessage]:
        """获取最后一条消息"""
        return self.messages[-1] if self.messages else None
    
    def get_messages_by_role(self, role: LLMRole) -> List[LLMMessage]:
        """根据角色获取消息"""
        return [msg for msg in self.messages if msg.role == role]


class LLMFunction(BaseModel):
    """LLM函数模型"""
    name: str = Field(..., description="函数名称")
    description: str = Field(..., description="函数描述")
    parameters: Dict[str, Any] = Field(..., description="函数参数")
    
    class Config:
        schema_extra = {
            "example": {
                "name": "get_weather",
                "description": "获取指定城市的天气信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "city": {
                            "type": "string",
                            "description": "城市名称"
                        }
                    },
                    "required": ["city"]
                }
            }
        }


class LLMFunctionCall(BaseModel):
    """LLM函数调用模型"""
    name: str = Field(..., description="函数名称")
    arguments: str = Field(..., description="函数参数（JSON字符串）")


class LLMTool(BaseModel):
    """LLM工具模型"""
    type: str = Field("function", description="工具类型")
    function: LLMFunction = Field(..., description="函数定义")


class LLMRequestWithTools(LLMRequest):
    """带工具的LLM请求模型"""
    tools: Optional[List[LLMTool]] = Field(None, description="可用工具列表")
    tool_choice: Optional[Union[str, Dict[str, Any]]] = Field(None, description="工具选择策略")
