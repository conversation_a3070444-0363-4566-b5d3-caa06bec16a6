# k8s/security/network-policies/10-app-api-policy.yaml
# 为API服务定义详细的入站和出站流量规则

apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: astrbot-api-policy
  namespace: astrbot-saas
spec:
  # 应用于API Server的Pod
  podSelector:
    matchLabels:
      app: astrbot-saas-api
  
  policyTypes:
  - Ingress
  - Egress
  
  # 入站规则
  ingress:
  # 允许来自Ingress Controller的流量
  - from:
    - namespaceSelector:
        matchLabels:
          # Ingress Nginx的命名空间标签
          name: ingress-nginx
      podSelector:
        matchLabels:
          app.kubernetes.io/name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
      
  # 允许来自监控命名空间的Prometheus的流量  
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
      podSelector:
        matchLabels:
          app: prometheus
    ports:
    - protocol: TCP
      port: 8080 # metrics端口
      
  # 出站规则
  egress:
  # 允许访问数据库
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
      
  # 允许访问Redis
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
      
  # 允许访问外部LLM服务
  - to:
    - ipBlock:
        # 允许访问公网
        cidr: 0.0.0.0/0
        except:
          # 排除私有IP段，防止内网扫描
          - 10.0.0.0/8
          - **********/12
          - ***********/16
    ports:
    - protocol: TCP
      port: 443 # HTTPS端口 