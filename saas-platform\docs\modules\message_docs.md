# 📖 技术文档：消息模型 (message.py)

## 🎯 1. 模块概述

**功能**：定义`Message`数据模型，对应数据库中的`messages`表。

**核心职责**：
- **数据结构**：定义消息的核心字段，如`content`, `message_type`, `sender_type`。
- **关系**：定义与`Tenant`, `Session`模型的关系。
- **业务逻辑**：包含与消息相关的业务逻辑，如`is_from_user`属性。

## 🚀 2. 快速使用

### 2.1 创建消息

```python
from app.models.message import Message, MessageType, SenderType

new_message = Message(
    tenant_id=tenant.id,
    session_id=session.id,
    content="Hello, world!",
    message_type=MessageType.TEXT,
    sender_type=SenderType.USER,
    sender_id="user123",
    timestamp=datetime.utcnow(),
)
db.add(new_message)
await db.commit()
```

### 2.2 查询消息

```python
from sqlalchemy import select

stmt = select(Message).where(Message.session_id == session.id)
result = await db.execute(stmt)
messages = result.scalars().all()
```

## 🏗️ 3. 架构设计

### 3.1 关键字段

- **`id`**: `BigInteger` - 主键，支持大量消息
- **`tenant_id`**: `UUID` - 所属租户ID
- **`session_id`**: `UUID` - 所属会话ID
- **`content`**: `Text` - 消息内容
- **`message_type`**: `MessageType` - 消息类型
- **`sender_type`**: `SenderType` - 发送者类型
- **`sender_id`**: `str` - 发送者ID
- **`timestamp`**: `datetime` - 消息时间戳

### 3.2 关系

```mermaid
erDiagram
    MESSAGE }o--|| SESSION : "belongs to"
    MESSAGE }o--|| TENANT : "belongs to"
```

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_message_model.py`

## 💡 6. 维护与扩展

- **附件**：`attachments`字段可以用于存储图片、文件等附件信息。
- **消息状态**：可以添加`status`字段来表示消息的发送状态（如`sent`, `delivered`, `read`）。
- **富文本**：`content`字段可以支持Markdown或HTML等富文本格式。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 