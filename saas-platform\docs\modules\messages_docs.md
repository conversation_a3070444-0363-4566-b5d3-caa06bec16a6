# 📖 技术文档：消息API (messages.py)

## 🎯 1. 模块概述

**功能**：提供消息的发送、查询和管理功能。

**核心职责**：
- **消息发送**：提供发送消息的端点。
- **消息查询**：提供获取会话消息列表的端点。
- **消息搜索**：提供消息内容的全文搜索功能。
- **消息管理**：提供更新和删除消息的端点。

## 🚀 2. 快速使用

### 2.1 依赖注入

```python
from app.services.message_service import MessageService, get_message_service

@router.post("/")
async def send_message(
    # ...
    message_service: MessageService = Depends(get_message_service),
):
    # ...
```

### 2.2 核心端点

- `POST /` - 发送消息
- `GET /` - 获取会话消息列表
- `GET /search` - 搜索消息
- `PUT /{message_id}` - 更新消息
- `DELETE /{message_id}` - 删除消息

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    A[API Endpoints] --> B(MessageService)
    B --> C(SessionService)
    B --> D(Message Model)
```

### 3.2 数据流

**发送消息流程**：
1. **API接收**：接收发送消息的请求。
2. **服务调用**：调用`MessageService.store_message`。
3. **响应返回**：返回创建的消息。

## 🔧 4. API参考

| HTTP动词 | 端点 | 描述 |
|---|---|---|
| `POST` | `/` | 发送消息 |
| `GET` | `/` | 获取会话消息列表 |
| `GET`|`/search`|搜索消息|
|`PUT`|`/{message_id}`|更新消息|
|`DELETE`|`/{message_id}`|删除消息|
|`POST`|`/{message_id}/status`|更新消息状态|

## 🧪 5. 测试策略

- **集成测试**：`tests/integration/test_messages_api.py`

## 💡 6. 维护与扩展

- **消息编辑记录**：可以添加消息编辑历史记录功能。
- **消息撤回**：可以添加消息撤回功能。
- **附件管理**：可以添加独立的附件上传和管理功能。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 