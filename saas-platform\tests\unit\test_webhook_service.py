"""
Webhook 服务单元测试
"""
import uuid
import hmac
import json
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from app.services.webhook_service import WebhookService, SecurityError
from app.models.message import SenderType


@pytest.fixture
def mock_db_session():
    """创建一个 mock 的异步数据库会话"""
    return AsyncMock()


@pytest.fixture
def mock_session_service():
    """创建一个 mock 的 SessionService"""
    return AsyncMock()


@pytest.fixture
def mock_message_service():
    """创建一个 mock 的 MessageService"""
    return AsyncMock()


@pytest.fixture
def webhook_service(
    mock_db_session, mock_session_service, mock_message_service
):
    """创建一个带有 mock 依赖的 WebhookService 实例"""
    with patch(
        "app.services.webhook_service.SessionService", return_value=mock_session_service
    ), patch(
        "app.services.webhook_service.MessageService", return_value=mock_message_service
    ):
        service = WebhookService(mock_db_session)
        return service


class TestWebhookProcessing:
    """测试 Webhook 的核心处理和分发逻辑"""

    TENANT_ID = uuid.uuid4()

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "event_type, handler_name",
        [
            ("message.received", "_handle_message_received"),
            ("message.sent", "_handle_message_sent"),
            ("session.created", "_handle_session_created"),
            ("session.closed", "_handle_session_closed"),
            ("unknown_event", "_handle_unknown_event"),
            (None, "_handle_unknown_event"),
        ],
    )
    async def test_process_message_webhook_routing(
        self, webhook_service, event_type, handler_name
    ):
        """测试 process_message_webhook 能根据 event_type 正确路由到处理函数"""
        # Arrange
        webhook_data = {"event_type": event_type, "data": {"key": "value"}}
        handler_mock = AsyncMock(return_value={"status": "handled"})

        with patch.object(webhook_service, handler_name, handler_mock):
            # Act
            result = await webhook_service.process_message_webhook(
                self.TENANT_ID, webhook_data
            )

            # Assert
            handler_mock.assert_awaited_once_with(
                self.TENANT_ID, webhook_data.get("data", {})
            )
            assert result["status"] == "success"
            assert result["result"] == {"status": "handled"}

    @pytest.mark.asyncio
    async def test_verify_webhook_signature_success(self, webhook_service):
        """测试当签名正确时，验签成功"""
        # Arrange
        secret = "my-super-secret-key"
        body_dict = {"key": "value"}
        # 必须使用与服务端完全一致的方式生成签名
        body_bytes = json.dumps(body_dict, separators=(",", ":")).encode("utf-8")
        expected_signature = hmac.new(
            secret.encode("utf-8"), body_bytes, "sha256"
        ).hexdigest()

        with patch.object(
            webhook_service, "_get_tenant_webhook_secret", return_value=secret
        ):
            # Act & Assert
            try:
                await webhook_service._verify_webhook_signature(
                    self.TENANT_ID, body_dict, expected_signature
                )
            except SecurityError:
                pytest.fail("验签成功时不应抛出 SecurityError")

    @pytest.mark.asyncio
    async def test_verify_webhook_signature_failure(self, webhook_service):
        """测试当签名错误时，应抛出 SecurityError"""
        # Arrange
        secret = "my-super-secret-key"
        body = '{"key": "value"}'
        wrong_signature = "wrong_signature"

        with patch.object(
            webhook_service, "_get_tenant_webhook_secret", return_value=secret
        ):
            # Act & Assert
            with pytest.raises(SecurityError) as exc_info:
                await webhook_service._verify_webhook_signature(
                    self.TENANT_ID, json.loads(body), wrong_signature
                )
            assert "Invalid signature" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_verify_webhook_no_secret_failure(self, webhook_service):
        """测试当租户没有配置 secret 时，应抛出 SecurityError"""
        # Arrange
        body = '{"key": "value"}'
        signature = "any_signature"

        with patch.object(
            webhook_service, "_get_tenant_webhook_secret", return_value=None
        ):
            # Act & Assert
            with pytest.raises(SecurityError) as exc_info:
                await webhook_service._verify_webhook_signature(
                    self.TENANT_ID, json.loads(body), signature
                )
            assert "secret not configured" in str(exc_info.value)


class TestEventHandlers:
    """测试具体的事件处理函数"""

    TENANT_ID = uuid.uuid4()
    SESSION_ID = uuid.uuid4()
    USER_ID = "test-user"

    @pytest.mark.asyncio
    async def test_handle_message_received_success(
        self, webhook_service, mock_session_service, mock_message_service
    ):
        """测试 _handle_message_received 成功处理消息"""
        # Arrange
        event_data = {
            "session_id": str(self.SESSION_ID),
            "user_id": self.USER_ID,
            "content": "Hello, world!",
            "platform": "test_platform",
        }
        mock_session = MagicMock()
        mock_session.id = self.SESSION_ID
        mock_session_service.create_or_get_session.return_value = mock_session

        mock_message = MagicMock()
        mock_message.id = uuid.uuid4()
        mock_message.session_id = self.SESSION_ID
        mock_message.created_at.isoformat.return_value = "2023-01-01T12:00:00"
        mock_message_service.store_message.return_value = mock_message

        # Act
        result = await webhook_service._handle_message_received(
            self.TENANT_ID, event_data
        )

        # Assert
        mock_session_service.create_or_get_session.assert_awaited_once()
        mock_message_service.store_message.assert_awaited_once()

        # 验证传递给 store_message 的参数
        call_args = mock_message_service.store_message.call_args
        message_create_arg = call_args[0][0]
        assert message_create_arg.session_id == self.SESSION_ID
        assert message_create_arg.content == "Hello, world!"
        assert message_create_arg.sender_id == self.USER_ID

        assert result["message_id"] == str(mock_message.id)
        assert result["session_id"] == str(self.SESSION_ID)

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "missing_field", ["session_id", "user_id", "content"]
    )
    async def test_handle_message_received_missing_data(
        self, webhook_service, missing_field
    ):
        """测试当缺少必要字段时 _handle_message_received 抛出 ValueError"""
        # Arrange
        event_data = {
            "session_id": str(self.SESSION_ID),
            "user_id": self.USER_ID,
            "content": "Hello",
        }
        del event_data[missing_field]

        # Act & Assert
        with pytest.raises(ValueError) as exc_info:
            await webhook_service._handle_message_received(self.TENANT_ID, event_data)
        assert "Missing required fields" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_handle_message_sent_success(
        self, webhook_service, mock_message_service
    ):
        """测试 _handle_message_sent 成功处理消息"""
        # Arrange
        agent_id = "agent-007"
        event_data = {
            "session_id": str(self.SESSION_ID),
            "user_id": agent_id,  # 在这里 user_id 代表 agent_id
            "content": "This is an agent response.",
        }
        mock_message = MagicMock()
        mock_message.id = uuid.uuid4()
        mock_message.session_id = self.SESSION_ID
        mock_message.created_at.isoformat.return_value = "2023-01-01T12:01:00"
        mock_message_service.store_message.return_value = mock_message

        # Act
        result = await webhook_service._handle_message_sent(self.TENANT_ID, event_data)

        # Assert
        mock_message_service.store_message.assert_awaited_once()
        call_args = mock_message_service.store_message.call_args
        message_create_arg = call_args[0][0]
        assert message_create_arg.session_id == self.SESSION_ID
        assert message_create_arg.sender_type == SenderType.STAFF  # 关键验证
        assert message_create_arg.sender_id == agent_id

        assert result["message_id"] == str(mock_message.id)

    @pytest.mark.asyncio
    async def test_handle_session_closed_success(
        self, webhook_service, mock_session_service
    ):
        """测试 _handle_session_closed 成功处理关闭事件"""
        # Arrange
        event_data = {"session_id": str(self.SESSION_ID), "reason": "user_left"}

        # Act
        result = await webhook_service._handle_session_closed(
            self.TENANT_ID, event_data
        )

        # Assert
        mock_session_service.update_session_status.assert_awaited_once()
        call_args = mock_session_service.update_session_status.call_args
        session_id_arg = call_args[0][0]
        status_update_arg = call_args[0][2]

        assert session_id_arg == self.SESSION_ID
        assert status_update_arg.status == "closed"
        assert status_update_arg.reason == "user_left"

        assert result["session_id"] == str(self.SESSION_ID)
        assert result["status"] == "closed"

    @pytest.mark.asyncio
    async def test_handle_session_closed_missing_id(self, webhook_service):
        """测试 _handle_session_closed 在缺少 session_id 时失败"""
        # Arrange
        event_data = {"reason": "timeout"}

        # Act & Assert
        with pytest.raises(ValueError) as exc_info:
            await webhook_service._handle_session_closed(self.TENANT_ID, event_data)
        assert "Missing or invalid session_id" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_handle_message_sent_missing_fields(self, webhook_service):
        """测试当缺少必要字段时，_handle_message_sent 抛出 ValueError"""
        # Arrange
        event_data = {
            "session_id": str(self.SESSION_ID),
            "user_id": self.USER_ID,
            "content": "This is an agent response.",
        }
        del event_data["user_id"]

        # Act & Assert
        with pytest.raises(ValueError) as exc_info:
            await webhook_service._handle_message_sent(self.TENANT_ID, event_data)
        assert "Missing required fields" in str(exc_info.value) 