#!/usr/bin/env python3
"""
全面的API质量检查脚本
检查API端点的各个方面：文档、命名、类型注解、错误处理等
"""

import ast
from pathlib import Path
from typing import List, Dict, Any, Set
from collections import defaultdict


class APIQualityChecker:
    """API质量检查器"""

    def __init__(self):
        self.issues = defaultdict(list)
        self.total_endpoints = 0

    def check_api_file(self, file_path: Path) -> None:
        """检查单个API文件"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            tree = ast.parse(content)

            # 查找路由函数
            functions_found = 0
            routes_found = 0

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    functions_found += 1
                    # 检查是否是路由函数
                    if self._is_route_function(node):
                        routes_found += 1
                        self.total_endpoints += 1
                        self._check_endpoint_quality(node, file_path, content)

            print(f"  函数总数: {functions_found}, 路由函数: {routes_found}")

        except Exception as e:
            self.issues["file_errors"].append(f"无法分析文件 {file_path}: {e}")

    def _is_route_function(self, node: ast.FunctionDef) -> bool:
        """判断是否是路由函数"""
        for decorator in node.decorator_list:
            # 检查 @router.method(...) 格式
            if isinstance(decorator, ast.Call):
                if (
                    hasattr(decorator.func, "attr")
                    and hasattr(decorator.func.value, "id")
                    and decorator.func.value.id == "router"
                    and decorator.func.attr in ["get", "post", "put", "patch", "delete"]
                ):
                    return True
            # 检查直接装饰器格式，如 @router.get (没有调用)
            elif isinstance(decorator, ast.Attribute):
                if (
                    hasattr(decorator.value, "id")
                    and decorator.value.id == "router"
                    and decorator.attr in ["get", "post", "put", "patch", "delete"]
                ):
                    return True
        return False

    def _check_endpoint_quality(
        self, node: ast.FunctionDef, file_path: Path, content: str
    ) -> None:
        """检查端点质量"""
        func_name = node.name
        file_rel = str(file_path.relative_to(Path("app")))

        # 1. 检查函数命名
        self._check_function_naming(func_name, file_rel, node.lineno)

        # 2. 检查参数类型注解
        self._check_parameter_annotations(node, file_rel)

        # 3. 检查返回类型注解
        self._check_return_annotation(node, file_rel)

        # 4. 检查错误处理
        self._check_error_handling(node, file_rel)

        # 5. 检查文档字符串质量
        self._check_docstring_quality(node, file_rel)

        # 6. 检查认证依赖
        self._check_auth_dependency(node, file_rel)

        # 7. 检查日志记录
        self._check_logging_practices(node, file_rel, content)

    def _check_function_naming(self, func_name: str, file_rel: str, line: int) -> None:
        """检查函数命名规范"""
        good_prefixes = [
            "get_",
            "post_",
            "put_",
            "patch_",
            "delete_",
            "create_",
            "update_",
            "list_",
            "search_",
            "generate_",
        ]

        if not any(func_name.startswith(prefix) for prefix in good_prefixes):
            self.issues["naming"].append(
                {
                    "file": file_rel,
                    "function": func_name,
                    "line": line,
                    "issue": f"函数名不符合RESTful命名规范: {func_name}",
                }
            )

    def _check_parameter_annotations(
        self, node: ast.FunctionDef, file_rel: str
    ) -> None:
        """检查参数类型注解"""
        for arg in node.args.args:
            if not arg.annotation and arg.arg not in ["self", "cls"]:
                # 跳过依赖注入参数（通常有默认值）
                if not any(default for default in node.args.defaults):
                    self.issues["type_annotations"].append(
                        {
                            "file": file_rel,
                            "function": node.name,
                            "parameter": arg.arg,
                            "line": node.lineno,
                            "issue": f"参数 {arg.arg} 缺少类型注解",
                        }
                    )

    def _check_return_annotation(self, node: ast.FunctionDef, file_rel: str) -> None:
        """检查返回类型注解"""
        if not node.returns:
            self.issues["return_annotations"].append(
                {
                    "file": file_rel,
                    "function": node.name,
                    "line": node.lineno,
                    "issue": "缺少返回类型注解",
                }
            )

    def _check_error_handling(self, node: ast.FunctionDef, file_rel: str) -> None:
        """检查错误处理"""
        has_try_except = False
        has_http_exception = False
        has_bare_except = False

        for child in ast.walk(node):
            if isinstance(child, ast.Try):
                has_try_except = True
                for handler in child.handlers:
                    if handler.type is None:
                        has_bare_except = True
            elif isinstance(child, ast.Raise):
                if hasattr(child.exc, "id") and child.exc.id == "HTTPException":
                    has_http_exception = True

        if not has_try_except:
            self.issues["error_handling"].append(
                {
                    "file": file_rel,
                    "function": node.name,
                    "line": node.lineno,
                    "issue": "缺少try-except错误处理",
                }
            )

        if has_bare_except:
            self.issues["error_handling"].append(
                {
                    "file": file_rel,
                    "function": node.name,
                    "line": node.lineno,
                    "issue": "使用了裸露的except语句",
                }
            )

    def _check_docstring_quality(self, node: ast.FunctionDef, file_rel: str) -> None:
        """检查文档字符串质量"""
        docstring = ast.get_docstring(node)
        if docstring:
            # 检查文档字符串长度和内容
            if len(docstring.strip()) < 20:
                self.issues["docstring_quality"].append(
                    {
                        "file": file_rel,
                        "function": node.name,
                        "line": node.lineno,
                        "issue": "文档字符串过于简短",
                    }
                )

            # 检查是否包含参数说明
            if "- **" not in docstring and len(node.args.args) > 1:
                self.issues["docstring_quality"].append(
                    {
                        "file": file_rel,
                        "function": node.name,
                        "line": node.lineno,
                        "issue": "文档字符串缺少参数说明",
                    }
                )

    def _check_auth_dependency(self, node: ast.FunctionDef, file_rel: str) -> None:
        """检查认证依赖"""
        has_auth = False
        auth_patterns = [
            "get_current_tenant",
            "get_tenant_from_auth",
            "get_current_user",
        ]

        # 检查依赖注入参数
        for default in node.args.defaults:
            if isinstance(default, ast.Call):
                if hasattr(default.func, "id") and default.func.id == "Depends":
                    if hasattr(default.args[0], "id") and any(
                        pattern in default.args[0].id for pattern in auth_patterns
                    ):
                        has_auth = True
                        break

        # 公共端点可以跳过
        public_endpoints = ["health_check", "root", "health", "create_tenant"]
        if not has_auth and node.name not in public_endpoints:
            self.issues["security"].append(
                {
                    "file": file_rel,
                    "function": node.name,
                    "line": node.lineno,
                    "issue": "可能缺少认证依赖",
                }
            )

    def _check_logging_practices(
        self, node: ast.FunctionDef, file_rel: str, content: str
    ) -> None:
        """检查日志记录实践"""
        func_start = node.lineno
        func_end = node.end_lineno or func_start + 50

        lines = content.split("\n")[func_start - 1 : func_end]
        func_content = "\n".join(lines)

        # 检查是否有日志记录
        has_info_log = "logger.info(" in func_content
        has_error_log = "logger.error(" in func_content

        if not has_info_log:
            self.issues["logging"].append(
                {
                    "file": file_rel,
                    "function": node.name,
                    "line": node.lineno,
                    "issue": "缺少信息日志记录",
                }
            )

    def generate_report(self) -> Dict[str, Any]:
        """生成检查报告"""
        total_issues = sum(len(issues) for issues in self.issues.values())

        return {
            "summary": {
                "total_endpoints": self.total_endpoints,
                "total_issues": total_issues,
                "categories": {
                    category: len(issues) for category, issues in self.issues.items()
                },
            },
            "details": dict(self.issues),
        }


def main():
    """主函数"""
    print("🔍 开始全面API质量检查...")

    checker = APIQualityChecker()

    api_dir = Path("app/api/v1")
    if not api_dir.exists():
        print(f"❌ API目录不存在: {api_dir}")
        return

    # 检查所有API文件
    for py_file in api_dir.glob("*.py"):
        if py_file.name == "__init__.py":
            continue

        print(f"🔍 检查文件: {py_file}")
        checker.check_api_file(py_file)

    # 生成报告
    report = checker.generate_report()

    print(f"\n📊 API质量检查报告")
    print("=" * 60)
    print(f"检查端点数: {report['summary']['total_endpoints']}")
    print(f"发现问题数: {report['summary']['total_issues']}")
    print()

    # 按类别显示问题
    categories = {
        "naming": "命名规范",
        "type_annotations": "类型注解",
        "return_annotations": "返回类型注解",
        "error_handling": "错误处理",
        "docstring_quality": "文档质量",
        "security": "安全认证",
        "logging": "日志记录",
    }

    for category, count in report["summary"]["categories"].items():
        if count > 0:
            category_name = categories.get(category, category)
            print(f"📋 {category_name}: {count}个问题")

            # 显示具体问题（限制显示数量）
            for issue in report["details"][category][:3]:  # 只显示前3个
                print(
                    f"  - {issue['file']}:{issue['line']} {issue['function']}() - {issue['issue']}"
                )

            if len(report["details"][category]) > 3:
                print(f"  ... 还有 {len(report['details'][category]) - 3} 个类似问题")
            print()

    if report["summary"]["total_issues"] == 0:
        print("🎉 所有API端点质量检查通过！")
    else:
        print(f"🎯 建议优化 {report['summary']['total_issues']} 个问题以提升API质量")


if __name__ == "__main__":
    main()
