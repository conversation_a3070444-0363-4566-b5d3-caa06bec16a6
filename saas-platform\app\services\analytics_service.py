"""
数据统计与分析服务

提供多维度的数据统计分析功能，支持：
- 会话统计分析
- 消息统计分析
- 实时监控数据
- 业务报表生成
"""

from datetime import datetime, timedelta
from typing import Any
from uuid import UUID

from sqlalchemy import and_, extract, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.message import Message, SenderType
from app.models.session import Session
from app.schemas.analytics import (
    AnalyticsFilter,
    MessageStatsResponse,
    RealtimeMetrics,
    SessionStatsResponse,
    TimeSeriesData,
    TrendAnalysis,
)
from app.utils.logging import get_logger
from app.core.database import get_database_url

# 配置日志
logger = get_logger(__name__)


class AnalyticsService:
    """数据分析服务类"""

    def __init__(self, db: AsyncSession) -> None:
        """
        初始化分析服务

        Args:
            db: 数据库会话
        """
        self.db = db

    async def get_session_stats(
        self, tenant_id: UUID, filters: AnalyticsFilter
    ) -> SessionStatsResponse:
        """
        获取会话统计数据

        Args:
            tenant_id: 租户ID
            filters: 过滤条件

        Returns:
            会话统计响应
        """
        try:
            # 构建基础查询条件
            conditions = [Session.tenant_id == tenant_id]
            if filters.start_date:
                conditions.append(Session.created_at >= filters.start_date)
            if filters.end_date:
                conditions.append(Session.created_at <= filters.end_date)
            if filters.status:
                conditions.append(Session.status.in_(filters.status))

            # 总会话数
            total_sessions_query = select(func.count(Session.id)).where(*conditions)
            total_sessions = (await self.db.execute(total_sessions_query)).scalar_one()

            # 状态分布统计
            status_stats_query = (
                select(Session.status, func.count(Session.id).label("count"))
                .where(Session.tenant_id == tenant_id)
                .group_by(Session.status)
            )
            if filters.start_date:
                status_stats_query = status_stats_query.where(Session.created_at >= filters.start_date)
            if filters.end_date:
                status_stats_query = status_stats_query.where(Session.created_at <= filters.end_date)
            
            status_stats_result = await self.db.execute(status_stats_query)
            status_stats = status_stats_result.all()


            # 平均会话时长
            # 检查实际使用的数据库引擎
            engine_name = str(self.db.bind.engine.url.drivername).lower()
            
            try:
                if "sqlite" in engine_name:
                    # SQLite使用JULIANDAY计算时间差(秒)
                    avg_duration_query = select(
                        func.avg(
                            func.cast(
                                (func.julianday(Session.updated_at) - func.julianday(Session.created_at)) * 86400,
                                Integer
                            )
                        )
                    ).where(
                        Session.tenant_id == tenant_id,
                        Session.status == "closed",
                    )
                else:
                    # PostgreSQL使用extract epoch
                    avg_duration_query = select(
                        func.avg(extract("epoch", Session.updated_at - Session.created_at))
                    ).where(
                        Session.tenant_id == tenant_id,
                        Session.status == "closed",
                    )
                    
                if filters.start_date:
                    avg_duration_query = avg_duration_query.where(Session.created_at >= filters.start_date)
                if filters.end_date:
                    avg_duration_query = avg_duration_query.where(Session.created_at <= filters.end_date)

                avg_duration = (await self.db.execute(avg_duration_query)).scalar()
                
            except Exception as duration_error:
                logger.error("avg_duration_calculation_failed", 
                           error=str(duration_error), 
                           error_type=type(duration_error).__name__)
                # 设置默认值并继续处理
                avg_duration = 0


            # 按时间分组的会话数
            time_series = await self._get_sessions_time_series(tenant_id, filters)

            logger.info(
                "session_stats_retrieved",
                tenant_id=str(tenant_id),
                total_sessions=total_sessions,
                time_range=f"{filters.start_date} to {filters.end_date}",
            )

            return SessionStatsResponse(
                total_sessions=total_sessions,
                status_distribution={stat.status: stat.count for stat in status_stats},
                avg_duration_seconds=avg_duration or 0,
                time_series=time_series,
            )

        except Exception as e:
            logger.error("session_stats_failed", tenant_id=str(tenant_id), error=str(e))
            raise

    async def get_message_stats(
        self, tenant_id: UUID, filters: AnalyticsFilter
    ) -> MessageStatsResponse:
        """
        获取消息统计数据

        Args:
            tenant_id: 租户ID
            filters: 过滤条件

        Returns:
            消息统计响应
        """
        try:
            # 构建基础查询条件
            conditions = [Message.tenant_id == tenant_id]
            if filters.start_date:
                conditions.append(Message.created_at >= filters.start_date)
            if filters.end_date:
                conditions.append(Message.created_at <= filters.end_date)
            if filters.message_types:
                conditions.append(Message.type.in_(filters.message_types))

            # 总消息数
            total_messages_query = select(func.count(Message.id)).where(*conditions)
            total_messages = (await self.db.execute(total_messages_query)).scalar_one()

            # 消息类型分布
            type_stats_query = (
                select(Message.type, func.count(Message.id).label("count"))
                .where(Message.tenant_id == tenant_id)
                .group_by(Message.type)
            )
            if filters.start_date:
                type_stats_query = type_stats_query.where(Message.created_at >= filters.start_date)
            if filters.end_date:
                type_stats_query = type_stats_query.where(Message.created_at <= filters.end_date)

            type_stats_result = await self.db.execute(type_stats_query)
            type_stats = type_stats_result.all()


            # 平均响应时间（仅适用于客服回复）
            avg_response_time = await self._calculate_avg_response_time(
                tenant_id, filters
            )

            # 按时间分组的消息数
            time_series = await self._get_messages_time_series(tenant_id, filters)

            # 最活跃的会话
            top_sessions = await self._get_top_active_sessions(tenant_id, filters)

            logger.info(
                "message_stats_retrieved",
                tenant_id=str(tenant_id),
                total_messages=total_messages,
                time_range=f"{filters.start_date} to {filters.end_date}",
            )

            return MessageStatsResponse(
                total_messages=total_messages,
                type_distribution={stat.type: stat.count for stat in type_stats},
                avg_response_time_seconds=avg_response_time,
                time_series=time_series,
                top_active_sessions=top_sessions,
            )

        except Exception as e:
            logger.error("message_stats_failed", tenant_id=str(tenant_id), error=str(e))
            raise

    async def get_realtime_metrics(self, tenant_id: UUID) -> RealtimeMetrics:
        """
        获取实时监控指标

        Args:
            tenant_id: 租户ID

        Returns:
            实时指标数据
        """
        try:
            now = datetime.utcnow()
            one_hour_ago = now - timedelta(hours=1)

            # 活跃会话数
            active_sessions_query = select(func.count(Session.id)).where(
                Session.tenant_id == tenant_id, Session.status == "active"
            )
            active_sessions = (await self.db.execute(active_sessions_query)).scalar_one()

            # 过去1小时新会话数
            new_sessions_hour_query = select(func.count(Session.id)).where(
                    Session.tenant_id == tenant_id, Session.created_at >= one_hour_ago
            )
            new_sessions_hour = (await self.db.execute(new_sessions_hour_query)).scalar_one()

            # 过去1小时消息数
            messages_hour_query = select(func.count(Message.id)).where(
                    Message.tenant_id == tenant_id, Message.created_at >= one_hour_ago
                )
            messages_hour = (await self.db.execute(messages_hour_query)).scalar_one()


            # 待处理会话数 (例如，状态为 'open' 且10分钟内无客服回复)
            ten_minutes_ago = now - timedelta(minutes=10)
            
            # This query is more complex and requires a subquery to find the latest message in each session.
            # For now, we will use a simplified version.
            # A more accurate implementation would require a more complex query.
            pending_sessions_query = select(func.count(Session.id)).where(
                Session.tenant_id == tenant_id,
                Session.status == "open",
                Session.updated_at < ten_minutes_ago
            )
            pending_sessions = (await self.db.execute(pending_sessions_query)).scalar_one()


            logger.info(
                "realtime_metrics_retrieved",
                tenant_id=str(tenant_id),
                active_sessions=active_sessions,
                new_sessions_hour=new_sessions_hour,
            )

            return RealtimeMetrics(
                active_sessions=active_sessions,
                new_sessions_hour=new_sessions_hour,
                messages_hour=messages_hour,
                pending_sessions=pending_sessions,
            )

        except Exception as e:
            logger.error("realtime_metrics_failed", tenant_id=str(tenant_id), error=str(e))
            raise

    async def get_trend_analysis(self, tenant_id: UUID, days: int = 7) -> TrendAnalysis:
        """
        获取趋势分析数据

        Args:
            tenant_id: 租户ID
            days: 分析天数

        Returns:
            趋势分析结果
        """
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)

            # 获取两个时间段的数据进行对比
            current_period = AnalyticsFilter(start_date=start_date, end_date=end_date)
            previous_period = AnalyticsFilter(
                start_date=start_date - timedelta(days=days), end_date=start_date
            )

            # 当前期间统计
            current_sessions = await self._count_sessions_in_period(
                tenant_id, current_period
            )
            current_messages = await self._count_messages_in_period(
                tenant_id, current_period
            )

            # 前期统计
            previous_sessions = await self._count_sessions_in_period(
                tenant_id, previous_period
            )
            previous_messages = await self._count_messages_in_period(
                tenant_id, previous_period
            )

            # 计算变化率
            sessions_change = self._calculate_change_rate(
                previous_sessions, current_sessions
            )
            messages_change = self._calculate_change_rate(
                previous_messages, current_messages
            )

            # 按小时分组的活动分布
            hourly_distribution = await self._get_hourly_activity_distribution(
                tenant_id, current_period
            )

            logger.info(
                "trend_analysis_retrieved",
                tenant_id=str(tenant_id),
                days=days,
                sessions_change=sessions_change,
                messages_change=messages_change,
            )

            return TrendAnalysis(
                period_days=days,
                sessions_change_percent=sessions_change,
                messages_change_percent=messages_change,
                hourly_activity_distribution=hourly_distribution,
                analysis_date=end_date,
            )

        except Exception as e:
            logger.error(
                "trend_analysis_failed", tenant_id=str(tenant_id), error=str(e)
            )
            raise

    async def _get_sessions_time_series(
        self, tenant_id: UUID, filters: AnalyticsFilter
    ) -> list[TimeSeriesData]:
        """按天聚合会话数"""
        # 根据数据库类型选择适当的时间截断函数
        # 检查实际使用的数据库引擎
        engine_name = str(self.db.bind.engine.url.drivername).lower()
        
        if "sqlite" in engine_name:
            # SQLite使用DATE函数
            date_trunc_func = func.date(Session.created_at)
        else:
            # PostgreSQL使用date_trunc
            date_trunc_func = func.date_trunc("day", Session.created_at)
        
        query = (
            select(
                date_trunc_func.label("time_unit"),
                func.count(Session.id).label("count"),
            )
            .where(Session.tenant_id == tenant_id)
            .group_by("time_unit")
            .order_by("time_unit")
        )
        if filters.start_date:
            query = query.where(Session.created_at >= filters.start_date)
        if filters.end_date:
            query = query.where(Session.created_at <= filters.end_date)
        
        result = await self.db.execute(query)
        return [
            TimeSeriesData(timestamp=row.time_unit, value=row.count) for row in result
        ]

    async def _get_messages_time_series(
        self, tenant_id: UUID, filters: AnalyticsFilter
    ) -> list[TimeSeriesData]:
        """按天聚合消息数"""
        # 根据数据库类型选择适当的时间截断函数
        # 检查实际使用的数据库引擎
        engine_name = str(self.db.bind.engine.url.drivername).lower()
        
        if "sqlite" in engine_name:
            # SQLite使用DATE函数
            date_trunc_func = func.date(Message.created_at)
        else:
            # PostgreSQL使用date_trunc
            date_trunc_func = func.date_trunc("day", Message.created_at)

        query = (
            select(
                date_trunc_func.label("time_unit"),
                func.count(Message.id).label("count"),
            )
            .where(Message.tenant_id == tenant_id)
            .group_by("time_unit")
            .order_by("time_unit")
        )

        if filters.start_date:
            query = query.where(Message.created_at >= filters.start_date)
        if filters.end_date:
            query = query.where(Message.created_at <= filters.end_date)

        result = await self.db.execute(query)
        return [
            TimeSeriesData(timestamp=row.time_unit, value=row.count) for row in result
        ]

    async def _calculate_avg_response_time(
        self, tenant_id: UUID, filters: AnalyticsFilter
    ) -> float:
        """
        计算平均响应时间
        逻辑：找出所有客服回复，并找到其对应的上一条用户消息，计算时间差
        """
        # This is a complex calculation that is hard to express in a single query
        # without window functions or CTEs which can be complex with SQLAlchemy core.
        # We will fetch relevant messages and process in Python.

        conditions = [Message.tenant_id == tenant_id]
        if filters.start_date:
            conditions.append(Message.created_at >= filters.start_date)
        if filters.end_date:
            conditions.append(Message.created_at <= filters.end_date)

        messages_query = (
            select(Message.session_id, Message.created_at, Message.sender_type)
            .where(*conditions)
            .order_by(Message.session_id, Message.created_at)
        )
        
        result = await self.db.execute(messages_query)
        messages = result.all()

        response_times = []
        # Group messages by session_id
        from itertools import groupby

        for _, g in groupby(messages, key=lambda x: x.session_id):
            session_messages = list(g)
            for i in range(len(session_messages) - 1):
                current_msg = session_messages[i]
                next_msg = session_messages[i+1]
                if current_msg.sender_type == SenderType.USER and next_msg.sender_type == SenderType.STAFF:
                    response_time = (next_msg.created_at - current_msg.created_at).total_seconds()
                    response_times.append(response_time)

        return sum(response_times) / len(response_times) if response_times else 0.0

    async def _get_top_active_sessions(
        self, tenant_id: UUID, filters: AnalyticsFilter, limit: int = 10
    ) -> list[dict[str, Any]]:
        """获取消息数最多的会话"""
        query = (
            select(
                    Message.session_id,
                    func.count(Message.id).label("message_count"),
            )
            .where(Message.tenant_id == tenant_id)
                .group_by(Message.session_id)
                .order_by(func.count(Message.id).desc())
                .limit(limit)
            )
        if filters.start_date:
            query = query.where(Message.created_at >= filters.start_date)
        if filters.end_date:
            query = query.where(Message.created_at <= filters.end_date)

        result = await self.db.execute(query)
        return [
            {"session_id": str(row.session_id), "message_count": row.message_count}
            for row in result
            ]


    async def _count_sessions_in_period(
        self, tenant_id: UUID, period: AnalyticsFilter
    ) -> int:
        """计算指定时间段内的会话数"""
        conditions = [Session.tenant_id == tenant_id]
        if period.start_date:
            conditions.append(Session.created_at >= period.start_date)
        if period.end_date:
            conditions.append(Session.created_at <= period.end_date)
            
        query = select(func.count(Session.id)).where(*conditions)
        count = (await self.db.execute(query)).scalar_one()
        return count

    async def _count_messages_in_period(
        self, tenant_id: UUID, period: AnalyticsFilter
    ) -> int:
        """计算指定时间段内的消息数"""
        conditions = [Message.tenant_id == tenant_id]
        if period.start_date:
            conditions.append(Message.created_at >= period.start_date)
        if period.end_date:
            conditions.append(Message.created_at <= period.end_date)

        query = select(func.count(Message.id)).where(*conditions)
        count = (await self.db.execute(query)).scalar_one()
        return count


    def _calculate_change_rate(self, previous: int, current: int) -> float:
        """计算变化率"""
        if previous == 0:
            return 100.0 if current > 0 else 0.0

        return round(((current - previous) / previous) * 100, 2)

    async def _get_hourly_activity_distribution(
        self, tenant_id: UUID, period: AnalyticsFilter
    ) -> dict[int, int]:
        """获取24小时内的活动分布"""
        # 检查实际使用的数据库引擎
        engine_name = str(self.db.bind.engine.url.drivername).lower()
        
        if "sqlite" in engine_name:
            # SQLite使用STRFTIME提取小时
            hour_extract = func.strftime('%H', Message.created_at)
        else:
            # PostgreSQL使用extract
            hour_extract = extract("hour", Message.created_at)
            
        query = (
            select(
                hour_extract.label("hour"),
                    func.count(Message.id).label("count"),
                )
            .where(Message.tenant_id == tenant_id)
            .group_by("hour")
            .order_by("hour")
        )
        if period.start_date:
            query = query.where(Message.created_at >= period.start_date)
        if period.end_date:
            query = query.where(Message.created_at <= period.end_date)
            
        result = await self.db.execute(query)

        # Initialize all hours to 0
        distribution = {h: 0 for h in range(24)}
        for row in result:
            # SQLite STRFTIME returns string, PostgreSQL extract returns int
            hour = int(row.hour) if isinstance(row.hour, str) else row.hour
            distribution[hour] = row.count

        return distribution
