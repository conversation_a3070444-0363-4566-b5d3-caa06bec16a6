#!/usr/bin/env python3
"""
AstrBot SaaS Platform 服务器实际测试脚本
DevOps执行专家 - 专业服务器测试验证
"""

import requests
import json
import time
from datetime import datetime

def print_header(title):
    """打印测试标题"""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def print_result(test_name, success, details=""):
    """打印测试结果"""
    status = "✅ 通过" if success else "❌ 失败"
    print(f"{status} {test_name}")
    if details:
        print(f"   详情: {details}")

def test_server_health():
    """测试服务器健康状态"""
    print_header("服务器健康检查")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        success = response.status_code == 200
        details = f"状态码: {response.status_code}"
        if success:
            try:
                data = response.json()
                details += f", 响应: {json.dumps(data, ensure_ascii=False)}"
            except:
                details += f", 响应: {response.text[:100]}"
        
        print_result("健康检查", success, details)
        return success
    except Exception as e:
        print_result("健康检查", False, f"连接错误: {e}")
        return False

def test_api_docs():
    """测试API文档访问"""
    print_header("API文档访问测试")

    try:
        response = requests.get("http://localhost:8000/api/v1/docs", timeout=5)
        success = response.status_code == 200
        details = f"状态码: {response.status_code}"
        if success:
            details += f", 内容长度: {len(response.text)} 字符"
            if "swagger" in response.text.lower() or "openapi" in response.text.lower():
                details += ", 包含Swagger/OpenAPI内容"

        print_result("API文档访问", success, details)
        return success
    except Exception as e:
        print_result("API文档访问", False, f"连接错误: {e}")
        return False

def test_openapi_spec():
    """测试OpenAPI规范"""
    print_header("OpenAPI规范测试")

    try:
        response = requests.get("http://localhost:8000/api/v1/openapi.json", timeout=5)
        success = response.status_code == 200
        details = f"状态码: {response.status_code}"
        if success:
            try:
                spec = response.json()
                details += f", 标题: {spec.get('info', {}).get('title', 'N/A')}"
                details += f", 版本: {spec.get('info', {}).get('version', 'N/A')}"
                details += f", 路径数量: {len(spec.get('paths', {}))}"
            except:
                details += ", JSON解析失败"

        print_result("OpenAPI规范", success, details)
        return success
    except Exception as e:
        print_result("OpenAPI规范", False, f"连接错误: {e}")
        return False

def test_api_endpoints():
    """测试主要API端点"""
    print_header("主要API端点测试")
    
    endpoints = [
        ("/api/v1/tenants/", "租户API"),
        ("/api/v1/auth/login", "认证API"),
        ("/api/v1/sessions/", "会话API"),
        ("/api/v1/messages/", "消息API"),
    ]
    
    results = []
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"http://localhost:8000{endpoint}", timeout=5)
            # 对于需要认证的端点，401是正常的
            success = response.status_code in [200, 401, 422]
            details = f"状态码: {response.status_code}"
            if response.status_code == 401:
                details += " (需要认证，正常)"
            elif response.status_code == 422:
                details += " (参数验证，正常)"
            
            print_result(name, success, details)
            results.append(success)
        except Exception as e:
            print_result(name, False, f"连接错误: {e}")
            results.append(False)
    
    return all(results)

def test_server_performance():
    """测试服务器性能"""
    print_header("服务器性能测试")
    
    try:
        # 测试响应时间
        start_time = time.time()
        response = requests.get("http://localhost:8000/health", timeout=5)
        response_time = (time.time() - start_time) * 1000
        
        success = response.status_code == 200 and response_time < 1000
        details = f"响应时间: {response_time:.2f}ms"
        
        print_result("响应时间测试", success, details)
        
        # 测试并发请求
        import concurrent.futures
        import threading
        
        def make_request():
            try:
                resp = requests.get("http://localhost:8000/health", timeout=5)
                return resp.status_code == 200
            except:
                return False
        
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        concurrent_time = (time.time() - start_time) * 1000
        success_rate = sum(results) / len(results) * 100
        
        concurrent_success = success_rate >= 80
        details = f"10个并发请求, 成功率: {success_rate:.1f}%, 总时间: {concurrent_time:.2f}ms"
        
        print_result("并发请求测试", concurrent_success, details)
        
        return success and concurrent_success
        
    except Exception as e:
        print_result("性能测试", False, f"测试错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 AstrBot SaaS Platform 服务器实际测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 测试目标: http://localhost:8000")
    
    # 等待服务器完全启动
    print("\n⏳ 等待服务器完全启动...")
    time.sleep(3)
    
    # 执行测试
    tests = [
        ("服务器健康检查", test_server_health),
        ("API文档访问", test_api_docs),
        ("OpenAPI规范", test_openapi_spec),
        ("API端点测试", test_api_endpoints),
        ("服务器性能", test_server_performance),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 测试总结
    print_header("测试总结")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    success_rate = (passed / total) * 100
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    print(f"📈 成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 服务器测试通过！SaaS平台运行正常")
        return 0
    else:
        print("⚠️ 服务器测试部分失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    exit(main())
