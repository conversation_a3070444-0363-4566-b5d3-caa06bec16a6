# AstrBot SaaS Platform - 监控与可观测性栈
# DevOps最佳实践：Prometheus监控、Grafana可视化、<PERSON><PERSON><PERSON>链路追踪、告警管理

version: '3.8'

services:
  # =====================================================
  # Prometheus - 指标收集与存储
  # =====================================================
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: astrbot-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./prometheus/alerts.yml:/etc/prometheus/alerts.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=10GB'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--log.level=info'
    networks:
      - monitoring
    labels:
      org.label-schema.group: "monitoring"

  # =====================================================
  # Grafana - 数据可视化与仪表盘
  # =====================================================
  grafana:
    image: grafana/grafana:10.0.0
    container_name: astrbot-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      # 安全配置
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD:-admin123}
      GF_SECURITY_SECRET_KEY: ${GRAFANA_SECRET_KEY:-grafana-secret-key}
      
      # 数据库配置
      GF_DATABASE_TYPE: postgres
      GF_DATABASE_HOST: grafana-postgres:5432
      GF_DATABASE_NAME: grafana
      GF_DATABASE_USER: grafana
      GF_DATABASE_PASSWORD: ${GRAFANA_DB_PASSWORD:-grafana_password}
      
      # 基础配置
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource,grafana-piechart-panel
      GF_FEATURE_TOGGLES_ENABLE: publicDashboards
      
      # 告警配置
      GF_ALERTING_ENABLED: true
      GF_UNIFIED_ALERTING_ENABLED: true
      
      # 用户配置
      GF_USERS_ALLOW_SIGN_UP: false
      GF_USERS_ALLOW_ORG_CREATE: false
      
      # SMTP配置（可选）
      GF_SMTP_ENABLED: true
      GF_SMTP_HOST: ${SMTP_HOST:-smtp.example.com:587}
      GF_SMTP_USER: ${SMTP_USER:-<EMAIL>}
      GF_SMTP_PASSWORD: ${SMTP_PASSWORD:-}
      GF_SMTP_FROM_ADDRESS: ${SMTP_FROM:-<EMAIL>}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
      - ./grafana/dashboards:/var/lib/grafana/dashboards:ro
    depends_on:
      - grafana-postgres
      - prometheus
    networks:
      - monitoring
    labels:
      org.label-schema.group: "monitoring"

  # =====================================================
  # Grafana PostgreSQL数据库
  # =====================================================
  grafana-postgres:
    image: postgres:15-alpine
    container_name: astrbot-grafana-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: grafana
      POSTGRES_USER: grafana
      POSTGRES_PASSWORD: ${GRAFANA_DB_PASSWORD:-grafana_password}
    volumes:
      - grafana_postgres_data:/var/lib/postgresql/data
    networks:
      - monitoring
    labels:
      org.label-schema.group: "monitoring"

  # =====================================================
  # AlertManager - 告警管理
  # =====================================================
  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: astrbot-alertmanager
    restart: unless-stopped
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://alertmanager:9093'
      - '--log.level=info'
    networks:
      - monitoring
    labels:
      org.label-schema.group: "monitoring"

  # =====================================================
  # Jaeger - 分布式链路追踪
  # =====================================================
  jaeger:
    image: jaegertracing/all-in-one:1.48
    container_name: astrbot-jaeger
    restart: unless-stopped
    ports:
      - "14250:14250"  # gRPC
      - "14268:14268"  # HTTP
      - "16686:16686"  # Web UI
      - "6831:6831/udp"  # UDP
      - "6832:6832/udp"  # UDP
    environment:
      COLLECTOR_OTLP_ENABLED: true
      COLLECTOR_ZIPKIN_HOST_PORT: :9411
      SPAN_STORAGE_TYPE: elasticsearch
      ES_SERVER_URLS: http://elasticsearch:9200
      ES_USERNAME: ${ES_USERNAME:-elastic}
      ES_PASSWORD: ${ES_PASSWORD:-elasticsearch_password}
    depends_on:
      - elasticsearch
    networks:
      - monitoring
    labels:
      org.label-schema.group: "monitoring"

  # =====================================================
  # Elasticsearch - Jaeger存储后端
  # =====================================================
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: astrbot-elasticsearch
    restart: unless-stopped
    ports:
      - "9200:9200"
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - monitoring
    labels:
      org.label-schema.group: "monitoring"

  # =====================================================
  # Node Exporter - 系统指标收集
  # =====================================================
  node-exporter:
    image: prom/node-exporter:v1.6.0
    container_name: astrbot-node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
      - /run/systemd/private:/run/systemd/private:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
      - '--collector.systemd'
      - '--collector.processes'
    networks:
      - monitoring
    labels:
      org.label-schema.group: "monitoring"

  # =====================================================
  # cAdvisor - 容器指标收集
  # =====================================================
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.0
    container_name: astrbot-cadvisor
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    networks:
      - monitoring
    labels:
      org.label-schema.group: "monitoring"

  # =====================================================
  # Redis Exporter - Redis指标收集
  # =====================================================
  redis-exporter:
    image: oliver006/redis_exporter:v1.52.0
    container_name: astrbot-redis-exporter
    restart: unless-stopped
    ports:
      - "9121:9121"
    environment:
      REDIS_ADDR: ${REDIS_URL:-redis://redis:6379}
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
    networks:
      - monitoring
    labels:
      org.label-schema.group: "monitoring"

  # =====================================================
  # Postgres Exporter - PostgreSQL指标收集
  # =====================================================
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.13.2
    container_name: astrbot-postgres-exporter
    restart: unless-stopped
    ports:
      - "9187:9187"
    environment:
      DATA_SOURCE_NAME: ${DATABASE_URL:-****************************************/dbname?sslmode=disable}
    networks:
      - monitoring
    labels:
      org.label-schema.group: "monitoring"

  # =====================================================
  # Nginx Prometheus Exporter
  # =====================================================
  nginx-exporter:
    image: nginx/nginx-prometheus-exporter:0.11.0
    container_name: astrbot-nginx-exporter
    restart: unless-stopped
    ports:
      - "9113:9113"
    command:
      - '-nginx.scrape-uri=http://nginx/nginx_status'
    networks:
      - monitoring
    labels:
      org.label-schema.group: "monitoring"

  # =====================================================
  # Blackbox Exporter - 外部服务监控
  # =====================================================
  blackbox-exporter:
    image: prom/blackbox-exporter:v0.24.0
    container_name: astrbot-blackbox-exporter
    restart: unless-stopped
    ports:
      - "9115:9115"
    volumes:
      - ./blackbox/blackbox.yml:/etc/blackbox_exporter/config.yml:ro
    networks:
      - monitoring
    labels:
      org.label-schema.group: "monitoring"

  # =====================================================
  # Loki - 日志聚合
  # =====================================================
  loki:
    image: grafana/loki:2.8.0
    container_name: astrbot-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./loki/local-config.yaml:/etc/loki/local-config.yaml:ro
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - monitoring
    labels:
      org.label-schema.group: "monitoring"

  # =====================================================
  # Promtail - 日志收集器
  # =====================================================
  promtail:
    image: grafana/promtail:2.8.0
    container_name: astrbot-promtail
    restart: unless-stopped
    volumes:
      - ./promtail/config.yml:/etc/promtail/config.yml:ro
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - monitoring
    labels:
      org.label-schema.group: "monitoring"

# =====================================================
# 网络配置
# =====================================================
networks:
  monitoring:
    driver: bridge
    name: astrbot-monitoring

# =====================================================
# 数据卷配置
# =====================================================
volumes:
  prometheus_data:
    name: astrbot-prometheus-data
  grafana_data:
    name: astrbot-grafana-data
  grafana_postgres_data:
    name: astrbot-grafana-postgres-data
  alertmanager_data:
    name: astrbot-alertmanager-data
  elasticsearch_data:
    name: astrbot-elasticsearch-data
  loki_data:
    name: astrbot-loki-data 