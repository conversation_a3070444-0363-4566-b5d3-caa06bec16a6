#!/usr/bin/env python3
"""
异步函数优化脚本 - 将不包含await的异步函数转换为同步函数
"""

import ast
import re
from pathlib import Path
from typing import List, Dict, Any


class AsyncFunctionOptimizer:
    """异步函数优化器"""

    def __init__(self):
        self.optimized_count = 0

    def optimize_file(self, file_path: Path) -> bool:
        """优化单个文件的异步函数"""
        try:
            content = file_path.read_text(encoding="utf-8")
            original_content = content

            # 解析AST以找到不必要的异步函数
            tree = ast.parse(content)
            functions_to_optimize = self._find_unnecessary_async_functions(tree)

            if not functions_to_optimize:
                print(f"⏭️  无需优化: {file_path}")
                return False

            # 应用优化
            for func_info in functions_to_optimize:
                content = self._convert_async_to_sync(content, func_info)
                self.optimized_count += 1

            # 如果有修改，写回文件
            if content != original_content:
                file_path.write_text(content, encoding="utf-8")
                print(f"✅ 优化文件: {file_path} ({len(functions_to_optimize)}个函数)")
                return True
            else:
                print(f"⏭️  无需优化: {file_path}")
                return False

        except Exception as e:
            print(f"❌ 优化失败 {file_path}: {e}")
            return False

    def _find_unnecessary_async_functions(self, tree: ast.AST) -> List[Dict[str, Any]]:
        """找到不必要的异步函数"""
        unnecessary_funcs = []

        for node in ast.walk(tree):
            if isinstance(node, ast.AsyncFunctionDef):
                # 检查函数体是否包含await
                has_await = self._has_await_in_function(node)

                if not has_await and not self._is_special_function(node.name):
                    unnecessary_funcs.append(
                        {"name": node.name, "lineno": node.lineno, "is_async": True}
                    )

        return unnecessary_funcs

    def _has_await_in_function(self, func_node: ast.AsyncFunctionDef) -> bool:
        """检查函数是否包含await调用"""
        for node in ast.walk(func_node):
            if isinstance(node, ast.Await):
                return True
            # 检查是否调用了其他异步函数（以async_或await_开头的调用）
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name):
                    if node.func.id.startswith(("async_", "await_")):
                        return True
                elif isinstance(node.func, ast.Attribute):
                    if node.func.attr.startswith(("async_", "await_")):
                        return True
        return False

    def _is_special_function(self, func_name: str) -> bool:
        """检查是否是特殊函数（应保持异步）"""
        special_patterns = [
            "generate_stream_",  # 流式生成函数
            "aclose",  # 异步关闭函数
            "__aenter__",  # 异步上下文管理器
            "__aexit__",  # 异步上下文管理器
        ]

        for pattern in special_patterns:
            if pattern in func_name:
                return True

        return False

    def _convert_async_to_sync(self, content: str, func_info: Dict[str, Any]) -> str:
        """将异步函数转换为同步函数"""
        lines = content.split("\n")
        func_name = func_info["name"]

        for i, line in enumerate(lines):
            # 匹配函数定义行
            if f"async def {func_name}(" in line:
                # 移除async关键字
                lines[i] = line.replace("async def", "def")
                break

        return "\n".join(lines)

    def optimize_services_directory(self, services_path: Path) -> tuple[int, int]:
        """优化services目录下的所有文件"""
        optimized_files = 0
        total_files = 0

        # 优先处理的文件列表（基于警告分析结果）
        priority_files = [
            "agent_suggestion_service.py",
            "auth_service.py",
            "auto_reply_service.py",
            "context_manager.py",
            "instance_config_service.py",
            "session_summary_service.py",
            "webhook_service.py",
        ]

        # 先处理优先文件
        for filename in priority_files:
            file_path = services_path / filename
            if file_path.exists():
                total_files += 1
                if self.optimize_file(file_path):
                    optimized_files += 1

        # 然后处理其他文件
        for py_file in services_path.rglob("*.py"):
            if py_file.name.startswith("__") or py_file.name in priority_files:
                continue

            total_files += 1
            if self.optimize_file(py_file):
                optimized_files += 1

        return optimized_files, total_files


def main():
    """主函数"""
    print("🚀 开始优化异步函数...")

    optimizer = AsyncFunctionOptimizer()
    services_path = Path("app/services")

    if not services_path.exists():
        print(f"❌ 目录不存在: {services_path}")
        return

    optimized_files, total_files = optimizer.optimize_services_directory(services_path)

    print(f"\n📊 优化统计:")
    print(f"检查文件数: {total_files}")
    print(f"优化文件数: {optimized_files}")
    print(f"优化函数数: {optimizer.optimized_count}")

    if optimized_files > 0:
        print(f"\n🎉 成功优化 {optimized_files} 个文件的异步函数！")
        print("⚠️  注意：请确保调用这些函数的地方也移除await关键字")
    else:
        print(f"\n✅ 所有异步函数都是必要的！")


if __name__ == "__main__":
    main()
