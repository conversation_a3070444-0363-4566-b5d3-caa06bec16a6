"""
AstrBot SaaS Platform - 断言工具
===============================

提供常用的自定义断言工具，简化测试代码

自动生成: TestCodeOptimizer
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import json

class AssertionHelpers:
    """断言辅助工具类"""
    
    @staticmethod
    def assert_response_success(response, expected_status=200):
        """断言API响应成功"""
        assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}"
        assert "error" not in response.json(), f"Response contains error: {response.json()}"
    
    @staticmethod
    def assert_response_error(response, expected_status=400):
        """断言API响应错误"""
        assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}"
        response_data = response.json()
        assert "error" in response_data or "detail" in response_data, "Response should contain error information"
    
    @staticmethod
    def assert_dict_contains(actual_dict: Dict, expected_subset: Dict):
        """断言字典包含指定的键值对"""
        for key, expected_value in expected_subset.items():
            assert key in actual_dict, f"Key '{key}' not found in {actual_dict}"
            assert actual_dict[key] == expected_value, f"Expected {key}={expected_value}, got {actual_dict[key]}"
    
    @staticmethod
    def assert_datetime_recent(dt_string: str, max_seconds_ago: int = 60):
        """断言时间戳是最近的"""
        dt = datetime.fromisoformat(dt_string.replace('Z', '+00:00'))
        now = datetime.utcnow().replace(tzinfo=dt.tzinfo)
        diff = (now - dt).total_seconds()
        assert diff <= max_seconds_ago, f"Datetime {dt_string} is {diff} seconds old, expected <= {max_seconds_ago}"
    
    @staticmethod
    def assert_list_contains_item(items: List[Dict], **filters):
        """断言列表包含符合条件的项目"""
        matching_items = [
            item for item in items 
            if all(item.get(k) == v for k, v in filters.items())
        ]
        assert len(matching_items) > 0, f"No items found matching {filters} in {items}"
    
    @staticmethod
    def assert_pagination_response(response_data: Dict, page: int = 1, per_page: int = 10):
        """断言分页响应格式"""
        required_keys = ['items', 'total', 'page', 'per_page', 'pages']
        for key in required_keys:
            assert key in response_data, f"Pagination response missing key: {key}"
        
        assert response_data['page'] == page, f"Expected page {page}, got {response_data['page']}"
        assert response_data['per_page'] == per_page, f"Expected per_page {per_page}, got {response_data['per_page']}"
        assert isinstance(response_data['items'], list), "Items should be a list"

# 便捷函数
def assert_success(response, status=200):
    """快速断言成功响应"""
    AssertionHelpers.assert_response_success(response, status)

def assert_error(response, status=400):
    """快速断言错误响应"""
    AssertionHelpers.assert_response_error(response, status)

def assert_contains(actual, expected):
    """快速断言包含关系"""
    AssertionHelpers.assert_dict_contains(actual, expected)

def assert_recent(dt_string, max_seconds=60):
    """快速断言时间最近"""
    AssertionHelpers.assert_datetime_recent(dt_string, max_seconds)
