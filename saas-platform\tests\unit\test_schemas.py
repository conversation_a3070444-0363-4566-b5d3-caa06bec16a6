"""
Schema层测试 - 专注于Pydantic模型验证
"""

import pytest
from uuid import uuid4
from datetime import datetime
from pydantic import ValidationError

from app.schemas.tenant import TenantCreate, TenantRead, TenantUpdate
from app.schemas.user import UserCreate, UserRead, UserUpdate
from app.schemas.session import Session<PERSON><PERSON>, SessionRead, SessionUpdate
from app.schemas.message import MessageCreate, MessageRead
from app.schemas.common import PaginationParams
from app.models.session import ChannelType  # 添加导入
from app.models.message import MessageType  # 添加导入


class TestTenantSchemas:
    """测试租户Schema"""

    def test_tenant_create_valid(self):
        """测试有效的租户创建数据"""
        # Arrange & Act
        tenant_data = TenantCreate(name="Test Corporation", email="<EMAIL>")

        # Assert
        assert tenant_data.name == "Test Corporation"
        assert tenant_data.email == "<EMAIL>"

    def test_tenant_create_invalid_email(self):
        """测试无效邮箱验证"""
        # Arrange & Act & Assert
        with pytest.raises(ValidationError):
            TenantCreate(name="Test Corp", email="invalid-email")

    def test_tenant_create_missing_name(self):
        """测试缺少必填字段"""
        # Arrange & Act & Assert
        with pytest.raises(ValidationError):
            TenantCreate(email="<EMAIL>")  # 缺少name

    def test_tenant_read_schema(self):
        """测试租户读取Schema"""
        # Arrange
        tenant_id = uuid4()

        # Act
        tenant_read = TenantRead(
            id=tenant_id,
            name="Test Corp",
            email="<EMAIL>",
            status="active",
            plan="basic",
            created_at=datetime.now(),
            updated_at=datetime.now(),
            is_active=True,  # 必需字段
            display_name="Test Corp",  # 必需字段
        )

        # Assert
        assert tenant_read.id == tenant_id
        assert tenant_read.name == "Test Corp"
        assert tenant_read.status == "active"
        assert tenant_read.is_active is True
        assert tenant_read.display_name == "Test Corp"

    def test_tenant_update_schema(self):
        """测试租户更新Schema"""
        # Act
        tenant_update = TenantUpdate(name="Updated Corp")

        # Assert
        assert tenant_update.name == "Updated Corp"
        assert tenant_update.email is None  # 可选字段


class TestUserSchemas:
    """测试用户Schema"""

    def test_user_create_valid(self):
        """测试有效的用户创建数据"""
        # Arrange
        tenant_id = uuid4()

        # Act
        user_data = UserCreate(
            platform="telegram",
            user_id="123456",
            tenant_id=tenant_id,
            nickname="Test User",
        )

        # Assert
        assert user_data.platform == "telegram"
        assert user_data.user_id == "123456"
        assert user_data.tenant_id == tenant_id
        assert user_data.nickname == "Test User"

    def test_user_create_minimal(self):
        """测试最小必填字段"""
        # Arrange
        tenant_id = uuid4()

        # Act
        user_data = UserCreate(
            platform="wechat", user_id="user123", tenant_id=tenant_id
        )

        # Assert
        assert user_data.platform == "wechat"
        assert user_data.user_id == "user123"
        assert user_data.nickname is None

    def test_user_read_schema(self):
        """测试用户读取Schema"""
        # Arrange
        tenant_id = uuid4()

        # Act
        user_read = UserRead(
            id="telegram:123456",
            tenant_id=tenant_id,
            platform="telegram",
            user_id="123456",
            nickname="Test User",
            display_name="Test User",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        # Assert
        assert user_read.id == "telegram:123456"
        assert user_read.platform == "telegram"
        assert user_read.display_name == "Test User"


class TestSessionSchemas:
    """测试会话Schema"""

    def test_session_create_valid(self):
        """测试有效的会话创建数据"""
        # Act
        session_data = SessionCreate(user_id="telegram:123456", platform="telegram")

        # Assert
        assert session_data.user_id == "telegram:123456"
        assert session_data.platform == "telegram"
        assert session_data.channel_type == ChannelType.DIRECT  # 默认值
        assert session_data.priority == 5  # 默认值

    def test_session_read_schema(self):
        """测试会话读取Schema"""
        # Arrange
        session_id = uuid4()
        tenant_id = uuid4()

        # Act
        session_read = SessionRead(
            id=session_id,
            tenant_id=tenant_id,
            user_id="telegram:123456",
            platform="telegram",
            status="active",
            channel_type="direct",
            priority=5,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        # Assert
        assert session_read.id == session_id
        assert session_read.status == "active"
        assert session_read.priority == 5


class TestMessageSchemas:
    """测试消息Schema"""

    def test_message_create_valid(self):
        """测试有效的消息创建数据"""
        # Arrange
        session_id = uuid4()

        # Act
        message_data = MessageCreate(
            session_id=session_id,
            content="Hello, World!",
            sender_type="user",
            sender_id="user123",
        )

        # Assert
        assert message_data.session_id == session_id
        assert message_data.content == "Hello, World!"
        assert message_data.sender_type == "user"
        assert message_data.sender_id == "user123"
        assert message_data.message_type == MessageType.TEXT  # 默认值

    def test_message_create_with_attachments(self):
        """测试带附件的消息创建"""
        # Arrange
        session_id = uuid4()
        attachments = [
            {"type": "image", "url": "http://example.com/image.jpg", "size": 1024}
        ]

        # Act
        message_data = MessageCreate(
            session_id=session_id,
            content="Check this image",
            sender_type="user",
            sender_id="user123",
            message_type="image",
            attachments=attachments,
        )

        # Assert
        assert message_data.message_type == "image"
        assert len(message_data.attachments) == 1
        assert message_data.attachments[0]["type"] == "image"

    def test_message_read_schema(self):
        """测试消息读取Schema"""
        # Arrange
        tenant_id = uuid4()
        session_id = uuid4()

        # Act
        message_read = MessageRead(
            id=1,
            tenant_id=tenant_id,
            session_id=session_id,
            content="Hello, World!",
            message_type="text",
            sender_type="user",
            sender_id="user123",
            timestamp=datetime.now(),
            created_at=datetime.now(),
        )

        # Assert
        assert message_read.id == 1
        assert message_read.content == "Hello, World!"
        assert message_read.message_type == "text"


class TestCommonSchemas:
    """测试通用Schema"""

    def test_pagination_params_default(self):
        """测试分页参数默认值"""
        # Act
        pagination = PaginationParams()

        # Assert
        assert pagination.skip == 0
        assert pagination.limit == 20

    def test_pagination_params_custom(self):
        """测试自定义分页参数"""
        # Act
        pagination = PaginationParams(skip=10, limit=50)

        # Assert
        assert pagination.skip == 10
        assert pagination.limit == 50

    def test_pagination_params_validation(self):
        """测试分页参数验证"""
        # 测试负数skip - 应该被允许（0或正数）
        with pytest.raises(ValidationError):
            PaginationParams(skip=-1)

        # 测试超出范围的limit
        with pytest.raises(ValidationError):
            PaginationParams(limit=0)  # 最小值应该是1

        with pytest.raises(ValidationError):
            PaginationParams(limit=101)  # 最大值应该是100


class TestSchemaValidation:
    """测试Schema验证逻辑"""

    def test_email_validation(self):
        """测试邮箱格式验证"""
        # 有效邮箱
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]

        for email in valid_emails:
            tenant = TenantCreate(name="Test", email=email)
            assert tenant.email == email

        # 无效邮箱
        invalid_emails = ["invalid-email", "@domain.com", "user@", "user@domain", ""]

        for email in invalid_emails:
            with pytest.raises(ValidationError):
                TenantCreate(name="Test", email=email)

    def test_uuid_validation(self):
        """测试UUID格式验证"""
        # 有效UUID
        valid_uuid = uuid4()
        user = UserCreate(platform="test", user_id="123", tenant_id=valid_uuid)
        assert user.tenant_id == valid_uuid

        # 无效UUID字符串
        with pytest.raises(ValidationError):
            UserCreate(platform="test", user_id="123", tenant_id="invalid-uuid")

    def test_enum_validation(self):
        """测试枚举值验证"""
        # 有效的消息类型
        valid_message_types = [
            "text",
            "image",
            "file",
            "voice",
            "video",
            "location",
            "system",
        ]

        for msg_type in valid_message_types:
            message = MessageCreate(
                tenant_id=uuid4(),
                session_id=uuid4(),
                content="test",
                sender_type="user",
                sender_id="user123",
                message_type=msg_type,
            )
            assert message.message_type == msg_type

        # 无效的消息类型
        with pytest.raises(ValidationError):
            MessageCreate(
                tenant_id=uuid4(),
                session_id=uuid4(),
                content="test",
                sender_type="user",
                sender_id="user123",
                message_type="invalid_type",
            )


class TestSchemaIntegration:
    """测试Schema集成场景"""

    def test_tenant_user_relationship(self):
        """测试租户-用户关系数据"""
        # Arrange
        tenant_id = uuid4()

        # Act - 创建租户
        tenant = TenantCreate(name="Test Corp", email="<EMAIL>")

        # Act - 创建该租户下的用户
        user = UserCreate(
            platform="telegram",
            user_id="123456",
            tenant_id=tenant_id,
            nickname="Test User",
        )

        # Assert
        assert user.tenant_id == tenant_id
        assert tenant.name == "Test Corp"

    def test_session_message_relationship(self):
        """测试会话-消息关系数据"""
        # Arrange
        session_id = uuid4()

        # Act - 创建会话
        session = SessionCreate(user_id="telegram:123456", platform="telegram")

        # Act - 创建会话消息
        message = MessageCreate(
            session_id=session_id,
            content="Hello in session",
            sender_type="user",
            sender_id="user123",
        )

        # Assert
        assert session.user_id == "telegram:123456"
        assert session.platform == "telegram"
        assert message.session_id == session_id
        assert message.content == "Hello in session"
