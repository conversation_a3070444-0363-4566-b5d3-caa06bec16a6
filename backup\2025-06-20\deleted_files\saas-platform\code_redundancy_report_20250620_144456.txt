================================================================================
🔍 AstrBot SaaS Platform - 代码冗余分析报告
================================================================================
📅 生成时间: 2025-06-20 14:44:56
📁 项目路径: .
🎯 发现问题: 1347 个

📊 问题概览
----------------------------------------
🔴 CRITICAL: 12 个问题
🔴 HIGH: 294 个问题
🔴 MEDIUM: 959 个问题
🔴 LOW: 82 个问题
💾 估计冗余大小: 78.31 MB

🚨 CRITICAL 级别问题
----------------------------------------
1. 【批量重复文件】发现 153 个相似文件（模式: performance_baseline_report_TIMESTAMP.json）
   📄 涉及文件: 153 个
      - performance_baseline_report_20250616_180411.json
      - performance_baseline_report_20250616_180413.json
      - performance_baseline_report_20250617_190406.json
      ... 还有 150 个文件
   💾 冗余大小: 2225.1 KB
   💡 建议: 清理历史文件，仅保留最新的几个文件，或建立归档机制

2. 【批量重复文件】发现 13 个相似文件（模式: types.data.json）
   📄 涉及文件: 13 个
      - .mypy_cache\3.11\types.data.json
      - .mypy_cache\3.11\click\types.data.json
      - .mypy_cache\3.11\cryptography\hazmat\primitives\asymmetric\types.data.json
      ... 还有 10 个文件
   💾 冗余大小: 1144.2 KB
   💡 建议: 清理历史文件，仅保留最新的几个文件，或建立归档机制

3. 【批量重复文件】发现 13 个相似文件（模式: types.meta.json）
   📄 涉及文件: 13 个
      - .mypy_cache\3.11\types.meta.json
      - .mypy_cache\3.11\click\types.meta.json
      - .mypy_cache\3.11\cryptography\hazmat\primitives\asymmetric\types.meta.json
      ... 还有 10 个文件
   💾 冗余大小: 26.4 KB
   💡 建议: 清理历史文件，仅保留最新的几个文件，或建立归档机制

4. 【批量重复文件】发现 287 个相似文件（模式: __init__.data.json）
   📄 涉及文件: 287 个
      - .mypy_cache\3.11\annotated_types\__init__.data.json
      - .mypy_cache\3.11\anyio\__init__.data.json
      - .mypy_cache\3.11\anyio\abc\__init__.data.json
      ... 还有 284 个文件
   💾 冗余大小: 12373.1 KB
   💡 建议: 清理历史文件，仅保留最新的几个文件，或建立归档机制

5. 【批量重复文件】发现 287 个相似文件（模式: __init__.meta.json）
   📄 涉及文件: 287 个
      - .mypy_cache\3.11\annotated_types\__init__.meta.json
      - .mypy_cache\3.11\anyio\__init__.meta.json
      - .mypy_cache\3.11\anyio\abc\__init__.meta.json
      ... 还有 284 个文件
   💾 冗余大小: 550.4 KB
   💡 建议: 清理历史文件，仅保留最新的几个文件，或建立归档机制

6. 【批量重复文件】发现 16 个相似文件（模式: exceptions.data.json）
   📄 涉及文件: 16 个
      - .mypy_cache\3.11\argcomplete\exceptions.data.json
      - .mypy_cache\3.11\asyncio\exceptions.data.json
      - .mypy_cache\3.11\attr\exceptions.data.json
      ... 还有 13 个文件
   💾 冗余大小: 229.4 KB
   💡 建议: 清理历史文件，仅保留最新的几个文件，或建立归档机制

7. 【批量重复文件】发现 16 个相似文件（模式: exceptions.meta.json）
   📄 涉及文件: 16 个
      - .mypy_cache\3.11\argcomplete\exceptions.meta.json
      - .mypy_cache\3.11\asyncio\exceptions.meta.json
      - .mypy_cache\3.11\attr\exceptions.meta.json
      ... 还有 13 个文件
   💾 冗余大小: 28.9 KB
   💡 建议: 清理历史文件，仅保留最新的几个文件，或建立归档机制

8. 【批量重复文件】发现 30 个相似文件（模式: utils.data.json）
   📄 涉及文件: 30 个
      - .mypy_cache\3.11\click\utils.data.json
      - .mypy_cache\3.11\cryptography\utils.data.json
      - .mypy_cache\3.11\cryptography\hazmat\primitives\asymmetric\utils.data.json
      ... 还有 27 个文件
   💾 冗余大小: 897.9 KB
   💡 建议: 清理历史文件，仅保留最新的几个文件，或建立归档机制

9. 【批量重复文件】发现 30 个相似文件（模式: utils.meta.json）
   📄 涉及文件: 30 个
      - .mypy_cache\3.11\click\utils.meta.json
      - .mypy_cache\3.11\cryptography\utils.meta.json
      - .mypy_cache\3.11\cryptography\hazmat\primitives\asymmetric\utils.meta.json
      ... 还有 27 个文件
   💾 冗余大小: 58.7 KB
   💡 建议: 清理历史文件，仅保留最新的几个文件，或建立归档机制

10. 【批量重复文件】发现 35 个相似文件（模式: base.data.json）
   📄 涉及文件: 35 个
      - .mypy_cache\3.11\cryptography\hazmat\primitives\ciphers\base.data.json
      - .mypy_cache\3.11\cryptography\hazmat\primitives\serialization\base.data.json
      - .mypy_cache\3.11\httpcore\_backends\base.data.json
      ... 还有 32 个文件
   💾 冗余大小: 2260.4 KB
   💡 建议: 清理历史文件，仅保留最新的几个文件，或建立归档机制

11. 【批量重复文件】发现 35 个相似文件（模式: base.meta.json）
   📄 涉及文件: 35 个
      - .mypy_cache\3.11\cryptography\hazmat\primitives\ciphers\base.meta.json
      - .mypy_cache\3.11\cryptography\hazmat\primitives\serialization\base.meta.json
      - .mypy_cache\3.11\httpcore\_backends\base.meta.json
      ... 还有 32 个文件
   💾 冗余大小: 79.3 KB
   💡 建议: 清理历史文件，仅保留最新的几个文件，或建立归档机制

12. 【批量重复文件】发现 14 个相似文件（模式: __init__.py）
   📄 涉及文件: 14 个
      - app\__init__.py
      - app\api\__init__.py
      - app\api\deps\__init__.py
      ... 还有 11 个文件
   💾 冗余大小: 13.8 KB
   💡 建议: 清理历史文件，仅保留最新的几个文件，或建立归档机制

🚨 HIGH 级别问题
----------------------------------------
1. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\abc.data.json
      - .mypy_cache\3.12\abc.data.json
   💾 冗余大小: 28.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

2. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\argparse.data.json
      - .mypy_cache\3.12\argparse.data.json
   💾 冗余大小: 227.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

3. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\atexit.data.json
      - .mypy_cache\3.12\atexit.data.json
   💾 冗余大小: 10.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

4. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\bdb.data.json
      - .mypy_cache\3.12\bdb.data.json
   💾 冗余大小: 55.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

5. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\bisect.data.json
      - .mypy_cache\3.12\bisect.data.json
   💾 冗余大小: 14.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

6. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cmd.data.json
      - .mypy_cache\3.12\cmd.data.json
   💾 冗余大小: 21.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

7. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\codeop.data.json
      - .mypy_cache\3.12\codeop.data.json
   💾 冗余大小: 6.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

8. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\colorsys.data.json
      - .mypy_cache\3.12\colorsys.data.json
   💾 冗余大小: 8.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

9. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\contextlib.data.json
      - .mypy_cache\3.12\contextlib.data.json
   💾 冗余大小: 152.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

10. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\copyreg.data.json
      - .mypy_cache\3.12\copyreg.data.json
   💾 冗余大小: 13.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

11. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cProfile.data.json
      - .mypy_cache\3.12\cProfile.data.json
   💾 冗余大小: 20.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

12. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\decimal.data.json
      - .mypy_cache\3.12\decimal.data.json
   💾 冗余大小: 5.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

13. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\difflib.data.json
      - .mypy_cache\3.12\difflib.data.json
   💾 冗余大小: 77.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

14. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\errno.data.json
      - .mypy_cache\3.12\errno.data.json
   💾 冗余大小: 24.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

15. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\fnmatch.data.json
      - .mypy_cache\3.12\fnmatch.data.json
   💾 冗余大小: 7.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

16. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\gc.data.json
      - .mypy_cache\3.12\gc.data.json
   💾 冗余大小: 16.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

17. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\getopt.data.json
      - .mypy_cache\3.12\getopt.data.json
   💾 冗余大小: 7.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

18. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\getpass.data.json
      - .mypy_cache\3.12\getpass.data.json
   💾 冗余大小: 4.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

19. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\gettext.data.json
      - .mypy_cache\3.12\gettext.data.json
   💾 冗余大小: 61.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

20. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\glob.data.json
      - .mypy_cache\3.12\glob.data.json
   💾 冗余大小: 11.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

21. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\ipaddress.data.json
      - .mypy_cache\3.12\ipaddress.data.json
   💾 冗余大小: 171.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

22. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\keyword.data.json
      - .mypy_cache\3.12\keyword.data.json
   💾 冗余大小: 3.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

23. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\linecache.data.json
      - .mypy_cache\3.12\linecache.data.json
   💾 冗余大小: 9.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

24. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\mimetypes.data.json
      - .mypy_cache\3.12\mimetypes.data.json
   💾 冗余大小: 17.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

25. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\msvcrt.data.json
      - .mypy_cache\3.12\msvcrt.data.json
   💾 冗余大小: 13.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

26. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\netrc.data.json
      - .mypy_cache\3.12\netrc.data.json
   💾 冗余大小: 8.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

27. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\nturl2path.data.json
      - .mypy_cache\3.12\nturl2path.data.json
   💾 冗余大小: 2.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

28. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\operator.data.json
      - .mypy_cache\3.12\operator.data.json
   💾 冗余大小: 56.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

29. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\pprint.data.json
      - .mypy_cache\3.12\pprint.data.json
   💾 冗余大小: 13.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

30. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\profile.data.json
      - .mypy_cache\3.12\profile.data.json
   💾 冗余大小: 21.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

31. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\pstats.data.json
      - .mypy_cache\3.12\pstats.data.json
   💾 冗余大小: 52.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

32. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\pydoc.data.json
      - .mypy_cache\3.12\pydoc.data.json
   💾 冗余大小: 115.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

33. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\resource.data.json
      - .mypy_cache\3.12\resource.data.json
   💾 冗余大小: 1.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

34. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\runpy.data.json
      - .mypy_cache\3.12\runpy.data.json
   💾 冗余大小: 11.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

35. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\secrets.data.json
      - .mypy_cache\3.12\secrets.data.json
   💾 冗余大小: 7.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

36. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\select.data.json
      - .mypy_cache\3.12\select.data.json
   💾 冗余大小: 8.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

37. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\signal.data.json
      - .mypy_cache\3.12\signal.data.json
   💾 冗余大小: 16.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

38. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\site.data.json
      - .mypy_cache\3.12\site.data.json
   💾 冗余大小: 16.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

39. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\sre_compile.data.json
      - .mypy_cache\3.12\sre_compile.data.json
   💾 冗余大小: 14.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

40. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\sre_constants.data.json
      - .mypy_cache\3.12\sre_constants.data.json
   💾 冗余大小: 28.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

41. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\stat.data.json
      - .mypy_cache\3.12\stat.data.json
   💾 冗余大小: 9.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

42. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\timeit.data.json
      - .mypy_cache\3.12\timeit.data.json
   💾 冗余大小: 13.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

43. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\tomllib.data.json
      - .mypy_cache\3.12\tomllib.data.json
   💾 冗余大小: 5.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

44. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\_bisect.data.json
      - .mypy_cache\3.12\_bisect.data.json
   💾 冗余大小: 79.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

45. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\_locale.data.json
      - .mypy_cache\3.12\_locale.data.json
   💾 冗余大小: 6.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

46. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\_lsprof.data.json
      - .mypy_cache\3.12\_lsprof.data.json
   💾 冗余大小: 17.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

47. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\_stat.data.json
      - .mypy_cache\3.12\_stat.data.json
   💾 冗余大小: 24.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

48. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\_weakrefset.data.json
      - .mypy_cache\3.12\_weakrefset.data.json
   💾 冗余大小: 65.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

49. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\__main__.data.json
      - .mypy_cache\3.12\__main__.data.json
   💾 冗余大小: 2.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

50. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\anyio\streams\__init__.data.json
      - .mypy_cache\3.12\anyio\streams\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

51. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\anyio\_core\__init__.data.json
      - .mypy_cache\3.12\anyio\_core\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

52. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\app\__init__.data.json
      - .mypy_cache\3.12\app\__init__.data.json
   💾 冗余大小: 2.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

53. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\app\api\__init__.data.json
      - .mypy_cache\3.12\app\api\__init__.data.json
   💾 冗余大小: 1.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

54. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\app\core\__init__.data.json
      - .mypy_cache\3.12\app\core\__init__.data.json
   💾 冗余大小: 1.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

55. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\app\core\auth\__init__.data.json
      - .mypy_cache\3.12\app\core\auth\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

56. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\app\models\__init__.data.json
      - .mypy_cache\3.12\app\models\__init__.data.json
   💾 冗余大小: 1.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

57. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\app\schemas\__init__.data.json
      - .mypy_cache\3.12\app\schemas\__init__.data.json
   💾 冗余大小: 1.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

58. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\app\services\session_summary.data.json
      - .mypy_cache\3.12\app\services\session_summary.data.json
   💾 冗余大小: 1.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

59. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\app\services\__init__.data.json
      - .mypy_cache\3.12\app\services\__init__.data.json
   💾 冗余大小: 1.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

60. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\app\utils\__init__.data.json
      - .mypy_cache\3.12\app\utils\__init__.data.json
   💾 冗余大小: 1.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

61. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\argcomplete\exceptions.data.json
      - .mypy_cache\3.12\argcomplete\exceptions.data.json
   💾 冗余大小: 2.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

62. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\argcomplete\packages\__init__.data.json
      - .mypy_cache\3.12\argcomplete\packages\__init__.data.json
   💾 冗余大小: 1.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

63. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\asyncio\coroutines.data.json
      - .mypy_cache\3.12\asyncio\coroutines.data.json
   💾 冗余大小: 38.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

64. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\asyncio\futures.data.json
      - .mypy_cache\3.12\asyncio\futures.data.json
   💾 冗余大小: 43.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

65. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\asyncio\threads.data.json
      - .mypy_cache\3.12\asyncio\threads.data.json
   💾 冗余大小: 7.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

66. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\asyncio\timeouts.data.json
      - .mypy_cache\3.12\asyncio\timeouts.data.json
   💾 冗余大小: 10.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

67. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\asyncio\unix_events.data.json
      - .mypy_cache\3.12\asyncio\unix_events.data.json
   💾 冗余大小: 20.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

68. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\asyncio\windows_utils.data.json
      - .mypy_cache\3.12\asyncio\windows_utils.data.json
   💾 冗余大小: 20.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

69. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\attr\converters.data.json
      - .mypy_cache\3.12\attr\converters.data.json
   💾 冗余大小: 12.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

70. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\attr\exceptions.data.json
      - .mypy_cache\3.12\attr\exceptions.data.json
   💾 冗余大小: 12.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

71. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\attr\filters.data.json
      - .mypy_cache\3.12\attr\filters.data.json
   💾 冗余大小: 3.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

72. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\attr\setters.data.json
      - .mypy_cache\3.12\attr\setters.data.json
   💾 冗余大小: 8.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

73. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\attr\_typing_compat.data.json
      - .mypy_cache\3.12\attr\_typing_compat.data.json
   💾 冗余大小: 3.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

74. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\attr\_version_info.data.json
      - .mypy_cache\3.12\attr\_version_info.data.json
   💾 冗余大小: 7.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

75. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\attr\__init__.data.json
      - .mypy_cache\3.12\attr\__init__.data.json
   💾 冗余大小: 242.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

76. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\certifi\__init__.data.json
      - .mypy_cache\3.12\certifi\__init__.data.json
   💾 冗余大小: 2.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

77. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\concurrent\__init__.data.json
      - .mypy_cache\3.12\concurrent\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

78. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\concurrent\futures\__init__.data.json
      - .mypy_cache\3.12\concurrent\futures\__init__.data.json
   💾 冗余大小: 4.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

79. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\__init__.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\__init__.data.json
   💾 冗余大小: 1.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

80. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\openssl\__init__.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\openssl\__init__.data.json
   💾 冗余大小: 1.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

81. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\asn1.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\asn1.data.json
   💾 冗余大小: 4.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

82. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\exceptions.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\exceptions.data.json
   💾 冗余大小: 6.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

83. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\_openssl.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\_openssl.data.json
   💾 冗余大小: 2.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

84. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\__init__.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\__init__.data.json
   💾 冗余大小: 11.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

85. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\aead.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\aead.data.json
   💾 冗余大小: 31.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

86. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\ciphers.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\ciphers.data.json
   💾 冗余大小: 17.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

87. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\cmac.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\cmac.data.json
   💾 冗余大小: 6.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

88. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\dh.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\dh.data.json
   💾 冗余大小: 27.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

89. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\dsa.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\dsa.data.json
   💾 冗余大小: 25.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

90. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\ec.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\ec.data.json
   💾 冗余大小: 23.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

91. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\ed25519.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\ed25519.data.json
   💾 冗余大小: 6.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

92. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\ed448.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\ed448.data.json
   💾 冗余大小: 6.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

93. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\hashes.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\hashes.data.json
   💾 冗余大小: 7.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

94. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\hmac.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\hmac.data.json
   💾 冗余大小: 8.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

95. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\kdf.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\kdf.data.json
   💾 冗余大小: 3.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

96. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\keys.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\keys.data.json
   💾 冗余大小: 6.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

97. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\poly1305.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\poly1305.data.json
   💾 冗余大小: 8.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

98. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\rsa.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\rsa.data.json
   💾 冗余大小: 23.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

99. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\x25519.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\x25519.data.json
   💾 冗余大小: 6.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

100. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\x448.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\x448.data.json
   💾 冗余大小: 6.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

101. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\__init__.data.json
      - .mypy_cache\3.12\cryptography\hazmat\bindings\_rust\openssl\__init__.data.json
   💾 冗余大小: 16.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

102. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\primitives\__init__.data.json
      - .mypy_cache\3.12\cryptography\hazmat\primitives\__init__.data.json
   💾 冗余大小: 1.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

103. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\cryptography\hazmat\primitives\asymmetric\__init__.data.json
      - .mypy_cache\3.12\cryptography\hazmat\primitives\asymmetric\__init__.data.json
   💾 冗余大小: 2.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

104. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\ctypes\wintypes.data.json
      - .mypy_cache\3.12\ctypes\wintypes.data.json
   💾 冗余大小: 81.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

105. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\email\charset.data.json
      - .mypy_cache\3.12\email\charset.data.json
   💾 冗余大小: 16.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

106. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\email\contentmanager.data.json
      - .mypy_cache\3.12\email\contentmanager.data.json
   💾 冗余大小: 7.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

107. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\email\errors.data.json
      - .mypy_cache\3.12\email\errors.data.json
   💾 冗余大小: 24.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

108. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\email\header.data.json
      - .mypy_cache\3.12\email\header.data.json
   💾 冗余大小: 9.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

109. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\email\message.data.json
      - .mypy_cache\3.12\email\message.data.json
   💾 冗余大小: 192.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

110. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\email\policy.data.json
      - .mypy_cache\3.12\email\policy.data.json
   💾 冗余大小: 12.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

111. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\email\_policybase.data.json
      - .mypy_cache\3.12\email\_policybase.data.json
   💾 冗余大小: 26.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

112. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\email\__init__.data.json
      - .mypy_cache\3.12\email\__init__.data.json
   💾 冗余大小: 8.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

113. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\h11\_abnf.data.json
      - .mypy_cache\3.12\h11\_abnf.data.json
   💾 冗余大小: 5.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

114. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\h11\_version.data.json
      - .mypy_cache\3.12\h11\_version.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

115. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\html\entities.data.json
      - .mypy_cache\3.12\html\entities.data.json
   💾 冗余大小: 3.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

116. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\html\__init__.data.json
      - .mypy_cache\3.12\html\__init__.data.json
   💾 冗余大小: 5.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

117. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\http\cookiejar.data.json
      - .mypy_cache\3.12\http\cookiejar.data.json
   💾 冗余大小: 64.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

118. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\httpcore\__init__.data.json
      - .mypy_cache\3.12\httpcore\__init__.data.json
   💾 冗余大小: 8.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

119. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\httpcore\_async\__init__.data.json
      - .mypy_cache\3.12\httpcore\_async\__init__.data.json
   💾 冗余大小: 3.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

120. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\httpcore\_backends\__init__.data.json
      - .mypy_cache\3.12\httpcore\_backends\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

121. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\httpcore\_sync\__init__.data.json
      - .mypy_cache\3.12\httpcore\_sync\__init__.data.json
   💾 冗余大小: 2.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

122. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\httpx\__init__.data.json
      - .mypy_cache\3.12\httpx\__init__.data.json
   💾 冗余大小: 9.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

123. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\httpx\__version__.data.json
      - .mypy_cache\3.12\httpx\__version__.data.json
   💾 冗余大小: 2.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

124. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\httpx\_transports\__init__.data.json
      - .mypy_cache\3.12\httpx\_transports\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

125. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\idna\idnadata.data.json
      - .mypy_cache\3.12\idna\idnadata.data.json
   💾 冗余大小: 2.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

126. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\idna\package_data.data.json
      - .mypy_cache\3.12\idna\package_data.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

127. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\importlib\resources\abc.data.json
      - .mypy_cache\3.12\importlib\resources\abc.data.json
   💾 冗余大小: 2.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

128. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\display.data.json
      - .mypy_cache\3.12\IPython\display.data.json
   💾 冗余大小: 5.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

129. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\core\error.data.json
      - .mypy_cache\3.12\IPython\core\error.data.json
   💾 冗余大小: 6.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

130. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\core\getipython.data.json
      - .mypy_cache\3.12\IPython\core\getipython.data.json
   💾 冗余大小: 1.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

131. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\core\latex_symbols.data.json
      - .mypy_cache\3.12\IPython\core\latex_symbols.data.json
   💾 冗余大小: 2.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

132. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\core\release.data.json
      - .mypy_cache\3.12\IPython\core\release.data.json
   💾 冗余大小: 5.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

133. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\core\__init__.data.json
      - .mypy_cache\3.12\IPython\core\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

134. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\extensions\__init__.data.json
      - .mypy_cache\3.12\IPython\extensions\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

135. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\lib\__init__.data.json
      - .mypy_cache\3.12\IPython\lib\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

136. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\terminal\__init__.data.json
      - .mypy_cache\3.12\IPython\terminal\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

137. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\testing\skipdoctest.data.json
      - .mypy_cache\3.12\IPython\testing\skipdoctest.data.json
   💾 冗余大小: 1.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

138. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\utils\data.data.json
      - .mypy_cache\3.12\IPython\utils\data.data.json
   💾 冗余大小: 2.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

139. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\utils\importstring.data.json
      - .mypy_cache\3.12\IPython\utils\importstring.data.json
   💾 冗余大小: 1.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

140. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\utils\sentinel.data.json
      - .mypy_cache\3.12\IPython\utils\sentinel.data.json
   💾 冗余大小: 3.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

141. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\utils\_sysinfo.data.json
      - .mypy_cache\3.12\IPython\utils\_sysinfo.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

142. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\IPython\utils\__init__.data.json
      - .mypy_cache\3.12\IPython\utils\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

143. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\json\__init__.data.json
      - .mypy_cache\3.12\json\__init__.data.json
   💾 冗余大小: 16.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

144. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\jwt\exceptions.data.json
      - .mypy_cache\3.12\jwt\exceptions.data.json
   💾 冗余大小: 20.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

145. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\jwt\warnings.data.json
      - .mypy_cache\3.12\jwt\warnings.data.json
   💾 冗余大小: 2.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

146. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\jwt\__init__.data.json
      - .mypy_cache\3.12\jwt\__init__.data.json
   💾 冗余大小: 6.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

147. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\logging\config.data.json
      - .mypy_cache\3.12\logging\config.data.json
   💾 冗余大小: 55.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

148. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\markdown_it\__init__.data.json
      - .mypy_cache\3.12\markdown_it\__init__.data.json
   💾 冗余大小: 2.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

149. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\markdown_it\common\html_blocks.data.json
      - .mypy_cache\3.12\markdown_it\common\html_blocks.data.json
   💾 冗余大小: 1.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

150. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\markdown_it\common\__init__.data.json
      - .mypy_cache\3.12\markdown_it\common\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

151. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\markdown_it\helpers\__init__.data.json
      - .mypy_cache\3.12\markdown_it\helpers\__init__.data.json
   💾 冗余大小: 2.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

152. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\markdown_it\presets\commonmark.data.json
      - .mypy_cache\3.12\markdown_it\presets\commonmark.data.json
   💾 冗余大小: 1.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

153. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\markdown_it\presets\default.data.json
      - .mypy_cache\3.12\markdown_it\presets\default.data.json
   💾 冗余大小: 1.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

154. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\markdown_it\presets\zero.data.json
      - .mypy_cache\3.12\markdown_it\presets\zero.data.json
   💾 冗余大小: 1.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

155. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\markdown_it\presets\__init__.data.json
      - .mypy_cache\3.12\markdown_it\presets\__init__.data.json
   💾 冗余大小: 4.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

156. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\markdown_it\rules_block\__init__.data.json
      - .mypy_cache\3.12\markdown_it\rules_block\__init__.data.json
   💾 冗余大小: 3.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

157. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\markdown_it\rules_core\__init__.data.json
      - .mypy_cache\3.12\markdown_it\rules_core\__init__.data.json
   💾 冗余大小: 3.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

158. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\markdown_it\rules_inline\__init__.data.json
      - .mypy_cache\3.12\markdown_it\rules_inline\__init__.data.json
   💾 冗余大小: 4.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

159. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\mdurl\__init__.data.json
      - .mypy_cache\3.12\mdurl\__init__.data.json
   💾 冗余大小: 3.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

160. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\multiprocessing\popen_fork.data.json
      - .mypy_cache\3.12\multiprocessing\popen_fork.data.json
   💾 冗余大小: 2.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

161. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\multiprocessing\popen_forkserver.data.json
      - .mypy_cache\3.12\multiprocessing\popen_forkserver.data.json
   💾 冗余大小: 2.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

162. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\multiprocessing\popen_spawn_posix.data.json
      - .mypy_cache\3.12\multiprocessing\popen_spawn_posix.data.json
   💾 冗余大小: 2.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

163. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\multiprocessing\spawn.data.json
      - .mypy_cache\3.12\multiprocessing\spawn.data.json
   💾 冗余大小: 10.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

164. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\nbformat\corpus\__init__.data.json
      - .mypy_cache\3.12\nbformat\corpus\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

165. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\ctypeslib.data.json
      - .mypy_cache\3.12\numpy\ctypeslib.data.json
   💾 冗余大小: 162.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

166. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\dtypes.data.json
      - .mypy_cache\3.12\numpy\dtypes.data.json
   💾 冗余大小: 14.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

167. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\exceptions.data.json
      - .mypy_cache\3.12\numpy\exceptions.data.json
   💾 冗余大小: 14.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

168. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\version.data.json
      - .mypy_cache\3.12\numpy\version.data.json
   💾 冗余大小: 2.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

169. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\_pytesttester.data.json
      - .mypy_cache\3.12\numpy\_pytesttester.data.json
   💾 冗余大小: 5.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

170. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\core\arrayprint.data.json
      - .mypy_cache\3.12\numpy\core\arrayprint.data.json
   💾 冗余大小: 30.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

171. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\core\defchararray.data.json
      - .mypy_cache\3.12\numpy\core\defchararray.data.json
   💾 冗余大小: 235.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

172. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\core\einsumfunc.data.json
      - .mypy_cache\3.12\numpy\core\einsumfunc.data.json
   💾 冗余大小: 81.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

173. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\core\fromnumeric.data.json
      - .mypy_cache\3.12\numpy\core\fromnumeric.data.json
   💾 冗余大小: 559.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

174. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\core\function_base.data.json
      - .mypy_cache\3.12\numpy\core\function_base.data.json
   💾 冗余大小: 74.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

175. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\core\numeric.data.json
      - .mypy_cache\3.12\numpy\core\numeric.data.json
   💾 冗余大小: 295.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

176. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\core\numerictypes.data.json
      - .mypy_cache\3.12\numpy\core\numerictypes.data.json
   💾 冗余大小: 63.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

177. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\core\shape_base.data.json
      - .mypy_cache\3.12\numpy\core\shape_base.data.json
   💾 冗余大小: 86.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

178. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\core\_asarray.data.json
      - .mypy_cache\3.12\numpy\core\_asarray.data.json
   💾 冗余大小: 19.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

179. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\core\_internal.data.json
      - .mypy_cache\3.12\numpy\core\_internal.data.json
   💾 冗余大小: 30.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

180. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\core\_type_aliases.data.json
      - .mypy_cache\3.12\numpy\core\_type_aliases.data.json
   💾 冗余大小: 5.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

181. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\core\_ufunc_config.data.json
      - .mypy_cache\3.12\numpy\core\_ufunc_config.data.json
   💾 冗余大小: 11.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

182. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\core\__init__.data.json
      - .mypy_cache\3.12\numpy\core\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

183. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\fft\helper.data.json
      - .mypy_cache\3.12\numpy\fft\helper.data.json
   💾 冗余大小: 32.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

184. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\fft\_pocketfft.data.json
      - .mypy_cache\3.12\numpy\fft\_pocketfft.data.json
   💾 冗余大小: 20.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

185. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\fft\__init__.data.json
      - .mypy_cache\3.12\numpy\fft\__init__.data.json
   💾 冗余大小: 4.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

186. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\arraypad.data.json
      - .mypy_cache\3.12\numpy\lib\arraypad.data.json
   💾 冗余大小: 26.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

187. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\arraysetops.data.json
      - .mypy_cache\3.12\numpy\lib\arraysetops.data.json
   💾 冗余大小: 238.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

188. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\arrayterator.data.json
      - .mypy_cache\3.12\numpy\lib\arrayterator.data.json
   💾 冗余大小: 35.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

189. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\format.data.json
      - .mypy_cache\3.12\numpy\lib\format.data.json
   💾 冗余大小: 7.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

190. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\function_base.data.json
      - .mypy_cache\3.12\numpy\lib\function_base.data.json
   💾 冗余大小: 328.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

191. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\histograms.data.json
      - .mypy_cache\3.12\numpy\lib\histograms.data.json
   💾 冗余大小: 9.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

192. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\index_tricks.data.json
      - .mypy_cache\3.12\numpy\lib\index_tricks.data.json
   💾 冗余大小: 90.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

193. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\mixins.data.json
      - .mypy_cache\3.12\numpy\lib\mixins.data.json
   💾 冗余大小: 49.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

194. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\nanfunctions.data.json
      - .mypy_cache\3.12\numpy\lib\nanfunctions.data.json
   💾 冗余大小: 101.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

195. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\npyio.data.json
      - .mypy_cache\3.12\numpy\lib\npyio.data.json
   💾 冗余大小: 145.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

196. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\polynomial.data.json
      - .mypy_cache\3.12\numpy\lib\polynomial.data.json
   💾 冗余大小: 136.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

197. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\scimath.data.json
      - .mypy_cache\3.12\numpy\lib\scimath.data.json
   💾 冗余大小: 88.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

198. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\shape_base.data.json
      - .mypy_cache\3.12\numpy\lib\shape_base.data.json
   💾 冗余大小: 145.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

199. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\stride_tricks.data.json
      - .mypy_cache\3.12\numpy\lib\stride_tricks.data.json
   💾 冗余大小: 35.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

200. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\twodim_base.data.json
      - .mypy_cache\3.12\numpy\lib\twodim_base.data.json
   💾 冗余大小: 116.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

201. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\type_check.data.json
      - .mypy_cache\3.12\numpy\lib\type_check.data.json
   💾 冗余大小: 154.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

202. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\ufunclike.data.json
      - .mypy_cache\3.12\numpy\lib\ufunclike.data.json
   💾 冗余大小: 37.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

203. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\utils.data.json
      - .mypy_cache\3.12\numpy\lib\utils.data.json
   💾 冗余大小: 33.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

204. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\_version.data.json
      - .mypy_cache\3.12\numpy\lib\_version.data.json
   💾 冗余大小: 9.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

205. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\lib\__init__.data.json
      - .mypy_cache\3.12\numpy\lib\__init__.data.json
   💾 冗余大小: 23.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

206. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\linalg\linalg.data.json
      - .mypy_cache\3.12\numpy\linalg\linalg.data.json
   💾 冗余大小: 246.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

207. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\linalg\__init__.data.json
      - .mypy_cache\3.12\numpy\linalg\__init__.data.json
   💾 冗余大小: 5.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

208. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\ma\core.data.json
      - .mypy_cache\3.12\numpy\ma\core.data.json
   💾 冗余大小: 144.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

209. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\ma\extras.data.json
      - .mypy_cache\3.12\numpy\ma\extras.data.json
   💾 冗余大小: 24.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

210. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\ma\mrecords.data.json
      - .mypy_cache\3.12\numpy\ma\mrecords.data.json
   💾 冗余大小: 16.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

211. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\ma\__init__.data.json
      - .mypy_cache\3.12\numpy\ma\__init__.data.json
   💾 冗余大小: 27.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

212. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\matrixlib\defmatrix.data.json
      - .mypy_cache\3.12\numpy\matrixlib\defmatrix.data.json
   💾 冗余大小: 6.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

213. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\matrixlib\__init__.data.json
      - .mypy_cache\3.12\numpy\matrixlib\__init__.data.json
   💾 冗余大小: 2.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

214. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\polynomial\chebyshev.data.json
      - .mypy_cache\3.12\numpy\polynomial\chebyshev.data.json
   💾 冗余大小: 16.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

215. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\polynomial\hermite.data.json
      - .mypy_cache\3.12\numpy\polynomial\hermite.data.json
   💾 冗余大小: 14.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

216. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\polynomial\hermite_e.data.json
      - .mypy_cache\3.12\numpy\polynomial\hermite_e.data.json
   💾 冗余大小: 14.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

217. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\polynomial\laguerre.data.json
      - .mypy_cache\3.12\numpy\polynomial\laguerre.data.json
   💾 冗余大小: 14.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

218. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\polynomial\legendre.data.json
      - .mypy_cache\3.12\numpy\polynomial\legendre.data.json
   💾 冗余大小: 14.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

219. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\polynomial\polynomial.data.json
      - .mypy_cache\3.12\numpy\polynomial\polynomial.data.json
   💾 冗余大小: 13.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

220. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\polynomial\polyutils.data.json
      - .mypy_cache\3.12\numpy\polynomial\polyutils.data.json
   💾 冗余大小: 5.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

221. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\polynomial\_polybase.data.json
      - .mypy_cache\3.12\numpy\polynomial\_polybase.data.json
   💾 冗余大小: 27.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

222. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\polynomial\__init__.data.json
      - .mypy_cache\3.12\numpy\polynomial\__init__.data.json
   💾 冗余大小: 4.2 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

223. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\random\bit_generator.data.json
      - .mypy_cache\3.12\numpy\random\bit_generator.data.json
   💾 冗余大小: 79.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

224. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\random\mtrand.data.json
      - .mypy_cache\3.12\numpy\random\mtrand.data.json
   💾 冗余大小: 430.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

225. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\random\_generator.data.json
      - .mypy_cache\3.12\numpy\random\_generator.data.json
   💾 冗余大小: 374.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

226. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\random\_mt19937.data.json
      - .mypy_cache\3.12\numpy\random\_mt19937.data.json
   💾 冗余大小: 11.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

227. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\random\_pcg64.data.json
      - .mypy_cache\3.12\numpy\random\_pcg64.data.json
   💾 冗余大小: 16.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

228. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\random\_philox.data.json
      - .mypy_cache\3.12\numpy\random\_philox.data.json
   💾 冗余大小: 12.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

229. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\random\_sfc64.data.json
      - .mypy_cache\3.12\numpy\random\_sfc64.data.json
   💾 冗余大小: 9.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

230. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\random\__init__.data.json
      - .mypy_cache\3.12\numpy\random\__init__.data.json
   💾 冗余大小: 9.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

231. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\testing\__init__.data.json
      - .mypy_cache\3.12\numpy\testing\__init__.data.json
   💾 冗余大小: 7.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

232. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\testing\_private\__init__.data.json
      - .mypy_cache\3.12\numpy\testing\_private\__init__.data.json
   💾 冗余大小: 1.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

233. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\_typing\_ufunc.data.json
      - .mypy_cache\3.12\numpy\_typing\_ufunc.data.json
   💾 冗余大小: 358.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

234. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\numpy\_utils\_convertions.data.json
      - .mypy_cache\3.12\numpy\_utils\_convertions.data.json
   💾 冗余大小: 2.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

235. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\prompt_toolkit\key_binding\bindings\__init__.data.json
      - .mypy_cache\3.12\prompt_toolkit\key_binding\bindings\__init__.data.json
   💾 冗余大小: 1.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

236. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\pydantic_settings\version.data.json
      - .mypy_cache\3.12\pydantic_settings\version.data.json
   💾 冗余大小: 1.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

237. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\pyexpat\errors.data.json
      - .mypy_cache\3.12\pyexpat\errors.data.json
   💾 冗余大小: 11.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

238. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\pyexpat\model.data.json
      - .mypy_cache\3.12\pyexpat\model.data.json
   💾 冗余大小: 3.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

239. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\rich\errors.data.json
      - .mypy_cache\3.12\rich\errors.data.json
   💾 冗余大小: 10.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

240. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\rich\_cell_widths.data.json
      - .mypy_cache\3.12\rich\_cell_widths.data.json
   💾 冗余大小: 2.0 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

241. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\rich\_emoji_codes.data.json
      - .mypy_cache\3.12\rich\_emoji_codes.data.json
   💾 冗余大小: 1.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

242. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\rich\_export_format.data.json
      - .mypy_cache\3.12\rich\_export_format.data.json
   💾 冗余大小: 2.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

243. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\rich\_spinners.data.json
      - .mypy_cache\3.12\rich\_spinners.data.json
   💾 冗余大小: 1.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

244. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\sniffio\_version.data.json
      - .mypy_cache\3.12\sniffio\_version.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

245. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\tomli\__init__.data.json
      - .mypy_cache\3.12\tomli\__init__.data.json
   💾 冗余大小: 2.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

246. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\unittest\async_case.data.json
      - .mypy_cache\3.12\unittest\async_case.data.json
   💾 冗余大小: 12.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

247. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\unittest\signals.data.json
      - .mypy_cache\3.12\unittest\signals.data.json
   💾 冗余大小: 18.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

248. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\unittest\suite.data.json
      - .mypy_cache\3.12\unittest\suite.data.json
   💾 冗余大小: 12.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

249. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\unittest\_log.data.json
      - .mypy_cache\3.12\unittest\_log.data.json
   💾 冗余大小: 32.4 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

250. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\urllib\error.data.json
      - .mypy_cache\3.12\urllib\error.data.json
   💾 冗余大小: 13.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

251. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\urllib\__init__.data.json
      - .mypy_cache\3.12\urllib\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

252. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\wsgiref\types.data.json
      - .mypy_cache\3.12\wsgiref\types.data.json
   💾 冗余大小: 16.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

253. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\wsgiref\__init__.data.json
      - .mypy_cache\3.12\wsgiref\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

254. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\xml\__init__.data.json
      - .mypy_cache\3.12\xml\__init__.data.json
   💾 冗余大小: 1.7 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

255. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\xml\dom\domreg.data.json
      - .mypy_cache\3.12\xml\dom\domreg.data.json
   💾 冗余大小: 5.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

256. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\xml\dom\minicompat.data.json
      - .mypy_cache\3.12\xml\dom\minicompat.data.json
   💾 冗余大小: 16.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

257. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\xml\dom\xmlbuilder.data.json
      - .mypy_cache\3.12\xml\dom\xmlbuilder.data.json
   💾 冗余大小: 36.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

258. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\xml\dom\__init__.data.json
      - .mypy_cache\3.12\xml\dom\__init__.data.json
   💾 冗余大小: 29.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

259. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\xml\parsers\__init__.data.json
      - .mypy_cache\3.12\xml\parsers\__init__.data.json
   💾 冗余大小: 1.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

260. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\xml\parsers\expat\__init__.data.json
      - .mypy_cache\3.12\xml\parsers\expat\__init__.data.json
   💾 冗余大小: 3.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

261. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\xml\sax\handler.data.json
      - .mypy_cache\3.12\xml\sax\handler.data.json
   💾 冗余大小: 26.8 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

262. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\xml\sax\xmlreader.data.json
      - .mypy_cache\3.12\xml\sax\xmlreader.data.json
   💾 冗余大小: 53.9 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

263. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\xml\sax\_exceptions.data.json
      - .mypy_cache\3.12\xml\sax\_exceptions.data.json
   💾 冗余大小: 13.1 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

264. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\_typeshed\importlib.data.json
      - .mypy_cache\3.12\_typeshed\importlib.data.json
   💾 冗余大小: 7.5 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

265. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\_typeshed\wsgi.data.json
      - .mypy_cache\3.12\_typeshed\wsgi.data.json
   💾 冗余大小: 4.6 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

266. 【文件重复】发现 2 个完全相同的文件
   📄 涉及文件: 2 个
      - .mypy_cache\3.11\_typeshed\xml.data.json
      - .mypy_cache\3.12\_typeshed\xml.data.json
   💾 冗余大小: 5.3 KB
   💡 建议: 保留一个文件，删除其余 1 个重复文件

267. 【文件重复】发现 6 个完全相同的文件
   📄 涉及文件: 6 个
      - app\core\auth\__init__.py
      - app\models\__init__.py
      - app\schemas\__init__.py
      ... 还有 3 个文件
   💾 冗余大小: 0.0 KB
   💡 建议: 保留一个文件，删除其余 5 个重复文件

268. 【重复函数】函数 update_metadata(self, key, value) 在 4 个文件中重复定义
   📄 涉及文件: 4 个
      - app\models\message.py
      - app\models\session.py
      - app\models\tenant.py
      - app\models\user.py
   💾 冗余大小: 0.7 KB
   💡 建议: 将重复函数提取到公共模块中

269. 【重复函数】函数 get_metadata(self, key, default) 在 3 个文件中重复定义
   📄 涉及文件: 3 个
      - app\models\message.py
      - app\models\session.py
      - app\models\tenant.py
   💾 冗余大小: 0.6 KB
   💡 建议: 将重复函数提取到公共模块中

270. 【重复函数】函数 passwords_match(cls, v, values) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - app\schemas\auth.py
      - app\schemas\auth.py
   💾 冗余大小: 0.3 KB
   💡 建议: 将重复函数提取到公共模块中

271. 【重复函数】函数 _handle_exception(self, exception) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 1.1 KB
   💡 建议: 将重复函数提取到公共模块中

272. 【重复函数】函数 mock_db(self) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
   💾 冗余大小: 0.5 KB
   💡 建议: 将重复函数提取到公共模块中

273. 【重复函数】函数 mock_db(self) 在 4 个文件中重复定义
   📄 涉及文件: 4 个
      - tests\unit\test_rbac_service.py
      - tests\unit\test_session_service.py
      - tests\unit\test_tenant_service_improved.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 1.6 KB
   💡 建议: 将重复函数提取到公共模块中

274. 【重复函数】函数 sample_session(self) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.6 KB
   💡 建议: 将重复函数提取到公共模块中

275. 【重复函数】函数 test_service_initialization(self, mock_db) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - tests\unit\test_tenant_service_improved.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.3 KB
   💡 建议: 将重复函数提取到公共模块中

276. 【重复函数】函数 mock_session_service(self) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_coverage_boost_services.py
   💾 冗余大小: 0.1 KB
   💡 建议: 将重复函数提取到公共模块中

277. 【重复函数】函数 message_service(self, mock_db, mock_session_service) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 将重复函数提取到公共模块中

278. 【重复函数】函数 test_service_initialization(self) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - tests\unit\test_agent_suggestion_service.py
      - tests\unit\test_context_manager.py
   💾 冗余大小: 0.1 KB
   💡 建议: 将重复函数提取到公共模块中

279. 【重复函数】函数 test_api_endpoints(self) 在 5 个文件中重复定义
   📄 涉及文件: 5 个
      - tests\unit\test_ai_features.py
      - tests\unit\test_instances.py
      - tests\unit\test_messages.py
      - tests\unit\test_rbac.py
      - tests\unit\test_sessions.py
   💾 冗余大小: 0.2 KB
   💡 建议: 将重复函数提取到公共模块中

280. 【重复函数】函数 test_api_authentication(self) 在 5 个文件中重复定义
   📄 涉及文件: 5 个
      - tests\unit\test_ai_features.py
      - tests\unit\test_instances.py
      - tests\unit\test_messages.py
      - tests\unit\test_rbac.py
      - tests\unit\test_sessions.py
   💾 冗余大小: 0.3 KB
   💡 建议: 将重复函数提取到公共模块中

281. 【重复函数】函数 test_schema_validation(self) 在 3 个文件中重复定义
   📄 涉及文件: 3 个
      - tests\unit\test_analytics.py
      - tests\unit\test_auth.py
      - tests\unit\test_common.py
   💾 冗余大小: 0.1 KB
   💡 建议: 将重复函数提取到公共模块中

282. 【重复函数】函数 test_schema_serialization(self) 在 3 个文件中重复定义
   📄 涉及文件: 3 个
      - tests\unit\test_analytics.py
      - tests\unit\test_auth.py
      - tests\unit\test_common.py
   💾 冗余大小: 0.2 KB
   💡 建议: 将重复函数提取到公共模块中

283. 【重复函数】函数 client(self) 在 9 个文件中重复定义
   📄 涉及文件: 9 个
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_api_endpoints.py
      ... 还有 6 个文件
   💾 冗余大小: 0.5 KB
   💡 建议: 将重复函数提取到公共模块中

284. 【重复函数】函数 auth_headers(self) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_api_endpoints.py
   💾 冗余大小: 0.3 KB
   💡 建议: 将重复函数提取到公共模块中

285. 【重复函数】函数 auth_headers(self) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_api_endpoints.py
   💾 冗余大小: 0.2 KB
   💡 建议: 将重复函数提取到公共模块中

286. 【重复函数】函数 auth_headers(self) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_api_endpoints.py
   💾 冗余大小: 0.2 KB
   💡 建议: 将重复函数提取到公共模块中

287. 【重复函数】函数 tenant_service(self, mock_db) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - tests\unit\test_tenant_service_improved.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.2 KB
   💡 建议: 将重复函数提取到公共模块中

288. 【重复函数】函数 test_model_creation(self) 在 3 个文件中重复定义
   📄 涉及文件: 3 个
      - tests\unit\test_role.py
      - tests\unit\test_session.py
      - tests\unit\test_user.py
   💾 冗余大小: 0.1 KB
   💡 建议: 将重复函数提取到公共模块中

289. 【重复函数】函数 test_model_validation(self) 在 3 个文件中重复定义
   📄 涉及文件: 3 个
      - tests\unit\test_role.py
      - tests\unit\test_session.py
      - tests\unit\test_user.py
   💾 冗余大小: 0.1 KB
   💡 建议: 将重复函数提取到公共模块中

290. 【重复函数】函数 mock_db_session(self) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.8 KB
   💡 建议: 将重复函数提取到公共模块中

291. 【重复函数】函数 mock_db_session(self) 在 3 个文件中重复定义
   📄 涉及文件: 3 个
      - tests\unit\_test_high_value_services.py
      - tests\unit\_test_high_value_services.py
      - tests\unit\_test_high_value_services.py
   💾 冗余大小: 0.2 KB
   💡 建议: 将重复函数提取到公共模块中

292. 【重复函数】函数 auth_service(self, mock_db_session) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - tests\unit\test_services_realistic.py
      - tests\unit\_test_high_value_services.py
   💾 冗余大小: 0.2 KB
   💡 建议: 将重复函数提取到公共模块中

293. 【重复函数】函数 sample_tenant_data(self) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - tests\unit\test_tenant_service_improved.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.3 KB
   💡 建议: 将重复函数提取到公共模块中

294. 【重复函数】函数 summary_service(self, mock_db_session, mock_llm_provider) 在 2 个文件中重复定义
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 将重复函数提取到公共模块中

🚨 MEDIUM 级别问题
----------------------------------------
1. 【大文件】文件大小 1.71MB 超过阈值
   📄 涉及文件: 1 个
      - .mypy_cache\3.11\builtins.data.json
   💾 冗余大小: 1746.8 KB
   💡 建议: 检查是否可以拆分或压缩文件

2. 【大文件】文件大小 3.10MB 超过阈值
   📄 涉及文件: 1 个
      - .mypy_cache\3.11\numpy\__init__.data.json
   💾 冗余大小: 3177.8 KB
   💡 建议: 检查是否可以拆分或压缩文件

3. 【大文件】文件大小 1.71MB 超过阈值
   📄 涉及文件: 1 个
      - .mypy_cache\3.12\builtins.data.json
   💾 冗余大小: 1748.5 KB
   💡 建议: 检查是否可以拆分或压缩文件

4. 【大文件】文件大小 3.10MB 超过阈值
   📄 涉及文件: 1 个
      - .mypy_cache\3.12\numpy\__init__.data.json
   💾 冗余大小: 3179.4 KB
   💡 建议: 检查是否可以拆分或压缩文件

5. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - code_redundancy_analyzer.py
      - code_redundancy_analyzer.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

6. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - code_redundancy_analyzer.py
      - code_redundancy_analyzer.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

7. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - code_redundancy_analyzer.py
      - scripts\analyze_warnings.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

8. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - code_redundancy_analyzer.py
      - scripts\analyze_warnings.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

9. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - debug_api.py
      - tests\unit\test_coverage_boost.py
      - tests\unit\test_coverage_boost_services.py
      ... 还有 3 个文件
   💾 冗余大小: 1.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

10. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - debug_api.py
      - tests\conftest.py
      - tests\unit\test_coverage_boost_services.py
      ... 还有 3 个文件
   💾 冗余大小: 1.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

11. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - debug_api.py
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_services_realistic.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

12. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - debug_tenant_auth.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

13. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - app\main.py
      - tests\helpers\mock_services.py
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_api_endpoints.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

14. 【重复代码块】发现 7 个相似的代码块（5+ 行）
   📄 涉及文件: 7 个
      - app\main.py
      - scripts\security_check.py
      - scripts\security_check.py
      ... 还有 4 个文件
   💾 冗余大小: 1.1 KB
   💡 建议: 提取重复代码到公共函数或方法中

15. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\deps.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

16. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\deps.py
      - app\api\deps.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

17. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\deps.py
      - app\api\deps.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

18. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\deps.py
      - app\api\deps.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

19. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - app\api\deps\__init__.py
      - app\api\deps\__init__.py
      - app\api\deps\__init__.py
      - app\api\deps\__init__.py
      - app\core\middleware.py
   💾 冗余大小: 1.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

20. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\deps\__init__.py
      - app\api\deps\__init__.py
      - app\api\deps\__init__.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

21. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\deps\__init__.py
      - app\api\deps\__init__.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

22. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\deps\__init__.py
      - app\api\v1\sessions.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

23. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

24. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

25. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

26. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

27. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

28. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

29. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

30. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

31. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

32. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

33. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

34. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

35. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

36. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

37. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

38. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

39. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

40. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

41. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

42. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

43. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\ai_features.py
      - app\api\v1\sessions.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

44. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\ai_features.py
      - app\api\v1\ai_features.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

45. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\ai_features.py
      - app\api\v1\ai_features.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

46. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\ai_features.py
      - app\api\v1\ai_features.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

47. 【重复代码块】发现 11 个相似的代码块（5+ 行）
   📄 涉及文件: 11 个
      - app\api\v1\ai_features.py
      - app\schemas\user.py
      - app\schemas\user.py
      ... 还有 8 个文件
   💾 冗余大小: 2.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

48. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\ai_features.py
      - tests\unit\test_config.py
      - tests\unit\test_config.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

49. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\analytics.py
      - app\api\v1\analytics.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

50. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

51. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

52. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

53. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

54. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

55. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

56. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

57. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

58. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

59. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

60. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

61. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

62. 【重复代码块】发现 16 个相似的代码块（5+ 行）
   📄 涉及文件: 16 个
      - app\api\v1\instances.py
      - app\api\v1\messages.py
      - app\api\v1\messages.py
      ... 还有 13 个文件
   💾 冗余大小: 1.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

63. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

64. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

65. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

66. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

67. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\instances.py
      - app\api\v1\instances.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

68. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\instances.py
      - app\api\v1\__init__.py
      - tests\e2e\test_business_flows.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

69. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\instances.py
      - app\services\agent_suggestion_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

70. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

71. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

72. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

73. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

74. 【重复代码块】发现 9 个相似的代码块（5+ 行）
   📄 涉及文件: 9 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
      - app\api\v1\rbac.py
      ... 还有 6 个文件
   💾 冗余大小: 1.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

75. 【重复代码块】发现 17 个相似的代码块（5+ 行）
   📄 涉及文件: 17 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
      - app\api\v1\rbac.py
      ... 还有 14 个文件
   💾 冗余大小: 2.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

76. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\services\message_service.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

77. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\services\message_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

78. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\services\message_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

79. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\services\message_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

80. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\services\message_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

81. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

82. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

83. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

84. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

85. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

86. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

87. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

88. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\messages.py
      - app\api\v1\sessions.py
      - app\api\v1\sessions.py
      - app\services\message_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

89. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\messages.py
      - app\api\v1\sessions.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

90. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\messages.py
      - app\api\v1\sessions.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

91. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\messages.py
      - app\api\v1\sessions.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

92. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\messages.py
      - app\api\v1\sessions.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

93. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
      - app\api\v1\messages.py
      - app\api\v1\sessions.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

94. 【重复代码块】发现 15 个相似的代码块（5+ 行）
   📄 涉及文件: 15 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
      - app\api\v1\messages.py
      ... 还有 12 个文件
   💾 冗余大小: 2.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

95. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

96. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\messages.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

97. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\messages.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

98. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\messages.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

99. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

100. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

101. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\messages.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

102. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\messages.py
      - app\api\v1\sessions.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

103. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\rbac.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

104. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\rbac.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

105. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\rbac.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

106. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\rbac.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

107. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

108. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

109. 【重复代码块】发现 7 个相似的代码块（5+ 行）
   📄 涉及文件: 7 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      ... 还有 4 个文件
   💾 冗余大小: 1.1 KB
   💡 建议: 提取重复代码到公共函数或方法中

110. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

111. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

112. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

113. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

114. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

115. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

116. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

117. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

118. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

119. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

120. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

121. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\models\user.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

122. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

123. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\user_roles.py
      ... 还有 3 个文件
   💾 冗余大小: 1.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

124. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\user_roles.py
      ... 还有 3 个文件
   💾 冗余大小: 1.1 KB
   💡 建议: 提取重复代码到公共函数或方法中

125. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

126. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\rbac.py
      - app\models\user.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

127. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

128. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

129. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

130. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

131. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\rbac.py
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

132. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

133. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

134. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

135. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

136. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\rbac.py
      - app\api\v1\rbac.py
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

137. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\rbac.py
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

138. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\sessions.py
      - app\core\middleware.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

139. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\sessions.py
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

140. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

141. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

142. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

143. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

144. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

145. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\sessions.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

146. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\sessions.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

147. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\sessions.py
      - app\api\v1\sessions.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

148. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\sessions.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

149. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

150. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
      ... 还有 3 个文件
   💾 冗余大小: 1.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

151. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
      ... 还有 3 个文件
   💾 冗余大小: 1.1 KB
   💡 建议: 提取重复代码到公共函数或方法中

152. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
      ... 还有 3 个文件
   💾 冗余大小: 1.1 KB
   💡 建议: 提取重复代码到公共函数或方法中

153. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

154. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

155. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

156. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

157. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

158. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

159. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

160. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

161. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

162. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

163. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

164. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

165. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

166. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

167. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

168. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

169. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

170. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\tenants.py
      - app\api\v1\tenants.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

171. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

172. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

173. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

174. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

175. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

176. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

177. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

178. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

179. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

180. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

181. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

182. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

183. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

184. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

185. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

186. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

187. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

188. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\user_roles.py
      - app\api\v1\user_roles.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

189. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

190. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

191. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

192. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

193. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

194. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

195. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

196. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

197. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

198. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

199. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

200. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

201. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

202. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

203. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

204. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

205. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

206. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\webhooks.py
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

207. 【重复代码块】发现 62 个相似的代码块（5+ 行）
   📄 涉及文件: 62 个
      - app\api\v1\webhooks.py
      - app\core\middleware.py
      - app\core\middleware.py
      ... 还有 59 个文件
   💾 冗余大小: 8.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

208. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\websocket.py
      - app\api\v1\websocket.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

209. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\websocket.py
      - app\api\v1\websocket.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

210. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\websocket.py
      - app\api\v1\websocket.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

211. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\websocket.py
      - app\api\v1\websocket.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

212. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\api\v1\websocket.py
      - app\api\v1\websocket.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

213. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\middleware.py
      - app\core\middleware.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

214. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\middleware.py
      - app\core\middleware.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

215. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\middleware.py
      - app\core\middleware.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

216. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\middleware.py
      - app\core\middleware.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

217. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\middleware.py
      - app\core\middleware.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

218. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\permissions.py
      - app\core\permissions.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

219. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - app\core\permissions.py
      - app\core\permissions.py
      - app\core\permissions.py
      - app\core\permissions.py
      - app\core\permissions.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

220. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\permissions.py
      - app\core\permissions.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

221. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\core\permissions.py
      - app\core\permissions.py
      - app\core\permissions.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

222. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\permissions.py
      - app\core\permissions.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

223. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\permissions.py
      - app\core\permissions.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

224. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\permissions.py
      - app\core\permissions.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

225. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\core\permissions.py
      - app\core\permissions.py
      - app\core\permissions.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

226. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\permissions.py
      - app\core\permissions.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

227. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\permissions.py
      - app\core\permissions.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

228. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\permissions.py
      - app\core\permissions.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

229. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\security.py
      - app\core\security.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

230. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\security.py
      - app\core\security.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

231. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\security.py
      - app\core\security.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

232. 【重复代码块】发现 22 个相似的代码块（5+ 行）
   📄 涉及文件: 22 个
      - app\core\config\settings.py
      - app\services\llm\openai_provider.py
      - app\services\session_summary\summary_analyzer.py
      ... 还有 19 个文件
   💾 冗余大小: 3.1 KB
   💡 建议: 提取重复代码到公共函数或方法中

233. 【重复代码块】发现 36 个相似的代码块（5+ 行）
   📄 涉及文件: 36 个
      - app\core\config\settings.py
      - app\core\config\settings.py
      - app\core\config\settings.py
      ... 还有 33 个文件
   💾 冗余大小: 7.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

234. 【重复代码块】发现 7 个相似的代码块（5+ 行）
   📄 涉及文件: 7 个
      - app\core\config\settings.py
      - app\schemas\auth.py
      - app\schemas\auth.py
      ... 还有 4 个文件
   💾 冗余大小: 1.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

235. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\config\settings.py
      - scripts\analyze_api_docs.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

236. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\core\config\settings.py
      - app\services\instance_config_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

237. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\models\message.py
      - app\models\session.py
      - app\models\user.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

238. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\models\message.py
      - app\models\message.py
      - app\models\session.py
      - app\models\user.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

239. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\models\message.py
      - app\models\session.py
      - app\models\tenant.py
      - app\models\user.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

240. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\models\message.py
      - app\models\session.py
      - app\models\tenant.py
      - app\models\user.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

241. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\models\message.py
      - app\models\session.py
      - app\models\tenant.py
      - app\models\user.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

242. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\models\message.py
      - app\models\session.py
      - app\models\tenant.py
      - app\models\user.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

243. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\models\message.py
      - app\models\session.py
      - app\models\tenant.py
      - app\models\user.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

244. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\models\message.py
      - app\models\session.py
      - app\models\tenant.py
      - app\models\user.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

245. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\models\message.py
      - app\models\session.py
      - app\models\tenant.py
      - app\models\user.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

246. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\models\message.py
      - app\models\message.py
      - app\models\message.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

247. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\models\message.py
      - app\models\message.py
      - app\models\message.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

248. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\models\message.py
      - app\models\message.py
      - app\models\message.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

249. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\models\message.py
      - app\models\message.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

250. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\models\session.py
      - app\models\tenant.py
      - app\models\user.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

251. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\models\session.py
      - app\models\tenant.py
      - app\models\user.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

252. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\models\session.py
      - app\models\tenant.py
      - app\models\user.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

253. 【重复代码块】发现 16 个相似的代码块（5+ 行）
   📄 涉及文件: 16 个
      - app\schemas\auth.py
      - app\schemas\auth.py
      - app\schemas\auth.py
      ... 还有 13 个文件
   💾 冗余大小: 2.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

254. 【重复代码块】发现 16 个相似的代码块（5+ 行）
   📄 涉及文件: 16 个
      - app\schemas\auth.py
      - app\schemas\auth.py
      - app\schemas\auth.py
      ... 还有 13 个文件
   💾 冗余大小: 3.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

255. 【重复代码块】发现 10 个相似的代码块（5+ 行）
   📄 涉及文件: 10 个
      - app\schemas\auth.py
      - app\schemas\auth.py
      - app\schemas\auth.py
      ... 还有 7 个文件
   💾 冗余大小: 1.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

256. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\schemas\auth.py
      - app\schemas\auth.py
      - app\schemas\auth.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

257. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\schemas\auth.py
      - app\schemas\auth.py
      - app\schemas\auth.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

258. 【重复代码块】发现 9 个相似的代码块（5+ 行）
   📄 涉及文件: 9 个
      - app\schemas\auth.py
      - app\schemas\auth.py
      - app\schemas\auth.py
      ... 还有 6 个文件
   💾 冗余大小: 1.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

259. 【重复代码块】发现 8 个相似的代码块（5+ 行）
   📄 涉及文件: 8 个
      - app\schemas\auth.py
      - app\schemas\auth.py
      - app\schemas\auth.py
      ... 还有 5 个文件
   💾 冗余大小: 1.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

260. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - app\schemas\auth.py
      - app\schemas\auth.py
      - app\schemas\auth.py
      - app\schemas\common.py
      - app\schemas\user.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

261. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - app\schemas\auth.py
      - app\schemas\auth.py
      - app\schemas\auth.py
      - app\schemas\tenant.py
      - app\schemas\user.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

262. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - app\schemas\auth.py
      - app\schemas\auth.py
      - app\schemas\auth.py
      - app\schemas\tenant.py
      - app\schemas\user.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

263. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\schemas\auth.py
      - app\schemas\auth.py
      - app\schemas\user.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

264. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\auth.py
      - app\schemas\auth.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

265. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\auth.py
      - app\schemas\tenant.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

266. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\auth.py
      - app\schemas\tenant.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

267. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\schemas\common.py
      - app\schemas\tenant.py
      - app\schemas\tenant.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

268. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\schemas\common.py
      - app\schemas\tenant.py
      - app\schemas\tenant.py
      - app\schemas\user.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

269. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\message.py
      - app\schemas\message.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

270. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\message.py
      - app\schemas\message.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

271. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\message.py
      - app\schemas\message.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

272. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\message.py
      - app\schemas\message.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

273. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\message.py
      - app\schemas\message.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

274. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\schemas\tenant.py
      - app\schemas\user.py
      - tests\e2e\test_business_flows.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

275. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\tenant.py
      - app\schemas\user.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

276. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\tenant.py
      - app\schemas\user.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

277. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\tenant.py
      - app\schemas\tenant.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

278. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\schemas\tenant.py
      - app\schemas\user.py
      - app\schemas\user.py
      - app\schemas\user.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

279. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\schemas\tenant.py
      - app\schemas\user.py
      - app\schemas\user.py
      - app\schemas\user.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

280. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\schemas\tenant.py
      - app\schemas\user.py
      - app\schemas\user.py
      - app\schemas\user.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

281. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\schemas\tenant.py
      - app\schemas\user.py
      - app\schemas\user.py
      - app\schemas\user.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

282. 【重复代码块】发现 19 个相似的代码块（5+ 行）
   📄 涉及文件: 19 个
      - app\schemas\tenant.py
      - app\schemas\user.py
      - app\schemas\user.py
      ... 还有 16 个文件
   💾 冗余大小: 3.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

283. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\schemas\tenant.py
      - app\schemas\user.py
      - app\schemas\user.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

284. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\schemas\tenant.py
      - app\schemas\user.py
      - app\schemas\user.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

285. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\schemas\tenant.py
      - app\schemas\user.py
      - app\schemas\user.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

286. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\tenant.py
      - app\schemas\user.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

287. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\tenant.py
      - app\schemas\user.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

288. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\tenant.py
      - app\schemas\user.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

289. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\schemas\tenant.py
      - app\schemas\tenant.py
      - app\schemas\user.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

290. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\schemas\tenant.py
      - app\schemas\tenant.py
      - app\schemas\user.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

291. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\schemas\tenant.py
      - app\schemas\tenant.py
      - app\schemas\user.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

292. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\tenant.py
      - app\schemas\user.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

293. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\tenant.py
      - app\schemas\user.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

294. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\tenant.py
      - scripts\security_check.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

295. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - app\schemas\tenant.py
      - app\schemas\tenant.py
      - scripts\security_check.py
      ... 还有 3 个文件
   💾 冗余大小: 1.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

296. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\user.py
      - scripts\generate_dashboards.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

297. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\user.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

298. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\user.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

299. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\user.py
      - app\schemas\user.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

300. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\user.py
      - app\schemas\user.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

301. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\schemas\user.py
      - tests\unit\test_api_layer_comprehensive.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

302. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\schemas\user.py
      - app\services\agent_suggestion_service.py
      - app\services\instance_config_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

303. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\schemas\user.py
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
      - app\services\instance_config_service.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

304. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\schemas\user.py
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
      - app\services\instance_config_service.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

305. 【重复代码块】发现 8 个相似的代码块（5+ 行）
   📄 涉及文件: 8 个
      - app\schemas\user.py
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
      ... 还有 5 个文件
   💾 冗余大小: 1.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

306. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\schemas\user.py
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
      - app\services\instance_config_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

307. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\auto_reply_service.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

308. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\auto_reply_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

309. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\auto_reply_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

310. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\auto_reply_service.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

311. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\agent_suggestion_service.py
      - app\services\auto_reply_service.py
      - app\services\session_summary_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

312. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

313. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

314. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

315. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

316. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

317. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\services\agent_suggestion_service.py
      - app\services\auto_reply_service.py
      - app\services\context_manager.py
      - app\services\session_summary_service.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

318. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\services\agent_suggestion_service.py
      - app\services\auto_reply_service.py
      - app\services\context_manager.py
      - app\services\session_summary_service.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

319. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\agent_suggestion_service.py
      - app\services\auto_reply_service.py
      - app\services\context_manager.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

320. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\agent_suggestion_service.py
      - app\services\auto_reply_service.py
      - app\services\context_manager.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

321. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

322. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

323. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\services\agent_suggestion_service.py
      - app\services\session_summary_service.py
      - app\services\session_summary_service.py
      - app\services\session_summary_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

324. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\services\agent_suggestion_service.py
      - app\services\session_summary_service.py
      - app\services\session_summary_service.py
      - app\services\session_summary_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

325. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

326. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
      - scripts\generate_dashboards.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

327. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

328. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

329. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\agent_suggestion_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

330. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\services\agent_suggestion_service.py
      - scripts\security_scan.py
      - tests\performance\performance_baseline_test.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

331. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\auto_reply_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

332. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\agent_suggestion_service.py
      - app\services\auto_reply_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

333. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\analytics_service.py
      - app\services\analytics_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

334. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\analytics_service.py
      - app\services\analytics_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

335. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\analytics_service.py
      - app\services\analytics_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

336. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\auth_service.py
      - app\services\auth_service.py
      - app\services\auth_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

337. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\auth_service.py
      - app\services\auth_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

338. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\auth_service.py
      - app\services\auth_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

339. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\auth_service.py
      - app\services\auth_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

340. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\auto_reply_service.py
      - app\services\auto_reply_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

341. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\auto_reply_service.py
      - app\services\auto_reply_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

342. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\auto_reply_service.py
      - app\services\auto_reply_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

343. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\auto_reply_service.py
      - app\services\auto_reply_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

344. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\auto_reply_service.py
      - app\services\auto_reply_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

345. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\auto_reply_service.py
      - app\services\auto_reply_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

346. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\auto_reply_service.py
      - tests\unit\test_auto_reply_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

347. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\auto_reply_service.py
      - tests\unit\test_auto_reply_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

348. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\context_manager.py
      - app\services\llm\base_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

349. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\context_manager.py
      - app\services\llm\base_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

350. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\context_manager.py
      - app\services\llm\base_provider.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

351. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\context_manager.py
      - app\services\llm\base_provider.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

352. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\context_manager.py
      - app\services\llm\base_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

353. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\context_manager.py
      - app\services\llm\base_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

354. 【重复代码块】发现 11 个相似的代码块（5+ 行）
   📄 涉及文件: 11 个
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
      ... 还有 8 个文件
   💾 冗余大小: 2.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

355. 【重复代码块】发现 13 个相似的代码块（5+ 行）
   📄 涉及文件: 13 个
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
      ... 还有 10 个文件
   💾 冗余大小: 2.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

356. 【重复代码块】发现 13 个相似的代码块（5+ 行）
   📄 涉及文件: 13 个
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
      ... 还有 10 个文件
   💾 冗余大小: 2.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

357. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
      ... 还有 3 个文件
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

358. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

359. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

360. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

361. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

362. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

363. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\instance_auth_service.py
      - app\services\instance_config_service.py
      - app\services\instance_config_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

364. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
      - app\services\instance_config_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

365. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\instance_auth_service.py
      - app\services\instance_auth_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

366. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\instance_auth_service.py
      - app\services\instance_config_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

367. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\instance_auth_service.py
      - app\services\instance_config_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

368. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\instance_config_service.py
      - app\services\instance_config_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

369. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

370. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

371. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\message_service.py
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

372. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\message_service.py
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

373. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\message_service.py
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

374. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\message_service.py
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

375. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\message_service.py
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

376. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\message_service.py
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

377. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

378. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

379. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

380. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

381. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

382. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

383. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

384. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\message_service.py
      - app\services\message_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

385. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\message_service.py
      - app\services\message_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

386. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\message_service.py
      - app\services\message_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

387. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

388. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

389. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

390. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

391. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

392. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\message_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

393. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\message_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

394. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\services\message_service.py
      - app\services\session_service.py
      - app\services\session_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

395. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\message_service.py
      - app\services\session_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

396. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\message_service.py
      - app\services\session_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

397. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - app\services\message_service.py
      - app\services\message_service.py
      - app\services\message_service.py
      - app\services\session_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

398. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - app\services\message_service.py
      - app\services\session_service.py
      - app\services\tenant_service.py
      - app\services\tenant_service.py
   💾 冗余大小: 1.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

399. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - app\services\message_service.py
      - app\services\session_service.py
      - app\services\tenant_service.py
      - app\services\tenant_service.py
      - app\services\tenant_service.py
   💾 冗余大小: 1.1 KB
   💡 建议: 提取重复代码到公共函数或方法中

400. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\rbac_service.py
      - app\services\rbac_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

401. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\rbac_service.py
      - app\services\rbac_service.py
      - app\services\rbac_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

402. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\rbac_service.py
      - app\services\rbac_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

403. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\rbac_service.py
      - app\services\rbac_service.py
      - app\services\rbac_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

404. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\rbac_service.py
      - app\services\rbac_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

405. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\rbac_service.py
      - app\services\rbac_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

406. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\rbac_service.py
      - app\services\rbac_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

407. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\rbac_service.py
      - app\services\rbac_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

408. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\rbac_service.py
      - app\services\rbac_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

409. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\rbac_service.py
      - app\services\rbac_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

410. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\rbac_service.py
      - app\services\rbac_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

411. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\rbac_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

412. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\session_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

413. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\session_service.py
      - app\services\session_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

414. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\session_summary_service.py
      - app\services\session_summary_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

415. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\session_summary_service.py
      - app\services\session_summary_service.py
      - app\services\session_summary_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

416. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\session_summary_service.py
      - app\services\session_summary_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

417. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\session_summary_service.py
      - app\services\session_summary_service.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

418. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\tenant_service.py
      - app\services\tenant_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

419. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\tenant_service.py
      - app\services\tenant_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

420. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\tenant_service.py
      - app\services\tenant_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

421. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\tenant_service.py
      - app\services\tenant_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

422. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\webhook_service.py
      - app\services\webhook_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

423. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\webhook_service.py
      - app\services\webhook_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

424. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\llm\base_provider.py
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

425. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

426. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

427. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

428. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

429. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

430. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

431. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

432. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

433. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

434. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

435. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

436. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

437. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

438. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

439. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

440. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

441. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

442. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

443. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

444. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

445. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

446. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

447. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

448. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

449. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

450. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

451. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

452. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

453. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

454. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

455. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

456. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

457. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

458. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

459. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

460. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

461. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

462. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

463. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

464. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

465. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

466. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

467. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

468. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

469. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

470. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

471. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

472. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

473. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

474. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

475. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

476. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

477. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

478. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

479. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\llm\dify_provider.py
      - app\services\llm\openai_provider.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

480. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\session_summary\summary_analyzer.py
      - app\services\session_summary\summary_analyzer.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

481. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\session_summary\summary_analyzer.py
      - app\services\session_summary\summary_analyzer.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

482. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - app\services\session_summary\summary_analyzer.py
      - app\services\session_summary\summary_analyzer.py
      - app\services\session_summary\summary_analyzer.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

483. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\session_summary\summary_analyzer.py
      - app\services\session_summary\summary_analyzer.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

484. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\session_summary\summary_analyzer.py
      - app\services\session_summary\summary_analyzer.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

485. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - app\services\session_summary\summary_analyzer.py
      - app\services\session_summary\summary_analyzer.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

486. 【重复代码块】发现 8 个相似的代码块（5+ 行）
   📄 涉及文件: 8 个
      - scripts\analyze_api_docs.py
      - scripts\analyze_api_docs.py
      - scripts\analyze_warnings.py
      ... 还有 5 个文件
   💾 冗余大小: 1.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

487. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\analyze_api_docs.py
      - scripts\coverage_analysis.py
      - scripts\coverage_analysis.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

488. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_api_docs.py
      - scripts\check_api_docs_simple.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

489. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_api_docs.py
      - scripts\check_api_docs_simple.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

490. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_api_docs.py
      - scripts\check_api_docs_simple.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

491. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_api_docs.py
      - scripts\check_api_docs_simple.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

492. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_api_docs.py
      - scripts\check_api_docs_simple.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

493. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

494. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

495. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

496. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

497. 【重复代码块】发现 7 个相似的代码块（5+ 行）
   📄 涉及文件: 7 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      ... 还有 4 个文件
   💾 冗余大小: 1.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

498. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

499. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

500. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

501. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

502. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

503. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

504. 【重复代码块】发现 10 个相似的代码块（5+ 行）
   📄 涉及文件: 10 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      ... 还有 7 个文件
   💾 冗余大小: 1.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

505. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

506. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

507. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

508. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

509. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

510. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

511. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

512. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

513. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

514. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

515. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

516. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

517. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

518. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_warnings.py
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

519. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\analyze_warnings.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

520. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\check_api_docs_simple.py
      - scripts\comprehensive_api_check.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

521. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

522. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

523. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

524. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

525. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

526. 【重复代码块】发现 7 个相似的代码块（5+ 行）
   📄 涉及文件: 7 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      ... 还有 4 个文件
   💾 冗余大小: 1.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

527. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

528. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

529. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

530. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

531. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

532. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

533. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

534. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      ... 还有 3 个文件
   💾 冗余大小: 1.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

535. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

536. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

537. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

538. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

539. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

540. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

541. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

542. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

543. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

544. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

545. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

546. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

547. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

548. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\code_quality_check.py
      - scripts\code_quality_check.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

549. 【重复代码块】发现 7 个相似的代码块（5+ 行）
   📄 涉及文件: 7 个
      - scripts\comprehensive_api_check.py
      - scripts\comprehensive_api_check.py
      - scripts\comprehensive_api_check.py
      ... 还有 4 个文件
   💾 冗余大小: 1.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

550. 【重复代码块】发现 7 个相似的代码块（5+ 行）
   📄 涉及文件: 7 个
      - scripts\comprehensive_api_check.py
      - scripts\comprehensive_api_check.py
      - scripts\comprehensive_api_check.py
      ... 还有 4 个文件
   💾 冗余大小: 1.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

551. 【重复代码块】发现 7 个相似的代码块（5+ 行）
   📄 涉及文件: 7 个
      - scripts\comprehensive_api_check.py
      - scripts\comprehensive_api_check.py
      - scripts\comprehensive_api_check.py
      ... 还有 4 个文件
   💾 冗余大小: 1.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

552. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\coverage_analysis.py
      - scripts\coverage_analysis.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

553. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\coverage_analysis.py
      - scripts\coverage_analysis.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

554. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - scripts\coverage_analysis.py
      - scripts\coverage_analysis.py
      - scripts\coverage_analysis.py
      ... 还有 3 个文件
   💾 冗余大小: 2.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

555. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\coverage_analysis.py
      - scripts\coverage_analysis.py
      - scripts\coverage_analysis.py
      - scripts\coverage_analysis.py
   💾 冗余大小: 1.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

556. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

557. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

558. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

559. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

560. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

561. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

562. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

563. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

564. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

565. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

566. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

567. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

568. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

569. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

570. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

571. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

572. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

573. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

574. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

575. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

576. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

577. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

578. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_integration_tests.py
      - tests\conftest.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

579. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_type_annotations.py
      - scripts\optimize_async_functions.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

580. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_type_annotations.py
      - scripts\optimize_async_functions.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

581. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\fix_type_annotations.py
      - scripts\optimize_async_functions.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

582. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\fix_type_annotations.py
      - scripts\fix_type_annotations.py
      - scripts\fix_type_annotations.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

583. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

584. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 1.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

585. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

586. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

587. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

588. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

589. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

590. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      ... 还有 3 个文件
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

591. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      ... 还有 3 个文件
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

592. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

593. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 1.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

594. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

595. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

596. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

597. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

598. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

599. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

600. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

601. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

602. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

603. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

604. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
      - scripts\run_quality_checks.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

605. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_check.py
      - tests\unit\test_api_layer_comprehensive.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

606. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_check.py
      - scripts\security_check.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

607. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_check.py
      - scripts\security_check.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

608. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_check.py
      - scripts\security_check.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

609. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_check.py
      - scripts\security_check.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

610. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_check.py
      - scripts\security_check.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

611. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\security_check.py
      - scripts\security_check.py
      - scripts\security_check.py
      - scripts\security_check.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

612. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_check.py
      - scripts\security_scan.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

613. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_check.py
      - scripts\security_check.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

614. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_check.py
      - scripts\security_check.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

615. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_check.py
      - scripts\security_check.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

616. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_check.py
      - scripts\security_check.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

617. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_check.py
      - scripts\security_check.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

618. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_check.py
      - scripts\security_check.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

619. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_scan.py
      - scripts\security_scan.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

620. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_scan.py
      - scripts\security_scan.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

621. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_scan.py
      - scripts\security_scan.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

622. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_scan.py
      - scripts\security_scan.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

623. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\security_scan.py
      - scripts\security_scan.py
      - scripts\security_scan.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

624. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_scan.py
      - scripts\security_scan.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

625. 【重复代码块】发现 7 个相似的代码块（5+ 行）
   📄 涉及文件: 7 个
      - scripts\security_scan.py
      - scripts\security_scan.py
      - scripts\security_scan.py
      ... 还有 4 个文件
   💾 冗余大小: 2.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

626. 【重复代码块】发现 9 个相似的代码块（5+ 行）
   📄 涉及文件: 9 个
      - scripts\security_scan.py
      - scripts\security_scan.py
      - scripts\security_scan.py
      ... 还有 6 个文件
   💾 冗余大小: 2.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

627. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\security_scan.py
      - scripts\security_scan.py
      - scripts\security_scan.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

628. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\security_scan.py
      - scripts\security_scan.py
      - scripts\security_scan.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

629. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\security_scan.py
      - scripts\security_scan.py
      - scripts\security_scan.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

630. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\security_scan.py
      - scripts\security_scan.py
      - scripts\security_scan.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

631. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\security_scan.py
      - scripts\security_scan.py
      - scripts\security_scan.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

632. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - scripts\security_scan.py
      - scripts\security_scan.py
      - scripts\security_scan.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

633. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_scan.py
      - tests\performance\performance_baseline_test.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

634. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - scripts\security_scan.py
      - tests\performance\performance_baseline_test.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

635. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - scripts\security_scan.py
      - scripts\security_scan.py
      - scripts\security_scan.py
      - tests\e2e\business_process_test.py
   💾 冗余大小: 1.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

636. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\conftest.py
      - tests\conftest.py
      - tests\integration\test_analytics_api.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

637. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\conftest.py
      - tests\conftest.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

638. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\conftest.py
      - tests\conftest.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

639. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\conftest.py
      - tests\unit\test_tenant_service_improved.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

640. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\conftest.py
      - tests\unit\test_tenant_service_improved.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

641. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\conftest.py
      - tests\unit\test_tenant_service_improved.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

642. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\business_process_test.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

643. 【重复代码块】发现 17 个相似的代码块（5+ 行）
   📄 涉及文件: 17 个
      - tests\e2e\business_process_test.py
      - tests\e2e\business_process_test.py
      - tests\e2e\business_process_test.py
      ... 还有 14 个文件
   💾 冗余大小: 2.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

644. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\business_process_test.py
      - tests\e2e\business_process_test.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

645. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\e2e\business_process_test.py
      - tests\e2e\business_process_test.py
      - tests\e2e\business_process_test.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

646. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\business_process_test.py
      - tests\e2e\business_process_test.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

647. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\business_process_test.py
      - tests\e2e\business_process_test.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

648. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\business_process_test.py
      - tests\e2e\business_process_test.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

649. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\test_business_flows.py
      - tests\e2e\test_business_flows.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

650. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\test_business_flows.py
      - tests\unit\test_api_endpoints.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

651. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\test_business_flows.py
      - tests\e2e\test_business_flows.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

652. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\test_business_flows.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

653. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

654. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

655. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

656. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

657. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

658. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

659. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

660. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

661. 【重复代码块】发现 8 个相似的代码块（5+ 行）
   📄 涉及文件: 8 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      ... 还有 5 个文件
   💾 冗余大小: 1.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

662. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

663. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

664. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

665. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

666. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

667. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

668. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

669. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

670. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

671. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

672. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

673. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

674. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

675. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

676. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

677. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

678. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

679. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

680. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
      - tests\e2e\test_customer_service_flow.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

681. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\helpers\mock_services.py
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

682. 【重复代码块】发现 8 个相似的代码块（5+ 行）
   📄 涉及文件: 8 个
      - tests\helpers\mock_services.py
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
      ... 还有 5 个文件
   💾 冗余大小: 1.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

683. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\helpers\mock_services.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

684. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\helpers\mock_services.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

685. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\helpers\mock_services.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

686. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\helpers\mock_services.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

687. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\helpers\mock_services.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

688. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

689. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

690. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

691. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

692. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

693. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

694. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

695. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

696. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

697. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

698. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

699. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

700. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

701. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\helpers\test_data_factory.py
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

702. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\integration\test_instances_api.py
      - tests\integration\test_messages_api.py
      - tests\integration\test_sessions_api.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

703. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\integration\test_instances_api.py
      - tests\integration\test_messages_api.py
      - tests\integration\test_sessions_api.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

704. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\integration\test_instances_api.py
      - tests\integration\test_messages_api.py
      - tests\integration\test_sessions_api.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

705. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\integration\test_instances_api.py
      - tests\integration\test_sessions_api.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

706. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\integration\test_sessions_api.py
      - tests\integration\test_sessions_api.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

707. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

708. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

709. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

710. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

711. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

712. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

713. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

714. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

715. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

716. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

717. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

718. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

719. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

720. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

721. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
      - tests\integration\test_tenant_api_integration.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

722. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\load_test.py
      - tests\performance\load_test.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

723. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\load_test.py
      - tests\performance\load_test.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

724. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_baseline_test.py
      - tests\performance\performance_baseline_test.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

725. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      ... 还有 3 个文件
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

726. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

727. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

728. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

729. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

730. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

731. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

732. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

733. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

734. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

735. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

736. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

737. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

738. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

739. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

740. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

741. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

742. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

743. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

744. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

745. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

746. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

747. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

748. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

749. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

750. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

751. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

752. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

753. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

754. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

755. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

756. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\performance\performance_test_suite.py
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

757. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\security\test_k8s_security_policies.py
      - tests\security\test_k8s_security_policies.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

758. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_additional_services.py
      - tests\unit\test_additional_services.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

759. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_additional_services.py
      - tests\unit\test_additional_services.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

760. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_additional_services.py
      - tests\unit\test_additional_services.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

761. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_additional_services.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

762. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_coverage_boost.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

763. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_coverage_boost.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

764. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_coverage_boost.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

765. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_coverage_boost.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

766. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_api_endpoints.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

767. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_api_endpoints.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

768. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_additional_services.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

769. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_mock_strategies.py
      - tests\unit\test_service_layer_fixes.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

770. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_mock_strategies.py
      - tests\unit\test_service_layer_fixes.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

771. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_mock_strategies.py
      - tests\unit\test_service_layer_fixes.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

772. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_additional_services.py
      - tests\unit\test_service_layer_fixes.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

773. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_coverage_boost.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

774. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_api_endpoints.py
      - tests\unit\_test_auth_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

775. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_api_endpoints.py
      - tests\unit\_test_auth_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

776. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_api_endpoints.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

777. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_api_endpoints.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

778. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_models.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

779. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_api_endpoints.py
      - tests\unit\test_api_endpoints.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

780. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_api_layers.py
      - tests\unit\test_schemas.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

781. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_api_layer_comprehensive.py
      - tests\unit\test_coverage_boost.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

782. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_api_layer_comprehensive.py
      - tests\unit\test_coverage_boost.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

783. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_api_layer_comprehensive.py
      - tests\unit\test_schemas.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

784. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_api_layer_comprehensive.py
      - tests\unit\test_schemas.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

785. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_auto_reply_service.py
      - tests\unit\test_auto_reply_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

786. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_config.py
      - tests\unit\test_config.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

787. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_config.py
      - tests\unit\test_config.py
      - tests\unit\test_config.py
      - tests\unit\test_config.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

788. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_config.py
      - tests\unit\test_config.py
      - tests\unit\test_config.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

789. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_config.py
      - tests\unit\test_config.py
      - tests\unit\test_config.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

790. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_config.py
      - tests\unit\test_config.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

791. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_coverage_boost.py
      - tests\unit\test_models.py
      - tests\unit\test_models.py
      - tests\unit\test_models.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

792. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_coverage_boost.py
      - tests\unit\test_models.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

793. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_coverage_boost.py
      - tests\unit\test_coverage_boost.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

794. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_coverage_boost.py
      - tests\unit\test_coverage_boost.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

795. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_coverage_boost.py
      - tests\unit\test_models.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

796. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
      ... 还有 3 个文件
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

797. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

798. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

799. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

800. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_services_realistic.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 1.1 KB
   💡 建议: 提取重复代码到公共函数或方法中

801. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_services_realistic.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

802. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

803. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

804. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

805. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_services_realistic.py
      - tests\unit\test_services_realistic.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

806. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_coverage_boost_services.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

807. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\test_tenant_model.py
      - tests\unit\_test_auth_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

808. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\_test_auth_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

809. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_coverage_boost_services.py
      - tests\unit\_test_auth_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

810. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_message.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

811. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_message.py
      - tests\unit\test_message.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

812. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_message.py
      - tests\unit\test_message.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

813. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - tests\unit\test_message.py
      - tests\unit\test_message.py
      - tests\unit\test_message.py
      ... 还有 3 个文件
   💾 冗余大小: 1.1 KB
   💡 建议: 提取重复代码到公共函数或方法中

814. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - tests\unit\test_message.py
      - tests\unit\test_message.py
      - tests\unit\test_message.py
      ... 还有 3 个文件
   💾 冗余大小: 1.1 KB
   💡 建议: 提取重复代码到公共函数或方法中

815. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_message.py
      - tests\unit\test_models.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

816. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_message.py
      - tests\unit\test_message.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

817. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_message.py
      - tests\unit\test_message.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

818. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_message.py
      - tests\unit\test_models.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

819. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_message.py
      - tests\unit\test_models.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

820. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_message.py
      - tests\unit\test_message.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

821. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - tests\unit\test_message.py
      - tests\unit\test_message.py
      - tests\unit\test_message.py
      ... 还有 3 个文件
   💾 冗余大小: 1.1 KB
   💡 建议: 提取重复代码到公共函数或方法中

822. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_message.py
      - tests\unit\test_message.py
      - tests\unit\test_message.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

823. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_message.py
      - tests\unit\test_message.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

824. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_message.py
      - tests\unit\test_message.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

825. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

826. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

827. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

828. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

829. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

830. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

831. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

832. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

833. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 1.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

834. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

835. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

836. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

837. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

838. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

839. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

840. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

841. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

842. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_message_service.py
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

843. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_mock_strategies.py
      - tests\unit\test_service_layer_fixes.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

844. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_mock_strategies.py
      - tests\unit\test_schemas.py
      - tests\unit\test_service_layer_fixes.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

845. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_mock_strategies.py
      - tests\unit\test_service_layer_fixes.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

846. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_mock_strategies.py
      - tests\unit\test_service_layer_fixes.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

847. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_models.py
      - tests\unit\test_models.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

848. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_models.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

849. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_models.py
      - tests\unit\test_session_summary_service_refactored.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

850. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_models.py
      - tests\unit\test_models.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

851. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_models.py
      - tests\unit\test_models.py
      - tests\unit\test_service_layer_fixes.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

852. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_models.py
      - tests\unit\test_models.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

853. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_rbac_service.py
      - tests\unit\test_session_service.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

854. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_rbac_service.py
      - tests\unit\test_session_service.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

855. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_rbac_service.py
      - tests\unit\test_session_service.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

856. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_rbac_service.py
      - tests\unit\test_session_service.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

857. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_rbac_service.py
      - tests\unit\test_session_service.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

858. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_rbac_service.py
      - tests\unit\test_session_service.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

859. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_rbac_service.py
      - tests\unit\test_session_service.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

860. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_rbac_service.py
      - tests\unit\test_session_service.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

861. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_rbac_service.py
      - tests\unit\test_session_service.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

862. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_rbac_service.py
      - tests\unit\test_session_service.py
      - tests\unit\test_tenant_service_improved.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

863. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_rbac_service.py
      - tests\unit\test_session_service.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

864. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_schemas.py
      - tests\unit\test_schemas.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

865. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - tests\unit\test_schemas.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

866. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_schemas.py
      - tests\unit\test_schemas.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

867. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_schemas.py
      - tests\unit\test_schemas.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

868. 【重复代码块】发现 6 个相似的代码块（5+ 行）
   📄 涉及文件: 6 个
      - tests\unit\test_schemas.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      ... 还有 3 个文件
   💾 冗余大小: 1.1 KB
   💡 建议: 提取重复代码到公共函数或方法中

869. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_schemas.py
      - tests\unit\test_schemas.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

870. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_services_realistic.py
      - tests\unit\_test_auth_service.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

871. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_services_realistic.py
      - tests\unit\test_services_realistic.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

872. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_services_realistic.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

873. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_services_realistic.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

874. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_services_realistic.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

875. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_services_realistic.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

876. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_services_realistic.py
      - tests\unit\test_service_layer_fixes.py
      - tests\unit\_test_high_value_services.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

877. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_services_realistic.py
      - tests\unit\test_service_layer_fixes.py
      - tests\unit\_test_auth_service.py
      - tests\unit\_test_high_value_services.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

878. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_services_realistic.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

879. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_services_realistic.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

880. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_services_realistic.py
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

881. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_service_layer_fixes.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

882. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_service_layer_fixes.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

883. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_service.py
      - tests\unit\test_session_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

884. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

885. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

886. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

887. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.1 KB
   💡 建议: 提取重复代码到公共函数或方法中

888. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

889. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

890. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

891. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

892. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

893. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

894. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

895. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

896. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

897. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

898. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

899. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

900. 【重复代码块】发现 8 个相似的代码块（5+ 行）
   📄 涉及文件: 8 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      ... 还有 5 个文件
   💾 冗余大小: 1.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

901. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

902. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 1.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

903. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

904. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

905. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

906. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

907. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

908. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

909. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

910. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

911. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.9 KB
   💡 建议: 提取重复代码到公共函数或方法中

912. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

913. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

914. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

915. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

916. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

917. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

918. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 1.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

919. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

920. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

921. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

922. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

923. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

924. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

925. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

926. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

927. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

928. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

929. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

930. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_professional.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

931. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_refactored.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

932. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_session_summary_service_refactored.py
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

933. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_tenant_service.py
      - tests\unit\test_tenant_service.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

934. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_tenant_service.py
      - tests\unit\test_tenant_service.py
      - tests\unit\test_tenant_service.py
      - tests\unit\test_tenant_service.py
   💾 冗余大小: 1.0 KB
   💡 建议: 提取重复代码到公共函数或方法中

935. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_tenant_service_improved.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

936. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_tenant_service_improved.py
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

937. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

938. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

939. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

940. 【重复代码块】发现 4 个相似的代码块（5+ 行）
   📄 涉及文件: 4 个
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
   💾 冗余大小: 0.6 KB
   💡 建议: 提取重复代码到公共函数或方法中

941. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
   💾 冗余大小: 0.7 KB
   💡 建议: 提取重复代码到公共函数或方法中

942. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

943. 【重复代码块】发现 5 个相似的代码块（5+ 行）
   📄 涉及文件: 5 个
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
      - tests\unit\test_user_model.py
   💾 冗余大小: 0.8 KB
   💡 建议: 提取重复代码到公共函数或方法中

944. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_utils.py
      - tests\unit\test_utils.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

945. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_utils.py
      - tests\unit\test_utils.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

946. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_utils.py
      - tests\unit\test_utils.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

947. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_utils.py
      - tests\unit\test_utils.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

948. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_utils.py
      - tests\unit\test_utils.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

949. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_utils.py
      - tests\unit\test_utils.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

950. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_utils.py
      - tests\unit\test_utils.py
   💾 冗余大小: 0.2 KB
   💡 建议: 提取重复代码到公共函数或方法中

951. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_utils.py
      - tests\unit\test_utils.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

952. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_webhook_service.py
      - tests\unit\test_webhook_service.py
   💾 冗余大小: 0.5 KB
   💡 建议: 提取重复代码到公共函数或方法中

953. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\test_webhook_service.py
      - tests\unit\test_webhook_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

954. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\_test_auth_service.py
      - tests\unit\_test_auth_service.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

955. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\_test_high_value_services.py
      - tests\unit\_test_high_value_services.py
   💾 冗余大小: 0.3 KB
   💡 建议: 提取重复代码到公共函数或方法中

956. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\_test_high_value_services.py
      - tests\unit\_test_high_value_services.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

957. 【重复代码块】发现 2 个相似的代码块（5+ 行）
   📄 涉及文件: 2 个
      - tests\unit\_test_high_value_services.py
      - tests\unit\_test_high_value_services.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

958. 【重复代码块】发现 3 个相似的代码块（5+ 行）
   📄 涉及文件: 3 个
      - tests\unit\_test_high_value_services.py
      - tests\unit\_test_high_value_services.py
      - tests\unit\_test_high_value_services.py
   💾 冗余大小: 0.4 KB
   💡 建议: 提取重复代码到公共函数或方法中

959. 【重复配置】发现 841396 个重复的配置项
   📄 涉及文件: 4679 个
      - .mypy_cache\3.11\cryptography\hazmat\bindings\_rust\openssl\kdf.meta.json
      - .mypy_cache\3.12\app\services\rbac_service.data.json
      - .mypy_cache\3.12\markdown_it\rules_block\lheading.data.json
      ... 还有 4676 个文件
   💾 冗余大小: 41083.8 KB
   💡 建议: 创建共享配置文件或配置继承机制

🚨 LOW 级别问题
----------------------------------------
1. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - code_redundancy_analyzer.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

2. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - alembic\env.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

3. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - app\api\v1\ai_features.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

4. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - app\api\v1\instances.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

5. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - app\api\v1\webhooks.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

6. 【未使用导入】发现 10 个未使用的导入
   📄 涉及文件: 1 个
      - app\api\v1\__init__.py
   💾 冗余大小: 0.1 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

7. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - app\core\middleware.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

8. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - app\core\config\__init__.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

9. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - app\models\message.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

10. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - app\models\role.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

11. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - app\models\session.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

12. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - app\models\tenant.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

13. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - app\models\user.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

14. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - app\services\analytics_service.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

15. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - app\services\session_summary_service.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

16. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - app\services\llm\__init__.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

17. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - scripts\analyze_api_docs.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

18. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - scripts\analyze_warnings.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

19. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - scripts\check_api_docs_simple.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

20. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - scripts\code_quality_check.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

21. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - scripts\comprehensive_api_check.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

22. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - scripts\coverage_analysis.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

23. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - scripts\fix_integration_tests.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

24. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - scripts\fix_type_annotations.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

25. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - scripts\optimize_async_functions.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

26. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - scripts\security_check.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

27. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - scripts\security_scan.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

28. 【未使用导入】发现 8 个未使用的导入
   📄 涉及文件: 1 个
      - tests\conftest.py
   💾 冗余大小: 0.1 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

29. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\e2e\business_process_test.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

30. 【未使用导入】发现 10 个未使用的导入
   📄 涉及文件: 1 个
      - tests\e2e\test_business_flows.py
   💾 冗余大小: 0.1 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

31. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - tests\e2e\__init__.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

32. 【未使用导入】发现 5 个未使用的导入
   📄 涉及文件: 1 个
      - tests\helpers\mock_services.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

33. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - tests\helpers\smart_test_selector.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

34. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\helpers\test_data_factory.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

35. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - tests\integration\test_analytics_api.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

36. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - tests\integration\test_webhooks_api.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

37. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\performance\baseline_manager.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

38. 【未使用导入】发现 6 个未使用的导入
   📄 涉及文件: 1 个
      - tests\performance\performance_test_suite.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

39. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - tests\performance\__init__.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

40. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_additional_services.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

41. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_agent_suggestion_service.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

42. 【未使用导入】发现 5 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_ai_features.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

43. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_analytics.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

44. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_api_endpoints.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

45. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_api_layers.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

46. 【未使用导入】发现 6 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_api_layer_comprehensive.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

47. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_auth.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

48. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_auto_reply_service.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

49. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_common.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

50. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_context_manager.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

51. 【未使用导入】发现 9 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_coverage_boost.py
   💾 冗余大小: 0.1 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

52. 【未使用导入】发现 8 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_coverage_boost_services.py
   💾 冗余大小: 0.1 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

53. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_database.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

54. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_deps.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

55. 【未使用导入】发现 5 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_instances.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

56. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_logging.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

57. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_message.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

58. 【未使用导入】发现 5 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_messages.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

59. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_message_service.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

60. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_middleware.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

61. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_mock_strategies.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

62. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_permissions.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

63. 【未使用导入】发现 5 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_rbac.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

64. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_rbac_service.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

65. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_role.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

66. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_schemas.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

67. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_scripts.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

68. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_security.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

69. 【未使用导入】发现 13 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_services_realistic.py
   💾 冗余大小: 0.1 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

70. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_service_layer_fixes.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

71. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_session.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

72. 【未使用导入】发现 5 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_sessions.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

73. 【未使用导入】发现 1 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_session_summary_service_professional.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

74. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_session_summary_service_refactored.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

75. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_settings.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

76. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_tenant_service.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

77. 【未使用导入】发现 3 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_tenant_service_improved.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

78. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_tenant_service_simplified.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

79. 【未使用导入】发现 4 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_user.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

80. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\test_utils.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

81. 【未使用导入】发现 2 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\_test_auth_service.py
   💾 冗余大小: 0.0 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

82. 【未使用导入】发现 6 个未使用的导入
   📄 涉及文件: 1 个
      - tests\unit\_test_high_value_services.py
   💾 冗余大小: 0.1 KB
   💡 建议: 移除未使用的导入以减少依赖和提高加载速度

🎯 优先处理建议
----------------------------------------
1. 批量重复文件 - 可节省 12.08MB
   清理历史文件，仅保留最新的几个文件，或建立归档机制
2. 批量重复文件 - 可节省 2.21MB
   清理历史文件，仅保留最新的几个文件，或建立归档机制
3. 批量重复文件 - 可节省 2.17MB
   清理历史文件，仅保留最新的几个文件，或建立归档机制
4. 批量重复文件 - 可节省 1.12MB
   清理历史文件，仅保留最新的几个文件，或建立归档机制
5. 批量重复文件 - 可节省 0.88MB
   清理历史文件，仅保留最新的几个文件，或建立归档机制