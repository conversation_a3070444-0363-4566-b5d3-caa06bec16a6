# 持续集成/持续部署 (CI/CD) 知识体系

## 🏗️ 核心概念与原则

### 1. CI/CD 核心理念

- **持续集成 (Continuous Integration)**: 开发者频繁地将代码集成到共享仓库。每次集成都会通过自动化的构建和测试来验证，从而尽快地发现集成错误。
- **持续交付 (Continuous Delivery)**: 在持续集成的基础上，将集成后的代码自动部署到类生产环境（如测试环境、预发布环境），确保可以随时手动部署到生产环境。
- **持续部署 (Continuous Deployment)**: 在持续交付的基础上，将通过所有自动化测试的代码自动部署到生产环境，实现从代码提交到上线的全自动化。

### 2. CI/CD 核心原则

- **单一代码库**: 所有代码和配置都在版本控制系统中管理（如Git）。
- **自动化构建**: 从代码编译、打包到环境准备，一切都是自动化的。
- **自动化测试**: 每个变更都必须触发全面的自动化测试。
- **快速反馈**: 构建和测试结果必须在几分钟内反馈给开发团队。
- **增量式变更**: 每次提交的变更都应该很小，易于理解和回滚。
- **随时可部署**: 主干分支上的任何版本都应该是可部署的。

## 🚀 主流CI/CD平台与实践

### 1. GitHub Actions

#### 完整CI/CD工作流
```yaml
# .github/workflows/main.yml
name: Main CI/CD Pipeline

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main

env:
  PYTHON_VERSION: '3.11'
  POSTGRES_VERSION: '15'

jobs:
  lint-and-format:
    name: Code Quality Check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      - name: Install dependencies
        run: pip install -r requirements-dev.txt
      - name: Run Linters
        run: |
          black --check .
          isort --check-only .
          flake8 .

  unit-test:
    name: Unit & Integration Tests
    runs-on: ubuntu-latest
    needs: lint-and-format
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_USER: testuser
          POSTGRES_PASSWORD: testpassword
          POSTGRES_DB: testdb
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      - name: Install dependencies
        run: pip install -r requirements.txt -r requirements-dev.txt
      - name: Run Tests
        env:
          DATABASE_URL: "postgresql+asyncpg://testuser:testpassword@localhost:5432/testdb"
        run: |
          pytest tests/unit/ tests/integration/ \
            --cov=app --cov-report=xml --junitxml=test-results.xml
      - name: Upload Coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml

  build-and-push-docker:
    name: Build & Push Docker Image
    runs-on: ubuntu-latest
    needs: unit-test
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/astrbot-saas:latest,${{ secrets.DOCKERHUB_USERNAME }}/astrbot-saas:${{ github.sha }}

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build-and-push-docker
    environment: staging
    steps:
      - name: Deploy to Staging Server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USERNAME }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script: |
            cd /opt/astrbot-saas
            docker-compose pull
            docker-compose up -d
            echo "Deployment to staging complete."

  e2e-test-on-staging:
    name: E2E Tests on Staging
    runs-on: ubuntu-latest
    needs: deploy-staging
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      - name: Install dependencies
        run: pip install -r requirements-dev.txt && npx playwright install --with-deps
      - name: Run E2E tests
        env:
          BASE_URL: ${{ secrets.STAGING_URL }}
        run: pytest tests/e2e/

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: e2e-test-on-staging
    environment: production
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to Production Server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USERNAME }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          script: |
            cd /opt/astrbot-saas
            docker-compose pull
            docker-compose up -d
            echo "Deployment to production complete."
```

### 2. Jenkins

#### Jenkinsfile (Pipeline as Code)
```groovy
// Jenkinsfile
pipeline {
    agent any

    environment {
        PYTHON_VERSION = '3.11'
        DOCKERHUB_CREDENTIALS = credentials('dockerhub-credentials')
    }

    stages {
        stage('Checkout') {
            steps {
                git branch: 'main', url: 'https://github.com/your-repo/astrbot-saas.git'
            }
        }

        stage('Code Quality') {
            steps {
                sh 'pip install -r requirements-dev.txt'
                sh 'black --check .'
                sh 'isort --check-only .'
                sh 'flake8 .'
                sh 'bandit -r app/'
            }
        }

        stage('Unit & Integration Tests') {
            steps {
                sh 'pip install -r requirements.txt'
                sh 'pytest tests/unit/ tests/integration/ --junitxml=test-results.xml'
            }
            post {
                always {
                    junit 'test-results.xml'
                }
            }
        }

        stage('Build Docker Image') {
            when { branch 'main' }
            steps {
                script {
                    def dockerImage = docker.build("your-dockerhub-id/astrbot-saas:${env.BUILD_NUMBER}")
                    docker.withRegistry('https://registry.hub.docker.com', DOCKERHUB_CREDENTIALS) {
                        dockerImage.push()
                        dockerImage.push("latest")
                    }
                }
            }
        }

        stage('Deploy to Staging') {
            when { branch 'main' }
            steps {
                input message: 'Deploy to Staging Environment?'
                sshagent(['staging-ssh-key']) {
                    sh 'ssh -o StrictHostKeyChecking=no user@staging-server "cd /opt/app && docker-compose pull && docker-compose up -d"'
                }
            }
        }

        stage('E2E Tests on Staging') {
            steps {
                // E2E test execution commands
            }
        }

        stage('Deploy to Production') {
            when { branch 'main' }
            steps {
                input message: 'Deploy to Production Environment?'
                sshagent(['production-ssh-key']) {
                    sh 'ssh -o StrictHostKeyChecking=no user@prod-server "cd /opt/app && docker-compose pull && docker-compose up -d"'
                }
            }
        }
    }

    post {
        always {
            cleanWs()
        }
        success {
            echo 'Pipeline finished successfully!'
            // Add notification steps (Slack, Email, etc.)
        }
        failure {
            echo 'Pipeline failed!'
            // Add notification steps
        }
    }
}
```

## 📈 CI/CD 最佳实践

### 1. 流程优化
- **并行化执行**: 将独立的任务（如单元测试、代码分析）并行执行以缩短总时间。
- **缓存依赖**: 缓存不变的依赖项（如Docker层、第三方库）来加速构建。
- **优化测试**: 使用智能测试选择（基于代码变更）来运行最相关的测试，而不是全部运行。
- **快速失败**: 将最快、最可能失败的阶段（如linting、单元测试）放在最前面。

### 2. 安全实践 (DevSecOps)
- **密钥管理**: 使用CI/CD平台的密钥管理系统（如GitHub Secrets, Jenkins Credentials）存储敏感信息，不要硬编码。
- **依赖扫描**: 在CI流程中集成依赖安全扫描工具（如Safety, Snyk, Trivy）。
- **静态应用安全测试 (SAST)**: 集成SAST工具（如Bandit, Semgrep, SonarQube）进行代码级漏洞扫描。
- **动态应用安全测试 (DAST)**: 在部署到测试环境后，使用DAST工具（如OWASP ZAP）扫描运行中的应用。
- **容器安全**: 扫描容器镜像中的漏洞，并使用最小权限原则配置容器。

### 3. 可观察性与监控
- **流水线监控**: 监控CI/CD流水线的执行时间、成功率、失败热点等指标。
- **质量门禁**: 设置明确的质量门禁，如测试覆盖率、代码质量评分、安全漏洞数量，不满足则自动阻断流水线。
- **结构化日志**: 在流水线脚本中使用结构化的日志，方便后续的查询和分析。
- **告警与通知**: 将流水线状态（成功、失败、警告）实时通知到开发团队（如Slack, Teams, Email）。

### 4. 环境管理
- **基础设施即代码 (IaC)**: 使用Terraform、Ansible等工具来定义和管理测试、预发布和生产环境，确保一致性。
- **容器化**: 使用Docker和Docker Compose来打包应用及其依赖，确保在不同环境中行为一致。
- **部署策略**:
  - **蓝绿部署**: 维护两个相同的生产环境，一次只有一个环境对外提供服务。部署时，将新版本部署到空闲环境，测试通过后，切换流量。
  - **金丝雀部署**: 逐步将新版本发布给一小部分用户，监控其表现，如果没有问题，再逐步扩大发布范围。
  - **滚动更新**: 逐个替换旧版本的实例，直到所有实例都更新为新版本。

此知识体系为测试执行专家提供了在现代CI/CD环境中工作所需的核心知识，从基本概念到高级平台实践和最佳策略。 