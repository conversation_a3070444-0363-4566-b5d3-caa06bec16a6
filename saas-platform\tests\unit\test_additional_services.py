"""
额外服务层测试
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from uuid import uuid4, UUID
from datetime import datetime

# 导入要测试的服务
from app.services.session_service import SessionService
from app.services.message_service import MessageService
from app.services.auth_service import AuthService

# 导入模型和异常
from app.models.session import Session, SessionStatus
from app.models.message import Message, MessageType, SenderType
from app.models.tenant import Tenant
from app.models.user import User
from app.schemas.message import MessageCreate, MessageRead
from app.schemas.session import SessionRead


class TestSessionService:
    """测试会话服务"""

    @pytest.fixture
    def mock_db(self):
        """Mock数据库会话"""
        mock = AsyncMock()
        mock.execute = AsyncMock()
        mock.execute.return_value = AsyncMock()
        mock.execute.return_value.scalar_one_or_none = AsyncMock()
        mock.execute.return_value.scalars = AsyncMock()
        mock.execute.return_value.scalars.return_value.all = AsyncMock()

        mock.add = Mock()
        mock.commit = AsyncMock()
        mock.rollback = AsyncMock()
        mock.refresh = AsyncMock()
        return mock

    @pytest.fixture
    def session_service(self, mock_db):
        """SessionService实例"""
        return SessionService(mock_db)

    @pytest.fixture
    def sample_session(self):
        """示例会话数据"""
        session = Session(
            tenant_id=uuid4(),
            user_id="telegram:123456",
            platform="telegram",
            status=SessionStatus.WAITING,
        )
        session.id = uuid4()
        session.created_at = datetime.utcnow()
        session.updated_at = datetime.utcnow()
        return session

    @pytest.fixture
    def sample_session_read(self, sample_session):
        """示例会话读取数据"""
        return SessionRead(
            id=sample_session.id,
            tenant_id=sample_session.tenant_id,
            user_id=sample_session.user_id,
            platform=sample_session.platform,
            status=sample_session.status,
            created_at=sample_session.created_at,
            updated_at=sample_session.updated_at,
        )

    @pytest.mark.asyncio
    async def test_create_or_get_session_success(
        self, session_service, mock_db, sample_session, sample_session_read
    ):
        """测试创建或获取会话成功"""
        # Arrange
        tenant_id = uuid4()
        user_id = "telegram:123456"
        platform = "telegram"

        # Mock没有现有会话
        mock_db.execute.return_value.scalar_one_or_none.return_value = None
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None

        # Mock Session构造函数和SessionRead.model_validate
        with patch("app.services.session_service.Session") as MockSession:
            MockSession.return_value = sample_session
            with patch(
                "app.services.session_service.SessionRead.model_validate",
                return_value=sample_session_read,
            ):
                # Act
                result = await session_service.create_or_get_session(
                    user_id=user_id, platform=platform, tenant_id=tenant_id
                )

                # Assert
                assert result is not None
                assert isinstance(result, SessionRead)
                assert result.user_id == user_id
                assert result.platform == platform

                # 验证数据库操作
                mock_db.add.assert_called_once()
                mock_db.commit.assert_called_once()
                mock_db.refresh.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_session_success(
        self, session_service, mock_db, sample_session, sample_session_read
    ):
        """测试获取会话成功"""
        # Arrange
        session_id = uuid4()
        tenant_id = uuid4()

        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_session

        # Mock SessionRead.model_validate
        with patch(
            "app.services.session_service.SessionRead.model_validate",
            return_value=sample_session_read,
        ):
            # Act
            result = await session_service.get_session(session_id, tenant_id)

            # Assert
            assert result is not None
            assert isinstance(result, SessionRead)
            assert result.id == sample_session.id

    @pytest.mark.asyncio
    async def test_get_session_not_found(self, session_service, mock_db):
        """测试会话不存在"""
        # Arrange
        session_id = uuid4()
        tenant_id = uuid4()

        mock_db.execute.return_value.scalar_one_or_none.return_value = None

        # Act
        result = await session_service.get_session(session_id, tenant_id)

        # Assert
        assert result is None

    def test_service_initialization(self, mock_db):
        """测试服务初始化"""
        # Act
        service = SessionService(mock_db)

        # Assert
        assert service.db == mock_db


class TestMessageService:
    """MessageService测试类"""

    @pytest.fixture
    def mock_db(self):
        """Mock数据库会话"""
        mock = AsyncMock()
        mock.execute = AsyncMock()
        mock.execute.return_value = AsyncMock()
        mock.execute.return_value.scalar_one_or_none = AsyncMock()
        mock.execute.return_value.scalars = AsyncMock()
        mock.execute.return_value.scalars.return_value = AsyncMock()
        mock.execute.return_value.scalars.return_value.all = AsyncMock()

        mock.commit = AsyncMock()
        mock.rollback = AsyncMock()
        mock.refresh = AsyncMock()
        mock.add = Mock()

        return mock

    @pytest.fixture
    def mock_session_service(self):
        """Mock会话服务"""
        return AsyncMock()

    @pytest.fixture
    def message_service(self, mock_db, mock_session_service):
        """创建MessageService实例"""
        return MessageService(mock_db, mock_session_service)

    @pytest.fixture
    def sample_message_create(self):
        """示例消息创建数据"""
        return MessageCreate(
            session_id=uuid4(),
            content="Hello, this is a test message",
            sender_type=SenderType.USER,
            sender_id="test_user",
            metadata={"test": "data"},
        )

    @pytest.fixture
    def sample_message(self):
        """示例消息模型实例"""
        message = Message(
            session_id=uuid4(),
            content="Hello, this is a test message",
            sender_type=SenderType.USER,
            extra_data={"test": "data"},
        )
        message.id = 123  # 设置id为整数
        message.created_at = datetime.utcnow()
        return message

    @pytest.fixture
    def sample_message_read(self, sample_message):
        """示例消息读取数据"""
        return MessageRead(
            id=sample_message.id,
            tenant_id=uuid4(),
            session_id=sample_message.session_id,
            content=sample_message.content,
            message_type=MessageType.TEXT,
            sender_type=sample_message.sender_type,
            sender_id="test_user",
            metadata=sample_message.extra_data or {},
            timestamp=datetime.utcnow(),
            created_at=sample_message.created_at,
        )

    @pytest.mark.asyncio
    async def test_create_message_success(
        self,
        message_service,
        mock_db,
        sample_message_create,
        sample_message,
        sample_message_read,
        mock_session_service,
    ):
        """测试成功创建消息"""
        # Arrange
        tenant_id = uuid4()

        # Mock session service 返回有效会话
        mock_session = SessionRead(
            id=sample_message_create.session_id,
            tenant_id=tenant_id,
            user_id="telegram:user123",
            platform="telegram",
            status="waiting",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        mock_session_service.get_session.return_value = mock_session

        # Mock数据库操作
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None

        # Mock Message构造函数
        with patch("app.services.message_service.Message") as MockMessage:
            MockMessage.return_value = sample_message

            # Mock MessageRead.model_validate
            with patch(
                "app.services.message_service.MessageRead.model_validate",
                return_value=sample_message_read,
            ):
                # Act
                result = await message_service.store_message(
                    sample_message_create, tenant_id
                )

                # Assert
                assert result is not None
                assert isinstance(result, MessageRead)
                assert result.content == sample_message_create.content
                assert result.sender_type == sample_message_create.sender_type

                # 验证数据库操作
                mock_db.add.assert_called_once()
                mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_session_messages_success(
        self,
        message_service,
        mock_db,
        sample_message,
        sample_message_read,
        mock_session_service,
    ):
        """测试成功获取会话消息列表"""
        # Arrange
        session_id = uuid4()
        tenant_id = uuid4()

        # Mock session service验证
        mock_session_service.get_session.return_value = Mock()

        mock_db.execute.return_value.scalars.return_value.all.return_value = [
            sample_message
        ]

        # Mock MessageRead.model_validate
        with patch(
            "app.services.message_service.MessageRead.model_validate",
            return_value=sample_message_read,
        ):
            # Act
            result = await message_service.get_session_messages(session_id, tenant_id)

            # Assert
            assert isinstance(result, list)
            assert len(result) == 1
            assert isinstance(result[0], MessageRead)
            assert result[0].content == sample_message.content

    def test_service_initialization(self, mock_db, mock_session_service):
        """测试服务初始化"""
        # Act
        service = MessageService(mock_db, mock_session_service)

        # Assert
        assert service.db == mock_db
        assert service.session_service == mock_session_service


class TestAuthService:
    """测试认证服务"""

    @pytest.fixture
    def mock_db(self):
        """Mock数据库会话"""
        mock = AsyncMock()
        mock.execute = AsyncMock()
        mock.execute.return_value = AsyncMock()
        mock.execute.return_value.scalar_one_or_none = AsyncMock()

        mock.commit = AsyncMock()
        mock.rollback = AsyncMock()
        mock.refresh = AsyncMock()
        mock.add = Mock()

        return mock

    @pytest.fixture
    def auth_service(self, mock_db):
        """AuthService实例"""
        return AuthService(mock_db)

    def test_service_initialization(self, mock_db):
        """测试认证服务初始化"""
        # Act
        service = AuthService(mock_db)

        # Assert
        assert service.db == mock_db
        assert isinstance(service, AuthService)

    @pytest.mark.asyncio
    async def test_basic_auth_functionality(self, auth_service, mock_db):
        """测试基本认证功能存在"""
        # Assert - 验证服务有必要的属性
        assert hasattr(auth_service, "db")
        assert auth_service.db == mock_db


class TestServiceIntegration:
    """测试服务集成"""

    def test_service_initialization(self):
        """测试服务初始化"""
        # Arrange
        mock_db = Mock()

        # Act
        session_service = SessionService(mock_db)
        auth_service = AuthService(mock_db)

        # Assert
        assert session_service.db == mock_db
        assert auth_service.db == mock_db

    def test_enum_validations(self):
        """测试枚举验证"""
        # 测试会话状态枚举
        assert SessionStatus.ACTIVE == "active"
        assert SessionStatus.CLOSED == "closed"

        # 测试消息类型枚举
        assert MessageType.TEXT == "text"
        assert MessageType.IMAGE == "image"

        # 测试发送者类型枚举
        assert SenderType.USER == "user"
        assert SenderType.SYSTEM == "system"


class TestBusinessLogic:
    """测试业务逻辑"""

    def test_session_lifecycle(self):
        """测试会话生命周期"""
        # Arrange
        mock_session = Mock(spec=Session)
        mock_session.status = SessionStatus.WAITING
        mock_session.is_active = True

        # Act - 激活会话
        mock_session.status = SessionStatus.ACTIVE
        assert mock_session.status == SessionStatus.ACTIVE

        # Act - 关闭会话
        mock_session.status = SessionStatus.CLOSED
        mock_session.is_active = False
        assert mock_session.status == SessionStatus.CLOSED
        assert mock_session.is_active is False

    def test_message_validation(self):
        """测试消息验证"""
        # 测试消息内容验证
        valid_contents = ["Hello", "How are you?", "Thank you!"]
        for content in valid_contents:
            assert len(content) > 0
            assert isinstance(content, str)

        # 测试消息类型验证
        assert MessageType.TEXT in [
            MessageType.TEXT,
            MessageType.IMAGE,
            MessageType.FILE,
        ]

    def test_user_permissions(self):
        """测试用户权限"""
        # Mock用户角色
        mock_user = Mock()
        mock_user.roles = []

        # 测试无角色用户
        assert len(mock_user.roles) == 0

        # 添加角色
        mock_role = Mock()
        mock_role.name = "admin"
        mock_user.roles.append(mock_role)

        assert len(mock_user.roles) == 1
        assert mock_user.roles[0].name == "admin"
