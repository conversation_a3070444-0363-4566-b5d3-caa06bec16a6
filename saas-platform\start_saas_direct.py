#!/usr/bin/env python3
"""
🚀 SaaS平台直接启动脚本
在saas-platform目录内直接启动，避免路径问题
"""

import os
import sys
import subprocess


def main():
    """主启动流程"""
    print("🚀 SaaS平台直接启动")
    print("=" * 40)
    
    # 设置环境变量
    env_vars = {
        'DATABASE_URL': 'postgresql+asyncpg://astrbot:astrbot123@localhost:5432/astrbot_saas',
        'REDIS_URL': 'redis://:redis123@localhost:6379/0',
        'SECRET_KEY': '09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7',
        'FIRST_SUPERUSER': '<EMAIL>',
        'FIRST_SUPERUSER_PASSWORD': 'ChangeMeASAP!',
        'BACKEND_CORS_ORIGINS': '["http://localhost:3000","http://localhost:8080","http://localhost:6185"]',
        'ENVIRONMENT': 'development',
        'DEBUG': 'true',
        'LOG_LEVEL': 'INFO',
        'PROJECT_NAME': 'AstrBot SaaS Platform',
        'API_V1_STR': '/api/v1'
    }
    
    print("⚙️ 设置环境变量...")
    for key, value in env_vars.items():
        os.environ[key] = value
    
    # 启动uvicorn
    print("🌐 启动Uvicorn服务器...")
    print("🎯 服务地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/api/v1/docs")
    print("🔍 健康检查: http://localhost:8000/health")
    print("⏹️ 停止服务: Ctrl+C")
    print("\n" + "=" * 40)
    
    try:
        # 使用subprocess启动uvicorn
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "app.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--log-level", "info",
            "--access-log"
        ]
        
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
