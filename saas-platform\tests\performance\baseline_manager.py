"""
性能基准管理器

负责管理性能基准数据的建立、存储、检索和对比分析。
支持多环境、多版本的基准数据管理。
"""

import json
import os
import statistics
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

import logging

logger = logging.getLogger(__name__)


class PerformanceMetric(Enum):
    """性能指标类型枚举"""
    RESPONSE_TIME = "response_time"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"
    CPU_USAGE = "cpu_usage"
    MEMORY_USAGE = "memory_usage"
    DATABASE_QUERY_TIME = "db_query_time"
    CONCURRENT_USERS = "concurrent_users"


@dataclass
class PerformanceData:
    """性能数据结构"""
    metric: PerformanceMetric
    value: float
    unit: str
    timestamp: datetime
    environment: str = "test"
    version: str = "unknown"
    test_case: str = "default"
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['metric'] = self.metric.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'PerformanceData':
        """从字典创建实例"""
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        data['metric'] = PerformanceMetric(data['metric'])
        return cls(**data)


@dataclass
class BaselineData:
    """基准数据结构"""
    metrics: Dict[str, PerformanceData]
    created_at: datetime
    environment: str
    version: str
    description: str = ""
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'metrics': {k: v.to_dict() for k, v in self.metrics.items()},
            'created_at': self.created_at.isoformat(),
            'environment': self.environment,
            'version': self.version,
            'description': self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'BaselineData':
        """从字典创建实例"""
        metrics = {k: PerformanceData.from_dict(v) for k, v in data['metrics'].items()}
        return cls(
            metrics=metrics,
            created_at=datetime.fromisoformat(data['created_at']),
            environment=data['environment'],
            version=data['version'],
            description=data.get('description', '')
        )


@dataclass
class ComparisonResult:
    """性能对比结果"""
    metric_name: str
    baseline_value: float
    current_value: float
    difference: float
    percentage_change: float
    status: str  # "improved", "degraded", "stable"
    threshold_exceeded: bool
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return asdict(self)


class BaselineManager:
    """性能基准管理器"""
    
    DEFAULT_THRESHOLDS = {
        PerformanceMetric.RESPONSE_TIME: 0.1,  # 10%性能退化阈值
        PerformanceMetric.THROUGHPUT: -0.05,   # 5%吞吐量下降阈值
        PerformanceMetric.ERROR_RATE: 0.01,    # 1%错误率增加阈值
        PerformanceMetric.CPU_USAGE: 0.15,     # 15%CPU使用率增加阈值
        PerformanceMetric.MEMORY_USAGE: 0.2,   # 20%内存使用增加阈值
    }
    
    def __init__(self, baseline_dir: str = "tests/performance/baselines"):
        """
        初始化基准管理器
        
        Args:
            baseline_dir: 基准数据存储目录
        """
        self.baseline_dir = Path(baseline_dir)
        self.baseline_dir.mkdir(parents=True, exist_ok=True)
        self.thresholds = self.DEFAULT_THRESHOLDS.copy()
        
        logger.info(f"基准管理器初始化完成，存储目录: {self.baseline_dir}")
    
    def create_baseline(self, 
                       metrics: List[PerformanceData], 
                       environment: str,
                       version: str,
                       description: str = "") -> BaselineData:
        """
        创建性能基准
        
        Args:
            metrics: 性能数据列表
            environment: 环境名称
            version: 版本号
            description: 基准描述
            
        Returns:
            BaselineData: 创建的基准数据
        """
        # 按测试用例和指标类型组织数据
        organized_metrics = {}
        for metric in metrics:
            key = f"{metric.test_case}_{metric.metric.value}"
            if key in organized_metrics:
                # 如果有多个相同指标的数据，取平均值
                existing = organized_metrics[key]
                existing.value = (existing.value + metric.value) / 2
                logger.debug(f"合并重复指标 {key}，新平均值: {existing.value}")
            else:
                organized_metrics[key] = metric
        
        baseline = BaselineData(
            metrics=organized_metrics,
            created_at=datetime.now(),
            environment=environment,
            version=version,
            description=description
        )
        
        # 保存到文件
        self._save_baseline(baseline)
        
        logger.info(f"创建基准完成: {environment}@{version}, 包含 {len(organized_metrics)} 个指标")
        return baseline
    
    def get_baseline(self, 
                    environment: str,
                    version: Optional[str] = None) -> Optional[BaselineData]:
        """
        获取性能基准
        
        Args:
            environment: 环境名称
            version: 版本号，如果为None则获取最新版本
            
        Returns:
            BaselineData: 基准数据，如果不存在则返回None
        """
        if version:
            baseline_file = self.baseline_dir / f"{environment}_{version}.json"
            if baseline_file.exists():
                return self._load_baseline(baseline_file)
        else:
            # 获取该环境下最新的基准
            pattern = f"{environment}_*.json"
            baseline_files = list(self.baseline_dir.glob(pattern))
            if baseline_files:
                # 按修改时间排序，取最新的
                latest_file = max(baseline_files, key=lambda f: f.stat().st_mtime)
                return self._load_baseline(latest_file)
        
        logger.warning(f"未找到基准数据: {environment}@{version or 'latest'}")
        return None
    
    def compare_with_baseline(self, 
                            current_metrics: List[PerformanceData],
                            baseline: BaselineData) -> List[ComparisonResult]:
        """
        与基准进行对比
        
        Args:
            current_metrics: 当前性能数据
            baseline: 基准数据
            
        Returns:
            List[ComparisonResult]: 对比结果列表
        """
        results = []
        
        # 组织当前数据
        current_data = {}
        for metric in current_metrics:
            key = f"{metric.test_case}_{metric.metric.value}"
            current_data[key] = metric
        
        # 逐项对比
        for metric_key, baseline_metric in baseline.metrics.items():
            if metric_key not in current_data:
                logger.warning(f"当前数据中缺少指标: {metric_key}")
                continue
            
            current_metric = current_data[metric_key]
            result = self._compare_metrics(baseline_metric, current_metric)
            results.append(result)
        
        # 检查新增的指标
        for metric_key in current_data:
            if metric_key not in baseline.metrics:
                logger.info(f"发现新增指标: {metric_key}")
        
        logger.info(f"性能对比完成，共对比 {len(results)} 个指标")
        return results
    
    def _compare_metrics(self, 
                        baseline: PerformanceData, 
                        current: PerformanceData) -> ComparisonResult:
        """对比两个指标数据"""
        baseline_value = baseline.value
        current_value = current.value
        
        # 计算差异
        difference = current_value - baseline_value
        percentage_change = (difference / baseline_value) if baseline_value != 0 else float('inf')
        
        # 确定状态
        metric_type = current.metric
        threshold = self.thresholds.get(metric_type, 0.1)  # 默认10%阈值
        
        if metric_type in [PerformanceMetric.ERROR_RATE, PerformanceMetric.RESPONSE_TIME,
                          PerformanceMetric.CPU_USAGE, PerformanceMetric.MEMORY_USAGE]:
            # 这些指标越小越好
            if percentage_change <= -0.05:  # 改善超过5%
                status = "improved"
            elif percentage_change >= threshold:
                status = "degraded"
            else:
                status = "stable"
            threshold_exceeded = percentage_change >= threshold
        else:
            # 吞吐量等指标越大越好
            if percentage_change >= 0.05:  # 改善超过5%
                status = "improved"
            elif percentage_change <= threshold:
                status = "degraded"
            else:
                status = "stable"
            threshold_exceeded = percentage_change <= threshold
        
        return ComparisonResult(
            metric_name=f"{current.test_case}_{metric_type.value}",
            baseline_value=baseline_value,
            current_value=current_value,
            difference=difference,
            percentage_change=percentage_change,
            status=status,
            threshold_exceeded=threshold_exceeded
        )
    
    def update_thresholds(self, thresholds: Dict[PerformanceMetric, float]):
        """更新性能阈值配置"""
        self.thresholds.update(thresholds)
        logger.info(f"更新性能阈值: {thresholds}")
    
    def list_baselines(self, environment: Optional[str] = None) -> List[Dict]:
        """
        列出所有基准数据
        
        Args:
            environment: 环境名称，如果为None则列出所有环境
            
        Returns:
            List[Dict]: 基准数据信息列表
        """
        pattern = f"{environment}_*.json" if environment else "*.json"
        baseline_files = list(self.baseline_dir.glob(pattern))
        
        baselines = []
        for file_path in baseline_files:
            try:
                baseline = self._load_baseline(file_path)
                baselines.append({
                    'environment': baseline.environment,
                    'version': baseline.version,
                    'created_at': baseline.created_at.isoformat(),
                    'description': baseline.description,
                    'metrics_count': len(baseline.metrics),
                    'file_path': str(file_path)
                })
            except Exception as e:
                logger.error(f"加载基准文件失败: {file_path}, 错误: {e}")
        
        return sorted(baselines, key=lambda x: x['created_at'], reverse=True)
    
    def cleanup_old_baselines(self, environment: str, keep_count: int = 5):
        """
        清理旧的基准数据，保留最新的几个版本
        
        Args:
            environment: 环境名称
            keep_count: 保留的版本数量
        """
        pattern = f"{environment}_*.json"
        baseline_files = list(self.baseline_dir.glob(pattern))
        
        if len(baseline_files) <= keep_count:
            return
        
        # 按修改时间排序
        baseline_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
        
        # 删除多余的文件
        for file_path in baseline_files[keep_count:]:
            try:
                file_path.unlink()
                logger.info(f"删除旧基准文件: {file_path}")
            except Exception as e:
                logger.error(f"删除基准文件失败: {file_path}, 错误: {e}")
    
    def _save_baseline(self, baseline: BaselineData):
        """保存基准数据到文件"""
        filename = f"{baseline.environment}_{baseline.version}.json"
        file_path = self.baseline_dir / filename
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(baseline.to_dict(), f, indent=2, ensure_ascii=False)
            logger.debug(f"基准数据已保存: {file_path}")
        except Exception as e:
            logger.error(f"保存基准数据失败: {file_path}, 错误: {e}")
            raise
    
    def _load_baseline(self, file_path: Path) -> BaselineData:
        """从文件加载基准数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return BaselineData.from_dict(data)
        except Exception as e:
            logger.error(f"加载基准数据失败: {file_path}, 错误: {e}")
            raise


# 便捷函数
def get_default_baseline_manager() -> BaselineManager:
    """获取默认配置的基准管理器实例"""
    return BaselineManager()


def create_performance_data(metric_type: PerformanceMetric,
                           value: float,
                           unit: str,
                           test_case: str = "default",
                           environment: str = "test",
                           version: str = "unknown") -> PerformanceData:
    """创建性能数据的便捷函数"""
    return PerformanceData(
        metric=metric_type,
        value=value,
        unit=unit,
        timestamp=datetime.now(),
        environment=environment,
        version=version,
        test_case=test_case
    ) 