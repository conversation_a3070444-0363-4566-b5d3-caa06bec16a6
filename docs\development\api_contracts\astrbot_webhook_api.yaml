openapi: 3.0.0
info:
  title: AstrBot Instance Webhook API
  description: |
    AstrBot实例向SaaS平台发送的Webhook接口规范
    
    本API定义了AstrBot实例如何向SaaS平台报告消息、状态变化等事件。
  version: "1.0.0"
  contact:
    name: AstrBot SaaS Team
    email: <EMAIL>
  license:
    name: AGPL-3.0
    url: https://www.gnu.org/licenses/agpl-3.0.html

servers:
  - url: https://api.astrbot.com/api/v1/webhooks
    description: 生产环境
  - url: https://staging-api.astrbot.com/api/v1/webhooks
    description: 测试环境

paths:
  /astrbot/{tenant_id}:
    post:
      summary: 统一Webhook入口
      description: |
        接收来自AstrBot实例的所有类型事件通知
        
        支持的事件类型：
        - message.received - 收到新消息
        - message.sent - 发送消息完成
        - session.created - 创建新会话
        - session.closed - 会话结束
        - instance.status_changed - 实例状态变化
        - instance.heartbeat - 实例心跳
      parameters:
        - name: tenant_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: 租户ID
        - name: X-AstrBot-Signature
          in: header
          required: false
          schema:
            type: string
          description: HMAC-SHA256签名，用于验证请求合法性
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebhookEvent'
            examples:
              message_received:
                summary: 收到新消息
                value:
                  event_type: "message.received"
                  timestamp: "2024-01-01T12:00:00Z"
                  data:
                    session_id: "123e4567-e89b-12d3-a456-************"
                    message_id: "msg_123456"
                    content: "用户消息内容"
                    sender:
                      id: "user_123"
                      name: "张三"
                      platform: "wechat"
              session_created:
                summary: 创建新会话
                value:
                  event_type: "session.created"
                  timestamp: "2024-01-01T12:00:00Z"
                  data:
                    session_id: "123e4567-e89b-12d3-a456-************"
                    user_id: "user_123"
                    platform: "wechat"
                    initial_message: "用户的第一条消息"
      responses:
        '200':
          description: 事件处理成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookResponse'
        '400':
          description: 请求格式错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: 签名验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /messages/{tenant_id}:
    post:
      summary: 消息Webhook（兼容接口）
      description: |
        专门处理消息相关的Webhook事件
        为了兼容旧版本AstrBot实例而保留的接口
      parameters:
        - name: tenant_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: 租户ID
        - name: X-Webhook-Signature
          in: header
          required: false
          schema:
            type: string
          description: HMAC-SHA256签名
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MessageWebhookEvent'
      responses:
        '200':
          description: 消息处理成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookResponse'
        '400':
          description: 请求格式错误
        '403':
          description: 签名验证失败
        '500':
          description: 服务器内部错误

components:
  schemas:
    WebhookEvent:
      type: object
      required:
        - event_type
        - timestamp
        - data
      properties:
        event_type:
          type: string
          enum:
            - "message.received"
            - "message.sent"
            - "session.created"
            - "session.closed"
            - "instance.status_changed"
            - "instance.heartbeat"
          description: 事件类型
        timestamp:
          type: string
          format: date-time
          description: 事件发生时间戳 (ISO 8601)
        data:
          type: object
          description: 事件数据，根据event_type有不同结构
          additionalProperties: true
        metadata:
          type: object
          description: 额外的元数据信息
          properties:
            instance_id:
              type: string
              description: AstrBot实例ID
            version:
              type: string
              description: AstrBot实例版本
            platform:
              type: string
              description: 消息平台 (wechat, qq, telegram等)

    MessageWebhookEvent:
      type: object
      required:
        - event_type
        - timestamp
        - data
      properties:
        event_type:
          type: string
          enum:
            - "message.received"
            - "message.sent"
          default: "message.received"
        timestamp:
          type: string
          format: date-time
        data:
          $ref: '#/components/schemas/MessageData'

    MessageData:
      type: object
      required:
        - session_id
        - message_id
        - content
        - sender
      properties:
        session_id:
          type: string
          format: uuid
          description: 会话ID
        message_id:
          type: string
          description: 消息ID
        content:
          type: string
          description: 消息内容
        message_type:
          type: string
          enum: ["text", "image", "voice", "file", "location"]
          default: "text"
          description: 消息类型
        sender:
          $ref: '#/components/schemas/MessageSender'
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/MessageAttachment'
          description: 消息附件（图片、文件等）

    MessageSender:
      type: object
      required:
        - id
        - name
        - platform
      properties:
        id:
          type: string
          description: 发送者在平台上的唯一标识
        name:
          type: string
          description: 发送者显示名称
        platform:
          type: string
          enum: ["wechat", "qq", "telegram", "discord", "slack"]
          description: 消息平台
        avatar_url:
          type: string
          format: uri
          description: 发送者头像URL

    MessageAttachment:
      type: object
      required:
        - type
        - url
      properties:
        type:
          type: string
          enum: ["image", "voice", "file", "video"]
          description: 附件类型
        url:
          type: string
          format: uri
          description: 附件URL
        filename:
          type: string
          description: 文件名
        size:
          type: integer
          description: 文件大小（字节）

    WebhookResponse:
      type: object
      required:
        - status
        - message
      properties:
        status:
          type: string
          enum: ["success", "error", "ignored"]
          description: 处理状态
        message:
          type: string
          description: 处理结果说明
        data:
          type: object
          description: 返回的附加数据
          additionalProperties: true

    ErrorResponse:
      type: object
      required:
        - error
        - message
      properties:
        error:
          type: string
          description: 错误类型
        message:
          type: string
          description: 错误详细说明
        code:
          type: integer
          description: 错误代码
        details:
          type: object
          description: 错误详情
          additionalProperties: true

  securitySchemes:
    WebhookSignature:
      type: apiKey
      in: header
      name: X-AstrBot-Signature
      description: |
        HMAC-SHA256签名验证
        
        计算方法：
        1. 使用租户的webhook_secret作为密钥
        2. 对请求体的原始JSON字符串计算HMAC-SHA256
        3. 将结果转换为十六进制字符串
        4. 添加"sha256="前缀

security:
  - WebhookSignature: [] 