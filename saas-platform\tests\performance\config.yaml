# AstrBot SaaS Platform - 性能测试配置
# 定义性能测试的各种参数、阈值和场景配置

# 全局配置
global:
  # 基础URL配置
  base_url: "http://localhost:8000"
  
  # 默认超时设置（秒）
  request_timeout: 10
  connection_timeout: 5
  
  # 测试环境配置
  environment: "test"
  version: "1.0.0"
  
  # 日志级别
  log_level: "INFO"
  
  # 结果输出目录
  output_dir: "tests/performance/results"
  
  # 基准数据目录
  baseline_dir: "tests/performance/baselines"

# 性能阈值配置
thresholds:
  # 响应时间阈值（秒）
  response_time:
    critical: 2.0    # 超过此值为严重问题
    warning: 1.0     # 超过此值为警告
    good: 0.5        # 低于此值为良好
  
  # 吞吐量阈值（请求/秒）
  throughput:
    minimum: 10      # 最低要求
    good: 50         # 良好水平
    excellent: 100   # 优秀水平
  
  # 错误率阈值（百分比）
  error_rate:
    critical: 5.0    # 超过5%为严重
    warning: 1.0     # 超过1%为警告
    acceptable: 0.1  # 低于0.1%为可接受
  
  # 资源使用率阈值（百分比）
  cpu_usage:
    critical: 90     # 超过90%为严重
    warning: 70      # 超过70%为警告
    normal: 50       # 低于50%为正常
  
  memory_usage:
    critical: 85     # 超过85%为严重
    warning: 70      # 超过70%为警告
    normal: 50       # 低于50%为正常
  
  # 性能退化阈值（百分比变化）
  degradation:
    response_time: 10     # 响应时间增加10%视为退化
    throughput: -5        # 吞吐量减少5%视为退化
    error_rate: 1         # 错误率增加1%视为退化
    cpu_usage: 15         # CPU使用率增加15%视为退化
    memory_usage: 20      # 内存使用率增加20%视为退化

# 测试场景配置
test_scenarios:
  
  # API性能测试场景
  api_tests:
    
    # 租户管理相关
    tenant_management:
      - name: "获取租户详情"
        description: "测试单个租户信息查询的性能"
        endpoint: "/api/v1/tenants/{tenant_id}"
        method: "GET"
        auth_required: true
        expected_response_time: 0.2
        concurrent_users: 5
        iterations: 20
        
      - name: "租户列表查询"
        description: "测试租户列表分页查询性能"
        endpoint: "/api/v1/tenants?skip=0&limit=20"
        method: "GET"
        auth_required: true
        expected_response_time: 0.3
        concurrent_users: 3
        iterations: 15
        
      - name: "创建租户"
        description: "测试租户创建接口性能"
        endpoint: "/api/v1/tenants"
        method: "POST"
        auth_required: false
        payload:
          name: "性能测试租户_{random}"
          email: "perf_test_{random}@example.com"
          plan: "basic"
        expected_response_time: 0.5
        concurrent_users: 2
        iterations: 10
    
    # 用户管理相关
    user_management:
      - name: "用户列表查询"
        description: "测试用户列表分页查询性能"
        endpoint: "/api/v1/users?skip=0&limit=20"
        method: "GET"
        auth_required: true
        expected_response_time: 0.3
        concurrent_users: 4
        iterations: 15
        
      - name: "获取用户详情"
        description: "测试单个用户信息查询性能"
        endpoint: "/api/v1/users/{user_id}"
        method: "GET"
        auth_required: true
        expected_response_time: 0.2
        concurrent_users: 5
        iterations: 20
    
    # 会话管理相关
    session_management:
      - name: "创建会话"
        description: "测试会话创建接口性能"
        endpoint: "/api/v1/sessions"
        method: "POST"
        auth_required: true
        payload:
          user_id: "test:user_{random}"
          platform: "test"
          channel_type: "direct"
          priority: 5
        expected_response_time: 0.4
        concurrent_users: 3
        iterations: 12
        
      - name: "获取会话列表"
        description: "测试会话列表查询性能"
        endpoint: "/api/v1/sessions?skip=0&limit=20&status=active"
        method: "GET"
        auth_required: true
        expected_response_time: 0.3
        concurrent_users: 4
        iterations: 15
        
      - name: "更新会话状态"
        description: "测试会话状态更新性能"
        endpoint: "/api/v1/sessions/{session_id}/status"
        method: "PATCH"
        auth_required: true
        payload:
          status: "closed"
        expected_response_time: 0.3
        concurrent_users: 2
        iterations: 10
    
    # 消息管理相关
    message_management:
      - name: "发送消息"
        description: "测试消息发送接口性能"
        endpoint: "/api/v1/messages"
        method: "POST"
        auth_required: true
        payload:
          session_id: "{session_id}"
          content: "性能测试消息内容 - {timestamp}"
          message_type: "text"
          sender_type: "user"
          sender_id: "test:user_001"
        expected_response_time: 0.4
        concurrent_users: 5
        iterations: 20
        
      - name: "获取消息列表"
        description: "测试消息列表查询性能"
        endpoint: "/api/v1/messages?session_id={session_id}&skip=0&limit=50"
        method: "GET"
        auth_required: true
        expected_response_time: 0.3
        concurrent_users: 4
        iterations: 15
        
      - name: "批量消息操作"
        description: "测试批量消息处理性能"
        endpoint: "/api/v1/messages/batch"
        method: "POST"
        auth_required: true
        payload:
          operation: "mark_read"
          message_ids: ["{message_id_1}", "{message_id_2}"]
        expected_response_time: 0.6
        concurrent_users: 2
        iterations: 8
    
    # 数据分析相关
    analytics:
      - name: "会话统计概览"
        description: "测试会话统计数据查询性能"
        endpoint: "/api/v1/analytics/sessions-overview"
        method: "GET"
        auth_required: true
        expected_response_time: 0.8
        concurrent_users: 2
        iterations: 10
        
      - name: "消息统计分析"
        description: "测试消息统计分析性能"
        endpoint: "/api/v1/analytics/messages-analysis"
        method: "GET"
        auth_required: true
        query_params:
          start_date: "2024-01-01"
          end_date: "2024-12-31"
          granularity: "day"
        expected_response_time: 1.2
        concurrent_users: 2
        iterations: 8
        
      - name: "用户行为分析"
        description: "测试用户行为数据分析性能"
        endpoint: "/api/v1/analytics/user-behavior"
        method: "GET"
        auth_required: true
        query_params:
          metric: "active_users"
          period: "7d"
        expected_response_time: 1.0
        concurrent_users: 2
        iterations: 6

  # 数据库性能测试场景
  database_tests:
    
    # 查询性能测试
    query_performance:
      - name: "用户查询优化测试"
        description: "测试用户表查询的性能优化效果"
        query: "SELECT * FROM users WHERE tenant_id = :tenant_id LIMIT 100"
        expected_execution_time: 0.05
        iterations: 50
        
      - name: "会话复杂查询测试"
        description: "测试会话表的复杂连接查询性能"
        query: >
          SELECT s.*, u.nickname, COUNT(m.id) as message_count 
          FROM sessions s 
          JOIN users u ON s.user_id = u.id 
          LEFT JOIN messages m ON s.id = m.session_id 
          WHERE s.tenant_id = :tenant_id 
          GROUP BY s.id, u.nickname 
          ORDER BY s.created_at DESC 
          LIMIT 20
        expected_execution_time: 0.1
        iterations: 30
        
      - name: "消息时间范围查询测试"
        description: "测试消息时间范围查询和索引效果"
        query: >
          SELECT * FROM messages 
          WHERE tenant_id = :tenant_id 
          AND timestamp BETWEEN :start_date AND :end_date 
          ORDER BY timestamp DESC 
          LIMIT 100
        expected_execution_time: 0.08
        iterations: 40
    
    # 写入性能测试
    write_performance:
      - name: "批量消息插入测试"
        description: "测试批量消息插入的性能"
        operation: "batch_insert"
        table: "messages"
        batch_size: 100
        expected_execution_time: 0.2
        iterations: 10
        
      - name: "并发会话创建测试"
        description: "测试并发创建会话的数据库性能"
        operation: "concurrent_insert"
        table: "sessions"
        concurrent_count: 5
        expected_execution_time: 0.1
        iterations: 20

# 负载测试配置
load_tests:
  
  # 轻负载测试
  light_load:
    description: "轻量负载测试，模拟正常用户使用"
    duration: 300  # 5分钟
    users: 10
    ramp_up_time: 30  # 30秒内达到目标用户数
    scenarios:
      - "获取租户详情"
      - "用户列表查询"
      - "发送消息"
    
  # 中等负载测试
  medium_load:
    description: "中等负载测试，模拟业务高峰期"
    duration: 600  # 10分钟
    users: 50
    ramp_up_time: 60
    scenarios:
      - "获取租户详情"
      - "用户列表查询"
      - "创建会话"
      - "发送消息"
      - "获取消息列表"
    
  # 重负载测试
  heavy_load:
    description: "重负载测试，测试系统极限容量"
    duration: 900  # 15分钟
    users: 100
    ramp_up_time: 120
    scenarios:
      - "获取租户详情"
      - "用户列表查询"
      - "创建会话"
      - "发送消息"
      - "获取消息列表"
      - "会话统计概览"
      - "消息统计分析"

# 压力测试配置
stress_tests:
  
  # CPU密集型测试
  cpu_intensive:
    description: "CPU密集型操作压力测试"
    scenarios:
      - "消息统计分析"
      - "用户行为分析"
    concurrent_users: 20
    duration: 300
    
  # 内存密集型测试
  memory_intensive:
    description: "内存密集型操作压力测试"
    scenarios:
      - "获取消息列表"
      - "批量消息操作"
    concurrent_users: 15
    duration: 300
    
  # 数据库密集型测试
  database_intensive:
    description: "数据库密集型操作压力测试"
    scenarios:
      - "会话复杂查询测试"
      - "消息时间范围查询测试"
      - "批量消息插入测试"
    concurrent_users: 10
    duration: 300

# 监控配置
monitoring:
  
  # 系统监控
  system_metrics:
    enabled: true
    interval: 1.0  # 监控间隔（秒）
    metrics:
      - cpu_usage
      - memory_usage
      - disk_io
      - network_io
  
  # 应用监控
  application_metrics:
    enabled: true
    metrics:
      - response_time
      - throughput
      - error_rate
      - active_connections
  
  # 数据库监控
  database_metrics:
    enabled: true
    metrics:
      - connection_count
      - query_execution_time
      - lock_wait_time
      - cache_hit_ratio

# 报告配置
reporting:
  
  # 输出格式
  formats:
    - json
    - html
    - csv
  
  # 报告内容
  include:
    - summary_statistics
    - detailed_metrics
    - comparison_with_baseline
    - performance_trends
    - recommendations
  
  # 图表配置
  charts:
    enabled: true
    types:
      - response_time_trends
      - throughput_distribution
      - error_rate_timeline
      - resource_usage_graphs
  
  # 自动报告
  auto_report:
    enabled: true
    schedule: "daily"  # daily, weekly, monthly
    recipients:
      - "<EMAIL>"
      - "<EMAIL>"

# 环境特定配置
environments:
  
  # 开发环境
  development:
    base_url: "http://localhost:8000"
    database_url: "postgresql://test:test@localhost:5432/testdb"
    reduced_iterations: true  # 减少迭代次数以加快测试
    
  # 测试环境
  testing:
    base_url: "http://test-api.example.com"
    database_url: "***********************************/testdb"
    full_test_suite: true
    
  # 预生产环境
  staging:
    base_url: "http://staging-api.example.com"
    database_url: "**************************************/proddb"
    production_like_tests: true
    
  # 生产环境（只读监控）
  production:
    base_url: "http://api.example.com"
    monitoring_only: true  # 只进行监控，不执行性能测试
    alert_thresholds:
      response_time: 1.0
      error_rate: 1.0
      cpu_usage: 80
      memory_usage: 75 