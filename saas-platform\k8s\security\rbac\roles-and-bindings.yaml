# k8s/security/rbac/roles-and-bindings.yaml
# 为每个ServiceAccount定义最小化的权限

# API Server的角色和绑定
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: astrbot-saas
  name: astrbot-api-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: astrbot-api-binding
  namespace: astrbot-saas
subjects:
- kind: ServiceAccount
  name: astrbot-saas-api
roleRef:
  kind: Role
  name: astrbot-api-role
  apiGroup: rbac.authorization.k8s.io

---
# Worker的角色和绑定
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: astrbot-saas
  name: astrbot-worker-role
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list", "create", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: astrbot-worker-binding
  namespace: astrbot-saas
subjects:
- kind: ServiceAccount
  name: astrbot-saas-worker
roleRef:
  kind: Role
  name: astrbot-worker-role
  apiGroup: rbac.authorization.k8s.io 