"""
AstrBot SaaS Platform - Mock工具
===============================

提供常用的Mock工具和配置，减少测试代码重复

自动生成: TestCodeOptimizer
"""

from unittest.mock import Mock, MagicMock, AsyncMock, patch
from typing import Dict, Any, Optional, List
import asyncio

class MockHelpers:
    """Mock辅助工具类"""
    
    @staticmethod
    def create_mock_db_session():
        """创建模拟数据库会话"""
        mock_session = AsyncMock()
        mock_session.add = Mock()
        mock_session.commit = AsyncMock()
        mock_session.rollback = AsyncMock()
        mock_session.refresh = AsyncMock()
        mock_session.close = AsyncMock()
        return mock_session
    
    @staticmethod
    def create_mock_tenant(tenant_id: int = 1, name: str = "Test Tenant"):
        """创建模拟租户对象"""
        mock_tenant = Mock()
        mock_tenant.id = tenant_id
        mock_tenant.name = name
        mock_tenant.email = f"test@{name.lower().replace(' ', '')}.com"
        mock_tenant.is_active = True
        return mock_tenant
    
    @staticmethod
    def create_mock_user(user_id: int = 1, username: str = "testuser"):
        """创建模拟用户对象"""
        mock_user = Mock()
        mock_user.id = user_id
        mock_user.username = username
        mock_user.email = f"{username}@test.com"
        mock_user.is_active = True
        mock_user.tenant_id = 1
        return mock_user
    
    @staticmethod
    def create_mock_service_response(
        data: Optional[Any] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ):
        """创建模拟服务响应"""
        mock_response = Mock()
        mock_response.data = data
        mock_response.success = success
        mock_response.error_message = error_message
        return mock_response
    
    @staticmethod
    def create_mock_async_client():
        """创建模拟异步HTTP客户端"""
        mock_client = AsyncMock()
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json = AsyncMock(return_value={"status": "success"})
        mock_client.get = AsyncMock(return_value=mock_response)
        mock_client.post = AsyncMock(return_value=mock_response)
        mock_client.put = AsyncMock(return_value=mock_response)
        mock_client.delete = AsyncMock(return_value=mock_response)
        return mock_client
    
    @staticmethod
    def create_mock_redis():
        """创建模拟Redis客户端"""
        mock_redis = AsyncMock()
        mock_redis.get = AsyncMock(return_value=None)
        mock_redis.set = AsyncMock(return_value=True)
        mock_redis.delete = AsyncMock(return_value=1)
        mock_redis.exists = AsyncMock(return_value=False)
        return mock_redis

class AsyncContextManager:
    """异步上下文管理器工具"""
    
    def __init__(self, mock_obj):
        self.mock_obj = mock_obj
    
    async def __aenter__(self):
        return self.mock_obj
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass

# 便捷函数
def mock_db_session():
    """快速创建数据库会话Mock"""
    return MockHelpers.create_mock_db_session()

def mock_tenant(tenant_id=1, name="Test Tenant"):
    """快速创建租户Mock"""
    return MockHelpers.create_mock_tenant(tenant_id, name)

def mock_user(user_id=1, username="testuser"):
    """快速创建用户Mock"""
    return MockHelpers.create_mock_user(user_id, username)

def mock_async_client():
    """快速创建异步客户端Mock"""
    return MockHelpers.create_mock_async_client()

def mock_service_success(data=None):
    """快速创建成功响应Mock"""
    return MockHelpers.create_mock_service_response(data=data, success=True)

def mock_service_error(error_message="Test error"):
    """快速创建错误响应Mock"""
    return MockHelpers.create_mock_service_response(success=False, error_message=error_message)
