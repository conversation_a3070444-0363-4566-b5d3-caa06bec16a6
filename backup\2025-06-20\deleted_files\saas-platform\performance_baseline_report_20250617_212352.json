{"test_summary": {"total_tests": 62, "successful_tests": 60, "success_rate": 96.7741935483871, "test_duration_seconds": 2.034018}, "performance_metrics": {"avg_response_time_ms": 170.0981275076744, "min_response_time_ms": 7.307328435793487, "max_response_time_ms": 1521.4546693941445, "median_response_time_ms": 106.69699534368434}, "test_results": [{"endpoint": "/health", "method": "GET", "response_time_ms": 21.659505275109144, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.6518474}, {"endpoint": "/health", "method": "GET", "response_time_ms": 7.307328435793487, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.704448}, {"endpoint": "/health", "method": "GET", "response_time_ms": 8.718587049242768, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.7551043}, {"endpoint": "/health", "method": "GET", "response_time_ms": 12.956109886147065, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.8056052}, {"endpoint": "/health", "method": "GET", "response_time_ms": 9.136781081383353, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.8565004}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 17.54601622416718, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.9068937}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 20.60448590402556, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.9576}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 19.309689893714378, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.0078812}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 14.651225262299413, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.0580997}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 14.791585855449826, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.1082969}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 77.30602131325058, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.1590323}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 142.8481867799784, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.2095883}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 76.86285492910014, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.2597005}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 475.27412771364527, "expected_time_ms": 100, "success": false, "status_code": 500, "timestamp": **********.309979}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 80.46623689017025, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.360567}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 175.24949842256476, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.4109106}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 165.76473525990392, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.4612415}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 254.73909625210382, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.5115826}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 140.71843967979046, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.562281}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 200.5342924337524, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.612757}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 116.06393174700105, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.6632984}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 110.10810429864344, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.7136126}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 123.6136344001294, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.764052}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 154.1624287983572, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.8142092}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 219.00222534876144, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.8650935}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 102.56316011958549, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.9158878}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 124.82764012200997, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.9667819}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 122.00968167725836, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750166632.0172095}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 98.42765183858882, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750166632.0676763}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 131.9286762551026, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750166632.1192982}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 592.547951702274, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750166632.169678}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 430.65766972793466, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750166632.229842}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 407.54095472899536, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750166632.2815464}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 1521.4546693941445, "expected_time_ms": 500, "success": false, "status_code": 500, "timestamp": 1750166632.3320024}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 496.1945936702161, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750166632.3824644}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 320.82208554395163, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750166632.4328294}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 349.0421399086344, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750166632.4835722}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 389.9296445575537, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750166632.5340123}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 310.2116125830569, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750166632.5851164}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 220.11538988032547, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750166632.635669}, {"operation": "SELECT租户列表", "response_time_ms": 86.75634212447181, "expected_time_ms": 50, "success": true}, {"operation": "SELECT租户列表", "response_time_ms": 80.8253184941126, "expected_time_ms": 50, "success": true}, {"operation": "SELECT租户列表", "response_time_ms": 52.69280026702812, "expected_time_ms": 50, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 124.98642403946624, "expected_time_ms": 80, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 103.28588638872523, "expected_time_ms": 80, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 84.13189091257328, "expected_time_ms": 80, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 72.30579100626792, "expected_time_ms": 60, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 73.635055769063, "expected_time_ms": 60, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 83.71139362339265, "expected_time_ms": 60, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 57.27635653370079, "expected_time_ms": 40, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 45.11219009332754, "expected_time_ms": 40, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 71.06077506281657, "expected_time_ms": 40, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 36.34647128486255, "expected_time_ms": 30, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 51.282371768423516, "expected_time_ms": 30, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 33.90196697167872, "expected_time_ms": 30, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 140.01690944243796, "expected_time_ms": 150, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 238.0390543385703, "expected_time_ms": 150, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 152.65574648007996, "expected_time_ms": 150, "success": true}, {"scenario": "5个并发用户", "concurrent_users": 5, "avg_response_time_ms": 158.74760587684605, "p95_response_time_ms": 178.03893928036354, "max_response_time_ms": 174.91040734704842, "expected_max_ms": 200, "success": true}, {"scenario": "10个并发用户", "concurrent_users": 10, "avg_response_time_ms": 206.54149816406277, "p95_response_time_ms": 240.16183111201934, "max_response_time_ms": 234.2600457592836, "expected_max_ms": 350, "success": true}, {"scenario": "20个并发用户", "concurrent_users": 20, "avg_response_time_ms": 319.4545764075589, "p95_response_time_ms": 337.22052978691954, "max_response_time_ms": 337.28748672302044, "expected_max_ms": 600, "success": true}, {"scenario": "50个并发用户", "concurrent_users": 50, "avg_response_time_ms": 615.4073076384163, "p95_response_time_ms": 638.1259368598858, "max_response_time_ms": 639.1595077864539, "expected_max_ms": 1200, "success": true}], "recommendations": ["🔧 数据库连接池优化: 确保数据库连接池大小适合并发负载", "📊 API响应时间监控: 建立API响应时间监控和告警机制", "🚀 缓存策略: 对频繁查询的数据实施Redis缓存", "⚡ 异步处理: AI功能采用异步处理减少响应时间", "🔍 SQL查询优化: 审查慢查询并添加适当索引", "📈 负载均衡: 考虑在高并发场景下实施负载均衡", "💾 数据库分片: 大量数据时考虑数据库分片策略", "🛡️ 限流保护: 实施API限流保护系统稳定性"]}