#!/usr/bin/env python3
"""
AstrBot SaaS Platform 服务器启动脚本
包含详细的启动日志和错误处理
"""

import os
import sys
import asyncio
import signal
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_environment():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    
    # 确保必要的环境变量存在
    env_vars = {
        'DATABASE_URL': 'postgresql+asyncpg://astrbot:astrbot123@localhost:5432/astrbot_saas',
        'REDIS_URL': 'redis://:redis123@localhost:6379/0',
        'SECRET_KEY': 'dev-secret-key',
        'ENVIRONMENT': 'development'
    }
    
    for key, default_value in env_vars.items():
        if key not in os.environ:
            os.environ[key] = default_value
            print(f"   设置 {key}={default_value}")

def check_prerequisites():
    """检查启动前提条件"""
    print("🔍 检查启动前提条件...")
    
    try:
        # 检查应用导入
        from app.main import app
        print(f"✅ FastAPI应用导入成功: {app.title}")
        
        # 检查uvicorn
        import uvicorn
        print(f"✅ uvicorn可用: {uvicorn.__version__}")
        
        return True
    except Exception as e:
        print(f"❌ 前提条件检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_server():
    """启动服务器"""
    print("🚀 启动AstrBot SaaS Platform服务器...")
    
    try:
        import uvicorn
        
        # 服务器配置 - 使用导入字符串支持reload
        config = {
            'app': 'app.main:app',  # 使用导入字符串而不是应用实例
            'host': '0.0.0.0',
            'port': 8000,
            'log_level': 'info',
            'reload': True,
            'access_log': True
        }
        
        print(f"📡 服务器配置:")
        print(f"   应用: {config['app']}")
        print(f"   主机: {config['host']}")
        print(f"   端口: {config['port']}")
        print(f"   重载: {config['reload']}")
        print(f"   日志级别: {config['log_level']}")
        
        print(f"\n🌐 服务将启动在: http://localhost:{config['port']}")
        print(f"📚 API文档: http://localhost:{config['port']}/docs")
        print(f"🔍 健康检查: http://localhost:{config['port']}/health")
        print(f"\n按 Ctrl+C 停止服务器\n")
        
        # 启动服务器
        uvicorn.run(**config)
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

def main():
    """主函数"""
    print("🌟 AstrBot SaaS Platform 服务器启动器")
    print("=" * 50)
    
    # 设置环境
    setup_environment()
    
    # 检查前提条件
    if not check_prerequisites():
        print("❌ 前提条件检查失败，无法启动服务器")
        return 1
    
    # 启动服务器
    return start_server()

if __name__ == "__main__":
    sys.exit(main()) 