#!/usr/bin/env python3
"""
简化的API文档检查脚本
专注于检查FastAPI路由的文档完整性
"""

import ast
import os
from pathlib import Path
from typing import List, Dict, Any


def analyze_api_file(file_path: Path) -> List[Dict[str, Any]]:
    """分析单个API文件"""
    issues = []

    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        tree = ast.parse(content)

        # 查找路由装饰器
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # 检查是否有路由装饰器
                for decorator in node.decorator_list:
                    if isinstance(decorator, ast.Call):
                        if (
                            hasattr(decorator.func, "attr")
                            and hasattr(decorator.func.value, "id")
                            and decorator.func.value.id == "router"
                            and decorator.func.attr
                            in ["get", "post", "put", "patch", "delete"]
                        ):

                            # 这是一个路由函数，检查其文档
                            issue = check_route_documentation(
                                decorator, node, file_path
                            )
                            if issue:
                                issues.append(issue)

    except Exception as e:
        print(f"Error analyzing {file_path}: {e}")

    return issues


def check_route_documentation(
    decorator: ast.Call, func_node: ast.FunctionDef, file_path: Path
) -> Dict[str, Any]:
    """检查路由文档完整性"""
    method = decorator.func.attr.upper()

    # 检查装饰器参数
    has_response_model = False
    has_summary = False
    has_description = False
    has_tags = False

    for keyword in decorator.keywords:
        if keyword.arg == "response_model":
            has_response_model = True
        elif keyword.arg == "summary":
            has_summary = True
        elif keyword.arg == "description":
            has_description = True
        elif keyword.arg == "tags":
            has_tags = True

    # 检查函数文档字符串
    has_docstring = ast.get_docstring(func_node) is not None

    # 收集缺失的文档项
    missing_items = []

    if not has_response_model and method in ["GET", "POST", "PUT", "PATCH"]:
        missing_items.append("response_model")

    if not has_summary:
        missing_items.append("summary")

    if not has_description and not has_docstring:
        missing_items.append("description或docstring")

    if not has_tags:
        missing_items.append("tags")

    if missing_items:
        return {
            "file": str(file_path.relative_to(Path("app"))),
            "function": func_node.name,
            "method": method,
            "line": func_node.lineno,
            "missing": missing_items,
        }

    return None


def main():
    """主函数"""
    print("检查API文档完整性...")

    api_dir = Path("app/api/v1")
    if not api_dir.exists():
        print(f"API目录不存在: {api_dir}")
        return

    all_issues = []

    for py_file in api_dir.glob("*.py"):
        if py_file.name == "__init__.py":
            continue

        print(f"分析文件: {py_file}")
        issues = analyze_api_file(py_file)
        all_issues.extend(issues)

    # 输出结果
    print(f"\n发现 {len(all_issues)} 个API文档问题:")
    print("=" * 60)

    for issue in all_issues:
        print(f"文件: {issue['file']}")
        print(f"函数: {issue['function']} ({issue['method']}) - 行 {issue['line']}")
        print(f"缺失: {', '.join(issue['missing'])}")
        print("-" * 40)

    if not all_issues:
        print("✅ 所有API端点都有完整的文档!")


if __name__ == "__main__":
    main()
