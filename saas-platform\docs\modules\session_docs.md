# 📖 技术文档：会话模型 (session.py)

## 🎯 1. 模块概述

**功能**：定义`Session`数据模型，对应数据库中的`sessions`表。

**核心职责**：
- **数据结构**：定义会话的核心字段，如`status`, `platform`, `user_id`。
- **关系**：定义与`Tenant`, `User`, `Message`模型的关系。
- **业务逻辑**：包含与会话相关的业务逻辑，如`is_active`属性。

## 🚀 2. 快速使用

### 2.1 创建会话

```python
from app.models.session import Session

new_session = Session(
    tenant_id=tenant.id,
    user_id=user.id,
    platform="web",
    status="active",
)
db.add(new_session)
await db.commit()
```

### 2.2 查询会话

```python
from sqlalchemy import select

stmt = select(Session).where(Session.status == "active")
result = await db.execute(stmt)
sessions = result.scalars().all()
```

## 🏗️ 3. 架构设计

### 3.1 关键字段

- **`id`**: `UUID` - 主键
- **`tenant_id`**: `UUID` - 所属租户ID
- **`user_id`**: `str` - 关联的用户ID
- **`platform`**: `str` - 来源平台
- **`status`**: `SessionStatus` - 会话状态
- **`assigned_staff_id`**: `str` - 分配的客服ID
- **`last_message_at`**: `datetime` - 最后消息时间

### 3.2 关系

```mermaid
erDiagram
    SESSION }o--|| TENANT : "belongs to"
    SESSION }o--|| USER : "belongs to"
    SESSION ||--o{ MESSAGE : "has"
```

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_session_model.py`

## 💡 6. 维护与扩展

- **会话摘要**：`context_summary`字段可以用于存储由AI生成的会话摘要。
- **会话评分**：可以添加字段来存储用户或系统对会话的评分。
- **会话归档**：可以添加定时任务来归档长时间未活跃的会话。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 