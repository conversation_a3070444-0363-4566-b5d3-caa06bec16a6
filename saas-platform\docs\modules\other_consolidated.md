# Other 综合文档

*合并自: deps_comprehensive_docs.md, permissions_comprehensive_docs.md, schemas_comprehensive_docs.md*

*合并时间: 2025-06-20 16:08:22*

---

## 来源: deps_comprehensive_docs.md

# AstrBot SaaS API 依赖注入系统 - 技术文档

## 📋 模块概览

### 🎯 模块定位
`app/api/deps.py` 是 AstrBot SaaS 平台的核心依赖注入（Dependency Injection）系统。它构建在 FastAPI 的依赖注入机制之上，为API路由提供了统一、安全、可重用的认证、授权和数据访问依赖。

### ✨ 核心功能
- **认证与授权** - 提供基于 JWT Token 和 API Key 的认证机制。
- **用户与租户注入** - 将当前认证的用户和租户对象注入到API路由中。
- **租户隔离** - 确保所有数据访问都严格限制在当前租户范围内。
- **角色验证** - 提供基于角色的访问控制（RBAC）依赖。
- **标准化异常处理** - 定义了统一的认证和授权异常。

### 🏗️ 依赖注入流程

```mermaid
graph TD
    subgraph "API路由"
        A[GET /users/me]
    end
    
    subgraph "依赖注入 (deps.py)"
        B(get_current_user)
        C(get_current_user_id)
        D(get_current_tenant_id)
        E(get_current_token)
        F(HTTPBearer)
        G(get_db)
    end
    
    subgraph "核心模块"
        H[数据库]
        I[安全模块]
    end
    
    A -- Depends --> B
    B -- Depends --> C
    B -- Depends --> D
    B -- Depends --> G
    C -- Depends --> E
    D -- Depends --> E
    E -- Depends --> F
    
    B -- 使用 --> H
    C -- 使用 --> I
    D -- 使用 --> I
    
    style B fill:#cde4ff,stroke:#6a8ebf,stroke-width:2px
    style E fill:#e6ffc2,stroke:#8dbf6a,stroke-width:2px
```
**流程说明:**
1.  API路由 `GET /users/me` 声明依赖于 `get_current_user`。
2.  FastAPI 解析依赖关系，发现 `get_current_user` 依赖于 `get_current_user_id`, `get_current_tenant_id` 和 `get_db`。
3.  这个依赖链一直追溯到最基础的依赖：`HTTPBearer`（从请求头提取 `Authorization: Bearer <token>`）和 `get_db`（提供数据库会话）。
4.  请求到达时，FastAPI 按顺序执行依赖函数，从Token中提取信息，查询数据库，最终将 `User` 对象注入到路由函数中。
5.  在任何步骤验证失败，都会抛出相应的HTTPException，终止请求并返回标准错误。

## 🔧 核心依赖项分析

### 1. 认证依赖链

- **`get_current_token(...)`**
  - **功能**: 从 `Authorization` 头中提取并返回原始的JWT Token字符串。
  - **依赖**: `fastapi.security.HTTPBearer`。
  - **失败场景**: 请求头中缺少或格式不正确的 `Authorization`。

- **`get_current_user_id(...)` / `get_current_tenant_id(...)`**
  - **功能**: 从Token中解析出 `user_id` (sub) 和 `tenant_id`。
  - **依赖**: `get_current_token`。
  - **失败场景**: Token过期、签名无效、或Token中缺少必要的信息。

- **`get_current_user(...)`**
  - **功能**: 查询数据库，获取当前用户`User`的ORM模型实例。
  - **依赖**: `get_current_user_id`, `get_current_tenant_id`, `get_db`。
  - **核心安全检查**:
    1.  验证用户是否存在于数据库中。
    2.  **强制验证用户的 `tenant_id` 是否与Token中的 `tenant_id` 匹配**，这是防止水平越权攻击的关键。
  - **失败场景**: 用户不存在，或用户尝试访问不属于其租户的数据。

- **`get_current_tenant(...)`**
  - **功能**: 获取当前租户`Tenant`的ORM模型实例。
  - **依赖**: `get_current_tenant_id`, `get_db`。
  - **核心安全检查**: 验证租户是否存在且状态为 `active`。
  - **失败场景**: 租户被禁用或不存在。

### 2. 可选认证依赖

- **`get_optional_current_user(...)`**
  - **功能**: 尝试获取当前用户，但如果认证失败或未提供认证信息，则返回 `None` 而不是抛出异常。
  - **应用场景**: 用于那些对认证用户和匿名用户都开放的API端点（例如，公开的产品列表，但如果用户登录了，可以显示个性化推荐）。

### 3. 角色和权限依赖

- **`require_role(required_role: str)`**
  - **功能**: 这是一个依赖工厂，返回一个用于检查用户角色的依赖函数。
  - **实现**: 从Token中解析 `role` 信息，并与要求的角色进行比较。
  - **示例**: `require_admin_role = require_role("admin")` 创建了一个只允许 `admin` 或更高权限角色访问的依赖。

- **`validate_tenant_resource_access(...)`**
  - **功能**: 快捷依赖，用于验证当前用户是否有权访问某个特定租户的资源。
  - **应用场景**: 在管理后台，当超级管理员需要操作特定租户的资源时，确保操作的目标 `resource_tenant_id` 是合法的。

### 4. API Key 认证依赖

- **`require_api_key(...)`**
  - **功能**: 从 `X-API-Key` 请求头获取API密钥，并查询数据库以验证其有效性。
  - **应用场景**: 主要用于服务器到服务器的通信，如 Webhooks 或第三方系统集成。
  - **返回**: 如果验证成功，返回对应的 `Tenant` 对象。

### 5. 自定义异常

模块内定义了三个自定义HTTP异常，使错误响应更具语义化：
- `AuthenticationError` (401 Unauthorized)
- `AuthorizationError` (403 Forbidden)
- `TenantAccessError` (403 Forbidden)

## 🚀 使用指南

### 1. 保护API路由

#### 获取当前用户和租户
这是最常见的使用模式。只需在API路由的参数中声明依赖，即可获得经过完全验证的用户和租户对象。

```python
from fastapi import APIRouter, Depends
from app.models.user import User
from app.models.tenant import Tenant
from app.api.deps import get_current_user, get_current_tenant

router = APIRouter()

@router.get("/me")
async def read_users_me(
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """获取当前用户信息，此路由需要有效认证"""
    return {
        "user_id": current_user.id,
        "tenant_id": current_tenant.id,
        "tenant_name": current_tenant.name
    }
```

### 2. 基于角色的访问控制

使用预定义的角色依赖来限制对特定路由的访问。

```python
from app.api.deps import require_admin_role

@router.post("/admin/settings", dependencies=[Depends(require_admin_role)])
async def update_admin_settings(settings: dict):
    """
    更新管理后台设置。
    只有 admin 或更高级别的角色才能访问。
    `dependencies` 参数会执行依赖，但不会将结果注入函数。
    """
    # ... update settings logic
    return {"message": "Settings updated successfully"}
```

### 3. 使用API Key保护路由

保护 Webhook 或外部服务调用的端点。

```python
from app.api.deps import require_api_key

@router.post("/webhooks/github")
async def handle_github_webhook(
    payload: dict,
    tenant: Tenant = Depends(require_api_key)
):
    """
    处理来自GitHub的Webhook。
    请求必须包含有效的 X-API-Key 头。
    `tenant` 对象是与该API Key关联的租户。
    """
    # 使用 tenant.id 来处理特定租户的逻辑
    process_webhook_for_tenant(tenant.id, payload)
    return {"status": "ok"}
```

### 4. 创建新的依赖

当需要新的、可重用的验证逻辑时，可以轻松创建新的依赖函数。

```python
from app.schemas.tenant import TenantPlan

async def require_pro_plan(tenant: Tenant = Depends(get_current_tenant)):
    """
    依赖项：要求租户必须是 'pro' 或 'enterprise' 套餐
    """
    if tenant.plan not in [TenantPlan.PRO, TenantPlan.ENTERPRISE]:
        raise AuthorizationError(
            detail="This feature requires a Pro plan or higher."
        )

# 在路由中使用新依赖
@router.get("/features/advanced-analytics", dependencies=[Depends(require_pro_plan)])
async def get_advanced_analytics():
    return {"data": "Super advanced analytics data"}
```

## 🛡️ 安全核心

- **依赖链验证**: 任何一个依赖环节失败（如Token过期、用户不存在、租户不匹配），整个请求链都会被中断，确保了安全性。
- **租户隔离的强制执行**: `get_current_user` 依赖中的 `user.tenant_id == tenant_id` 检查是多租户安全的核心，它从根本上防止了一个用户访问到其他租户的数据。
- **单一职责**: 每个依赖函数只做一件事（如获取Token、获取用户ID、查询用户），使得代码清晰、易于测试和维护。

## 🔮 扩展规划

- **细粒度权限 (Scopes)**: 可以扩展 `require_role` 或创建一个新的 `require_scope` 依赖，以支持 OAuth2 风格的权限范围检查（例如，从Token中解析 `scopes` 列表）。
- **缓存**: 对于不经常变动的依赖（如从ID查询用户），可以引入缓存（如 `fastapi-cache`）来减少数据库负载。
- **第三方认证集成**: 可以创建新的依赖项来处理第三方认证（如 `Auth0`, `Okta`），通过验证外部Token并映射到内部用户。

---

## 📝 总结

`app/api/deps.py` 是一个设计精良的模块，它充分利用了FastAPI的依赖注入特性，构建了一个健壮、安全且易于扩展的认证和授权系统。通过将复杂的验证逻辑封装在可重用的依赖项中，极大地简化了API路由的实现，并提高了整个应用的一致性和安全性。 

---

## 来源: permissions_comprehensive_docs.md

# AstrBot SaaS 权限控制系统 - 技术文档

## 📋 模块概览

### 🎯 模块定位
`app/core/permissions.py` 是 AstrBot SaaS 平台的核心权限控制系统，提供细粒度的基于角色的访问控制（RBAC）机制。该模块确保系统的安全性和多租户数据隔离。

### ✨ 核心功能
- **装饰器权限检查** - 基于装饰器的函数级权限控制
- **依赖注入权限验证** - FastAPI 依赖注入式权限检查
- **复合权限支持** - 支持 ANY/ALL 逻辑的复合权限验证
- **预定义权限类** - 常用权限的便捷访问接口
- **结构化日志记录** - 完整的权限检查审计日志

### 🏗️ 系统架构

```mermaid
graph TB
    A[API 路由] --> B[权限装饰器]
    A --> C[权限检查器类]
    B --> D[RBAC 服务]
    C --> D
    D --> E[权限验证逻辑]
    E --> F[数据库权限查询]
    E --> G[日志记录]
    
    subgraph "权限系统层次"
        H[装饰器层] --> I[检查器层]
        I --> J[服务层]
        J --> K[数据层]
    end
    
    subgraph "权限类型"
        L[单一权限]
        M[任意权限 ANY]
        N[全部权限 ALL]
    end
```

## 🔧 核心组件分析

### 1. PermissionError 异常类

```python
class PermissionError(HTTPException):
    """权限错误异常 - 专用的权限拒绝异常处理"""
    
    def __init__(self, detail: str, resource: Optional[str], action: Optional[str]):
        # 继承 HTTPException，返回 403 状态码
        # 自动记录权限拒绝日志，便于安全审计
```

**设计特点：**
- 继承 `HTTPException`，直接返回 HTTP 403 状态码
- 自动记录安全审计日志，包含资源和操作信息
- 提供结构化的错误信息，便于前端处理

### 2. 权限装饰器系统

#### 2.1 基础权限装饰器

```python
@require_permission("session", "read")
async def get_sessions(...):
    """单一权限要求的装饰器使用"""
    pass
```

**实现原理：**
- 通过 `functools.wraps` 保持原函数元数据
- 从函数参数中获取用户、租户和数据库会话
- 调用 RBAC 服务进行权限验证
- 验证失败时抛出 `PermissionError`

#### 2.2 复合权限装饰器

```python
# 任意权限满足即可
@require_any_permission([("session", "read"), ("session", "write")])
async def manage_session(...):
    pass

# 所有权限都必须满足
@require_all_permissions([("session", "read"), ("message", "write")])
async def complex_operation(...):
    pass
```

**应用场景：**
- `require_any_permission`: 角色权限覆盖，如管理员或普通用户都可访问
- `require_all_permissions`: 复杂操作权限，需要多个资源的操作权限

### 3. PermissionChecker 类

```python
class PermissionChecker:
    """FastAPI 依赖注入式权限检查器"""
    
    async def __call__(
        self,
        current_user: User = Depends(get_current_user),
        current_tenant: Tenant = Depends(get_current_tenant),
        db: AsyncSession = Depends(get_db),
    ) -> bool:
        # 执行权限检查逻辑
```

**设计优势：**
- 实现 `__call__` 方法，可作为 FastAPI 依赖项
- 自动注入当前用户、租户和数据库会话
- 返回布尔值，验证失败时抛出异常

### 4. CommonPermissions 预定义权限

```python
class CommonPermissions:
    """常用权限的便捷访问接口"""
    
    # 预定义权限检查器实例
    tenant_read = PermissionChecker("tenant", "read")
    session_write = PermissionChecker("session", "write")
    ai_use = PermissionChecker("ai", "use")
    
    # 便捷别名方法
    @staticmethod
    def can_view_analytics():
        return CommonPermissions.analytics_read
```

## 📡 API 接口详解

### 1. 装饰器接口

| 装饰器 | 参数 | 功能说明 | 使用示例 |
|--------|------|----------|----------|
| `@require_permission` | resource, action | 单一权限检查 | `@require_permission("session", "read")` |
| `@require_any_permission` | permissions 列表 | 任意权限满足 | `@require_any_permission([("a", "read"), ("b", "write")])` |
| `@require_all_permissions` | permissions 列表 | 所有权限必须满足 | `@require_all_permissions([("a", "read"), ("b", "write")])` |

### 2. 便捷装饰器

| 装饰器 | 等效权限 | 应用场景 |
|--------|----------|----------|
| `@require_tenant_read` | tenant:read | 租户信息查看 |
| `@require_session_write` | session:write | 会话创建/修改 |
| `@require_message_read` | message:read | 消息历史查看 |
| `@require_ai_use` | ai:use | AI 功能使用 |
| `@require_instance_manage` | instance:write | 实例管理操作 |

### 3. 依赖注入接口

```python
# 在路由中使用权限检查器
@router.get("/protected-endpoint")
async def protected_endpoint(
    _: bool = Depends(CommonPermissions.session_read),
    current_user: User = Depends(get_current_user)
):
    return {"message": "Access granted"}
```

## 🚀 使用指南

### 1. 基础使用模式

#### 1.1 装饰器模式（推荐用于复杂逻辑）

```python
from app.core.permissions import require_permission, PermissionError

@require_permission("session", "read")
async def get_user_sessions(
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取用户会话列表 - 需要会话读权限"""
    try:
        # 业务逻辑实现
        sessions = await session_service.get_sessions(user_id=current_user.id)
        return sessions
    except PermissionError:
        # 权限错误会自动被 FastAPI 处理，返回 403
        raise
```

#### 1.2 依赖注入模式（推荐用于简单检查）

```python
from app.core.permissions import CommonPermissions

@router.post("/sessions")
async def create_session(
    session_data: SessionCreate,
    _: bool = Depends(CommonPermissions.session_write),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建新会话 - 依赖注入式权限检查"""
    # 权限检查已在依赖中完成，这里直接实现业务逻辑
    session = await session_service.create_session(session_data, current_user.id)
    return session
```

### 2. 复合权限使用

```python
# 场景：管理员或会话所有者都可以删除会话
@require_any_permission([
    ("session", "admin"),      # 管理员权限
    ("session", "delete")      # 普通删除权限
])
async def delete_session(session_id: int, ...):
    pass

# 场景：需要同时具备读取和写入权限的复杂操作
@require_all_permissions([
    ("session", "read"),       # 读取现有数据
    ("message", "write"),      # 写入新消息
    ("analytics", "create")    # 创建分析记录
])
async def complex_session_operation(...):
    pass
```

### 3. 自定义权限检查

```python
# 创建自定义权限检查器
custom_permission = PermissionChecker("custom_resource", "special_action")

@router.get("/custom-endpoint")
async def custom_endpoint(
    _: bool = Depends(custom_permission),
    ...
):
    pass
```

## 🔐 权限系统设计

### 1. 权限模型

```mermaid
erDiagram
    User ||--o{ UserRole : has
    UserRole }o--|| Role : assigned
    Role ||--o{ RolePermission : contains
    RolePermission }o--|| Permission : grants
    Permission {
        string resource
        string action
    }
    
    User {
        uuid id
        string email
        uuid tenant_id
    }
    
    Role {
        uuid id
        string name
        uuid tenant_id
    }
```

### 2. 权限资源类型

| 资源类型 | 操作类型 | 权限示例 | 应用场景 |
|----------|----------|----------|----------|
| `tenant` | read/write/delete | tenant:read | 租户信息管理 |
| `user` | read/write/delete | user:write | 用户账号管理 |
| `session` | read/write/delete | session:read | 会话数据访问 |
| `message` | read/write/delete | message:write | 消息内容管理 |
| `role` | read/write/assign | role:assign | 角色权限分配 |
| `ai` | use/config | ai:use | AI 功能使用 |
| `instance` | read/write/config | instance:config | AstrBot 实例管理 |
| `analytics` | read/create/export | analytics:export | 数据分析功能 |

### 3. 权限继承和层级

```mermaid
graph TD
    A[超级管理员] --> B[租户管理员]
    B --> C[普通管理员]
    C --> D[普通用户]
    
    A --> |全部权限| E[所有资源操作]
    B --> |租户范围| F[租户内所有操作]
    C --> |部分管理| G[指定资源管理]
    D --> |基础操作| H[读取和基本写入]
```

## 📊 性能优化

### 1. 权限缓存策略

```python
# 建议在 RBAC 服务中实现权限结果缓存
from functools import lru_cache
from datetime import datetime, timedelta

class RBACService:
    @lru_cache(maxsize=1000)
    async def check_user_permission_cached(
        self, user_id: str, tenant_id: str, resource: str, action: str
    ) -> bool:
        """缓存权限检查结果，减少数据库查询"""
        # 实现缓存逻辑，设置合适的过期时间
        pass
```

### 2. 批量权限检查

```python
async def check_multiple_permissions(
    user_id: str, 
    tenant_id: str, 
    permissions: list[tuple[str, str]]
) -> dict[str, bool]:
    """批量检查多个权限，减少数据库往返次数"""
    # 一次查询检查多个权限
    pass
```

## 🛡️ 安全最佳实践

### 1. 权限检查时机

```python
# ✅ 正确：在数据访问前进行权限检查
@require_permission("session", "read")
async def get_session(session_id: int, ...):
    session = await session_service.get_session(session_id)
    return session

# ❌ 错误：在数据访问后进行权限检查
async def get_session_wrong(session_id: int, ...):
    session = await session_service.get_session(session_id)  # 数据泄露风险
    # 权限检查太晚了
    if not has_permission:
        raise PermissionError()
    return session
```

### 2. 默认拒绝原则

```python
# ✅ 正确：默认无权限，明确授权
@require_permission("sensitive_data", "read")
async def get_sensitive_data(...):
    pass

# ❌ 错误：默认允许，条件限制
async def get_data_wrong(...):
    if not special_condition:  # 容易遗漏权限检查
        raise PermissionError()
```

### 3. 权限粒度控制

```python
# ✅ 推荐：细粒度权限控制
@require_permission("user_profile", "edit")  # 具体到编辑用户资料
async def update_user_profile(...):
    pass

@require_permission("user_password", "change")  # 密码修改单独权限
async def change_password(...):
    pass

# ❌ 不推荐：粗粒度权限
@require_permission("user", "write")  # 过于宽泛
async def any_user_operation(...):
    pass
```

## 🔧 集成指南

### 1. 在新路由中集成权限

```python
# 第一步：定义路由并添加权限检查
from app.core.permissions import require_permission, CommonPermissions

@router.get("/api/v1/my-resource")
async def get_my_resource(
    # 方式1：使用依赖注入
    _: bool = Depends(CommonPermissions.session_read),
    # 方式2：准备装饰器所需的参数
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    pass

# 方式2：使用装饰器
@require_permission("my_resource", "read")
async def get_my_resource_decorated(...):
    pass
```

### 2. 新权限类型的添加

```python
# 1. 在 CommonPermissions 中添加新权限
class CommonPermissions:
    # 添加新的权限检查器
    new_resource_read = PermissionChecker("new_resource", "read")
    new_resource_write = PermissionChecker("new_resource", "write")

# 2. 添加便捷装饰器
def require_new_resource_read(func: Callable) -> Callable:
    return require_permission("new_resource", "read")(func)

# 3. 在数据库中配置对应的权限记录
# INSERT INTO permissions (resource, action) VALUES ('new_resource', 'read');
```

## 📈 监控和审计

### 1. 权限日志分析

```python
# 权限检查日志格式
{
    "level": "WARNING",
    "message": "permission_denied",
    "detail": "Permission denied: session:write",
    "resource": "session",
    "action": "write",
    "user_id": "uuid",
    "tenant_id": "uuid",
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### 2. 安全监控指标

- **权限拒绝率**: 监控异常的权限访问尝试
- **权限检查延迟**: 性能监控
- **权限使用分布**: 了解功能使用情况
- **异常权限组合**: 检测潜在的权限配置问题

## 🐛 故障排除

### 1. 常见问题

#### 问题1：装饰器权限检查失败
```
错误：Permission check setup error (HTTP 500)
原因：依赖注入参数没有正确传递给装饰器
解决：确保路由函数参数包含 current_user, current_tenant, db
```

#### 问题2：权限检查器依赖注入失败
```
错误：权限检查器无法获取当前用户
原因：路由没有正确配置依赖注入
解决：检查 Depends(get_current_user) 是否正确配置
```

### 2. 调试技巧

```python
# 启用详细权限日志
import logging
logging.getLogger('app.core.permissions').setLevel(logging.DEBUG)

# 临时禁用权限检查（仅用于调试）
async def debug_endpoint():
    # 临时注释权限装饰器进行调试
    # @require_permission("test", "debug")
    pass
```

## 🔮 扩展规划

### 1. 高级权限功能

- **条件权限**: 基于上下文的动态权限检查
- **时间权限**: 基于时间范围的权限控制
- **配额权限**: 基于使用量的权限限制

### 2. 性能优化

- **权限预加载**: 批量预加载用户权限
- **分布式缓存**: Redis 权限缓存
- **权限计算优化**: 权限表达式引擎

---

## 📝 总结

AstrBot SaaS 权限控制系统提供了完整的 RBAC 权限管理功能，支持装饰器和依赖注入两种使用模式，具备良好的安全性和可扩展性。通过合理的权限设计和规范的使用方式，可以确保系统的安全性和数据隔离。 

---

## 来源: schemas_comprehensive_docs.md

# AstrBot SaaS Schemas 技术文档

## 📋 模块概览

### 🎯 模块定位
`app/schemas/` 模块是 AstrBot SaaS 平台的数据接口层，基于 Pydantic v2 构建。它定义了所有API的输入（请求体验证）和输出（响应体序列化）数据结构，是API层与服务层之间的核心契约。

### ✨ 核心功能
- **数据验证** - 严格的类型检查和业务规则验证
- **API契约** - 作为 OpenAPI/Swagger 文档的单一事实来源
- **序列化/反序列化** - 在JSON数据和Python对象之间高效转换
- **代码重用** - 通过继承和组合实现DRY（Don't Repeat Yourself）原则
- **清晰的层级分离** - 将数据模型（ORM）与API接口模型解耦

### 🏗️ Schema 设计模式

#### 1. CRUD 设计模式
项目中广泛采用基于 `Create`, `Read`, `Update` 的模式来组织Schema：

- **`Base`**: 包含所有子类共享的字段和验证规则。
- **`Create`**: 用于创建新资源，通常继承自 `Base`，并添加创建时必需的字段。
- **`Update`**: 用于更新资源，所有字段都是可选的（`Optional`），允许部分更新。
- **`Read`**: 用于从API读取资源，通常继承自 `Base`，并包含数据库生成或需要从ORM模型转换的字段（如 `id`, `created_at`）。

```mermaid
classDiagram
    class TenantBase {
        +str name
        +EmailStr email
        +TenantPlan plan
    }
    class TenantCreate {
        <<request>>
        +dict metadata
    }
    class TenantUpdate {
        <<request>>
        +Optional~str~ name
        +Optional~EmailStr~ email
        +Optional~TenantPlan~ plan
    }
    class TenantRead {
        <<response>>
        +UUID id
        +TenantStatus status
        +datetime created_at
    }

    TenantBase <|-- TenantCreate
    TenantBase <|-- TenantRead
```

#### 2. 通用响应模型
为了确保API响应的一致性，`app/schemas/common.py` 中定义了标准响应模型：

- **`StandardResponse[T]`**: 通用的成功/失败响应结构。
- **`PaginatedResponse[T]`**: 标准化的分页数据响应。
- **`ErrorResponse`**: 标准化的错误信息响应。

## 🔧 核心 Schema 分析

### 1. `common.py` - 通用模型

- **`StandardResponse[T]`**: 定义了 `success`, `message`, `data` 三个字段，是所有非分页API响应的基础。
- **`PaginatedResponse[T]`**: 封装了分页逻辑，自动计算 `has_next` 和 `has_prev`，简化了分页API的实现。
- **`PaginationParams` / `SearchParams`**: 作为依赖项注入到API路由中，处理分页和搜索参数。

### 2. `tenant.py` - 租户 Schema

- **`TenantCreate`**: 创建租户的请求体，包含名称、邮箱、套餐和元数据。
- **`TenantUpdate`**: 更新租户信息，所有字段均为可选。
- **`TenantRead`**: 从数据库模型转换而来，包含了 `id` 和时间戳，并启用了 `from_attributes=True`。
- **`TenantReadWithAPIKey`**: 继承自 `TenantRead`，额外包含了 `api_key`，用于需要特权的接口。
- **`TenantStatusUpdate`**: 用于更新租户状态，包含 `status` 和 `reason` 字段。
- **`TenantStats`**: 定义了租户的统计数据结构。

### 3. `user.py` - 用户 Schema

- **`UserCreate`**: 创建用户的请求体，需要提供 `tenant_id`。
- **`UserRead`**: 用户信息的标准响应，包含从数据库模型计算出的 `display_name`。
- **`PlatformUserInfo`**: 一个特殊的Schema，使用 `@validator` 自动生成复合ID `composite_id`。这展示了Pydantic强大的数据处理能力。
- **`UserBatchCreate`**: 支持批量创建用户，对列表长度进行了限制（1-100）。

### 4. `auth.py` - 认证 Schema

- **`LoginRequest` / `LoginResponse`**: 处理用户登录和Token返回。
- **`RegisterRequest`**: 包含密码和确认密码的匹配验证，使用了 `@validator`。
- **`RefreshTokenRequest` / `RefreshTokenResponse`**: 用于刷新访问令牌。
- **`ChangePasswordRequest` / `ResetPassword*`**: 提供了完整的密码管理流程。
- **`ApiKey*`**: 定义了API密钥的创建和响应模型。
- **`UserProfile`**: 用于展示用户个人资料的聚合模型。

### 5. `session.py` - 会话 Schema

- **`SessionCreate`**: 创建新会话的请求，包含用户ID、平台信息等。
- **`SessionRead`**: 标准的会话响应，从ORM模型映射而来。
- **`SessionUpdate`**: 支持部分更新会话的状态、优先级或摘要。

### 6. `message.py` - 消息 Schema

- **`MessageCreate`**: 创建消息的请求，包含会话ID、内容、发送者信息等。
- **`MessageRead`**: 消息的标准响应，利用 `@computed_field` 添加了多个计算属性（如 `is_from_user`），增强了响应体的可用性，而无需修改ORM模型。
- **`IncomingMessageData`**: 用于处理来自外部平台的传入消息，是一个解耦的设计。

### 7. `analytics.py` - 数据分析 Schema

- **`AnalyticsFilter`**: 定义了统一的数据分析过滤条件。
- **`TimeSeriesData`**: 标准的时间序列数据点结构。
- **`SessionStatsResponse` / `MessageStatsResponse`**: 结构化的统计数据响应。
- **`DashboardOverview`**: 一个复杂的聚合模型，用于前端仪表板的完整数据展示。

## 🚀 使用指南

### 1. 在API路由中使用Schema

```python
from fastapi import APIRouter, Depends
from app.schemas.tenant import TenantCreate, TenantRead
from app.services.tenant_service import TenantService

router = APIRouter()

# 使用 TenantCreate 作为请求体验证
# 使用 TenantRead 作为响应模型
@router.post("/", response_model=TenantRead, status_code=201)
async def create_tenant(
    tenant_data: TenantCreate,
    tenant_service: TenantService = Depends()
):
    """
    创建一个新租户。
    - 请求体将根据 TenantCreate 模型进行验证。
    - 返回值将根据 TenantRead 模型进行序列化。
    """
    tenant = await tenant_service.create_tenant(tenant_data)
    return tenant
```

### 2. ORM模型到Schema的转换

Pydantic的 `from_attributes=True` (在 `ConfigDict` 中) 是实现ORM模型到Schema自动转换的关键。

```python
# in app/schemas/tenant.py
class TenantRead(TenantBase):
    id: UUID
    # ... other fields

    model_config = ConfigDict(
        from_attributes=True, # 允许从ORM对象的属性进行映射
    )

# in service layer
from app.models.tenant import Tenant as TenantModel
from app.schemas.tenant import TenantRead

async def get_tenant(tenant_id: UUID) -> TenantRead:
    db_tenant: TenantModel = await db.get(TenantModel, tenant_id)
    # Pydantic会自动将db_tenant的属性映射到TenantRead的字段
    return TenantRead.model_validate(db_tenant)
```

### 3. 创建新的Schema

1.  **确定目的**: 是用于创建（Create）、更新（Update）还是读取（Read）？
2.  **创建Base模型**: 如果有共享字段，先创建一个 `...Base` 模型。
3.  **继承和扩展**:
    - `Create` 模型继承自 `Base`，添加创建时需要的字段。
    - `Update` 模型通常独立，所有字段设为 `Optional`。
    - `Read` 模型继承自 `Base`，添加数据库生成的字段，并设置 `from_attributes=True`。
4.  **添加验证器**: 使用 `@validator` 或 `@field_validator` 添加自定义验证逻辑。
5.  **提供示例**: 在 `ConfigDict` 的 `json_schema_extra` 中提供清晰的 `example`，这将直接显示在API文档中。

```python
# 示例：创建一个新的 "Product" Schema
from pydantic import BaseModel, Field, ConfigDict

# 1. Base Model
class ProductBase(BaseModel):
    name: str = Field(..., max_length=100)
    price: float = Field(..., gt=0)

# 2. Create Model
class ProductCreate(ProductBase):
    sku: str = Field(..., pattern=r'^[A-Z0-9]{8,12}$')

# 3. Read Model
class ProductRead(ProductBase):
    id: int
    sku: str
    
    model_config = ConfigDict(from_attributes=True)
```

## 🛡️ 数据验证和安全性

- **类型安全**: Pydantic 强制执行严格的类型检查，防止无效数据进入系统。
- **边界验证**: 使用 `Field` 的 `min_length`, `max_length`, `ge`, `gt` 等参数进行边界检查。
- **格式验证**: `EmailStr` 等特殊类型提供了内置的格式验证。
- **自定义验证**: `@validator` 提供了实现复杂业务规则验证的强大机制。
- **防止敏感信息泄露**: 通过定义不同的 `Read` Schema，可以精确控制API响应中包含的字段，避免意外泄露数据库中的敏感信息（如密码哈希、内部状态等）。

## 🔮 扩展和维护

- **保持一致性**: 在添加新功能时，遵循既有的 `Create/Read/Update` 模式。
- **利用通用模型**: 尽可能重用 `common.py` 中的模型，如分页和标准响应。
- **版本控制**: 当API发生重大变化时，可以创建新的Schema版本（例如，`UserReadV2`），而不是直接修改现有模型，以保持向后兼容性。
- **文档就是代码**: 始终保持Schema中的 `description` 和 `example` 更新，因为它们直接生成API文档。

---

## 📝 总结

`app/schemas/` 模块是AstrBot应用中类型安全和API文档生成的基石。通过其分层和可组合的设计，它不仅保证了数据的正确性，还极大地提高了开发效率和API的清晰度。 

---

