#!/usr/bin/env python3
"""
测试覆盖率分析脚本
分析现有测试覆盖率并建议改进方案
"""

import ast
import os
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Set
import json


class CoverageAnalyzer:
    """覆盖率分析器"""

    def __init__(self):
        self.app_files = []
        self.test_files = []
        self.coverage_data = {}
        self.uncovered_modules = []

    def analyze_app_structure(self) -> Dict[str, List[str]]:
        """分析app目录结构"""
        structure = {
            "models": [],
            "schemas": [],
            "services": [],
            "api": [],
            "core": [],
            "utils": [],
        }

        app_path = Path("app")
        if not app_path.exists():
            print(f"警告: app目录不存在，当前目录: {os.getcwd()}")
            return structure

        for root, dirs, files in os.walk(app_path):
            for file in files:
                if file.endswith(".py") and file != "__init__.py":
                    file_path = Path(root) / file
                    rel_path = str(file_path.relative_to(app_path))

                    # 分类 - 修复条件判断
                    root_str = str(root).replace("\\", "/")
                    if "/models" in root_str or root_str.endswith("models"):
                        structure["models"].append(rel_path)
                    elif "/schemas" in root_str or root_str.endswith("schemas"):
                        structure["schemas"].append(rel_path)
                    elif "/services" in root_str or root_str.endswith("services"):
                        structure["services"].append(rel_path)
                    elif "/api" in root_str or root_str.endswith("api"):
                        structure["api"].append(rel_path)
                    elif "/core" in root_str or root_str.endswith("core"):
                        structure["core"].append(rel_path)
                    elif "/utils" in root_str or root_str.endswith("utils"):
                        structure["utils"].append(rel_path)

        return structure

    def analyze_test_structure(self) -> Dict[str, List[str]]:
        """分析测试目录结构"""
        structure = {"unit": [], "integration": [], "e2e": [], "performance": []}

        tests_path = Path("tests")
        if not tests_path.exists():
            print(f"警告: tests目录不存在，当前目录: {os.getcwd()}")
            return structure

        for root, dirs, files in os.walk(tests_path):
            for file in files:
                if file.endswith(".py") and file.startswith("test_"):
                    file_path = Path(root) / file
                    rel_path = str(file_path.relative_to(tests_path))

                    # 分类 - 修复条件判断
                    root_str = str(root).replace("\\", "/")
                    if "/unit" in root_str or root_str.endswith("unit"):
                        structure["unit"].append(rel_path)
                    elif "/integration" in root_str or root_str.endswith("integration"):
                        structure["integration"].append(rel_path)
                    elif "/e2e" in root_str or root_str.endswith("e2e"):
                        structure["e2e"].append(rel_path)
                    elif "/performance" in root_str or root_str.endswith("performance"):
                        structure["performance"].append(rel_path)

        return structure

    def find_missing_tests(
        self, app_structure: Dict[str, List[str]], test_structure: Dict[str, List[str]]
    ) -> Dict[str, List[str]]:
        """查找缺失的测试"""
        missing = {
            "models": [],
            "schemas": [],
            "services": [],
            "api": [],
            "core": [],
            "utils": [],
        }

        # 检查每个app模块是否有对应的单元测试
        for category, files in app_structure.items():
            tested_modules = set()

            # 从测试文件中提取测试的模块
            for test_file in test_structure["unit"]:
                # 简单的文件名匹配
                test_name = Path(test_file).stem.replace("test_", "")
                tested_modules.add(test_name)

            for app_file in files:
                module_name = Path(app_file).stem
                if module_name not in tested_modules:
                    missing[category].append(app_file)

        return missing

    def analyze_function_coverage(self, file_path: str) -> Dict[str, List[str]]:
        """分析文件中的函数覆盖率"""
        try:
            with open(f"app/{file_path}", "r", encoding="utf-8") as f:
                content = f.read()

            tree = ast.parse(content)

            functions = []
            classes = []

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    if not node.name.startswith("_"):  # 公共函数
                        functions.append(node.name)
                elif isinstance(node, ast.ClassDef):
                    classes.append(node.name)
                    # 类中的公共方法
                    for item in node.body:
                        if isinstance(
                            item, ast.FunctionDef
                        ) and not item.name.startswith("_"):
                            functions.append(f"{node.name}.{item.name}")

            return {"functions": functions, "classes": classes}
        except Exception as e:
            print(f"分析文件 {file_path} 失败: {e}")
            return {"functions": [], "classes": []}

    def generate_coverage_report(self) -> Dict:
        """生成覆盖率报告"""
        print("📊 分析测试覆盖率...")

        app_structure = self.analyze_app_structure()
        test_structure = self.analyze_test_structure()
        missing_tests = self.find_missing_tests(app_structure, test_structure)

        report = {
            "summary": {
                "total_app_files": sum(len(files) for files in app_structure.values()),
                "total_test_files": sum(
                    len(files) for files in test_structure.values()
                ),
                "app_structure": app_structure,
                "test_structure": test_structure,
                "missing_tests": missing_tests,
            },
            "recommendations": self.generate_recommendations(missing_tests),
        }

        return report

    def generate_recommendations(
        self, missing_tests: Dict[str, List[str]]
    ) -> List[str]:
        """生成测试改进建议"""
        recommendations = []

        for category, files in missing_tests.items():
            if files:
                recommendations.append(
                    f"为{category}模块创建测试: {len(files)}个文件需要测试"
                )
                for file in files[:3]:  # 只显示前3个
                    recommendations.append(f"  - {file}")
                if len(files) > 3:
                    recommendations.append(f"  ... 还有{len(files) - 3}个文件")

        return recommendations

    def create_test_templates(self, missing_tests: Dict[str, List[str]]) -> None:
        """为缺失的测试创建模板"""
        print("📝 创建测试模板...")

        for category, files in missing_tests.items():
            if not files:
                continue

            for file_path in files[:2]:  # 只为前2个文件创建模板
                module_name = Path(file_path).stem
                test_file_path = f"tests/unit/test_{module_name}.py"

                if not Path(test_file_path).exists():
                    self._create_test_template(file_path, test_file_path, category)

    def _create_test_template(
        self, app_file: str, test_file: str, category: str
    ) -> None:
        """创建单个测试模板"""
        module_name = Path(app_file).stem
        class_name = "".join(word.capitalize() for word in module_name.split("_"))

        # 分析app文件中的函数和类
        coverage_info = self.analyze_function_coverage(app_file)

        template = f'''"""
测试 {module_name} 模块
"""
import pytest
from unittest.mock import Mock, patch

'''

        if category == "models":
            template += f'''from app.models.{module_name} import *


class Test{class_name}Model:
    """测试{class_name}模型"""
    
    def test_model_creation(self):
        """测试模型创建"""
        # TODO: 实现模型创建测试
        pass
    
    def test_model_validation(self):
        """测试模型验证"""
        # TODO: 实现模型验证测试
        pass
'''

        elif category == "services":
            template += f'''from app.services.{module_name} import *


class Test{class_name}:
    """测试{class_name}服务"""
    
    def test_service_initialization(self):
        """测试服务初始化"""
        # TODO: 实现服务初始化测试
        pass

'''
            # 为检测到的函数添加测试模板
            for func in coverage_info["functions"][:3]:  # 限制数量
                template += f'''    def test_{func.lower().replace('.', '_')}(self):
        """测试{func}方法"""
        # TODO: 实现{func}测试
        pass

'''

        elif category == "schemas":
            template += f'''from app.schemas.{module_name} import *


class Test{class_name}Schema:
    """测试{class_name}模式"""
    
    def test_schema_validation(self):
        """测试模式验证"""
        # TODO: 实现模式验证测试
        pass
    
    def test_schema_serialization(self):
        """测试模式序列化"""
        # TODO: 实现模式序列化测试
        pass
'''

        elif category == "api":
            template += f'''from app.api.v1.{module_name} import router
from fastapi.testclient import TestClient


class Test{class_name}API:
    """测试{class_name} API"""
    
    def test_api_endpoints(self):
        """测试API端点"""
        # TODO: 实现API端点测试
        pass
    
    def test_api_authentication(self):
        """测试API认证"""
        # TODO: 实现API认证测试
        pass
'''

        # 创建目录和文件
        os.makedirs(Path(test_file).parent, exist_ok=True)

        with open(test_file, "w", encoding="utf-8") as f:
            f.write(template)

        print(f"  ✅ 创建测试模板: {test_file}")


def main():
    """主函数"""
    analyzer = CoverageAnalyzer()

    print("🔍 开始测试覆盖率分析...")

    # 生成报告
    report = analyzer.generate_coverage_report()

    # 显示结果
    print("\n📊 测试覆盖率分析结果")
    print("=" * 60)

    summary = report["summary"]
    print(f"应用文件总数: {summary['total_app_files']}")
    print(f"测试文件总数: {summary['total_test_files']}")

    print("\n📋 应用模块分布:")
    for category, files in summary["app_structure"].items():
        print(f"  {category}: {len(files)}个文件")

    print("\n🧪 测试文件分布:")
    for category, files in summary["test_structure"].items():
        print(f"  {category}: {len(files)}个文件")

    print("\n⚠️  缺失测试分析:")
    total_missing = 0
    for category, files in summary["missing_tests"].items():
        if files:
            print(f"  {category}: {len(files)}个文件缺少测试")
            total_missing += len(files)

    if total_missing > 0:
        print(
            f"\n📈 测试覆盖率估算: {((summary['total_app_files'] - total_missing) / summary['total_app_files'] * 100):.1f}%"
        )
    else:
        print("\n🎉 所有模块都有对应的测试文件!")

    print("\n🎯 改进建议:")
    for rec in report["recommendations"]:
        print(f"  {rec}")

    # 询问是否创建测试模板
    print(f"\n💡 为缺失的测试创建模板文件...")
    analyzer.create_test_templates(summary["missing_tests"])

    # 保存报告
    with open("coverage_analysis_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    print(f"\n📊 详细报告已保存: coverage_analysis_report.json")


if __name__ == "__main__":
    main()
