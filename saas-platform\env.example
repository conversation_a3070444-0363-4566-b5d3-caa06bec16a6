# AstrBot SaaS Platform 环境变量配置示例
# 复制这个文件为 .env 并修改相应的值

# 基本应用配置
PROJECT_NAME=AstrBot SaaS Platform
VERSION=0.1.0
API_V1_STR=/api/v1

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000

# 安全配置
SECRET_KEY=your-secret-key-here-change-in-production-use-openssl-rand-hex-32
ACCESS_TOKEN_EXPIRE_MINUTES=11520  # 8天
REFRESH_TOKEN_EXPIRE_MINUTES=43200  # 30天

# JWT配置
ALGORITHM=HS256

# CORS配置
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8000

# 数据库配置
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=astrbot_saas
POSTGRES_PORT=5432

# 或者直接提供完整的数据库URL
# DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/astrbot_saas

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_LEVEL=INFO

# 邮件配置
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=AstrBot SaaS Platform

# 超级用户配置
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=admin123

# 环境配置
ENVIRONMENT=development

# 多租户配置
MAX_TENANTS_PER_USER=3

# 外部服务配置
WEBHOOK_BASE_URL=https://api.astrbot.com
ASTRBOT_API_TIMEOUT=30

# 文件存储配置
UPLOAD_DIR=uploads
MAX_UPLOAD_SIZE=10485760  # 10MB
