# AstrBot SaaS Platform - Prometheus监控配置
# DevOps最佳实践：全面监控、智能告警、性能追踪

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'astrbot-saas'
    environment: 'production'

# 告警规则配置
rule_files:
  - "alerts.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
      scheme: http
      timeout: 10s
      api_version: v2

# 监控目标配置
scrape_configs:
  # =====================================================
  # Prometheus自监控
  # =====================================================
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # =====================================================
  # AstrBot SaaS Platform应用监控
  # =====================================================
  - job_name: 'astrbot-saas'
    static_configs:
      - targets: 
        - 'astrbot-saas-service:8000'
        - 'astrbot-saas-service:8080'  # metrics端口
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s
    honor_labels: true
    params:
      'format': ['prometheus']

  # =====================================================
  # 系统指标监控
  # =====================================================
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    metrics_path: /metrics

  # =====================================================
  # 容器指标监控
  # =====================================================
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 10s
    metrics_path: /metrics

  # =====================================================
  # 数据库监控
  # =====================================================
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics

  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    metrics_path: /metrics

  # =====================================================
  # Web服务器监控
  # =====================================================
  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 15s
    metrics_path: /metrics

  # =====================================================
  # 外部服务健康检查
  # =====================================================
  - job_name: 'blackbox-http'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://api.astrbot.com/api/v1/health
        - https://app.astrbot.com
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  - job_name: 'blackbox-tcp'
    metrics_path: /probe
    params:
      module: [tcp_connect]
    static_configs:
      - targets:
        - postgres:5432
        - redis:6379
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # =====================================================
  # Kubernetes集群监控（生产环境）
  # =====================================================
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
    - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
      action: keep
      regex: default;kubernetes;https

  - job_name: 'kubernetes-nodes'
    kubernetes_sd_configs:
    - role: node
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - action: labelmap
      regex: __meta_kubernetes_node_label_(.+)
    - target_label: __address__
      replacement: kubernetes.default.svc:443
    - source_labels: [__meta_kubernetes_node_name]
      regex: (.+)
      target_label: __metrics_path__
      replacement: /api/v1/nodes/${1}/proxy/metrics

  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
    - role: pod
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
      action: keep
      regex: true
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
      action: replace
      target_label: __metrics_path__
      regex: (.+)
    - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
      action: replace
      regex: ([^:]+)(?::\d+)?;(\d+)
      replacement: $1:$2
      target_label: __address__
    - action: labelmap
      regex: __meta_kubernetes_pod_label_(.+)
    - source_labels: [__meta_kubernetes_namespace]
      action: replace
      target_label: kubernetes_namespace
    - source_labels: [__meta_kubernetes_pod_name]
      action: replace
      target_label: kubernetes_pod_name

  # =====================================================
  # 监控系统自身监控
  # =====================================================
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager:9093']
    scrape_interval: 30s

  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 30s
    metrics_path: /metrics

# =====================================================
# 远程写入配置（可选，用于长期存储）
# =====================================================
remote_write:
  - url: "http://thanos-receiver:19291/api/v1/receive"
    queue_config:
      max_samples_per_send: 10000
      max_shards: 200
      capacity: 10000
    # 数据保留策略
    write_relabel_configs:
      - source_labels: [__name__]
        regex: 'up|prometheus_build_info'
        action: keep

# =====================================================
# 存储配置
# =====================================================
storage:
  tsdb:
    retention_time: 30d
    retention_size: 10GB
    wal_compression: true 