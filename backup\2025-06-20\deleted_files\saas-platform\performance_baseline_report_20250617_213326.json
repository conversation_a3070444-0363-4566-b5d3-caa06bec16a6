{"test_summary": {"total_tests": 62, "successful_tests": 58, "success_rate": 93.54838709677419, "test_duration_seconds": 2.049028}, "performance_metrics": {"avg_response_time_ms": 194.4343273462313, "min_response_time_ms": 9.874575987683626, "max_response_time_ms": 1910.7118416606081, "median_response_time_ms": 109.50083469821638}, "test_results": [{"endpoint": "/health", "method": "GET", "response_time_ms": 14.802258777937976, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.7621377}, {"endpoint": "/health", "method": "GET", "response_time_ms": 9.874575987683626, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.8156998}, {"endpoint": "/health", "method": "GET", "response_time_ms": 14.362130989706403, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.8658078}, {"endpoint": "/health", "method": "GET", "response_time_ms": 38.33101335346345, "expected_time_ms": 10, "success": false, "status_code": 500, "timestamp": **********.9163187}, {"endpoint": "/health", "method": "GET", "response_time_ms": 56.30322309604974, "expected_time_ms": 10, "success": false, "status_code": 500, "timestamp": **********.9666579}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 14.55132056730268, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.0169625}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 13.593028146199238, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.0679715}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 16.342941266012218, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.1184773}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 10.511570600297203, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.168763}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 22.076786127113284, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.2189045}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 148.70905562405275, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.26929}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 93.69157183014852, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.3196242}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 93.63557864130652, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.3699036}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 133.97758645629312, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.420138}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 108.88690743751529, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.4706807}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 277.44426297474297, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.520853}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 180.14533404231156, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.5712209}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 239.33228489891974, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.6217334}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 824.1914350828304, "expected_time_ms": 200, "success": false, "status_code": 500, "timestamp": **********.6723044}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 144.25031878936514, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.7226052}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 147.04961917356115, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.772835}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 150.84894823242274, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.8231373}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 171.32967025211371, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.873458}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 175.0752418594261, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.9240384}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 212.44960819259302, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.9744809}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 139.7627338982494, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750167206.0248325}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 125.24955138489095, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750167206.0753326}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 113.38989155467266, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750167206.1255727}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 92.85163870587814, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750167206.1758478}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 110.11476195891746, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750167206.2262669}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 385.7186291756776, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750167206.2773087}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 484.95376794008223, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750167206.3279665}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 548.8204231106711, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750167206.3782248}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 1910.7118416606081, "expected_time_ms": 500, "success": false, "status_code": 500, "timestamp": 1750167206.428937}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 361.33503553080124, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750167206.479165}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 237.00875101073098, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167206.5297313}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 352.00108639101023, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167206.5800233}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 840.937903690673, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167206.630416}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 342.6633051860787, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167206.681014}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 379.9145586122965, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167206.7600982}, {"operation": "SELECT租户列表", "response_time_ms": 48.87304903377197, "expected_time_ms": 50, "success": true}, {"operation": "SELECT租户列表", "response_time_ms": 45.74511450044802, "expected_time_ms": 50, "success": true}, {"operation": "SELECT租户列表", "response_time_ms": 76.89815861082303, "expected_time_ms": 50, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 91.68129121920812, "expected_time_ms": 80, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 74.63422809503075, "expected_time_ms": 80, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 76.34558911410022, "expected_time_ms": 80, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 80.64762169285787, "expected_time_ms": 60, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 66.44333554429537, "expected_time_ms": 60, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 56.55860201592038, "expected_time_ms": 60, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 41.559301977223775, "expected_time_ms": 40, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 54.89847648066525, "expected_time_ms": 40, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 65.57708156589672, "expected_time_ms": 40, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 52.93858309935109, "expected_time_ms": 30, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 37.686357050815644, "expected_time_ms": 30, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 50.70582773616512, "expected_time_ms": 30, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 173.31837483609957, "expected_time_ms": 150, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 264.05995333841287, "expected_time_ms": 150, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 181.41988795972276, "expected_time_ms": 150, "success": true}, {"scenario": "5个并发用户", "concurrent_users": 5, "avg_response_time_ms": 162.46459334249636, "p95_response_time_ms": 178.45118300300123, "max_response_time_ms": 177.80348410173815, "expected_max_ms": 200, "success": true}, {"scenario": "10个并发用户", "concurrent_users": 10, "avg_response_time_ms": 215.78575369824122, "p95_response_time_ms": 238.30949018217703, "max_response_time_ms": 236.88709228389718, "expected_max_ms": 350, "success": true}, {"scenario": "20个并发用户", "concurrent_users": 20, "avg_response_time_ms": 313.13074372694814, "p95_response_time_ms": 336.2880771719242, "max_response_time_ms": 336.49600912900013, "expected_max_ms": 600, "success": true}, {"scenario": "50个并发用户", "concurrent_users": 50, "avg_response_time_ms": 615.4829650590231, "p95_response_time_ms": 638.2358561895047, "max_response_time_ms": 639.6706813772138, "expected_max_ms": 1200, "success": true}], "recommendations": ["🔧 数据库连接池优化: 确保数据库连接池大小适合并发负载", "📊 API响应时间监控: 建立API响应时间监控和告警机制", "🚀 缓存策略: 对频繁查询的数据实施Redis缓存", "⚡ 异步处理: AI功能采用异步处理减少响应时间", "🔍 SQL查询优化: 审查慢查询并添加适当索引", "📈 负载均衡: 考虑在高并发场景下实施负载均衡", "💾 数据库分片: 大量数据时考虑数据库分片策略", "🛡️ 限流保护: 实施API限流保护系统稳定性"]}