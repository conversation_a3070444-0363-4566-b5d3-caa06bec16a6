# k8s/security/network-policies/01-allow-dns.yaml
# 允许所有Pod进行DNS解析，这是集群内部通信的基础

apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-dns-access
  namespace: astrbot-saas
spec:
  # 应用于所有Pod
  podSelector: {}
  
  # 策略类型：出站流量
  policyTypes:
  - Egress
  
  # 出站规则
  egress:
  - to:
    # 允许访问kube-dns服务
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: kube-system
      podSelector:
        matchLabels:
          k8s-app: kube-dns
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53 