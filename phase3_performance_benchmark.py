#!/usr/bin/env python3
"""
阶段三：性能基准测试
DevOps执行专家 - 评估系统在高负载下的并发处理能力
"""

import asyncio
import json
import time
import psutil
import statistics
from datetime import datetime
from typing import Dict, Any, List
import httpx
import matplotlib.pyplot as plt
import numpy as np

class PerformanceBenchmarkTester:
    """性能基准测试器"""
    
    def __init__(self):
        self.proxy_url = "http://localhost:9000"
        self.test_results = []
        self.performance_data = []
        self.system_metrics = []
        
    def print_section(self, title: str):
        """打印测试章节"""
        print(f"\n{'='*80}")
        print(f"⚡ {title}")
        print(f"{'='*80}")
    
    def print_test(self, test_name: str, success: bool, details: str = "", metrics: Dict = None):
        """打印测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        if metrics:
            for key, value in metrics.items():
                print(f"   {key}: {value}")
        
        # 记录测试结果
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        if metrics:
            result["metrics"] = metrics
        
        self.test_results.append(result)
    
    def monitor_system_resources(self):
        """监控系统资源"""
        return {
            "cpu_percent": psutil.cpu_percent(interval=0.1),
            "memory_percent": psutil.virtual_memory().percent,
            "memory_used_mb": psutil.virtual_memory().used / 1024 / 1024,
            "disk_io": psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {},
            "network_io": psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {},
            "timestamp": time.time()
        }
    
    async def load_test_concurrent_users(self, concurrent_users: int, duration_seconds: int = 30):
        """负载测试：并发用户"""
        self.print_section(f"负载测试 - {concurrent_users}并发用户")
        
        # 创建HTTP客户端池
        clients = [httpx.AsyncClient(timeout=30.0) for _ in range(concurrent_users)]
        
        async def user_simulation(client: httpx.AsyncClient, user_id: int):
            """模拟单个用户的行为"""
            results = []
            start_time = time.time()
            message_count = 0
            
            while time.time() - start_time < duration_seconds:
                try:
                    request_start = time.time()
                    
                    response = await client.post(
                        f"{self.proxy_url}/api/v1/proxy/message",
                        json={
                            "tenant_id": f"load_test_tenant_{user_id % 10}",  # 10个租户
                            "message": f"负载测试消息 - 用户{user_id} - 消息{message_count}",
                            "user_id": f"load_user_{user_id}",
                            "session_id": f"load_session_{user_id}"
                        },
                        headers={"X-Tenant-ID": f"load_test_tenant_{user_id % 10}"}
                    )
                    
                    request_time = time.time() - request_start
                    message_count += 1
                    
                    results.append({
                        "user_id": user_id,
                        "message_count": message_count,
                        "success": response.status_code == 200,
                        "response_time": request_time,
                        "status_code": response.status_code,
                        "timestamp": time.time()
                    })
                    
                    # 模拟用户间隔
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    results.append({
                        "user_id": user_id,
                        "message_count": message_count,
                        "success": False,
                        "error": str(e),
                        "timestamp": time.time()
                    })
            
            return results
        
        # 开始系统监控
        monitoring_task = asyncio.create_task(self.monitor_system_during_test(duration_seconds))
        
        print(f"🚀 启动 {concurrent_users} 个并发用户，持续 {duration_seconds} 秒...")
        test_start_time = time.time()
        
        # 执行负载测试
        tasks = [user_simulation(clients[i], i) for i in range(concurrent_users)]
        user_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        test_duration = time.time() - test_start_time
        
        # 停止监控
        monitoring_task.cancel()
        
        # 关闭客户端
        for client in clients:
            await client.aclose()
        
        # 分析结果
        all_requests = []
        for result in user_results:
            if isinstance(result, list):
                all_requests.extend(result)
        
        # 计算性能指标
        total_requests = len(all_requests)
        successful_requests = sum(1 for req in all_requests if req.get("success", False))
        failed_requests = total_requests - successful_requests
        
        response_times = [req["response_time"] for req in all_requests if "response_time" in req]
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            p50_response_time = statistics.median(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18]
            p99_response_time = statistics.quantiles(response_times, n=100)[98]
        else:
            avg_response_time = min_response_time = max_response_time = 0
            p50_response_time = p95_response_time = p99_response_time = 0
        
        throughput = total_requests / test_duration
        success_rate = (successful_requests / total_requests * 100) if total_requests > 0 else 0
        
        # 性能评级
        if avg_response_time < 0.1 and success_rate > 95:
            performance_rating = "优秀"
        elif avg_response_time < 0.5 and success_rate > 90:
            performance_rating = "良好"
        elif avg_response_time < 1.0 and success_rate > 80:
            performance_rating = "一般"
        else:
            performance_rating = "需要优化"
        
        metrics = {
            "并发用户数": concurrent_users,
            "测试时长": f"{test_duration:.2f}s",
            "总请求数": total_requests,
            "成功请求": successful_requests,
            "失败请求": failed_requests,
            "成功率": f"{success_rate:.1f}%",
            "吞吐量": f"{throughput:.1f} req/s",
            "平均响应时间": f"{avg_response_time:.3f}s",
            "最小响应时间": f"{min_response_time:.3f}s",
            "最大响应时间": f"{max_response_time:.3f}s",
            "P50响应时间": f"{p50_response_time:.3f}s",
            "P95响应时间": f"{p95_response_time:.3f}s",
            "P99响应时间": f"{p99_response_time:.3f}s",
            "性能评级": performance_rating
        }
        
        self.print_test(
            f"{concurrent_users}并发用户负载测试",
            success_rate >= 80 and avg_response_time < 2.0,
            f"吞吐量: {throughput:.1f} req/s, 成功率: {success_rate:.1f}%",
            metrics
        )
        
        # 保存性能数据
        self.performance_data.append({
            "concurrent_users": concurrent_users,
            "metrics": metrics,
            "response_times": response_times,
            "all_requests": all_requests
        })
        
        return metrics
    
    async def monitor_system_during_test(self, duration: int):
        """测试期间监控系统资源"""
        try:
            while True:
                metrics = self.monitor_system_resources()
                self.system_metrics.append(metrics)
                await asyncio.sleep(1)  # 每秒采集一次
        except asyncio.CancelledError:
            pass
    
    async def test_database_connection_pool(self):
        """测试数据库连接池性能"""
        self.print_section("数据库连接池性能测试")
        
        # 通过大量并发请求测试数据库连接池
        concurrent_requests = 50
        
        async def database_intensive_request(client: httpx.AsyncClient, request_id: int):
            """数据库密集型请求"""
            try:
                start_time = time.time()
                
                # 发送需要数据库操作的请求
                response = await client.post(
                    f"{self.proxy_url}/api/v1/proxy/message",
                    json={
                        "tenant_id": f"db_test_tenant_{request_id % 5}",
                        "message": f"数据库测试请求 {request_id}",
                        "user_id": f"db_user_{request_id}",
                        "session_id": f"db_session_{request_id}"
                    }
                )
                
                response_time = time.time() - start_time
                
                return {
                    "request_id": request_id,
                    "success": response.status_code == 200,
                    "response_time": response_time,
                    "status_code": response.status_code
                }
                
            except Exception as e:
                return {
                    "request_id": request_id,
                    "success": False,
                    "error": str(e)
                }
        
        # 创建客户端并执行测试
        clients = [httpx.AsyncClient(timeout=30.0) for _ in range(concurrent_requests)]
        
        print(f"🗄️ 测试数据库连接池 - {concurrent_requests}个并发请求...")
        start_time = time.time()
        
        tasks = [database_intensive_request(clients[i], i) for i in range(concurrent_requests)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        test_duration = time.time() - start_time
        
        # 关闭客户端
        for client in clients:
            await client.aclose()
        
        # 分析结果
        successful_results = [r for r in results if isinstance(r, dict) and r.get("success", False)]
        failed_results = len(results) - len(successful_results)
        
        if successful_results:
            response_times = [r["response_time"] for r in successful_results]
            avg_response_time = statistics.mean(response_times)
            max_response_time = max(response_times)
        else:
            avg_response_time = max_response_time = 0
        
        success_rate = len(successful_results) / len(results) * 100
        
        metrics = {
            "并发请求数": concurrent_requests,
            "成功请求": len(successful_results),
            "失败请求": failed_results,
            "成功率": f"{success_rate:.1f}%",
            "平均响应时间": f"{avg_response_time:.3f}s",
            "最大响应时间": f"{max_response_time:.3f}s",
            "总耗时": f"{test_duration:.2f}s"
        }
        
        self.print_test(
            "数据库连接池性能",
            success_rate >= 95 and avg_response_time < 1.0,
            f"数据库连接池处理{concurrent_requests}个并发请求",
            metrics
        )
    
    async def test_redis_cache_performance(self):
        """测试Redis缓存性能"""
        self.print_section("Redis缓存性能测试")
        
        # 通过状态查询测试Redis缓存
        cache_requests = 100
        
        async def cache_request(client: httpx.AsyncClient, request_id: int):
            """缓存请求"""
            try:
                start_time = time.time()
                
                response = await client.get(f"{self.proxy_url}/api/v1/proxy/status")
                
                response_time = time.time() - start_time
                
                return {
                    "request_id": request_id,
                    "success": response.status_code == 200,
                    "response_time": response_time
                }
                
            except Exception as e:
                return {
                    "request_id": request_id,
                    "success": False,
                    "error": str(e)
                }
        
        client = httpx.AsyncClient(timeout=30.0)
        
        print(f"🔄 测试Redis缓存性能 - {cache_requests}个请求...")
        start_time = time.time()
        
        # 串行执行以测试缓存效果
        results = []
        for i in range(cache_requests):
            result = await cache_request(client, i)
            results.append(result)
        
        test_duration = time.time() - start_time
        await client.aclose()
        
        # 分析结果
        successful_results = [r for r in results if r.get("success", False)]
        
        if successful_results:
            response_times = [r["response_time"] for r in successful_results]
            avg_response_time = statistics.mean(response_times)
            
            # 检查缓存效果（后续请求应该更快）
            first_half = response_times[:len(response_times)//2]
            second_half = response_times[len(response_times)//2:]
            
            if first_half and second_half:
                first_half_avg = statistics.mean(first_half)
                second_half_avg = statistics.mean(second_half)
                cache_improvement = (first_half_avg - second_half_avg) / first_half_avg * 100
            else:
                cache_improvement = 0
        else:
            avg_response_time = cache_improvement = 0
        
        success_rate = len(successful_results) / len(results) * 100
        
        metrics = {
            "缓存请求数": cache_requests,
            "成功率": f"{success_rate:.1f}%",
            "平均响应时间": f"{avg_response_time:.3f}s",
            "缓存改进": f"{cache_improvement:.1f}%",
            "总耗时": f"{test_duration:.2f}s"
        }
        
        self.print_test(
            "Redis缓存性能",
            success_rate >= 95 and avg_response_time < 0.1,
            f"缓存效果改进: {cache_improvement:.1f}%",
            metrics
        )
    
    def generate_performance_charts(self):
        """生成性能图表"""
        if not self.performance_data:
            return
        
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 并发用户数 vs 吞吐量
        concurrent_users = [data["concurrent_users"] for data in self.performance_data]
        throughputs = [float(data["metrics"]["吞吐量"].split()[0]) for data in self.performance_data]
        
        ax1.plot(concurrent_users, throughputs, 'b-o')
        ax1.set_xlabel('并发用户数')
        ax1.set_ylabel('吞吐量 (req/s)')
        ax1.set_title('并发用户数 vs 吞吐量')
        ax1.grid(True)
        
        # 2. 并发用户数 vs 平均响应时间
        avg_response_times = [float(data["metrics"]["平均响应时间"].split('s')[0]) for data in self.performance_data]
        
        ax2.plot(concurrent_users, avg_response_times, 'r-o')
        ax2.set_xlabel('并发用户数')
        ax2.set_ylabel('平均响应时间 (s)')
        ax2.set_title('并发用户数 vs 平均响应时间')
        ax2.grid(True)
        
        # 3. 响应时间分布（最后一次测试）
        if self.performance_data:
            last_test_response_times = self.performance_data[-1]["response_times"]
            ax3.hist(last_test_response_times, bins=30, alpha=0.7, color='green')
            ax3.set_xlabel('响应时间 (s)')
            ax3.set_ylabel('频次')
            ax3.set_title('响应时间分布')
            ax3.grid(True)
        
        # 4. 系统资源使用情况
        if self.system_metrics:
            timestamps = [m["timestamp"] for m in self.system_metrics]
            cpu_usage = [m["cpu_percent"] for m in self.system_metrics]
            memory_usage = [m["memory_percent"] for m in self.system_metrics]
            
            ax4_twin = ax4.twinx()
            ax4.plot(timestamps, cpu_usage, 'b-', label='CPU使用率 (%)')
            ax4_twin.plot(timestamps, memory_usage, 'r-', label='内存使用率 (%)')
            
            ax4.set_xlabel('时间')
            ax4.set_ylabel('CPU使用率 (%)', color='b')
            ax4_twin.set_ylabel('内存使用率 (%)', color='r')
            ax4.set_title('系统资源使用情况')
            ax4.grid(True)
        
        plt.tight_layout()
        plt.savefig('performance_benchmark_charts.png', dpi=300, bbox_inches='tight')
        print("📊 性能图表已保存: performance_benchmark_charts.png")
    
    async def generate_phase3_report(self):
        """生成阶段三测试报告"""
        self.print_section("阶段三测试报告")
        
        # 统计测试结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"📊 测试统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {failed_tests}")
        print(f"   成功率: {success_rate:.1f}%")
        
        # 性能基准线
        if self.performance_data:
            print(f"\n📈 性能基准线:")
            for data in self.performance_data:
                concurrent = data["concurrent_users"]
                metrics = data["metrics"]
                print(f"   {concurrent}并发: {metrics['吞吐量']}, 平均响应时间: {metrics['平均响应时间']}")
        
        # 生成图表
        self.generate_performance_charts()
        
        # 保存详细报告
        report = {
            "phase": "性能基准测试",
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": success_rate
            },
            "performance_data": self.performance_data,
            "system_metrics": self.system_metrics[-100:] if self.system_metrics else [],  # 保留最后100个数据点
            "test_results": self.test_results
        }
        
        with open("phase3_performance_benchmark_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存: phase3_performance_benchmark_report.json")
        
        return success_rate >= 80
    
    async def run_all_tests(self):
        """运行所有性能基准测试"""
        print("⚡ 阶段三：性能基准测试开始")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 执行不同并发级别的负载测试
        concurrent_levels = [10, 50, 100]  # 根据系统能力调整
        
        for level in concurrent_levels:
            await self.load_test_concurrent_users(level, duration_seconds=20)
            await asyncio.sleep(5)  # 测试间隔
        
        # 执行专项性能测试
        await self.test_database_connection_pool()
        await self.test_redis_cache_performance()
        
        # 生成报告
        success = await self.generate_phase3_report()
        
        return success

async def main():
    """主函数"""
    tester = PerformanceBenchmarkTester()
    
    try:
        success = await tester.run_all_tests()
        return 0 if success else 1
    finally:
        pass

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
