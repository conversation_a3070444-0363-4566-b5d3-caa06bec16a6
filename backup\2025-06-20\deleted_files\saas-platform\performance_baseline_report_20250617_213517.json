{"test_summary": {"total_tests": 62, "successful_tests": 60, "success_rate": 96.7741935483871, "test_duration_seconds": 2.033359}, "performance_metrics": {"avg_response_time_ms": 174.3244987807175, "min_response_time_ms": 9.092726366353606, "max_response_time_ms": 730.8480988110185, "median_response_time_ms": 112.03609426329831}, "test_results": [{"endpoint": "/health", "method": "GET", "response_time_ms": 13.114993004314584, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.3259206}, {"endpoint": "/health", "method": "GET", "response_time_ms": 42.903456533457934, "expected_time_ms": 10, "success": false, "status_code": 500, "timestamp": **********.376411}, {"endpoint": "/health", "method": "GET", "response_time_ms": 9.529667856816332, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.426923}, {"endpoint": "/health", "method": "GET", "response_time_ms": 12.826857945578318, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.47753}, {"endpoint": "/health", "method": "GET", "response_time_ms": 9.092726366353606, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.5278833}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 18.648538536450246, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.5783525}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 14.895165747316407, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.6291554}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 18.499392582731804, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.6796134}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 11.2825580760762, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.729728}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 19.654027496969753, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.7805262}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 109.7602068655434, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.831164}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 92.32355160063463, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.8816829}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 70.32586686339357, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.9321487}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 147.40492659625932, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.982342}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 128.96106377749993, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750167316.0325508}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 203.6739513734554, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": 1750167316.0826883}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 203.40127406703084, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": 1750167316.1332}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 254.7855012851761, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": 1750167316.1833327}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 181.79730596738986, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": 1750167316.2333913}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 261.57855049097316, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": 1750167316.2839963}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 531.588316278678, "expected_time_ms": 150, "success": false, "status_code": 500, "timestamp": 1750167316.3342645}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 171.69596343253488, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": 1750167316.3846009}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 126.18801028102601, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": 1750167316.4349442}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 141.84614205376528, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": 1750167316.4854398}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 217.87370848463596, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": 1750167316.5355113}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 125.35914759076681, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750167316.5860941}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 78.3648522094362, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750167316.63643}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 104.75461238389897, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750167316.687197}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 114.31198166105324, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750167316.7377796}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 78.32698110167975, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750167316.7882028}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 730.8480988110185, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750167316.8385856}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 555.5653080571585, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750167316.889385}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 546.0862687223981, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750167316.9400876}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 704.6199451156122, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750167316.9905357}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 631.8227093269895, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750167317.0409625}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 379.1413065963658, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167317.0916083}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 322.8854277510451, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167317.1419873}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 366.53767635554607, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167317.1926987}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 294.7873748346596, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167317.255224}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 392.52022411869973, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750167317.3056831}, {"operation": "SELECT租户列表", "response_time_ms": 58.64892316655933, "expected_time_ms": 50, "success": true}, {"operation": "SELECT租户列表", "response_time_ms": 66.10869779432255, "expected_time_ms": 50, "success": true}, {"operation": "SELECT租户列表", "response_time_ms": 52.610153388043074, "expected_time_ms": 50, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 114.64709472188306, "expected_time_ms": 80, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 122.29622919384363, "expected_time_ms": 80, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 106.2913112019711, "expected_time_ms": 80, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 64.42925946072938, "expected_time_ms": 60, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 103.25685638227317, "expected_time_ms": 60, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 106.68323086235941, "expected_time_ms": 60, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 46.21500082346175, "expected_time_ms": 40, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 57.88526053051949, "expected_time_ms": 40, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 53.6626196422249, "expected_time_ms": 40, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 44.36811964184131, "expected_time_ms": 30, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 44.17689579406766, "expected_time_ms": 30, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 42.185720977439274, "expected_time_ms": 30, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 138.89240360283713, "expected_time_ms": 150, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 241.7398703112649, "expected_time_ms": 150, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 207.139643585585, "expected_time_ms": 150, "success": true}, {"scenario": "5个并发用户", "concurrent_users": 5, "avg_response_time_ms": 166.18982740652956, "p95_response_time_ms": 182.5083414707261, "max_response_time_ms": 182.50811265763326, "expected_max_ms": 200, "success": true}, {"scenario": "10个并发用户", "concurrent_users": 10, "avg_response_time_ms": 220.07421834309844, "p95_response_time_ms": 236.1642649158192, "max_response_time_ms": 235.53466970988194, "expected_max_ms": 350, "success": true}, {"scenario": "20个并发用户", "concurrent_users": 20, "avg_response_time_ms": 318.0343045610539, "p95_response_time_ms": 337.9229000906905, "max_response_time_ms": 337.975687063655, "expected_max_ms": 600, "success": true}, {"scenario": "50个并发用户", "concurrent_users": 50, "avg_response_time_ms": 612.721288489584, "p95_response_time_ms": 635.1938913573624, "max_response_time_ms": 635.8349058118481, "expected_max_ms": 1200, "success": true}], "recommendations": ["🔧 数据库连接池优化: 确保数据库连接池大小适合并发负载", "📊 API响应时间监控: 建立API响应时间监控和告警机制", "🚀 缓存策略: 对频繁查询的数据实施Redis缓存", "⚡ 异步处理: AI功能采用异步处理减少响应时间", "🔍 SQL查询优化: 审查慢查询并添加适当索引", "📈 负载均衡: 考虑在高并发场景下实施负载均衡", "💾 数据库分片: 大量数据时考虑数据库分片策略", "🛡️ 限流保护: 实施API限流保护系统稳定性"]}