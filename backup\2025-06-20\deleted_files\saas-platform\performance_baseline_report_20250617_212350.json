{"test_summary": {"total_tests": 62, "successful_tests": 57, "success_rate": 91.93548387096774, "test_duration_seconds": 2.042032}, "performance_metrics": {"avg_response_time_ms": 194.70843881576542, "min_response_time_ms": 10.593239936767002, "max_response_time_ms": 864.9046713009627, "median_response_time_ms": 106.42859978618901}, "test_results": [{"endpoint": "/health", "method": "GET", "response_time_ms": 50.55969400044936, "expected_time_ms": 10, "success": false, "status_code": 500, "timestamp": **********.5856924}, {"endpoint": "/health", "method": "GET", "response_time_ms": 12.887464396713419, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.6368244}, {"endpoint": "/health", "method": "GET", "response_time_ms": 14.644177542535855, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.6871789}, {"endpoint": "/health", "method": "GET", "response_time_ms": 14.651456757818128, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.7376344}, {"endpoint": "/health", "method": "GET", "response_time_ms": 13.90023221036617, "expected_time_ms": 10, "success": true, "status_code": 200, "timestamp": **********.7881885}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 17.913784919453743, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.8386574}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 10.593239936767002, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.8892224}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 94.20842306385093, "expected_time_ms": 15, "success": false, "status_code": 500, "timestamp": **********.939834}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 17.238884662180805, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.9907122}, {"endpoint": "/api/v1/health", "method": "GET", "response_time_ms": 11.245606677272868, "expected_time_ms": 15, "success": true, "status_code": 200, "timestamp": **********.0411334}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 70.42261421325935, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.0913393}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 87.11169506547574, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.1417544}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 107.61836003172361, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.1922898}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 129.34443712240324, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.242717}, {"endpoint": "/api/v1/tenants", "method": "GET", "response_time_ms": 146.58684384730105, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.2929418}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 230.20364941682146, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.343039}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 864.9046713009627, "expected_time_ms": 200, "success": false, "status_code": 500, "timestamp": **********.3938396}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 267.60135109864467, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.4439237}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 269.9598989898195, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.4941783}, {"endpoint": "/api/v1/tenants", "method": "POST", "response_time_ms": 421.29954275806926, "expected_time_ms": 200, "success": true, "status_code": 200, "timestamp": **********.5442822}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 781.2364788080018, "expected_time_ms": 150, "success": false, "status_code": 500, "timestamp": **********.594629}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 111.94051560989087, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.6448438}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 166.2018864153849, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.703392}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 189.36337983253304, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.753892}, {"endpoint": "/api/v1/sessions", "method": "POST", "response_time_ms": 140.06353061236223, "expected_time_ms": 150, "success": true, "status_code": 200, "timestamp": **********.804142}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 464.86298218629645, "expected_time_ms": 100, "success": false, "status_code": 500, "timestamp": **********.8546016}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 137.0001785869115, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.9052649}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 135.1290028694512, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": **********.9554942}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 105.23883954065442, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750166630.005823}, {"endpoint": "/api/v1/messages", "method": "POST", "response_time_ms": 103.30875669728482, "expected_time_ms": 100, "success": true, "status_code": 200, "timestamp": 1750166630.0560904}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 444.5875923757193, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750166630.1064813}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 592.4060154417502, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750166630.1570752}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 730.4345615313176, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750166630.20741}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 544.7642891017774, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750166630.2577617}, {"endpoint": "/api/v1/ai/auto-reply", "method": "POST", "response_time_ms": 520.7683767076607, "expected_time_ms": 500, "success": true, "status_code": 200, "timestamp": 1750166630.3083618}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 305.6306489925647, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750166630.358935}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 263.1712850556256, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750166630.409582}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 252.47211276104483, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750166630.460347}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 446.66031862720354, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750166630.5107098}, {"endpoint": "/api/v1/ai/agent-suggestions", "method": "POST", "response_time_ms": 357.9452929182222, "expected_time_ms": 300, "success": true, "status_code": 200, "timestamp": 1750166630.571141}, {"operation": "SELECT租户列表", "response_time_ms": 83.31926970924208, "expected_time_ms": 50, "success": true}, {"operation": "SELECT租户列表", "response_time_ms": 84.37114790541258, "expected_time_ms": 50, "success": true}, {"operation": "SELECT租户列表", "response_time_ms": 43.16202781120472, "expected_time_ms": 50, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 71.30724822938204, "expected_time_ms": 80, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 97.24975202479254, "expected_time_ms": 80, "success": true}, {"operation": "INSERT新租户", "response_time_ms": 70.87227949218601, "expected_time_ms": 80, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 89.14703010416125, "expected_time_ms": 60, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 63.127636995574214, "expected_time_ms": 60, "success": true}, {"operation": "UPDATE租户信息", "response_time_ms": 103.74667329924713, "expected_time_ms": 60, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 44.58603439986372, "expected_time_ms": 40, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 45.82167533048506, "expected_time_ms": 40, "success": true}, {"operation": "SELECT用户会话", "response_time_ms": 49.15264307087733, "expected_time_ms": 40, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 39.100311960589735, "expected_time_ms": 30, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 29.394975026440026, "expected_time_ms": 30, "success": true}, {"operation": "INSERT新消息", "response_time_ms": 41.049187445120936, "expected_time_ms": 30, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 175.31714781013156, "expected_time_ms": 150, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 269.21681630086357, "expected_time_ms": 150, "success": true}, {"operation": "复杂JOIN查询", "response_time_ms": 247.06552171527292, "expected_time_ms": 150, "success": true}, {"scenario": "5个并发用户", "concurrent_users": 5, "avg_response_time_ms": 159.74075739022373, "p95_response_time_ms": 202.48480341720028, "max_response_time_ms": 185.08301034187758, "expected_max_ms": 200, "success": true}, {"scenario": "10个并发用户", "concurrent_users": 10, "avg_response_time_ms": 218.36124128885547, "p95_response_time_ms": 237.99706115253213, "max_response_time_ms": 237.86734488921468, "expected_max_ms": 350, "success": true}, {"scenario": "20个并发用户", "concurrent_users": 20, "avg_response_time_ms": 318.8527130799346, "p95_response_time_ms": 338.5046024614817, "max_response_time_ms": 338.59860480633773, "expected_max_ms": 600, "success": true}, {"scenario": "50个并发用户", "concurrent_users": 50, "avg_response_time_ms": 612.9426712437028, "p95_response_time_ms": 637.1440832033404, "max_response_time_ms": 638.97143343295, "expected_max_ms": 1200, "success": true}], "recommendations": ["🔧 数据库连接池优化: 确保数据库连接池大小适合并发负载", "📊 API响应时间监控: 建立API响应时间监控和告警机制", "🚀 缓存策略: 对频繁查询的数据实施Redis缓存", "⚡ 异步处理: AI功能采用异步处理减少响应时间", "🔍 SQL查询优化: 审查慢查询并添加适当索引", "📈 负载均衡: 考虑在高并发场景下实施负载均衡", "💾 数据库分片: 大量数据时考虑数据库分片策略", "🛡️ 限流保护: 实施API限流保护系统稳定性"]}