<execution>
  <constraint>
    ## 技术文档客观限制
    - **Markdown格式约束**：必须遵循标准Markdown语法和GitHub Flavored Markdown规范
    - **Git版本控制限制**：文件大小、二进制文件处理、提交频率限制
    - **GitHub平台约束**：README文件长度限制、渲染性能限制、Pages构建时间
    - **用户认知负载**：文档复杂度不能超出目标用户的理解能力
    - **维护成本约束**：文档更新频率必须与项目开发节奏匹配
  </constraint>

  <rule>
    ## 文档编写强制规则
    - **用户优先原则**：所有文档必须从用户视角出发，解决实际问题
    - **准确性第一**：技术信息必须准确无误，代码示例必须可执行
    - **结构化组织**：使用清晰的标题层次和逻辑结构
    - **版本同步要求**：文档更新必须与代码变更同步进行
    - **可访问性标准**：确保不同技术水平的用户都能理解和使用
  </rule>

  <guideline>
    ## 文档编写指导原则
    - **简洁明了**：用最少的文字表达最多的信息
    - **示例驱动**：提供丰富的代码示例和使用场景
    - **渐进式披露**：从概览到细节的信息组织方式
    - **视觉友好**：合理使用图表、代码块、表格等视觉元素
    - **社区友好**：鼓励用户参与文档改进和贡献
  </guideline>

  <process>
    ## 文档优化执行流程

    ### Phase 1: 项目分析和需求调研 (30分钟)
    1. **项目现状分析**
       - 阅读现有README和文档
       - 分析项目结构和技术栈
       - 识别核心功能和特色
       - 评估文档完整性和质量

    2. **用户需求分析**
       - 确定目标用户群体
       - 分析用户使用场景
       - 识别关键信息需求
       - 评估学习路径设计

    3. **竞品文档研究**
       - 分析同类项目文档结构
       - 学习优秀文档案例
       - 识别行业最佳实践
       - 确定差异化定位

    ### Phase 2: 文档架构设计 (45分钟)
    1. **信息架构设计**
       ```
       README.md (项目门面)
       ├── 项目简介和价值主张
       ├── 核心功能和特性
       ├── 快速开始指南
       ├── 安装和配置
       ├── 使用示例
       ├── API文档链接
       ├── 贡献指南
       └── 许可证和联系方式
       ```

    2. **内容层次规划**
       - **L1 概览层**：项目介绍、核心价值、技术栈
       - **L2 入门层**：安装配置、快速开始、基础示例
       - **L3 功能层**：详细功能、API文档、配置选项
       - **L4 高级层**：架构设计、扩展开发、故障排除
       - **L5 贡献层**：开发指南、代码规范、发布流程

    3. **导航结构设计**
       - 设计清晰的目录结构
       - 建立文档间的链接关系
       - 创建快速导航机制
       - 确保信息查找效率

    ### Phase 3: 内容创作和优化 (90分钟)
    1. **README核心内容编写**
       ```markdown
       # 项目标题
       > 一句话描述项目价值

       ## 🚀 核心特性
       - 特性1：简洁描述
       - 特性2：简洁描述

       ## 📦 快速开始
       ```bash
       # 安装命令
       npm install project-name
       
       # 使用示例
       project-name --help
       ```

       ## 📖 详细文档
       - [安装指南](docs/installation.md)
       - [API文档](docs/api.md)
       ```

    2. **技术文档内容优化**
       - **代码示例**：确保所有代码可执行且有注释
       - **配置说明**：提供完整的配置选项和默认值
       - **故障排除**：收集常见问题和解决方案
       - **最佳实践**：提供使用建议和性能优化

    3. **视觉元素增强**
       - 添加项目Logo和截图
       - 使用Mermaid图表说明架构
       - 创建功能演示GIF
       - 设计清晰的表格和列表

    ### Phase 4: GitHub集成和同步 (30分钟)
    1. **Git版本控制设置**
       ```bash
       # 检查Git状态
       git status
       
       # 添加文档文件
       git add README.md docs/
       
       # 提交变更
       git commit -m "docs: 更新项目文档结构和内容"
       
       # 推送到远程仓库
       git push origin main
       ```

    2. **GitHub功能配置**
       - 设置Repository描述和标签
       - 配置GitHub Pages（如需要）
       - 创建Issue和PR模板
       - 设置GitHub Actions（文档自动检查）

    3. **文档发布和验证**
       - 验证GitHub渲染效果
       - 检查所有链接有效性
       - 测试代码示例可执行性
       - 确认移动端显示效果

    ### Phase 5: 质量检查和持续改进 (15分钟)
    1. **文档质量检查清单**
       - ✅ 标题层次清晰合理
       - ✅ 代码示例可执行
       - ✅ 链接全部有效
       - ✅ 拼写和语法正确
       - ✅ 格式统一规范
       - ✅ 信息准确完整

    2. **用户体验测试**
       - 模拟新用户阅读体验
       - 测试快速开始流程
       - 验证信息查找效率
       - 收集潜在改进点

    3. **维护机制建立**
       - 设置文档更新提醒
       - 建立用户反馈收集机制
       - 定期进行文档审查
       - 跟踪文档使用数据
  </process>

  <criteria>
    ## 文档质量评价标准

    ### 内容质量
    - ✅ 信息准确性：技术内容无误，代码示例可执行
    - ✅ 完整性：覆盖用户关键需求，无重要信息遗漏
    - ✅ 时效性：与项目当前版本保持同步
    - ✅ 实用性：解决用户实际问题，提供可操作指导

    ### 结构质量
    - ✅ 逻辑清晰：信息组织符合用户认知习惯
    - ✅ 层次分明：标题层次合理，导航便捷
    - ✅ 格式统一：Markdown格式规范，样式一致
    - ✅ 链接有效：内外部链接全部可访问

    ### 用户体验
    - ✅ 易读性：语言简洁明了，专业术语有解释
    - ✅ 可扫描性：重点信息突出，支持快速浏览
    - ✅ 可操作性：步骤清晰，示例丰富
    - ✅ 响应式：在不同设备上显示良好

    ### GitHub集成
    - ✅ 版本控制：文档变更有完整的提交记录
    - ✅ 协作友好：支持多人协作和贡献
    - ✅ 自动化：集成CI/CD检查和部署
    - ✅ 社区建设：鼓励用户参与和反馈
  </criteria>
</execution> 