# k8s/security/pod-security/default-security-context.yaml
# 为所有Pod定义默认的安全上下文，遵循安全最佳实践

apiVersion: v1
kind: Pod
metadata:
  name: default-pod-template
spec:
  # Pod级别的安全上下文
  securityContext:
    runAsNonRoot: true
    runAsUser: 1001
    runAsGroup: 1001
    fsGroup: 1001
    seccompProfile:
      type: RuntimeDefault
  
  # 容器级别的安全上下文
  containers:
  - name: default-container
    securityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      capabilities:
        drop:
        - ALL
      resources:
        limits:
          memory: "512Mi"
          cpu: "500m"
          ephemeral-storage: "1Gi"
        requests:
          memory: "256Mi"
          cpu: "250m"
          ephemeral-storage: "500Mi" 