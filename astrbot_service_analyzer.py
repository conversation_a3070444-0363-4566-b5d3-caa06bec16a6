#!/usr/bin/env python3
"""
AstrBot主服务接口分析工具
DevOps执行专家 - 系统性分析AstrBot服务的API接口和通信协议
"""

import requests
import json
import socket
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
import asyncio
import websockets
import subprocess

class AstrBotServiceAnalyzer:
    """AstrBot服务分析器"""
    
    def __init__(self):
        self.base_host = "localhost"
        self.ports = [6185, 6195, 6199]
        self.results = {}
        
    def print_section(self, title: str, level: int = 1):
        """打印章节标题"""
        if level == 1:
            print(f"\n{'='*80}")
            print(f"🔍 {title}")
            print(f"{'='*80}")
        else:
            print(f"\n{'-'*60}")
            print(f"📋 {title}")
            print(f"{'-'*60}")
    
    def check_port_status(self, port: int) -> bool:
        """检查端口是否开放"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((self.base_host, port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def analyze_http_service(self, port: int) -> Dict[str, Any]:
        """分析HTTP服务"""
        base_url = f"http://{self.base_host}:{port}"
        analysis = {
            "port": port,
            "protocol": "HTTP",
            "status": "unknown",
            "endpoints": [],
            "headers": {},
            "server_info": {},
            "api_docs": None
        }
        
        # 常见的API路径
        test_paths = [
            "/",
            "/api",
            "/api/v1",
            "/docs",
            "/swagger",
            "/openapi.json",
            "/health",
            "/status",
            "/info",
            "/version",
            "/ping",
            "/dashboard",
            "/admin",
            "/config"
        ]
        
        print(f"  🌐 分析HTTP服务 - 端口 {port}")
        
        for path in test_paths:
            try:
                response = requests.get(f"{base_url}{path}", timeout=5)
                if response.status_code < 500:  # 排除服务器错误
                    endpoint_info = {
                        "path": path,
                        "status_code": response.status_code,
                        "content_type": response.headers.get("content-type", ""),
                        "content_length": len(response.text),
                        "response_preview": response.text[:200] if response.text else ""
                    }
                    analysis["endpoints"].append(endpoint_info)
                    
                    # 记录服务器信息
                    if path == "/" and response.status_code == 200:
                        analysis["headers"] = dict(response.headers)
                        analysis["status"] = "active"
                        
                        # 尝试解析JSON响应
                        try:
                            json_data = response.json()
                            analysis["server_info"] = json_data
                        except:
                            pass
                    
                    # 检查API文档
                    if path in ["/docs", "/swagger"] and response.status_code == 200:
                        analysis["api_docs"] = f"{base_url}{path}"
                    
                    print(f"    ✅ {path}: {response.status_code} ({response.headers.get('content-type', 'unknown')})")
                
            except requests.exceptions.RequestException as e:
                if "Connection refused" not in str(e):
                    print(f"    ⚠️ {path}: 连接错误 - {e}")
        
        return analysis
    
    async def analyze_websocket_service(self, port: int) -> Dict[str, Any]:
        """分析WebSocket服务"""
        analysis = {
            "port": port,
            "protocol": "WebSocket",
            "status": "unknown",
            "connection_test": False,
            "message_test": False
        }
        
        ws_url = f"ws://{self.base_host}:{port}"
        print(f"  🔌 分析WebSocket服务 - 端口 {port}")
        
        try:
            # 尝试连接WebSocket
            async with websockets.connect(ws_url, timeout=5) as websocket:
                analysis["connection_test"] = True
                analysis["status"] = "active"
                print(f"    ✅ WebSocket连接成功")
                
                # 尝试发送测试消息
                try:
                    test_message = {"type": "ping", "data": "test"}
                    await websocket.send(json.dumps(test_message))
                    
                    # 等待响应
                    response = await asyncio.wait_for(websocket.recv(), timeout=3)
                    analysis["message_test"] = True
                    analysis["test_response"] = response[:200]
                    print(f"    ✅ 消息测试成功: {response[:50]}...")
                    
                except asyncio.TimeoutError:
                    print(f"    ⚠️ 消息响应超时")
                except Exception as e:
                    print(f"    ⚠️ 消息测试失败: {e}")
                    
        except Exception as e:
            print(f"    ❌ WebSocket连接失败: {e}")
            
        return analysis
    
    def check_docker_container_info(self):
        """检查Docker容器信息"""
        self.print_section("Docker容器信息分析", 2)
        
        try:
            # 获取AstrBot容器信息
            result = subprocess.run(
                ['docker', 'inspect', 'astrbot'], 
                capture_output=True, text=True, encoding='utf-8'
            )
            
            if result.returncode == 0:
                container_info = json.loads(result.stdout)[0]
                
                # 提取关键信息
                config = container_info.get('Config', {})
                network_settings = container_info.get('NetworkSettings', {})
                
                print(f"  📦 容器名称: {container_info.get('Name', 'N/A')}")
                print(f"  🏷️ 镜像: {config.get('Image', 'N/A')}")
                print(f"  🔧 状态: {container_info.get('State', {}).get('Status', 'N/A')}")
                
                # 端口映射
                ports = network_settings.get('Ports', {})
                print(f"  🌐 端口映射:")
                for container_port, host_bindings in ports.items():
                    if host_bindings:
                        for binding in host_bindings:
                            print(f"    {container_port} -> {binding.get('HostPort', 'N/A')}")
                
                # 环境变量
                env_vars = config.get('Env', [])
                relevant_env = [env for env in env_vars if any(key in env.upper() for key in ['API', 'PORT', 'HOST', 'URL'])]
                if relevant_env:
                    print(f"  🔧 相关环境变量:")
                    for env in relevant_env[:10]:  # 限制显示数量
                        print(f"    {env}")
                
                return container_info
            else:
                print(f"  ❌ 无法获取容器信息: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"  ❌ Docker检查失败: {e}")
            return None
    
    async def run_analysis(self):
        """运行完整分析"""
        self.print_section("AstrBot主服务接口分析报告")
        print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 分析目标: {self.base_host}:{', '.join(map(str, self.ports))}")
        
        # Docker容器信息
        container_info = self.check_docker_container_info()
        
        # 端口状态检查
        self.print_section("端口状态检查", 2)
        for port in self.ports:
            is_open = self.check_port_status(port)
            status = "🟢 开放" if is_open else "🔴 关闭"
            print(f"  端口 {port}: {status}")
        
        # HTTP服务分析
        self.print_section("HTTP服务分析", 2)
        for port in self.ports:
            if self.check_port_status(port):
                analysis = self.analyze_http_service(port)
                self.results[f"http_{port}"] = analysis
        
        # WebSocket服务分析
        self.print_section("WebSocket服务分析", 2)
        for port in self.ports:
            if self.check_port_status(port):
                try:
                    analysis = await self.analyze_websocket_service(port)
                    self.results[f"ws_{port}"] = analysis
                except Exception as e:
                    print(f"  ❌ 端口 {port} WebSocket分析失败: {e}")
        
        # 生成分析报告
        self.generate_report()
    
    def generate_report(self):
        """生成分析报告"""
        self.print_section("分析结果总结", 2)
        
        # HTTP服务总结
        http_services = [v for k, v in self.results.items() if k.startswith('http_')]
        active_http = [s for s in http_services if s.get('status') == 'active']
        
        print(f"📊 HTTP服务: {len(active_http)}/{len(http_services)} 活跃")
        for service in active_http:
            port = service['port']
            endpoints = len(service['endpoints'])
            api_docs = service.get('api_docs')
            print(f"  🌐 端口 {port}: {endpoints}个端点" + (f", API文档: {api_docs}" if api_docs else ""))
        
        # WebSocket服务总结
        ws_services = [v for k, v in self.results.items() if k.startswith('ws_')]
        active_ws = [s for s in ws_services if s.get('status') == 'active']
        
        print(f"🔌 WebSocket服务: {len(active_ws)}/{len(ws_services)} 活跃")
        for service in active_ws:
            port = service['port']
            msg_test = "支持消息" if service.get('message_test') else "仅连接"
            print(f"  🔌 端口 {port}: {msg_test}")
        
        # 集成建议
        self.print_section("集成建议", 2)
        if active_http:
            main_port = active_http[0]['port']
            print(f"💡 建议使用端口 {main_port} 作为主要API接口")
        
        if active_ws:
            ws_port = active_ws[0]['port']
            print(f"💡 建议使用端口 {ws_port} 进行WebSocket通信")
        
        print(f"💡 建议实现API代理模式进行SaaS集成")
        print(f"💡 建议使用多租户路由机制")

async def main():
    """主函数"""
    analyzer = AstrBotServiceAnalyzer()
    await analyzer.run_analysis()
    
    # 保存结果到文件
    with open('astrbot_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(analyzer.results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细结果已保存到: astrbot_analysis_results.json")

if __name__ == "__main__":
    asyncio.run(main())
