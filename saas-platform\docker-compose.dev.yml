# AstrBot SaaS Platform - 开发环境Docker Compose
# DevOps最佳实践：开发环境隔离、热重载、调试支持、本地数据持久化

version: '3.8'

services:
  # ===========================================
  # PostgreSQL数据库服务
  # ===========================================
  postgres:
    image: postgres:15-alpine
    container_name: astrbot-postgres-dev
    environment:
      POSTGRES_DB: astrbot_saas_dev
      POSTGRES_USER: astrbot_dev
      POSTGRES_PASSWORD: dev_password_123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U astrbot_dev -d astrbot_saas_dev"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - astrbot-dev

  # ===========================================
  # Redis缓存服务
  # ===========================================
  redis:
    image: redis:7-alpine
    container_name: astrbot-redis-dev
    command: redis-server --appendonly yes --requirepass dev_redis_123
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    restart: unless-stopped
    networks:
      - astrbot-dev

  # ===========================================
  # AstrBot SaaS主应用（开发模式）
  # ===========================================
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: development
    container_name: astrbot-app-dev
    environment:
      # 应用配置
      ENVIRONMENT: development
      DEBUG: "true"
      LOG_LEVEL: DEBUG
      RELOAD: "true"
      
      # 数据库配置
      DATABASE_URL: postgresql+asyncpg://astrbot_dev:dev_password_123@postgres:5432/astrbot_saas_dev
      
      # Redis配置
      REDIS_URL: redis://:dev_redis_123@redis:6379/0
      
      # JWT配置
      SECRET_KEY: dev-secret-key-change-in-production
      JWT_SECRET_KEY: dev-jwt-secret-change-in-production
      ACCESS_TOKEN_EXPIRE_MINUTES: 60
      
      # 开发工具配置
      UVICORN_RELOAD: "true"
      UVICORN_LOG_LEVEL: debug
      
      # 监控配置（开发环境简化）
      PROMETHEUS_ENABLED: "false"
      JAEGER_ENABLED: "false"
    ports:
      - "8000:8000"  # API端口
      - "5678:5678"  # Debug端口（VSCode调试）
    volumes:
      # 代码热重载
      - ./app:/app/app:ro
      - ./alembic:/app/alembic:ro
      - ./alembic.ini:/app/alembic.ini:ro
      - ./pyproject.toml:/app/pyproject.toml:ro
      
      # 开发数据持久化
      - dev_logs:/app/logs
      - dev_uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - astrbot-dev
    # 开发环境调试支持
    stdin_open: true
    tty: true

  # ===========================================
  # 数据库管理工具（可选）
  # ===========================================
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: astrbot-pgadmin-dev
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data_dev:/var/lib/pgadmin
      - ./config/pgadmin-servers.json:/pgadmin4/servers.json:ro
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - astrbot-dev
    profiles:
      - tools  # 使用 --profile tools 启动

  # ===========================================
  # Redis管理工具（可选）
  # ===========================================
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: astrbot-redis-commander-dev
    environment:
      REDIS_HOSTS: local:redis:6379:0:dev_redis_123
      HTTP_USER: admin
      HTTP_PASSWORD: admin123
    ports:
      - "8081:8081"
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - astrbot-dev
    profiles:
      - tools  # 使用 --profile tools 启动

  # ===========================================
  # Nginx反向代理（开发环境）
  # ===========================================
  nginx:
    image: nginx:alpine
    container_name: astrbot-nginx-dev
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.dev.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - astrbot-dev
    profiles:
      - proxy  # 使用 --profile proxy 启动

# ===========================================
# 网络配置
# ===========================================
networks:
  astrbot-dev:
    driver: bridge
    name: astrbot-dev-network

# ===========================================
# 数据卷配置
# ===========================================
volumes:
  # 数据库数据
  postgres_data_dev:
    name: astrbot-postgres-data-dev
  
  # Redis数据
  redis_data_dev:
    name: astrbot-redis-data-dev
  
  # PgAdmin数据
  pgadmin_data_dev:
    name: astrbot-pgadmin-data-dev
  
  # 应用数据
  dev_logs:
    name: astrbot-app-logs-dev
  
  dev_uploads:
    name: astrbot-app-uploads-dev

# ===========================================
# 扩展配置
# ===========================================
x-common-variables: &common-variables
  TZ: Asia/Shanghai
  LANG: zh_CN.UTF-8

x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"

# 所有服务应用日志配置
x-service-defaults: &service-defaults
  logging: *default-logging
  environment:
    <<: *common-variables 