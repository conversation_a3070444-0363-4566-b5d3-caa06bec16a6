{"timestamp": "2025-06-16T13:54:37.235003", "summary": {"total_issues": 7, "critical": 0, "high": 0, "medium": 0, "low": 0, "fixed": 0}, "checks": {"code_security_scan": {"status": "error", "error": "C:\\ProgramData\\anaconda3\\python.exe: No module named bandit\n", "issues_found": 0}, "dependency_vulnerabilities": {"status": "error", "error": "C:\\ProgramData\\anaconda3\\python.exe: No module named safety\n", "vulnerabilities_found": 0}, "sensitive_files_check": {"status": "completed", "issues_found": 2, "details": [{"type": "hardcoded_api_key", "file": "D:\\tool\\AstrBot\\saas-platform\\debug_api.py", "line": 26, "description": "可能的硬编码敏感信息: api_key=\"test_api_key_12345678\"...", "severity": "HIGH"}, {"type": "hardcoded_api_key", "file": "D:\\tool\\AstrBot\\saas-platform\\debug_tenant_auth.py", "line": 26, "description": "可能的硬编码敏感信息: api_key=\"debug_api_key_12345678\"...", "severity": "HIGH"}]}, "configuration_security": {"status": "completed", "issues_found": 5, "details": [{"type": "default_password", "file": "D:\\tool\\AstrBot\\saas-platform\\docker-compose.yml", "description": "可能使用了默认密码: password", "severity": "HIGH", "recommendation": "使用强密码并通过环境变量配置"}, {"type": "default_password", "file": "D:\\tool\\AstrBot\\saas-platform\\docker-compose.yml", "description": "可能使用了默认密码: 123456", "severity": "HIGH", "recommendation": "使用强密码并通过环境变量配置"}, {"type": "default_password", "file": "D:\\tool\\AstrBot\\saas-platform\\docker-compose.yml", "description": "可能使用了默认密码: admin", "severity": "HIGH", "recommendation": "使用强密码并通过环境变量配置"}, {"type": "default_password", "file": "D:\\tool\\AstrBot\\saas-platform\\docker-compose.yml", "description": "可能使用了默认密码: root", "severity": "HIGH", "recommendation": "使用强密码并通过环境变量配置"}, {"type": "default_password", "file": "D:\\tool\\AstrBot\\saas-platform\\docker-compose.yml", "description": "可能使用了默认密码: astrbot123", "severity": "HIGH", "recommendation": "使用强密码并通过环境变量配置"}]}}}