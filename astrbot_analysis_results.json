{"http_6185": {"port": 6185, "protocol": "HTTP", "status": "active", "endpoints": [{"path": "/", "status_code": 200, "content_type": "text/html; charset=utf-8", "content_length": 750, "response_preview": "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <link rel=\"icon\" href=\"/favicon.svg\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <met"}, {"path": "/api", "status_code": 401, "content_type": "application/json", "content_length": 62, "response_preview": "{\"status\":\"error\",\"message\":\"\\u672a\\u6388\\u6743\",\"data\":null}\n"}, {"path": "/api/v1", "status_code": 401, "content_type": "application/json", "content_length": 62, "response_preview": "{\"status\":\"error\",\"message\":\"\\u672a\\u6388\\u6743\",\"data\":null}\n"}, {"path": "/docs", "status_code": 200, "content_type": "text/html; charset=utf-8", "content_length": 98, "response_preview": "404 Not found。如果你初次使用打开面板发现 404, 请参考文档: https://astrbot.app/faq.html。如果你正在测试回调地址可达性，显示这段文字说明测试成功了。"}, {"path": "/swagger", "status_code": 200, "content_type": "text/html; charset=utf-8", "content_length": 98, "response_preview": "404 Not found。如果你初次使用打开面板发现 404, 请参考文档: https://astrbot.app/faq.html。如果你正在测试回调地址可达性，显示这段文字说明测试成功了。"}, {"path": "/openapi.json", "status_code": 200, "content_type": "text/html; charset=utf-8", "content_length": 98, "response_preview": "404 Not found。如果你初次使用打开面板发现 404, 请参考文档: https://astrbot.app/faq.html。如果你正在测试回调地址可达性，显示这段文字说明测试成功了。"}, {"path": "/health", "status_code": 200, "content_type": "text/html; charset=utf-8", "content_length": 98, "response_preview": "404 Not found。如果你初次使用打开面板发现 404, 请参考文档: https://astrbot.app/faq.html。如果你正在测试回调地址可达性，显示这段文字说明测试成功了。"}, {"path": "/status", "status_code": 200, "content_type": "text/html; charset=utf-8", "content_length": 98, "response_preview": "404 Not found。如果你初次使用打开面板发现 404, 请参考文档: https://astrbot.app/faq.html。如果你正在测试回调地址可达性，显示这段文字说明测试成功了。"}, {"path": "/info", "status_code": 200, "content_type": "text/html; charset=utf-8", "content_length": 98, "response_preview": "404 Not found。如果你初次使用打开面板发现 404, 请参考文档: https://astrbot.app/faq.html。如果你正在测试回调地址可达性，显示这段文字说明测试成功了。"}, {"path": "/version", "status_code": 200, "content_type": "text/html; charset=utf-8", "content_length": 98, "response_preview": "404 Not found。如果你初次使用打开面板发现 404, 请参考文档: https://astrbot.app/faq.html。如果你正在测试回调地址可达性，显示这段文字说明测试成功了。"}, {"path": "/ping", "status_code": 200, "content_type": "text/html; charset=utf-8", "content_length": 98, "response_preview": "404 Not found。如果你初次使用打开面板发现 404, 请参考文档: https://astrbot.app/faq.html。如果你正在测试回调地址可达性，显示这段文字说明测试成功了。"}, {"path": "/dashboard", "status_code": 200, "content_type": "text/html; charset=utf-8", "content_length": 98, "response_preview": "404 Not found。如果你初次使用打开面板发现 404, 请参考文档: https://astrbot.app/faq.html。如果你正在测试回调地址可达性，显示这段文字说明测试成功了。"}, {"path": "/admin", "status_code": 200, "content_type": "text/html; charset=utf-8", "content_length": 98, "response_preview": "404 Not found。如果你初次使用打开面板发现 404, 请参考文档: https://astrbot.app/faq.html。如果你正在测试回调地址可达性，显示这段文字说明测试成功了。"}, {"path": "/config", "status_code": 200, "content_type": "text/html; charset=utf-8", "content_length": 750, "response_preview": "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <link rel=\"icon\" href=\"/favicon.svg\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <met"}], "headers": {"content-type": "text/html; charset=utf-8", "content-length": "756", "last-modified": "Fri, 20 Jun 2025 09:06:02 GMT", "cache-control": "public, max-age=43200", "expires": "Wed, 25 Jun 2025 17:33:25 GMT", "etag": "\"1750410362.8524-756-2631404229\"", "date": "Wed, 25 Jun 2025 05:33:25 GMT", "server": "hypercorn-h11"}, "server_info": {}, "api_docs": "http://localhost:6185/swagger"}, "http_6195": {"port": 6195, "protocol": "HTTP", "status": "unknown", "endpoints": [], "headers": {}, "server_info": {}, "api_docs": null}, "http_6199": {"port": 6199, "protocol": "HTTP", "status": "unknown", "endpoints": [], "headers": {}, "server_info": {}, "api_docs": null}, "ws_6185": {"port": 6185, "protocol": "WebSocket", "status": "unknown", "connection_test": false, "message_test": false}, "ws_6195": {"port": 6195, "protocol": "WebSocket", "status": "unknown", "connection_test": false, "message_test": false}, "ws_6199": {"port": 6199, "protocol": "WebSocket", "status": "unknown", "connection_test": false, "message_test": false}}