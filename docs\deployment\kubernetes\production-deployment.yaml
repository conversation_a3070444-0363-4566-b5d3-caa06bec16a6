# AstrBot SaaS Platform - 生产环境Kubernetes部署配置
# 包含安全加固、多环境支持、自动扩缩容和完整监控

apiVersion: v1
kind: Namespace
metadata:
  name: astrbot-saas
  labels:
    name: astrbot-saas
    environment: production
    project: astrbot
  annotations:
    prometheus.io/scrape: "true"

---
# ConfigMap - 应用配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: astrbot-config
  namespace: astrbot-saas
  labels:
    app: astrbot-saas
    component: config
data:
  # 应用配置
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  DEBUG: "false"
  CORS_ORIGINS: '["https://app.astrbot.com", "https://admin.astrbot.com"]'

  # 数据库配置
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "astrbot_saas"
  DATABASE_USER: "astrbot"

  # Redis配置
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"

  # JWT配置
  JWT_ALGORITHM: "HS256"
  ACCESS_TOKEN_EXPIRE_MINUTES: "30"
  REFRESH_TOKEN_EXPIRE_DAYS: "7"

  # 监控配置
  PROMETHEUS_ENABLED: "true"
  JAEGER_ENABLED: "true"
  JAEGER_AGENT_HOST: "jaeger-agent"
  JAEGER_AGENT_PORT: "6831"

  # 性能配置
  UVICORN_WORKERS: "4"
  UVICORN_MAX_REQUESTS: "10000"
  UVICORN_MAX_REQUESTS_JITTER: "1000"

  # 缓存配置
  CACHE_TTL: "3600"
  RATE_LIMIT_REQUESTS: "1000"
  RATE_LIMIT_WINDOW: "3600"

---
# Secret - 敏感配置
apiVersion: v1
kind: Secret
metadata:
  name: astrbot-secrets
  namespace: astrbot-saas
  labels:
    app: astrbot-saas
    component: secrets
type: Opaque
data:
  # Base64编码的敏感信息 - 生产环境需要修改
  SECRET_KEY: eW91ci1zdXBlci1zZWNyZXQta2V5LWNoYW5nZS1pbi1wcm9kdWN0aW9u
  JWT_SECRET_KEY: and0LXNlY3JldC1jaGFuZ2UtaW4tcHJvZHVjdGlvbg==
  DATABASE_PASSWORD: ""  # 通过外部secret管理系统注入
  REDIS_PASSWORD: ""     # 通过外部secret管理系统注入
  POSTGRES_PASSWORD: ""  # 通过外部secret管理系统注入
  OPENAI_API_KEY: ""     # 通过外部secret管理系统注入
  DIFY_API_KEY: ""       # 通过外部secret管理系统注入

---
# ServiceAccount
apiVersion: v1
kind: ServiceAccount
metadata:
  name: astrbot-service-account
  namespace: astrbot-saas
  labels:
    app: astrbot-saas
    component: rbac
automountServiceAccountToken: false  # 安全加固

---
# Role - RBAC权限控制
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: astrbot-role
  namespace: astrbot-saas
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]

---
# RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: astrbot-rolebinding
  namespace: astrbot-saas
subjects:
- kind: ServiceAccount
  name: astrbot-service-account
  namespace: astrbot-saas
roleRef:
  kind: Role
  name: astrbot-role
  apiGroup: rbac.authorization.k8s.io

---
# PostgreSQL StatefulSet - 生产级数据库部署
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: astrbot-saas
  labels:
    app: postgres
    component: database
spec:
  serviceName: postgres-headless
  replicas: 1  # 生产环境可考虑主从复制
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
        component: database
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9187"
    spec:
      serviceAccountName: astrbot-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: postgres
        image: postgres:15-alpine
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: astrbot-config
              key: DATABASE_NAME
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: astrbot-config
              key: DATABASE_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: astrbot-secrets
              key: POSTGRES_PASSWORD
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: postgres-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        - name: init-db-scripts
          mountPath: /docker-entrypoint-initdb.d
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - "pg_isready -U $POSTGRES_USER -d $POSTGRES_DB"
          initialDelaySeconds: 15
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - "pg_isready -U $POSTGRES_USER -d $POSTGRES_DB"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      
      # PostgreSQL Exporter for Prometheus
      - name: postgres-exporter
        image: prometheuscommunity/postgres-exporter:latest
        ports:
        - containerPort: 9187
          name: metrics
        env:
        - name: DATA_SOURCE_NAME
          value: "postgresql://$(POSTGRES_USER):$(POSTGRES_PASSWORD)@localhost:5432/$(POSTGRES_DB)?sslmode=disable"
        envFrom:
        - configMapRef:
            name: astrbot-config
        - secretRef:
            name: astrbot-secrets
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      
      volumes:
      - name: postgres-config
        configMap:
          name: postgres-config
      - name: init-db-scripts
        configMap:
          name: init-db-scripts
          defaultMode: 0755
      
      initContainers:
      - name: postgres-init
        image: postgres:15-alpine
        command:
        - sh
        - -c
        - |
          chown -R 999:999 /var/lib/postgresql/data
          chmod 700 /var/lib/postgresql/data
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        securityContext:
          runAsUser: 0
          
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 100Gi

---
# PostgreSQL Services
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: astrbot-saas
  labels:
    app: postgres
    component: database
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9187"
spec:
  selector:
    app: postgres
  ports:
  - name: postgres
    port: 5432
    targetPort: 5432
  - name: metrics
    port: 9187
    targetPort: 9187
  type: ClusterIP

---
# PostgreSQL Headless Service for StatefulSet
apiVersion: v1
kind: Service
metadata:
  name: postgres-headless
  namespace: astrbot-saas
  labels:
    app: postgres
    component: database
spec:
  clusterIP: None
  selector:
    app: postgres
  ports:
  - name: postgres
    port: 5432
    targetPort: 5432

---
# Redis Deployment with Sentinel (High Availability)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: astrbot-saas
  labels:
    app: redis
    component: cache
spec:
  replicas: 1  # 生产环境建议使用Redis Cluster
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
        component: cache
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9121"
    spec:
      serviceAccountName: astrbot-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: redis
        image: redis:7-alpine
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 6379
          name: redis
        command:
        - redis-server
        - /etc/redis/redis.conf
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: astrbot-secrets
              key: REDIS_PASSWORD
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis/
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 3
          timeoutSeconds: 2
          failureThreshold: 3
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      
      # Redis Exporter for Prometheus
      - name: redis-exporter
        image: oliver006/redis_exporter:latest
        ports:
        - containerPort: 9121
          name: metrics
        env:
        - name: REDIS_ADDR
          value: "redis://localhost:6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: astrbot-secrets
              key: REDIS_PASSWORD
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc
      - name: redis-config
        configMap:
          name: redis-config

---
# Redis Service
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: astrbot-saas
  labels:
    app: redis
    component: cache
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9121"
spec:
  selector:
    app: redis
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
  - name: metrics
    port: 9121
    targetPort: 9121
  type: ClusterIP

---
# Redis PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: astrbot-saas
  labels:
    app: redis
    component: cache
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd

---
# AstrBot SaaS Application Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: astrbot-saas
  namespace: astrbot-saas
  labels:
    app: astrbot-saas
    component: api
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 1
  selector:
    matchLabels:
      app: astrbot-saas
      component: api
  template:
    metadata:
      labels:
        app: astrbot-saas
        component: api
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
        fluentd.collect: "true"
    spec:
      serviceAccountName: astrbot-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      
      # Init容器 - 数据库迁移
      initContainers:
      - name: migrate-db
        image: astrbot/saas-platform:latest
        imagePullPolicy: Always
        command: 
        - sh
        - -c
        - |
          echo "开始数据库迁移..."
          python -m alembic upgrade head
          echo "数据库迁移完成"
        envFrom:
        - configMapRef:
            name: astrbot-config
        - secretRef:
            name: astrbot-secrets
        env:
        - name: DATABASE_URL
          value: "postgresql+asyncpg://$(DATABASE_USER):$(DATABASE_PASSWORD)@$(DATABASE_HOST):$(DATABASE_PORT)/$(DATABASE_NAME)"
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      
      # 主应用容器
      containers:
      - name: astrbot-saas
        image: astrbot/saas-platform:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        - containerPort: 8080
          name: metrics
          protocol: TCP
        envFrom:
        - configMapRef:
            name: astrbot-config
        - secretRef:
            name: astrbot-secrets
        env:
        - name: DATABASE_URL
          value: "postgresql+asyncpg://$(DATABASE_USER):$(DATABASE_PASSWORD)@$(DATABASE_HOST):$(DATABASE_PORT)/$(DATABASE_NAME)"
        - name: REDIS_URL
          value: "redis://:$(REDIS_PASSWORD)@$(REDIS_HOST):$(REDIS_PORT)/$(REDIS_DB)"
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        
        volumeMounts:
        - name: app-logs
          mountPath: /app/logs
        - name: tmp-volume
          mountPath: /tmp
        - name: static-files
          mountPath: /app/static
        
        # 健康检查
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        
        # 启动探针
        startupProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 30
        
        # 资源限制
        resources:
          requests:
            memory: "1Gi"
            cpu: "1000m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        
        # 安全上下文
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
            add:
            - NET_BIND_SERVICE
        
        # 优雅停机
        lifecycle:
          preStop:
            exec:
              command:
              - sh
              - -c
              - "sleep 15"
      
      volumes:
      - name: app-logs
        emptyDir:
          sizeLimit: 1Gi
      - name: tmp-volume
        emptyDir:
          sizeLimit: 500Mi
      - name: static-files
        persistentVolumeClaim:
          claimName: static-files-pvc
      
      # DNS配置
      dnsPolicy: ClusterFirst
      
      # 容器镜像拉取配置
      imagePullSecrets:
      - name: regcred
      
      # Pod优雅停机时间
      terminationGracePeriodSeconds: 30
      
      # 节点选择器（生产环境节点）
      nodeSelector:
        kubernetes.io/environment: production
      
      # 反亲和性（确保Pod分布在不同节点）
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - astrbot-saas
              topologyKey: kubernetes.io/hostname

---
# AstrBot SaaS Service
apiVersion: v1
kind: Service
metadata:
  name: astrbot-saas-service
  namespace: astrbot-saas
  labels:
    app: astrbot-saas
    component: api
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  selector:
    app: astrbot-saas
    component: api
  ports:
  - name: http
    port: 80
    targetPort: 8000
    protocol: TCP
  - name: metrics
    port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP
  sessionAffinity: None

---
# Static Files PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: static-files-pvc
  namespace: astrbot-saas
  labels:
    app: astrbot-saas
    component: storage
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: shared-storage

---
# HorizontalPodAutoscaler - 自动扩缩容
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: astrbot-saas-hpa
  namespace: astrbot-saas
  labels:
    app: astrbot-saas
    component: autoscaling
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: astrbot-saas
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "1000"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Pods
        value: 2
        periodSeconds: 60
      - type: Percent
        value: 50
        periodSeconds: 60
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Pods
        value: 4
        periodSeconds: 60
      - type: Percent
        value: 100
        periodSeconds: 60
      selectPolicy: Max

---
# PodDisruptionBudget - 确保高可用
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: astrbot-saas-pdb
  namespace: astrbot-saas
  labels:
    app: astrbot-saas
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: astrbot-saas
      component: api

---
# NetworkPolicy - 网络安全
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: astrbot-saas-network-policy
  namespace: astrbot-saas
  labels:
    app: astrbot-saas
    component: security
spec:
  podSelector:
    matchLabels:
      app: astrbot-saas
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []  # 允许DNS查询
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  - to: []  # 允许HTTPS出站（用于外部API调用）
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80

---
# Ingress - 入口配置
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: astrbot-saas-ingress
  namespace: astrbot-saas
  labels:
    app: astrbot-saas
    component: ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "1000"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Frame-Options: DENY";
      more_set_headers "X-Content-Type-Options: nosniff";
      more_set_headers "X-XSS-Protection: 1; mode=block";
      more_set_headers "Strict-Transport-Security: max-age=31536000; includeSubDomains";
spec:
  tls:
  - hosts:
    - api.astrbot.com
    - admin.astrbot.com
    secretName: astrbot-tls-secret
  rules:
  - host: api.astrbot.com
    http:
      paths:
      - path: /api/(.*)
        pathType: Prefix
        backend:
          service:
            name: astrbot-saas-service
            port:
              number: 80
      - path: /docs
        pathType: Prefix
        backend:
          service:
            name: astrbot-saas-service
            port:
              number: 80
      - path: /redoc
        pathType: Prefix
        backend:
          service:
            name: astrbot-saas-service
            port:
              number: 80
      - path: /metrics
        pathType: Prefix
        backend:
          service:
            name: astrbot-saas-service
            port:
              number: 8080
  - host: admin.astrbot.com
    http:
      paths:
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: astrbot-saas-service
            port:
              number: 80 