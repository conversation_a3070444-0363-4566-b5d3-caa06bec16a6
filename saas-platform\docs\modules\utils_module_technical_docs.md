# AstrBot SaaS Platform 工具模块技术文档

## 📋 模块概述

`app/utils/` 目录包含了 AstrBot SaaS Platform 的核心工具模块，提供基础设施功能包括结构化日志记录、多租户上下文管理等关键工具。这些工具模块为整个应用提供横切关注点的支持。

## 🏗️ 模块架构

### 整体架构设计
```mermaid
graph TD
    A[Utils Module] --> B[Logging System]
    A --> C[Context Management]
    
    B --> B1[StructuredFormatter]
    B --> B2[ContextLogger]
    B --> B3[Global Logger Factory]
    
    C --> C1[TenantContext]
    C --> C2[ContextVar Management]
    
    B1 --> D[JSON Log Output]
    B2 --> E[Multi-tenant Logging]
    C1 --> F[Request-scoped Data]
    C2 --> G[Async-safe Context]
```

### 模块依赖关系
```mermaid
graph LR
    A[业务服务层] --> B[Utils Module]
    B --> C[Python Standard Library]
    B --> D[App Core Config]
    
    E[API层] --> B
    F[中间件层] --> B
    G[数据访问层] --> B
```

## 📁 模块结构

```
app/utils/
├── __init__.py              # 模块初始化
├── logging.py               # 结构化日志系统
└── context_vars.py          # 上下文变量管理
```

---

## 🔧 日志系统模块 (`logging.py`)

### 功能概述
提供企业级的结构化日志记录功能，支持多租户环境的日志管理和分析。

### 核心组件

#### 1. StructuredFormatter 类

**职责：** 将日志记录格式化为结构化JSON格式，便于日志分析和监控。

```python
class StructuredFormatter(logging.Formatter):
    def format(self, record: logging.LogRecord) -> str:
        # 生成结构化JSON日志
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        return json.dumps(log_data, ensure_ascii=False)
```

**特性：**
- **ISO时间戳：** 统一时间格式，便于跨系统分析
- **完整调用信息：** 模块、函数、行号定位
- **多租户支持：** 自动注入tenant_id等上下文信息
- **异常追踪：** 完整的异常堆栈信息
- **可扩展字段：** 支持动态添加extra_data

#### 2. ContextLogger 类

**职责：** 提供带上下文的日志记录器，支持租户隔离和请求追踪。

```python
class ContextLogger:
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.context: dict[str, Any] = {}
    
    def set_context(self, **kwargs) -> None:
        """设置日志上下文信息"""
        self.context.update(kwargs)
```

**核心方法：**
- `set_context(**kwargs)`: 设置上下文信息（租户ID、用户ID等）
- `clear_context()`: 清除上下文信息
- `debug/info/warning/error/critical()`: 不同级别的日志记录

**使用示例：**
```python
from app.utils.logging import get_logger

logger = get_logger(__name__)

# 设置上下文
logger.set_context(
    tenant_id=str(tenant.id),
    user_id=str(user.id),
    operation="user_login"
)

# 记录日志（自动包含上下文）
logger.info("用户登录成功", ip_address="***********")
```

#### 3. 日志配置管理

**开发环境配置：**
```python
# 简单的文本格式，便于阅读
formatter = logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
```

**生产环境配置：**
```python
# 结构化JSON格式，便于分析
formatter = StructuredFormatter()
```

**第三方库日志控制：**
```python
# 降低噪音级别
logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("asyncio").setLevel(logging.WARNING)
```

### 日志输出格式示例

#### 开发环境输出
```
2024-12-17 10:30:15,123 - app.services.user_service - INFO - 用户登录成功
```

#### 生产环境输出
```json
{
  "timestamp": "2024-12-17T10:30:15.123456",
  "level": "INFO",
  "logger": "app.services.user_service", 
  "message": "用户登录成功",
  "module": "user_service",
  "function": "authenticate_user",
  "line": 45,
  "tenant_id": "550e8400-e29b-41d4-a716-************",
  "user_id": "660e8400-e29b-41d4-a716-************",
  "ip_address": "***********"
}
```

---

## 🎯 上下文管理模块 (`context_vars.py`)

### 功能概述
提供线程安全的请求级别上下文数据管理，确保在异步环境中正确传递多租户信息。

### 核心组件

#### TenantContext 类

**职责：** 管理租户ID的上下文传递，确保多租户隔离。

```python
class TenantContext:
    _tenant_id_cv: ContextVar[Optional[UUID]] = ContextVar("tenant_id_cv", default=None)
    
    def set_tenant_id(self, tenant_id: UUID) -> None:
        """设置当前请求的租户ID"""
        self._tenant_id_cv.set(tenant_id)
    
    def get_tenant_id(self) -> Optional[UUID]:
        """获取当前请求的租户ID"""
        return self._tenant_id_cv.get()
```

### 使用场景

#### 1. 中间件设置上下文
```python
# 在认证中间件中设置租户上下文
async def auth_middleware(request: Request, call_next):
    tenant_id = extract_tenant_from_token(request)
    tenant_context.set_tenant_id(tenant_id)
    
    response = await call_next(request)
    return response
```

#### 2. 服务层访问上下文
```python
# 在数据访问层自动应用租户过滤
class BaseService:
    def get_current_tenant_id(self) -> UUID:
        tenant_id = tenant_context.get_tenant_id()
        if not tenant_id:
            raise HTTPException(400, "Missing tenant context")
        return tenant_id
```

#### 3. 数据库查询的租户隔离
```python
async def get_user_sessions(self, user_id: UUID) -> List[Session]:
    current_tenant = tenant_context.get_tenant_id()
    
    query = select(Session).where(
        and_(
            Session.user_id == user_id,
            Session.tenant_id == current_tenant  # 自动租户隔离
        )
    )
    return await self.db.execute(query)
```

### ContextVar 的优势

1. **异步安全：** 在异步函数调用链中保持上下文
2. **线程隔离：** 不同请求间的数据不会混淆
3. **性能优异：** 相比传参方式，上下文变量性能更好
4. **透明传递：** 无需在每个函数签名中显式传递

---

## 🔧 集成使用指南

### 1. 在服务中使用日志系统

```python
# 服务类中的标准用法
from app.utils.logging import get_logger
from app.utils.context_vars import tenant_context

class UserService:
    def __init__(self):
        self.logger = get_logger(__name__)
    
    async def create_user(self, user_data: UserCreate) -> User:
        # 设置操作上下文
        self.logger.set_context(
            tenant_id=str(tenant_context.get_tenant_id()),
            operation="create_user"
        )
        
        try:
            # 业务逻辑
            user = await self._create_user_in_db(user_data)
            
            # 成功日志
            self.logger.info(
                "用户创建成功",
                user_id=str(user.id),
                username=user.username
            )
            
            return user
            
        except Exception as e:
            # 错误日志
            self.logger.error(
                "用户创建失败", 
                error=str(e),
                user_data=user_data.dict()
            )
            raise
```

### 2. 在API端点中设置上下文

```python
from fastapi import Depends
from app.utils.context_vars import tenant_context

@router.post("/users")
async def create_user(
    user_data: UserCreate,
    current_tenant: Tenant = Depends(get_current_tenant)
):
    # 设置租户上下文
    tenant_context.set_tenant_id(current_tenant.id)
    
    # 调用服务（自动获得上下文）
    return await user_service.create_user(user_data)
```

### 3. 错误处理和日志记录

```python
from app.utils.logging import get_logger

logger = get_logger(__name__)

async def handle_webhook(webhook_data: dict):
    try:
        # 设置追踪上下文
        logger.set_context(
            webhook_id=webhook_data.get("id"),
            source=webhook_data.get("source")
        )
        
        logger.info("开始处理Webhook")
        
        # 处理逻辑
        result = await process_webhook(webhook_data)
        
        logger.info("Webhook处理完成", result_id=result.id)
        
    except ValidationError as e:
        logger.warning("Webhook数据验证失败", errors=e.errors())
        raise HTTPException(400, "Invalid webhook data")
        
    except Exception as e:
        logger.error("Webhook处理异常", exception=str(e))
        raise HTTPException(500, "Webhook processing failed")
```

---

## 🔍 最佳实践

### 1. 日志级别使用指南

```python
# DEBUG: 详细的调试信息，仅开发环境
logger.debug("SQL查询", query=str(query), params=params)

# INFO: 重要的业务操作记录
logger.info("用户登录", user_id=user.id, login_time=datetime.now())

# WARNING: 可恢复的错误或异常情况
logger.warning("API调用失败，正在重试", attempt=2, max_attempts=3)

# ERROR: 需要关注的错误，但不影响整体服务
logger.error("第三方服务不可用", service="email_service", error=str(e))

# CRITICAL: 严重错误，可能导致服务不可用
logger.critical("数据库连接失败", error=str(e))
```

### 2. 上下文信息标准化

```python
# 推荐的上下文字段命名
logger.set_context(
    tenant_id=str(tenant.id),           # 租户标识
    user_id=str(user.id),               # 用户标识  
    session_id=str(session.id),         # 会话标识
    request_id=str(uuid.uuid4()),       # 请求唯一标识
    operation="user_registration",       # 操作类型
    source="web_api"                    # 请求来源
)
```

### 3. 性能优化

```python
# 避免在高频路径上重复设置相同上下文
class OptimizedService:
    def __init__(self):
        self.logger = get_logger(__name__)
        self._context_set = False
    
    def ensure_context(self, tenant_id: UUID):
        if not self._context_set:
            self.logger.set_context(tenant_id=str(tenant_id))
            self._context_set = True
```

---

## 🐛 故障排除

### 常见问题

#### 1. 上下文丢失
**问题：** 异步任务中无法获取租户上下文

**原因：** 在新的任务中ContextVar会被重置

**解决方案：**
```python
# 显式传递上下文到异步任务
import asyncio
from contextvars import copy_context

async def background_task():
    # 在新任务中上下文为空
    tenant_id = tenant_context.get_tenant_id()  # None
    
# 正确的做法
async def create_background_task():
    # 复制当前上下文
    ctx = copy_context()
    
    # 在复制的上下文中运行任务
    task = asyncio.create_task(background_task(), context=ctx)
    await task
```

#### 2. 日志格式不一致
**问题：** 某些日志没有使用结构化格式

**原因：** 第三方库使用了自己的日志记录器

**解决方案：**
```python
# 统一第三方库的日志格式
def setup_third_party_logging():
    # 设置SQLAlchemy日志
    sqlalchemy_logger = logging.getLogger("sqlalchemy")
    sqlalchemy_logger.handlers = []
    sqlalchemy_logger.addHandler(console_handler)
```

#### 3. 日志级别不正确
**问题：** 生产环境中看到过多DEBUG日志

**解决方案：**
```python
# 检查环境配置
# .env 文件
LOG_LEVEL=INFO  # 生产环境不要使用DEBUG

# 代码中验证
if settings.ENVIRONMENT == "production":
    assert settings.LOG_LEVEL != "DEBUG", "Production should not use DEBUG level"
```

---

## 🔮 扩展和优化

### 1. 增强日志功能

```python
# 添加性能监控
class PerformanceLogger(ContextLogger):
    async def log_performance(self, operation: str, duration: float):
        if duration > 1.0:  # 超过1秒记录为警告
            self.warning(f"{operation}执行较慢", duration=duration)
        else:
            self.info(f"{operation}执行完成", duration=duration)

# 使用装饰器自动记录性能
def log_performance(operation: str):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"{operation}成功", duration=duration)
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"{operation}失败", duration=duration, error=str(e))
                raise
        return wrapper
    return decorator
```

### 2. 扩展上下文管理

```python
# 支持更多上下文类型
class EnhancedContext:
    def __init__(self):
        self.tenant_id_cv = ContextVar("tenant_id", default=None)
        self.user_id_cv = ContextVar("user_id", default=None)
        self.request_id_cv = ContextVar("request_id", default=None)
        self.trace_id_cv = ContextVar("trace_id", default=None)
    
    def set_request_context(self, tenant_id: UUID, user_id: UUID, request_id: str):
        self.tenant_id_cv.set(tenant_id)
        self.user_id_cv.set(user_id) 
        self.request_id_cv.set(request_id)
```

### 3. 集成外部日志系统

```python
# 集成ELK Stack
class ElasticsearchHandler(logging.Handler):
    def __init__(self, elasticsearch_client, index_name):
        super().__init__()
        self.es_client = elasticsearch_client
        self.index_name = index_name
    
    def emit(self, record):
        log_entry = json.loads(self.format(record))
        self.es_client.index(
            index=self.index_name,
            body=log_entry
        )
```

---

**文档维护：** 本文档应与代码变更同步更新，确保信息准确性。
**更新日期：** 2024年12月17日
**维护者：** 技术文档专家 