{"api_warnings": {"missing_response_model": [], "missing_summary": [], "missing_description": [], "missing_type_annotations": [], "missing_error_handling": [], "total": 0}, "services_warnings": {"missing_type_annotations": [], "missing_docstrings": [], "async_without_await": [{"file": "app\\services\\llm\\base_provider.py", "function": "generate_response", "line": 76}, {"file": "app\\services\\llm\\base_provider.py", "function": "generate_stream_response", "line": 95}, {"file": "app\\services\\llm\\base_provider.py", "function": "validate_config", "line": 114}, {"file": "app\\services\\llm\\base_provider.py", "function": "get_token_count", "line": 124}, {"file": "app\\services\\llm\\dify_provider.py", "function": "_get_client", "line": 76}, {"file": "app\\services\\llm\\mock_provider.py", "function": "generate_response", "line": 41}, {"file": "app\\services\\llm\\openai_provider.py", "function": "_get_client", "line": 78}], "missing_error_handling": [], "total": 7}, "total_warnings": 7, "priority_actions": [{"priority": 2, "category": "异步优化", "action": "检查异步函数必要性", "count": 7, "files": ["app\\services\\llm\\mock_provider.py", "app\\services\\llm\\openai_provider.py", "app\\services\\llm\\dify_provider.py", "app\\services\\llm\\base_provider.py"]}]}