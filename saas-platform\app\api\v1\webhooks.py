"""
Webhook API端点

处理AstrBot实例的消息上报、状态同步等Webhook请求
"""

from typing import Any, Dict, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, Header, HTTPException, Request
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import status

from app.api.deps import get_current_tenant_from_token
from app.core.database import get_db
from app.models.tenant import Tenant
from app.schemas.common import StandardResponse
from app.services.instance_auth_service import InstanceAuthService
from app.services.webhook_service import WebhookService, SecurityError
from app.utils.logging import get_logger

# 配置日志
logger = get_logger(__name__)

# 创建路由器 - 采用Sessions成功模式，无prefix避免双重prefix
router = APIRouter(prefix="/webhooks", tags=["webhooks"])


# 依赖注入
def get_webhook_service(db: AsyncSession = Depends(get_db)) -> WebhookService:
    return WebhookService(db)


@router.post(
    "/astrbot/{tenant_id}",
    summary="接收AstrBot实例Webhook",
    description="处理来自AstrBot实例的消息上报、状态同步等事件",
    response_model=dict[str, Any],
    status_code=status.HTTP_200_OK,
    tags=["Webhooks"],
)
async def handle_astrbot_webhook(
    tenant_id: UUID,
    webhook_data: dict[str, Any],
    signature: Optional[str] = Header(None, alias="X-AstrBot-Signature"),
    service: WebhookService = Depends(get_webhook_service),
) -> dict[str, Any]:
    """
    统一的Webhook入口，根据事件类型分发处理
    """
    event_type = webhook_data.get("event_type", "unknown")

    try:
        if event_type.startswith("message.") or event_type.startswith("session."):
            result = await service.process_message_webhook(
                tenant_id, webhook_data, signature
            )
        elif event_type.startswith("instance."):
            result = await service.process_status_webhook(
                tenant_id, webhook_data, signature
            )
        else:
            # 对于未知事件类型，记录并返回成功，避免重试风暴
            logger.warning("received_unknown_webhook_event", event_type=event_type)
            return {"status": "success", "message": "Unknown event type received"}

        return result

    except (ValueError, SecurityError) as e:
        logger.warning(
            "webhook_validation_error",
            tenant_id=tenant_id,
            event_type=event_type,
            error=str(e),
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(
            "webhook_unhandled_exception",
            tenant_id=tenant_id,
            event_type=event_type,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while processing webhook",
        )


# 添加兼容测试用例的消息Webhook端点
@router.post(
    "/messages/{tenant_id}",
    summary="接收消息Webhook(兼容接口)",
    description="专门处理消息相关的Webhook事件，兼容测试用例",
    response_model=dict[str, Any],
    status_code=status.HTTP_200_OK,
    tags=["Webhooks"],
)
async def handle_message_webhook(
    tenant_id: UUID,
    request: Request,
    signature: Optional[str] = Header(None, alias="X-Webhook-Signature"),
    service: WebhookService = Depends(get_webhook_service),
) -> dict[str, Any]:
    """
    专门处理消息相关的Webhook事件
    """
    # 获取原始请求体用于签名验证
    raw_body = await request.body()
    
    # 解析JSON数据
    try:
        webhook_data = await request.json()
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid JSON payload: {str(e)}")
    
    # 确保事件类型是消息相关的
    event_type = webhook_data.get("event_type", "message.received")
    if not event_type.startswith("message."):
        webhook_data["event_type"] = "message.received"

    try:
        result = await service.process_message_webhook(
            tenant_id, webhook_data, signature, raw_body
        )
        return result

    except SecurityError as e:
        error_msg = str(e)
        # 区分不同类型的安全错误
        if "Missing" in error_msg:
            status_code = 400  # Bad Request for missing headers
        else:
            status_code = 403  # Forbidden for invalid signatures
            
        logger.warning(
            "message_webhook_security_error",
            tenant_id=tenant_id,
            event_type=event_type,
            error=error_msg,
        )
        raise HTTPException(status_code=status_code, detail=error_msg)
    except (ValueError,) as e:
        logger.warning(
            "message_webhook_validation_error",
            tenant_id=tenant_id,
            event_type=event_type,
            error=str(e),
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(
            "message_webhook_unhandled_exception",
            tenant_id=tenant_id,
            event_type=event_type,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while processing message webhook",
        )


@router.get("/{tenant_id}/health")
async def webhook_health_check(
    tenant_id: UUID,
    tenant: Tenant = Depends(get_current_tenant_from_token),
    db: AsyncSession = Depends(get_db),
) -> StandardResponse:
    """
    Webhook健康检查端点

    Args:
        tenant_id: 租户ID
        tenant: 当前租户
        db: 数据库会话

    Returns:
        StandardResponse: 健康状态
    """
    try:
        # 验证租户权限
        if tenant.id != tenant_id:
            raise HTTPException(
                status_code=403, detail="Access denied: tenant ID mismatch"
            )

        # 检查Webhook配置
        webhook_config = {}
        if tenant.configuration:
            webhook_config = tenant.configuration.get("webhook_config", {})

        # 构建健康状态
        health_status = {
            "status": "healthy",
            "tenant_id": str(tenant_id),
            "webhook_endpoints": {
                "messages": f"/api/v1/webhooks/{tenant_id}/messages",
                "status": f"/api/v1/webhooks/{tenant_id}/status",
            },
            "webhook_config": {
                "has_secret": bool(webhook_config.get("webhook_secret")),
                "max_payload_size": webhook_config.get(
                    "max_payload_size", 1048576
                ),  # 1MB
                "timeout_seconds": webhook_config.get("timeout_seconds", 30),
            },
        }

        return StandardResponse(
            data=health_status, message="Webhook endpoint is healthy"
        )

    except Exception as e:
        logger.error("webhook_health_check_error", tenant_id=tenant_id, error=str(e))
        raise HTTPException(status_code=500, detail="Health check failed")


@router.post("/{tenant_id}/test")
async def test_webhook_endpoint(
    tenant_id: UUID,
    test_data: Optional[dict[str, Any]] = None,
    tenant: Tenant = Depends(get_current_tenant_from_token),
    db: AsyncSession = Depends(get_db),
) -> StandardResponse:
    """
    测试Webhook端点

    Args:
        tenant_id: 租户ID
        test_data: 测试数据
        tenant: 当前租户
        db: 数据库会话

    Returns:
        StandardResponse: 测试结果
    """
    try:
        # 验证租户权限
        if tenant.id != tenant_id:
            raise HTTPException(
                status_code=403, detail="Access denied: tenant ID mismatch"
            )

        # 构建默认测试数据
        if not test_data:
            test_data = {
                "event_type": "test.webhook",
                "timestamp": "2024-01-01T12:00:00Z",
                "data": {"test": True, "message": "This is a test webhook"},
            }

        # 验证测试数据格式
        webhook_service = WebhookService(db)
        try:
            webhook_service.validate_webhook_data(test_data)
        except ValueError as ve:
            # 对于测试端点，我们可以更宽松一些
            logger.warning(
                "test_webhook_validation_warning",
                tenant_id=tenant_id,
                validation_error=str(ve),
            )

        # 模拟处理测试Webhook
        test_result = {
            "status": "test_successful",
            "tenant_id": str(tenant_id),
            "test_data": test_data,
            "endpoints_available": [
                f"/api/v1/webhooks/{tenant_id}/messages",
                f"/api/v1/webhooks/{tenant_id}/status",
            ],
        }

        logger.info("webhook_test_completed", tenant_id=tenant_id, test_data=test_data)

        return StandardResponse(
            data=test_result, message="Webhook test completed successfully"
        )

    except Exception as e:
        logger.error("webhook_test_error", tenant_id=tenant_id, error=str(e))
        raise HTTPException(status_code=500, detail="Webhook test failed")


@router.get("/{tenant_id}/config")
async def get_webhook_config(
    tenant_id: UUID,
    tenant: Tenant = Depends(get_current_tenant_from_token),
    db: AsyncSession = Depends(get_db),
) -> StandardResponse:
    """
    获取Webhook配置

    Args:
        tenant_id: 租户ID
        tenant: 当前租户
        db: 数据库会话

    Returns:
        StandardResponse: Webhook配置
    """
    try:
        # 验证租户权限
        if tenant.id != tenant_id:
            raise HTTPException(
                status_code=403, detail="Access denied: tenant ID mismatch"
            )

        # 获取Webhook配置
        webhook_config = {}
        if tenant.configuration:
            webhook_config = tenant.configuration.get("webhook_config", {})

        # 构建配置响应（不包含敏感信息）
        config_response = {
            "tenant_id": str(tenant_id),
            "webhook_endpoints": {
                "messages": f"/api/v1/webhooks/{tenant_id}/messages",
                "status": f"/api/v1/webhooks/{tenant_id}/status",
                "test": f"/api/v1/webhooks/{tenant_id}/test",
            },
            "config": {
                "has_webhook_secret": bool(webhook_config.get("webhook_secret")),
                "max_payload_size": webhook_config.get("max_payload_size", 1048576),
                "timeout_seconds": webhook_config.get("timeout_seconds", 30),
                "retry_attempts": webhook_config.get("retry_attempts", 3),
                "signature_verification": webhook_config.get(
                    "signature_verification", True
                ),
            },
            "supported_events": [
                "message.received",
                "message.sent",
                "session.created",
                "session.closed",
                "instance.status_changed",
            ],
        }

        return StandardResponse(
            data=config_response, message="Webhook configuration retrieved successfully"
        )

    except Exception as e:
        logger.error("get_webhook_config_error", tenant_id=tenant_id, error=str(e))
        raise HTTPException(
            status_code=500, detail="Failed to get webhook configuration"
        )
