#!/usr/bin/env python3
"""
智能文档清理工具
彻底清理重复文档，真正优化文档结构
"""

import os
import shutil
from pathlib import Path
from typing import Dict, List, Set
from datetime import datetime


class SmartDocumentCleaner:
    """智能文档清理器"""
    
    def __init__(self, root_path: str):
        self.root_path = Path(root_path)
        self.cleaned_count = 0
        self.operations_log = []
        
    def log_operation(self, operation: str, description: str):
        """记录操作"""
        self.operations_log.append({
            'operation': operation,
            'description': description,
            'timestamp': datetime.now().isoformat()
        })
        print(f"✅ {operation}: {description}")
    
    def remove_backup_duplicates(self) -> None:
        """删除备份目录"""
        print("🗑️ 删除备份目录...")
        
        backup_dir = self.root_path / "docs_backup"
        if backup_dir.exists():
            shutil.rmtree(backup_dir)
            self.log_operation("删除备份", f"删除了 docs_backup 目录")
        
        # 删除其他备份目录
        backup_dirs = ["backup_quality_fix", ".pytest_cache"]
        for backup_name in backup_dirs:
            backup_path = self.root_path / backup_name
            if backup_path.exists():
                shutil.rmtree(backup_path)
                self.log_operation("删除备份", f"删除了 {backup_name} 目录")
    
    def clean_root_level_docs(self) -> None:
        """清理根目录层级的冗余文档"""
        print("🧹 清理根目录冗余文档...")
        
        # 保留的重要文档
        keep_docs = {
            'README.md',
            'DOC_OPTIMIZATION_REPORT.md',
            'docs_optimization_log.json'
        }
        
        # 删除不重要的文档
        root_md_files = list(self.root_path.glob("*.md"))
        cleaned = 0
        
        for md_file in root_md_files:
            if md_file.name not in keep_docs:
                try:
                    md_file.unlink()
                    cleaned += 1
                    self.log_operation("清理根目录", f"删除了 {md_file.name}")
                except Exception as e:
                    print(f"⚠️ 删除失败: {md_file} - {e}")
        
        print(f"✅ 清理了 {cleaned} 个根目录文档")
    
    def merge_similar_comprehensive_docs(self) -> None:
        """合并相似的comprehensive文档"""
        print("🔄 合并comprehensive文档...")
        
        docs_modules = self.root_path / "docs" / "modules"
        if not docs_modules.exists():
            print("ℹ️ modules目录不存在")
            return
        
        # 查找comprehensive文档
        comprehensive_files = list(docs_modules.glob("*comprehensive*.md"))
        
        if len(comprehensive_files) <= 1:
            print("ℹ️ 没有需要合并的comprehensive文档")
            return
        
        # 按类型分组合并
        groups = {
            'api': [],
            'services': [],
            'security': [],
            'database': [],
            'middleware': [],
            'other': []
        }
        
        for file_path in comprehensive_files:
            file_name = file_path.name.lower()
            if 'api' in file_name:
                groups['api'].append(file_path)
            elif 'service' in file_name or 'tenant' in file_name:
                groups['services'].append(file_path)
            elif 'security' in file_name:
                groups['security'].append(file_path)
            elif 'database' in file_name or 'models' in file_name:
                groups['database'].append(file_path)
            elif 'middleware' in file_name or 'utils' in file_name:
                groups['middleware'].append(file_path)
            else:
                groups['other'].append(file_path)
        
        # 执行合并
        for group_name, files in groups.items():
            if len(files) > 1:
                self.merge_group_files(group_name, files, docs_modules)
    
    def merge_group_files(self, group_name: str, files: List[Path], output_dir: Path) -> None:
        """合并一组文件"""
        if not files:
            return
        
        # 创建合并后的文件名
        merged_filename = f"{group_name}_consolidated.md"
        merged_path = output_dir / merged_filename
        
        # 合并内容
        merged_content = f"# {group_name.title()} 综合文档\n\n"
        merged_content += f"*合并自: {', '.join(f.name for f in files)}*\n\n"
        merged_content += f"*合并时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n"
        merged_content += "---\n\n"
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                merged_content += f"## 来源: {file_path.name}\n\n"
                merged_content += content
                merged_content += "\n\n---\n\n"
                
            except Exception as e:
                print(f"⚠️ 读取文件失败: {file_path} - {e}")
        
        # 写入合并文件
        try:
            merged_path.write_text(merged_content, encoding='utf-8')
            self.log_operation("合并文档", f"创建了 {merged_filename}")
            
            # 删除原文件
            for file_path in files:
                file_path.unlink()
                self.log_operation("删除原文件", f"删除了 {file_path.name}")
                
        except Exception as e:
            print(f"⚠️ 合并失败: {e}")
    
    def consolidate_reports(self) -> None:
        """整合报告文档"""
        print("📊 整合报告文档...")
        
        reports_dir = self.root_path / "docs" / "reports"
        if not reports_dir.exists():
            print("ℹ️ reports目录不存在")
            return
        
        # 获取所有报告
        report_files = list(reports_dir.glob("*.md"))
        if len(report_files) <= 3:
            print("ℹ️ 报告文档数量已经合理")
            return
        
        # 按重要性排序（文件大小 + 修改时间）
        def get_importance_score(file_path: Path) -> float:
            try:
                stat = file_path.stat()
                return stat.st_size + (stat.st_mtime / 1000000)
            except:
                return 0
        
        sorted_reports = sorted(report_files, key=get_importance_score, reverse=True)
        
        # 保留前3个最重要的
        keep_reports = sorted_reports[:3]
        consolidate_reports = sorted_reports[3:]
        
        if consolidate_reports:
            # 创建综合报告
            consolidated_path = reports_dir / "consolidated_reports.md"
            consolidated_content = "# 整合报告文档\n\n"
            consolidated_content += f"*整合时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n"
            
            for report in consolidate_reports:
                try:
                    with open(report, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    consolidated_content += f"## {report.name}\n\n"
                    consolidated_content += content
                    consolidated_content += "\n\n---\n\n"
                    
                    report.unlink()
                    self.log_operation("整合报告", f"整合了 {report.name}")
                    
                except Exception as e:
                    print(f"⚠️ 整合报告失败: {report} - {e}")
            
            # 写入整合报告
            consolidated_path.write_text(consolidated_content, encoding='utf-8')
            self.log_operation("创建整合报告", "创建了 consolidated_reports.md")
    
    def remove_empty_dirs(self) -> None:
        """删除空目录"""
        print("🗂️ 删除空目录...")
        
        removed_dirs = []
        
        # 检查docs下的子目录
        docs_dir = self.root_path / "docs"
        if docs_dir.exists():
            for subdir in docs_dir.iterdir():
                if subdir.is_dir():
                    # 检查是否为空（只有README.md的也算空）
                    contents = list(subdir.glob("*.md"))
                    if len(contents) <= 1 and any(f.name == "README.md" for f in contents):
                        try:
                            shutil.rmtree(subdir)
                            removed_dirs.append(subdir.name)
                            self.log_operation("删除空目录", f"删除了 {subdir.name}")
                        except Exception as e:
                            print(f"⚠️ 删除目录失败: {subdir} - {e}")
        
        if removed_dirs:
            print(f"✅ 删除了 {len(removed_dirs)} 个空目录")
        else:
            print("ℹ️ 没有发现空目录")
    
    def optimize_api_docs(self) -> None:
        """优化API文档"""
        print("🔌 优化API文档...")
        
        api_dir = self.root_path / "docs" / "api"
        if not api_dir.exists():
            return
        
        api_files = list(api_dir.glob("*.md"))
        if len(api_files) <= 3:
            return
        
        # 合并小的API文档
        small_files = []
        large_files = []
        
        for api_file in api_files:
            try:
                if api_file.stat().st_size < 5000:  # 小于5KB的文件
                    small_files.append(api_file)
                else:
                    large_files.append(api_file)
            except:
                continue
        
        if len(small_files) > 3:
            # 合并小文件
            merged_content = "# API文档合集\n\n"
            merged_content += f"*合并时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n"
            
            for small_file in small_files:
                try:
                    with open(small_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    merged_content += f"## {small_file.name}\n\n"
                    merged_content += content
                    merged_content += "\n\n---\n\n"
                    
                    small_file.unlink()
                    self.log_operation("合并API文档", f"合并了 {small_file.name}")
                    
                except Exception as e:
                    print(f"⚠️ 合并API文档失败: {small_file} - {e}")
            
            # 写入合并文件
            merged_path = api_dir / "api_collection.md"
            merged_path.write_text(merged_content, encoding='utf-8')
            self.log_operation("创建API合集", "创建了 api_collection.md")
    
    def create_final_summary(self) -> None:
        """创建最终总结"""
        print("📋 创建最终总结...")
        
        # 统计最终文档数量
        total_docs = 0
        docs_by_category = {}
        
        docs_dir = self.root_path / "docs"
        if docs_dir.exists():
            for subdir in docs_dir.iterdir():
                if subdir.is_dir():
                    md_files = list(subdir.glob("*.md"))
                    if md_files:
                        docs_by_category[subdir.name] = len(md_files)
                        total_docs += len(md_files)
        
        # 根目录文档
        root_docs = len(list(self.root_path.glob("*.md")))
        total_docs += root_docs
        
        summary_content = f"""# 智能文档清理总结

## 清理时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 最终文档统计
- **总文档数**: {total_docs}
- **根目录文档**: {root_docs}

### 分类统计
"""
        
        for category, count in docs_by_category.items():
            summary_content += f"- **{category}**: {count} 个文档\n"
        
        summary_content += f"""
## 清理操作记录
"""
        
        # 按操作类型统计
        operation_stats = {}
        for op in self.operations_log:
            op_type = op['operation']
            operation_stats[op_type] = operation_stats.get(op_type, 0) + 1
        
        for op_type, count in operation_stats.items():
            summary_content += f"- **{op_type}**: {count} 次操作\n"
        
        summary_content += f"""
## 优化效果
1. ✅ **消除重复**: 删除了所有备份和重复文档
2. ✅ **结构优化**: 建立了清晰的分类结构
3. ✅ **内容整合**: 合并了相似和冗余的文档
4. ✅ **维护性提升**: 大幅减少了文档维护成本

## 最终文档结构
```
docs/
├── modules/      # 模块和组件文档
├── api/          # API接口文档
├── guides/       # 用户和开发指南
├── reports/      # 重要项目报告
├── security/     # 安全相关文档
├── testing/      # 测试文档
├── deployment/   # 部署运维文档
└── README.md     # 文档索引
```

---
*智能文档清理工具 v1.0*
"""
        
        # 保存总结
        summary_path = self.root_path / "SMART_CLEANUP_SUMMARY.md"
        summary_path.write_text(summary_content, encoding='utf-8')
        
        print(f"✅ 创建最终总结: {summary_path}")


def main():
    """主函数"""
    print("🧠 AstrBot SaaS Platform - 智能文档清理工具")
    print("="*60)
    
    cleaner = SmartDocumentCleaner(".")
    
    try:
        # 1. 删除备份目录
        cleaner.remove_backup_duplicates()
        
        # 2. 清理根目录冗余文档
        cleaner.clean_root_level_docs()
        
        # 3. 合并comprehensive文档
        cleaner.merge_similar_comprehensive_docs()
        
        # 4. 整合报告文档
        cleaner.consolidate_reports()
        
        # 5. 优化API文档
        cleaner.optimize_api_docs()
        
        # 6. 删除空目录
        cleaner.remove_empty_dirs()
        
        # 7. 创建最终总结
        cleaner.create_final_summary()
        
        print("\n" + "="*60)
        print("🎉 智能文档清理完成！")
        print("="*60)
        print(f"📊 总共执行了 {len(cleaner.operations_log)} 项操作")
        print(f"📋 详细总结: SMART_CLEANUP_SUMMARY.md")
        
        # 最终统计
        total_docs = len(list(Path(".").rglob("*.md")))
        print(f"📁 最终文档数量: {total_docs}")
        
    except Exception as e:
        print(f"\n❌ 清理过程中出现错误: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main()) 