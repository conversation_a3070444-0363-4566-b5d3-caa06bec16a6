#!/usr/bin/env python3
"""
PostgreSQL Windows 安装和测试工具
支持多种安装方式：Docker、官方安装包、便携版
"""

import os
import sys
import subprocess
import json
import psutil
import time
import requests
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import asyncio
import asyncpg
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('postgresql_install.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class PostgreSQLInstaller:
    """PostgreSQL Windows 安装器"""
    
    def __init__(self):
        """初始化安装器"""
        self.install_methods = {
            "1": ("Docker容器", self.install_docker),
            "2": ("官方安装包", self.install_official),
            "3": ("便携版", self.install_portable),
            "4": ("检测现有安装", self.detect_existing)
        }
        
        # 数据库连接配置
        self.db_config = {
            "host": "localhost",
            "port": 5432,
            "user": "astrbot",
            "password": "astrbot123",
            "database": "astrbot_saas"
        }
        
        # 测试数据库配置
        self.test_db_config = {
            "host": "localhost", 
            "port": 5432,
            "user": "astrbot",
            "password": "astrbot123",
            "database": "astrbot_test"
        }

    def show_menu(self) -> str:
        """显示安装选项菜单"""
        print("\n" + "="*60)
        print(" PostgreSQL Windows 安装向导")
        print("="*60)
        print("\n请选择安装方式:")
        
        for key, (name, _) in self.install_methods.items():
            print(f"  {key}. {name}")
        
        print("\n  0. 退出程序")
        print("-"*60)
        
        while True:
            # TODO: Add input validation for security
            choice = input("\n请输入选择 (0-4): ").strip()
            if choice in ["0"] + list(self.install_methods.keys()):
                return choice
            print("❌ 无效选择，请重新输入")

    def check_prerequisites(self) -> bool:
        """检查系统前置条件"""
        logger.info("🔍 检查系统前置条件...")
        
        # 检查操作系统
        if os.name != 'nt':
            logger.error("❌ 此脚本仅支持Windows系统")
            return False
        
        # 检查Python版本
        if sys.version_info < (3, 7):
            logger.error("❌ 需要Python 3.7或更高版本")
            return False
        
        # 检查管理员权限
        try:
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if not is_admin:
                logger.warning("⚠️ 建议以管理员权限运行此脚本")
        except:
            pass
        
        logger.info("✅ 系统前置条件检查通过")
        return True

    def check_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port:
                    logger.warning(f"⚠️ 端口 {port} 已被占用")
                    return False
            return True
        except Exception as e:
            logger.warning(f"⚠️ 端口检查失败: {e}")
            return True

    def detect_existing(self) -> bool:
        """检测现有的PostgreSQL安装"""
        logger.info("🔍 检测现有PostgreSQL安装...")
        
        # 检查Windows服务
        try:
            services = subprocess.run(
                ['sc', 'query', 'state=', 'all'], 
                capture_output=True, text=True
            )
            if 'postgresql' in services.stdout.lower():
                logger.info("✅ 发现PostgreSQL Windows服务")
                return True
        except:
            pass
        
        # 检查常见安装路径
        common_paths = [
            r"C:\Program Files\PostgreSQL",
            r"C:\Program Files (x86)\PostgreSQL",
            r"C:\PostgreSQL"
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                logger.info(f"✅ 发现PostgreSQL安装: {path}")
                return True
        
        # 检查Docker容器
        try:
            result = subprocess.run(
                ['docker', 'ps', '-a', '--filter', 'name=postgres'],
                capture_output=True, text=True
            )
            if 'postgres' in result.stdout:
                logger.info("✅ 发现PostgreSQL Docker容器")
                return True
        except:
            pass
        
        logger.info("ℹ️ 未发现现有PostgreSQL安装")
        return False

    def install_docker(self) -> bool:
        """使用Docker安装PostgreSQL"""
        logger.info("🐳 开始Docker安装PostgreSQL...")
        
        # 检查Docker是否可用
        try:
            subprocess.run(['docker', '--version'], check=True, capture_output=True)
        except:
            logger.error("❌ Docker未安装或不可用，请先安装Docker Desktop")
            return False
        
        # 检查端口
        if not self.check_port_available(5432):
            logger.error("❌ 端口5432已被占用")
            return False
        
        try:
            # 停止并删除可能存在的容器
            subprocess.run(['docker', 'stop', 'astrbot-postgres'], capture_output=True)
            subprocess.run(['docker', 'rm', 'astrbot-postgres'], capture_output=True)
            
            # 启动PostgreSQL容器
            cmd = [
                'docker', 'run', '-d',
                '--name', 'astrbot-postgres',
                '-e', f'POSTGRES_USER={self.db_config["user"]}',
                '-e', f'POSTGRES_PASSWORD={self.db_config["password"]}',
                '-e', f'POSTGRES_DB={self.db_config["database"]}',
                '-p', f'{self.db_config["port"]}:5432',
                '-v', 'astrbot_postgres_data:/var/lib/postgresql/data',
                'postgres:15'
            ]
            
            logger.info("启动PostgreSQL Docker容器...")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"❌ 容器启动失败: {result.stderr}")
                return False
            
            logger.info("✅ PostgreSQL Docker容器启动成功")
            
            # 等待数据库启动
            logger.info("等待数据库初始化...")
            time.sleep(10)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Docker安装失败: {e}")
            return False

    def install_official(self) -> bool:
        """下载并安装官方PostgreSQL安装包"""
        logger.info("📦 开始下载官方PostgreSQL安装包...")
        
        # PostgreSQL 15 Windows安装包URL
        download_url = "https://get.enterprisedb.com/postgresql/postgresql-15.8-1-windows-x64.exe"
        installer_path = "postgresql_installer.exe"
        
        try:
            # 下载安装包
            logger.info("下载安装包...")
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            with open(installer_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info("✅ 安装包下载完成")
            
            # 静默安装
            logger.info("开始静默安装...")
            install_cmd = [
                installer_path,
                '--mode', 'unattended',
                '--unattendedmodeui', 'none',
                '--superpassword', self.db_config['password'],
                '--servicename', 'postgresql-x64-15',
                '--servicepassword', self.db_config['password'],
                '--serviceaccount', 'networkservice',
                '--datadir', r'C:\PostgreSQL\15\data',
                '--serverport', str(self.db_config['port'])
            ]
            
            result = subprocess.run(install_cmd, capture_output=True, text=True)
            
            # 清理安装包
            if os.path.exists(installer_path):
                os.remove(installer_path)
            
            if result.returncode == 0:
                logger.info("✅ PostgreSQL安装成功")
                return True
            else:
                logger.error(f"❌ 安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 官方安装包安装失败: {e}")
            return False

    def install_portable(self) -> bool:
        """安装便携版PostgreSQL"""
        logger.info("📁 安装便携版PostgreSQL...")
        
        # 便携版下载和配置逻辑
        logger.warning("⚠️ 便携版安装功能开发中，建议使用Docker方式")
        return False

    async def test_connection(self, config: Dict) -> bool:
        """测试数据库连接"""
        try:
            logger.info(f"🔗 测试数据库连接: {config['host']}:{config['port']}")
            
            # 建立连接
            conn = await asyncpg.connect(
                host=config['host'],
                port=config['port'],
                user=config['user'],
                password=config['password'],
                database='postgres'  # 连接默认数据库
            )
            
            # 测试查询
            version = await conn.fetchval('SELECT version()')
            logger.info(f"✅ 连接成功! PostgreSQL版本: {version}")
            
            await conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 连接失败: {e}")
            return False

    async def create_databases(self) -> bool:
        """创建项目数据库"""
        try:
            logger.info("🏗️ 创建项目数据库...")
            
            # 连接到默认数据库
            conn = await asyncpg.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database='postgres'
            )
            
            # 创建主数据库
            try:
                await conn.execute(f'CREATE DATABASE {self.db_config["database"]}')
                logger.info(f"✅ 主数据库创建成功: {self.db_config['database']}")
            except asyncpg.DuplicateDatabaseError:
                logger.info(f"ℹ️ 主数据库已存在: {self.db_config['database']}")
            
            # 创建测试数据库
            try:
                await conn.execute(f'CREATE DATABASE {self.test_db_config["database"]}')
                logger.info(f"✅ 测试数据库创建成功: {self.test_db_config['database']}")
            except asyncpg.DuplicateDatabaseError:
                logger.info(f"ℹ️ 测试数据库已存在: {self.test_db_config['database']}")
            
            await conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库创建失败: {e}")
            return False

    async def run_database_tests(self) -> bool:
        """运行数据库功能测试"""
        logger.info("🧪 开始数据库功能测试...")
        
        try:
            # 连接到主数据库
            conn = await asyncpg.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            
            # 测试1: 创建测试表
            logger.info("测试1: 创建测试表...")
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS test_table (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            logger.info("✅ 测试表创建成功")
            
            # 测试2: 插入数据
            logger.info("测试2: 插入测试数据...")
            await conn.execute(
                'INSERT INTO test_table (name) VALUES ($1)', 
                'AstrBot Test'
            )
            logger.info("✅ 数据插入成功")
            
            # 测试3: 查询数据
            logger.info("测试3: 查询数据...")
            rows = await conn.fetch('SELECT * FROM test_table')
            logger.info(f"✅ 查询成功，返回 {len(rows)} 条记录")
            
            # 测试4: 事务测试
            logger.info("测试4: 事务测试...")
            async with conn.transaction():
                await conn.execute(
                    'INSERT INTO test_table (name) VALUES ($1)', 
                    'Transaction Test'
                )
            logger.info("✅ 事务测试成功")
            
            # 清理测试数据
            await conn.execute('DROP TABLE test_table')
            
            await conn.close()
            logger.info("🎉 所有数据库测试通过!")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库测试失败: {e}")
            return False

    def generate_config_file(self) -> None:
        """生成数据库配置文件"""
        logger.info("📝 生成数据库配置文件...")
        
        config = {
            "database": {
                "host": self.db_config['host'],
                "port": self.db_config['port'],
                "user": self.db_config['user'],
                "password": self.db_config['password'],
                "database": self.db_config['database']
            },
            "test_database": {
                "host": self.test_db_config['host'],
                "port": self.test_db_config['port'],
                "user": self.test_db_config['user'],
                "password": self.test_db_config['password'],
                "database": self.test_db_config['database']
            }
        }
        
        # 生成JSON配置文件
        with open('database_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        # 生成.env配置
        env_content = f"""# PostgreSQL数据库配置
DATABASE_URL=postgresql://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}
TEST_DATABASE_URL=postgresql://{self.test_db_config['user']}:{self.test_db_config['password']}@{self.test_db_config['host']}:{self.test_db_config['port']}/{self.test_db_config['database']}

# 数据库连接配置
DB_HOST={self.db_config['host']}
DB_PORT={self.db_config['port']}
DB_USER={self.db_config['user']}
DB_PASSWORD={self.db_config['password']}
DB_NAME={self.db_config['database']}
"""
        
        with open('database.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        logger.info("✅ 配置文件生成完成:")
        logger.info("  - database_config.json")
        logger.info("  - database.env")

    async def run_installation(self) -> bool:
        """运行完整安装流程"""
        logger.info("🚀 开始PostgreSQL安装和测试流程...")
        
        # 检查前置条件
        if not self.check_prerequisites():
            return False
        
        # 显示菜单并获取选择
        choice = self.show_menu()
        
        if choice == "0":
            logger.info("👋 用户取消安装")
            return False
        
        # 执行安装
        method_name, install_func = self.install_methods[choice]
        logger.info(f"📦 选择安装方式: {method_name}")
        
        if not install_func():
            logger.error("❌ 安装失败")
            return False
        
        # 等待服务启动
        logger.info("⏳ 等待PostgreSQL服务启动...")
        await asyncio.sleep(5)
        
        # 测试连接
        if not await self.test_connection(self.db_config):
            logger.error("❌ 数据库连接测试失败")
            return False
        
        # 创建数据库
        if not await self.create_databases():
            logger.error("❌ 数据库创建失败")
            return False
        
        # 运行功能测试
        if not await self.run_database_tests():
            logger.error("❌ 数据库功能测试失败")
            return False
        
        # 生成配置文件
        self.generate_config_file()
        
        logger.info("🎉 PostgreSQL安装和测试完成!")
        return True

def main():
    """主函数"""
    print("🔧 PostgreSQL Windows 安装和测试工具")
    print("="*50)
    
    installer = PostgreSQLInstaller()
    
    try:
        # 运行异步安装流程
        result = asyncio.run(installer.run_installation())
        
        if result:
            print("\n" + "="*50)
            print("🎉 PostgreSQL安装和测试成功完成!")
            print("\n📋 接下来可以:")
            print("  1. 使用生成的配置文件连接数据库")
            print("  2. 运行AstrBot SaaS Platform应用")
            print("  3. 进行数据库迁移和初始化")
            print("="*50)
        else:
            print("\n❌ PostgreSQL安装失败，请检查日志")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断安装")
    except Exception as e:
        logger.error(f"❌ 安装过程出错: {e}")

if __name__ == "__main__":
    main() 