# 📖 技术文档：租户模型 (tenant.py)

## 🎯 1. 模块概述

**功能**：定义`Tenant`数据模型，对应数据库中的`tenants`表。

**核心职责**：
- **数据结构**：定义租户的核心字段，如`name`, `email`, `status`, `plan`。
- **关系**：定义与`User`, `Session`, `Role`等模型的关系。
- **业务逻辑**：包含与租户相关的业务逻辑，如`is_active`属性和`generate_api_key`方法。

## 🚀 2. 快速使用

### 2.1 创建租户

```python
from app.models.tenant import Tenant

new_tenant = Tenant(
    name="New Company",
    email="<EMAIL>",
)
db.add(new_tenant)
await db.commit()
```

### 2.2 查询租户

```python
from sqlalchemy import select

stmt = select(Tenant).where(Tenant.name == "New Company")
result = await db.execute(stmt)
tenant = result.scalar_one_or_none()
```

## 🏗️ 3. 架构设计

### 3.1 关键字段

- **`id`**: `UUID` - 主键
- **`name`**: `str` - 租户名称
- **`email`**: `str` - 管理员邮箱
- **`status`**: `TenantStatus` - 租户状态 (`ACTIVE`, `SUSPENDED`, `DEACTIVATED`)
- **`plan`**: `TenantPlan` - 租户套餐 (`BASIC`, `PRO`, `ENTERPRISE`)
- **`api_key`**: `str` - API访问密钥
- **`extra_data`**: `JSON` - 扩展元数据

### 3.2 关系

```mermaid
erDiagram
    TENANT ||--o{ USER : "has"
    TENANT ||--o{ SESSION : "has"
    TENANT ||--o{ ROLE : "has"
```

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_tenant_model.py`

## 💡 6. 维护与扩展

- **字段扩展**：可以通过`extra_data`字段灵活地添加自定义字段。
- **数据迁移**：任何对模型结构的更改都需要创建新的Alembic迁移脚本。
- **性能优化**：对于频繁查询的字段，应添加数据库索引。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 