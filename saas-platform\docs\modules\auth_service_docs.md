# 📖 技术文档：认证服务 (AuthService)

## 🎯 1. 模块概述

**功能**：提供全面的认证功能，包括用户登录、注册、密码管理和API密钥验证。

**核心职责**：
- **用户认证**：验证用户身份，生成JWT访问令牌和刷新令牌
- **用户注册**：创建新租户和用户，确保邮箱唯一性
- **密码管理**：支持安全修改密码
- **API密钥管理**：为租户生成、验证和撤销API密钥

## 🚀 2. 快速使用

### 2.1 依赖注入

在API端点中注入`AuthService`：

```python
from app.services.auth_service import AuthService, get_auth_service

@router.post("/login")
async def login(
    login_data: LoginRequest,
    auth_service: AuthService = Depends(get_auth_service),
):
    return await auth_service.login(login_data)
```

### 2.2 核心方法

- **`login(login_data)`** - 用户登录
- **`register(register_data)`** - 用户注册
- **`refresh_token(refresh_data)`** - 刷新访问令牌
- **`change_password(user_id, change_data)`** - 修改密码

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    subgraph "核心依赖"
        A[FastAPI] --> B(AuthService)
        C[SQLAlchemy] --> B
        D[Pydantic] --> B
    end

    subgraph "模块交互"
        B --> E(Tenant Model)
        B --> F(User Model)
        B --> G(Security Module)
    end

    style B fill:#c8e6c9
```

### 3.2 数据流

**登录流程**：
1. **API接收**：接收登录请求(email, password)
2. **服务处理**：
   - 查询租户是否存在
   - 验证租户状态
   - 生成JWT令牌
3. **响应返回**：返回`LoginResponse`包含Token

## 🔧 4. API参考

| 方法 | HTTP动词 | 端点 | 描述 |
|---|---|---|---|
| `login` | `POST` | `/api/v1/auth/login` | 用户登录 |
| `register` | `POST` | `/api/v1/auth/register` | 用户注册 |
| `refresh_token` | `POST` | `/api/v1/auth/refresh` | 刷新Token |
| `change_password`|`POST`|`/api/v1/auth/change-password`|修改密码|

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_auth_service.py`
- **集成测试**：`tests/integration/test_auth_api.py`

## 💡 6. 维护与扩展

- **密码哈希**：当前为简化实现，未来应集成`bcrypt`
- **OAuth 2.0**：可扩展支持第三方登录
- **多因素认证(MFA)**：可添加MFA服务

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 