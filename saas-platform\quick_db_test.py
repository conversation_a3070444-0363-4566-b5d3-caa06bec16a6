#!/usr/bin/env python3
"""
PostgreSQL 快速连接和功能测试工具
用于验证现有PostgreSQL安装的可用性
"""

import asyncio
import asyncpg
import logging
import sys
import subprocess
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PostgreSQLTester:
    """PostgreSQL快速测试器"""
    
    def __init__(self):
        """初始化测试器"""
        # 数据库配置
        self.db_configs = [
            {
                "name": "Docker容器",
                "host": "localhost",
                "port": 5432,
                "user": "astrbot",
                "password": "astrbot123",
                "database": "postgres"
            },
            {
                "name": "本地安装(postgres用户)",
                "host": "localhost", 
                "port": 5432,
                "user": "postgres",
                "password": "postgres",
                "database": "postgres"
            },
            {
                "name": "本地安装(默认配置)",
                "host": "localhost",
                "port": 5432,
                "user": "postgres",
                "password": "123456",
                "database": "postgres"
            }
        ]

    def check_docker_postgres(self) -> bool:
        """检查Docker PostgreSQL容器状态"""
        logger.info("🐳 检查Docker PostgreSQL容器...")
        
        try:
            # 检查容器是否存在并运行
            result = subprocess.run(
                ['docker', 'ps', '--filter', 'name=postgres', '--format', 'table {{.Names}}\t{{.Status}}'],
                capture_output=True, text=True, timeout=10
            )
            
            if result.returncode == 0 and 'postgres' in result.stdout:
                logger.info("✅ 发现运行中的PostgreSQL Docker容器")
                
                # 显示容器信息
                lines = result.stdout.strip().split('\n')[1:]  # 跳过表头
                for line in lines:
                    if line.strip():
                        logger.info(f"  容器信息: {line}")
                return True
            else:
                logger.info("ℹ️ 未发现运行中的PostgreSQL Docker容器")
                return False
                
        except subprocess.TimeoutExpired:
            logger.warning("⚠️ Docker命令超时")
            return False
        except Exception as e:
            logger.warning(f"⚠️ Docker检查失败: {e}")
            return False

    def check_windows_service(self) -> bool:
        """检查Windows PostgreSQL服务"""
        logger.info("🔧 检查Windows PostgreSQL服务...")
        
        try:
            result = subprocess.run(
                ['sc', 'query', 'state=', 'all'], 
                capture_output=True, text=True, timeout=10
            )
            
            if 'postgresql' in result.stdout.lower():
                logger.info("✅ 发现PostgreSQL Windows服务")
                
                # 获取具体服务信息
                service_result = subprocess.run(
                    ['sc', 'query', 'postgresql-x64-15'],
                    capture_output=True, text=True
                )
                
                if 'RUNNING' in service_result.stdout:
                    logger.info("  服务状态: 正在运行")
                else:
                    logger.info("  服务状态: 未运行")
                    
                return True
            else:
                logger.info("ℹ️ 未发现PostgreSQL Windows服务")
                return False
                
        except Exception as e:
            logger.warning(f"⚠️ Windows服务检查失败: {e}")
            return False

    async def test_connection(self, config: dict) -> bool:
        """测试数据库连接"""
        logger.info(f"🔗 测试连接: {config['name']}")
        logger.info(f"  连接信息: {config['user']}@{config['host']}:{config['port']}")
        
        try:
            # 建立连接
            conn = await asyncpg.connect(
                host=config['host'],
                port=config['port'],
                user=config['user'],
                password=config['password'],
                database=config['database'],
                timeout=5  # 5秒超时
            )
            
            # 获取数据库版本信息
            version = await conn.fetchval('SELECT version()')
            current_time = await conn.fetchval('SELECT NOW()')
            
            logger.info(f"✅ 连接成功!")
            logger.info(f"  PostgreSQL版本: {version.split(',')[0]}")
            logger.info(f"  服务器时间: {current_time}")
            
            await conn.close()
            return True
            
        except asyncio.TimeoutError:
            logger.error(f"❌ 连接超时: {config['name']}")
            return False
        except Exception as e:
            logger.error(f"❌ 连接失败: {config['name']} - {e}")
            return False

    async def run_basic_tests(self, config: dict) -> bool:
        """运行基础数据库功能测试"""
        logger.info(f"🧪 运行基础功能测试: {config['name']}")
        
        try:
            conn = await asyncpg.connect(
                host=config['host'],
                port=config['port'],
                user=config['user'],
                password=config['password'],
                database=config['database']
            )
            
            # 测试1: 创建测试表
            test_table = f"quick_test_{int(time.time())}"
            await conn.execute(f'''
                CREATE TABLE {test_table} (
                    id SERIAL PRIMARY KEY,
                    message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            logger.info("  ✅ 表创建测试通过")
            
            # 测试2: 插入数据
            await conn.execute(
                f'INSERT INTO {test_table} (message) VALUES ($1)', 
                'PostgreSQL连接测试成功'
            )
            logger.info("  ✅ 数据插入测试通过")
            
            # 测试3: 查询数据
            row = await conn.fetchrow(f'SELECT * FROM {test_table} ORDER BY id DESC LIMIT 1')
            logger.info(f"  ✅ 数据查询测试通过: {row['message']}")
            
            # 测试4: 事务测试
            async with conn.transaction():
                await conn.execute(
                    f'INSERT INTO {test_table} (message) VALUES ($1)', 
                    '事务测试'
                )
            logger.info("  ✅ 事务测试通过")
            
            # 测试5: 删除测试表
            await conn.execute(f'DROP TABLE {test_table}')
            logger.info("  ✅ 表删除测试通过")
            
            await conn.close()
            logger.info(f"🎉 {config['name']} 所有基础功能测试通过!")
            return True
            
        except Exception as e:
            logger.error(f"❌ 功能测试失败: {config['name']} - {e}")
            return False

    async def create_astrbot_databases(self, config: dict) -> bool:
        """创建AstrBot项目所需的数据库"""
        logger.info(f"🏗️ 创建AstrBot项目数据库: {config['name']}")
        
        try:
            conn = await asyncpg.connect(
                host=config['host'],
                port=config['port'],
                user=config['user'],
                password=config['password'],
                database=config['database']
            )
            
            databases_to_create = ['astrbot_saas', 'astrbot_test']
            
            for db_name in databases_to_create:
                try:
                    await conn.execute(f'CREATE DATABASE {db_name}')
                    logger.info(f"  ✅ 数据库创建成功: {db_name}")
                except asyncpg.DuplicateDatabaseError:
                    logger.info(f"  ℹ️ 数据库已存在: {db_name}")
                except Exception as e:
                    logger.warning(f"  ⚠️ 数据库创建失败: {db_name} - {e}")
            
            await conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库创建失败: {e}")
            return False

    def generate_connection_report(self, results: list) -> None:
        """生成连接测试报告"""
        logger.info("\n" + "="*60)
        logger.info("📊 PostgreSQL连接测试报告")
        logger.info("="*60)
        
        successful_configs = []
        
        for config, conn_success, test_success in results:
            status = "✅ 成功" if conn_success else "❌ 失败"
            test_status = "✅ 通过" if test_success else "❌ 失败"
            
            logger.info(f"\n🔸 {config['name']}")
            logger.info(f"  连接状态: {status}")
            if conn_success:
                logger.info(f"  功能测试: {test_status}")
                logger.info(f"  连接字符串: postgresql://{config['user']}:***@{config['host']}:{config['port']}/postgres")
                
                if test_success:
                    successful_configs.append(config)
        
        if successful_configs:
            logger.info(f"\n🎉 成功配置数量: {len(successful_configs)}")
            logger.info("📋 推荐使用配置:")
            
            for i, config in enumerate(successful_configs, 1):
                logger.info(f"  {i}. {config['name']}")
                logger.info(f"     DATABASE_URL=postgresql://{config['user']}:{config['password']}@{config['host']}:{config['port']}/astrbot_saas")
        else:
            logger.warning("\n⚠️ 未找到可用的PostgreSQL配置")
            logger.info("建议:")
            logger.info("  1. 运行 python install_postgresql_windows.py 进行安装")
            logger.info("  2. 检查PostgreSQL服务是否正在运行")
            logger.info("  3. 检查防火墙设置")
        
        logger.info("="*60)

    async def run_comprehensive_test(self) -> None:
        """运行综合测试"""
        logger.info("🚀 开始PostgreSQL综合测试...")
        logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 检查系统状态
        self.check_docker_postgres()
        self.check_windows_service()
        
        results = []
        
        # 测试所有配置
        for config in self.db_configs:
            logger.info(f"\n{'='*20} 测试 {config['name']} {'='*20}")
            
            # 测试连接
            conn_success = await self.test_connection(config)
            test_success = False
            
            if conn_success:
                # 运行功能测试
                test_success = await self.run_basic_tests(config)
                
                if test_success:
                    # 创建AstrBot数据库
                    await self.create_astrbot_databases(config)
            
            results.append((config, conn_success, test_success))
        
        # 生成报告
        self.generate_connection_report(results)

def main():
    """主函数"""
    print("🔧 PostgreSQL 快速连接和功能测试工具")
    print("="*50)
    
    tester = PostgreSQLTester()
    
    try:
        # 运行综合测试
        asyncio.run(tester.run_comprehensive_test())
        
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        logger.error(f"❌ 测试过程出错: {e}")

if __name__ == "__main__":
    main() 