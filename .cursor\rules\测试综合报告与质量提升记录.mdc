---
description: 在每次测试执行和相关代码修复完成后，根据最新的测试结果、修复内容和分析洞察，结构化地更新测试相关文档
globs:
alwaysApply: false
---


## Cursor 更新规则：AstrBot SaaS 测试综合报告与质量提升记录

**目标**: 在每次测试执行和相关代码修复完成后，根据最新的测试结果、修复内容和分析洞察，结构化地更新   [测试综合报告与质量提升记录.md](mdc:saas-platform/tests/测试综合报告与质量提升记录.md)文档。

**触发条件**: 当你（Cursor）完成了一轮测试执行（如E2E、单元测试）并可能实施了代码修复后。

**核心指令**:

请遵循以下步骤和要点，更新指定的Markdown文档：

1.  **文档定位**:
    *   确保你操作的是最新的文档。[测试综合报告与质量提升记录.md](mdc:saas-platform/tests/测试综合报告与质量提升记录.md)，
 2.  **顶部状态更新**:
    *   **最新更新**: 修改为当前日期和时间 (YYYY-MM-DD HH:MM:SS)。
    *   **文档状态**: 根据整体进展更新描述，例如：
        *   若有重大突破："🎉 **[新突破描述]，测试覆盖率提升至X%**"
        *   若稳定推进："⚙️ **持续集成与测试，[简要进展]**"
        *   若关键问题解决："✅ **关键问题[问题编号/名称]已解决，系统稳定性增强**"

3.  **Ⅰ. 测试状态总览**:
    *   更新表格中各“测试类型”的“状态”、“通过率”（例如 `100% (4/4 测试通过)`）和“关键成就”。
    *   如果某个测试类型（如集成测试）从“准备启动”变为“进行中”或“完成”，请相应更新。

4.  **Ⅱ. 重大里程碑与关键修复详情**:
    *   **新增修复**: 如果解决了新的问题（例如，你刚刚修复了 "Problem 12: API网关超时"）：
        *   在该部分下，按照现有格式（如 Problem 11, Logger一致性, Schema-Model一致性）添加一个新的子标题和详细描述。
        *   内容应包括：问题背景、错误分析（可选）、修复方案（可附带少量关键代码片段）、验证结果/影响。
        *   如果修复与已有的“高频问题模式”相关，请指出。
    *   **更新现有里程碑**: 如果对已列出的里程碑（如Logger一致性、Schema-Model一致性）有新的进展：
        *   更新其描述、完成度（如Logger修复率从66.7%提升到75%）。
        *   更新相关的统计数据或修复文件列表。

5.  **Ⅲ. 高频问题模式识别与解决成果**:
    *   **更新状态**: 如果某个已识别模式的解决进度有变化（如“Logger配置不一致”的修复率提升），请更新其状态描述。
    *   **新增模式**: 如果在测试和修复过程中识别出新的高频问题模式，请将其添加到列表中，并描述其风险和初步的解决思路或状态。

6.  **Ⅳ. 技术债务管理**:
    *   根据最新的修复情况，更新“已解决”、“进行中”、“计划中”表格的内容。
    *   将已完成修复的任务从“进行中”移至“已解决”。
    *   如果因为新的发现产生了新的技术债务，添加到“计划中”或“进行中”。

7.  **Ⅴ. 测试质量与成熟度**:
    *   **质量提升统计**: 更新“代码覆盖率”（如果可测量且有变化）、“Logger修复率”等具体指标。
    *   **测试方法论验证**: 如果本次修复过程再次验证或优化了Test-Role方法论的某个方面，可以简要提及。
    *   **测试成熟度评估**: 如果整体进展使得成熟度有显著变化，可以考虑更新。通常此部分变动较少。

8.  **Ⅵ. 测试价值体现**:
    *   一般情况下此部分不需要频繁更新，除非有特别显著的成果能更好地体现业务或技术价值。

9.  **Ⅶ. 关键成就总结**:
    *   如果本次更新包含重大成就（如E2E测试首次100%通过、某个长期存在的顽固bug被解决），可以考虑在列表中新增或更新相关条目。

10. **VIII. 后续工作重点与全局检查待办**:
    *   **更新优先级**: 根据已完成的工作和新出现的情况，调整P0、P1任务。
    *   **更新待办清单**: 如果全局检查清单中的项目有进展或新增项目，请更新。

11. **核心结论**:
    *   根据本次更新的整体情况，微调或重申核心结论，确保其能准确反映当前的项目测试状态和质量水平。

12. **页脚**:
    *   **最后更新**: 修改为与顶部一致的当前日期和时间。

**风格和格式要求**:

*   **保持结构**: 严格遵守文档现有的Markdown结构（标题层级、列表、表格、代码块等）。
*   **精确简洁**: 提供足够的信息，但避免不必要的冗余。
*   **数据驱动**: 尽可能使用具体数据（如通过率、修复百分比、文件数量）来支持描述。
*   **引用问题编号**: 如果修复与特定的Problem ID相关，请明确指出。
*   **代码片段**: 如需展示代码修复，选择最核心、最具代表性的片段，保持简短。
*   **语气专业**: 使用专业、客观的测试和技术术语。

**提供给Cursor的信息 (你需要告诉Cursor的)**:

当你要求Cursor执行此更新时，请提供以下信息：
*   本次测试的主要结果（例如，E2E测试结果，单元测试结果）。
*   已修复的关键问题描述（问题是什么，如何修复的，修复后的效果）。
*   任何新发现的问题模式或技术债务。
*   对后续工作优先级的调整建议（如果适用）。

**示例启动指令 (给Cursor)**:

"Cursor, 我刚刚完成了 [描述测试/修复活动，例如：对Logger一致性问题的又一轮修复，并重新运行了E2E和单元测试]。请根据这些最新进展，使用'AstrBot SaaS 测试综合报告更新规则'来更新我们的主测试报告文档。"

（然后提供上述“提供给Cursor的信息”）

---



这个规则应该能指导Cursor有效地更新您的文档。您可以根据实际使用情况进行微调。
