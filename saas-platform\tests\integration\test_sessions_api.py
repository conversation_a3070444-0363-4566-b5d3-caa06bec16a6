"""
会话API集成测试
测试会话API端点与服务层、数据库的完整集成
"""
import uuid

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.tenant import Tenant
from app.models.session import Session, SessionStatus
from app.core.security import create_access_token


@pytest.fixture
async def authenticated_client(
    client: AsyncClient, db_session: AsyncSession
) -> tuple[AsyncClient, Tenant]:
    """创建一个带有认证头信息的HTTPX客户端和关联的租户"""
    # 创建租户
    test_tenant = Tenant(
        name="Session Test Tenant", email=f"session-test-{uuid.uuid4().hex[:6]}@test.com"
    )
    db_session.add(test_tenant)
    await db_session.commit()
    await db_session.refresh(test_tenant)

    # 创建认证token
    token = create_access_token(
        subject=f"tenant:{test_tenant.id}",
        extra_data={"tenant_id": str(test_tenant.id)},
    )
    client.headers["Authorization"] = f"Bearer {token}"
    return client, test_tenant


class TestSessionAPIIntegration:
    """会a话API集成测试类"""

    @pytest.mark.asyncio
    async def test_create_session_and_get_it_back(
        self, authenticated_client: tuple[AsyncClient, Tenant], db_session: AsyncSession
    ):
        """测试创建新会话，然后能成功取回该会话"""
        client, tenant = authenticated_client
        
        # 1. 创建会话
        create_data = {
            "user_id": "integration-user-1",
            "platform": "web",
            "customer_name": "Integration Tester"
        }
        response = await client.post("/api/v1/sessions", json=create_data)

        # 验证创建响应
        assert response.status_code == 201
        response_data = response.json()["data"]
        session_id = response_data["id"]
        assert response_data["user_id"] == create_data["user_id"]
        assert response_data["platform"] == create_data["platform"]
        assert response_data["status"] == SessionStatus.WAITING.value
        assert response_data["extra_data"]["customer_name"] == create_data["customer_name"]

        # 2. 获取会话
        response = await client.get(f"/api/v1/sessions/{session_id}")
        
        # 验证获取响应
        assert response.status_code == 200
        get_response_data = response.json()["data"]
        assert get_response_data["id"] == session_id
        assert get_response_data["user_id"] == create_data["user_id"]

    @pytest.mark.asyncio
    async def test_update_session_status(
        self, authenticated_client: tuple[AsyncClient, Tenant], db_session: AsyncSession
    ):
        """测试更新会话状态"""
        client, tenant = authenticated_client
        
        # 先创建一个会话
        session = Session(tenant_id=tenant.id, user_id="user-for-status-update", platform="api")
        db_session.add(session)
        await db_session.commit()
        await db_session.refresh(session)
        
        assert session.status == SessionStatus.WAITING

        # 更新状态
        update_data = {"status": "active"}
        response = await client.patch(f"/api/v1/sessions/{session.id}/status", json=update_data)

        # 验证响应
        assert response.status_code == 200
        response_data = response.json()["data"]
        assert response_data["id"] == str(session.id)
        assert response_data["status"] == SessionStatus.ACTIVE.value
        
        # 验证数据库
        await db_session.refresh(session)
        assert session.status == SessionStatus.ACTIVE

    @pytest.mark.asyncio
    async def test_list_sessions_with_filter(
        self, authenticated_client: tuple[AsyncClient, Tenant], db_session: AsyncSession
    ):
        """测试带过滤条件的会话列表获取"""
        client, tenant = authenticated_client
        
        # 创建不同状态的会话
        db_session.add(Session(tenant_id=tenant.id, user_id="u1", platform="p1", status=SessionStatus.ACTIVE))
        db_session.add(Session(tenant_id=tenant.id, user_id="u2", platform="p2", status=SessionStatus.ACTIVE))
        db_session.add(Session(tenant_id=tenant.id, user_id="u3", platform="p1", status=SessionStatus.CLOSED))
        await db_session.commit()
        
        # 按状态过滤
        response = await client.get("/api/v1/sessions?status=active")
        assert response.status_code == 200
        response_data = response.json()["data"]
        assert len(response_data) == 2
        assert all(item["status"] == "active" for item in response_data)

        # 按平台过滤
        response = await client.get("/api/v1/sessions?platform=p1")
        assert response.status_code == 200
        response_data = response.json()["data"]
        assert len(response_data) == 2
        assert all(item["platform"] == "p1" for item in response_data) 