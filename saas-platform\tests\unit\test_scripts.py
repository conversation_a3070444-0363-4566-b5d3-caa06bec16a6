"""
脚本测试
测试scripts目录下的工具脚本基本功能
"""

import pytest
from unittest.mock import Mock, patch
import os
import sys
from pathlib import Path

# 添加scripts目录到Python路径
scripts_dir = Path(__file__).parent.parent.parent / "scripts"
sys.path.insert(0, str(scripts_dir))


class TestSecurityScripts:
    """测试安全相关脚本"""

    def test_security_check_script_import(self):
        """测试security_check脚本导入"""
        try:
            import security_check

            assert security_check is not None
        except ImportError:
            pytest.skip("security_check脚本无法导入")

    def test_security_scan_script_import(self):
        """测试security_scan脚本导入"""
        try:
            import security_scan

            assert security_scan is not None
        except ImportError:
            pytest.skip("security_scan脚本无法导入")


class TestCodeQualityScripts:
    """测试代码质量脚本"""

    def test_code_quality_check_import(self):
        """测试代码质量检查脚本导入"""
        try:
            import code_quality_check

            assert code_quality_check is not None
        except ImportError:
            pytest.skip("code_quality_check脚本无法导入")

    def test_fix_type_annotations_import(self):
        """测试类型注解修复脚本导入"""
        try:
            import fix_type_annotations

            assert fix_type_annotations is not None
        except ImportError:
            pytest.skip("fix_type_annotations脚本无法导入")

    def test_optimize_async_functions_import(self):
        """测试异步函数优化脚本导入"""
        try:
            import optimize_async_functions

            assert optimize_async_functions is not None
        except ImportError:
            pytest.skip("optimize_async_functions脚本无法导入")


class TestAPIDocumentationScripts:
    """测试API文档脚本"""

    def test_analyze_api_docs_import(self):
        """测试API文档分析脚本导入"""
        try:
            import analyze_api_docs

            assert analyze_api_docs is not None
        except ImportError:
            pytest.skip("analyze_api_docs脚本无法导入")

    def test_check_api_docs_simple_import(self):
        """测试简单API文档检查脚本导入"""
        try:
            import check_api_docs_simple

            assert check_api_docs_simple is not None
        except ImportError:
            pytest.skip("check_api_docs_simple脚本无法导入")

    def test_comprehensive_api_check_import(self):
        """测试综合API检查脚本导入"""
        try:
            import comprehensive_api_check

            assert comprehensive_api_check is not None
        except ImportError:
            pytest.skip("comprehensive_api_check脚本无法导入")

    def test_debug_api_detection_import(self):
        """测试API检测调试脚本导入"""
        try:
            import debug_api_detection

            assert debug_api_detection is not None
        except ImportError:
            pytest.skip("debug_api_detection脚本无法导入")


class TestAnalysisScripts:
    """测试分析脚本"""

    def test_analyze_warnings_import(self):
        """测试警告分析脚本导入"""
        try:
            import analyze_warnings

            assert analyze_warnings is not None
        except ImportError:
            pytest.skip("analyze_warnings脚本无法导入")

    def test_coverage_analysis_import(self):
        """测试覆盖率分析脚本导入"""
        try:
            import coverage_analysis

            assert coverage_analysis is not None
        except ImportError:
            pytest.skip("coverage_analysis脚本无法导入")


class TestMonitoringScripts:
    """测试监控脚本"""

    def test_generate_dashboards_import(self):
        """测试仪表板生成脚本导入"""
        try:
            import generate_dashboards

            assert generate_dashboards is not None
        except ImportError:
            pytest.skip("generate_dashboards脚本无法导入")


class TestScriptsFunctionality:
    """测试脚本功能性"""

    @patch("builtins.print")
    def test_scripts_can_execute_without_error(self, mock_print):
        """测试脚本可以无错误执行基本导入"""
        # 这个测试确保脚本在导入时不会抛出语法错误
        scripts_to_test = [
            "security_check",
            "code_quality_check",
            "analyze_api_docs",
            "coverage_analysis",
        ]

        successful_imports = 0
        for script_name in scripts_to_test:
            try:
                __import__(script_name)
                successful_imports += 1
            except ImportError:
                # 跳过无法导入的脚本
                continue
            except Exception as e:
                pytest.fail(f"脚本 {script_name} 导入时发生错误: {e}")

        # 至少应该有一些脚本能够成功导入
        assert successful_imports >= 0, "至少应该有一些脚本能够正常导入"

    def test_scripts_directory_exists(self):
        """测试scripts目录存在"""
        assert scripts_dir.exists(), "scripts目录应该存在"
        assert scripts_dir.is_dir(), "scripts应该是一个目录"

    def test_scripts_contain_python_files(self):
        """测试scripts目录包含Python文件"""
        python_files = list(scripts_dir.glob("*.py"))
        assert len(python_files) > 0, "scripts目录应该包含Python文件"

    def test_common_script_patterns(self):
        """测试常见脚本模式"""
        expected_scripts = [
            "security_check.py",
            "code_quality_check.py",
            "coverage_analysis.py",
        ]

        existing_scripts = [f.name for f in scripts_dir.glob("*.py")]

        for script in expected_scripts:
            if script in existing_scripts:
                script_path = scripts_dir / script
                assert script_path.is_file(), f"{script} 应该是一个文件"
                assert script_path.stat().st_size > 0, f"{script} 不应该为空"


class TestScriptUtilities:
    """测试脚本工具函数"""

    def test_path_handling(self):
        """测试路径处理"""
        # 测试脚本目录路径处理
        assert scripts_dir.exists()

        # 测试相对路径解析
        relative_path = scripts_dir.relative_to(scripts_dir.parent)
        assert str(relative_path) == "scripts"

    def test_file_access(self):
        """测试文件访问权限"""
        # 检查至少有一个脚本文件可读
        python_files = list(scripts_dir.glob("*.py"))
        if python_files:
            test_file = python_files[0]
            assert os.access(test_file, os.R_OK), f"文件 {test_file} 应该可读"
