# 📚 AstrBot SaaS Platform - 开发文档索引

## 🎯 文档导航总览

### 📖 **核心设计文档** (开发必读)

| 文档类型 | 文件路径 | 描述 | 用途 |
|----------|----------|------|------|
| **项目概述** | `../README.md` | 项目背景、目标、架构概览 | 🔄 贯穿全程 |
| **系统架构** | `架构说明.md` | 完整系统架构设计 | 🔄 贯穿全程 |
| **开发计划** | `后端开发计划.md` | 详细开发路线图和AI协同指导 | 🔄 贯穿全程 |
| **开发规范** | `开发规范.md` | 编码标准、工具配置 | 🔄 贯穿全程 |

### 🏗️ **技术架构文档**

| 专项 | 主要文档 | 关键内容 |
|------|----------|----------|
| **API设计** | `api_contracts/00_API_OVERVIEW.md` | 完整API规范和契约 |
| **SaaS平台API** | `api_contracts/saas_platform_api.yaml` | 平台核心API (OpenAPI 3.0) |
| **Webhook API** | `api_contracts/astrbot_webhook_api.yaml` | AstrBot实例回调接口 |
| **数据模型** | `api_contracts/models/common_models.yaml` | 统一数据模型定义 |
| **数据库设计** | `database_design/00_DB_DESIGN_OVERVIEW.md` | 数据库架构和ERD图 |
| **核心算法** | `algorithms/00_ALGORITHMS_OVERVIEW.md` | 会话分配、智能路由算法 |

### 💼 **业务功能文档**

| 文档名称 | 文件路径 | 主要内容 |
|----------|----------|----------|
| **需求规格** | `需求规格说明书.md` | 功能需求、非功能需求 |
| **功能说明** | `功能说明.md` | 详细业务功能描述 |
| **核心流程** | `运行逻辑（核心流程图）.md` | 业务流程可视化 |
| **技术栈** | `技术栈.md` | 技术选型、版本要求 |

### 🧪 **测试与质量**

| 文档名称 | 文件路径 | 主要内容 |
|----------|----------|----------|
| **测试策略** | `测试用例.md` | 测试策略、用例设计 |
| **质量记录** | `../saas-platform/tests/测试综合报告与质量提升记录.md` | 测试执行记录 |

### 🚀 **部署运维文档**

| 文档类型 | 文件路径 | 主要内容 |
|----------|----------|----------|
| **部署总览** | `../deployment/00_DEPLOYMENT_OVERVIEW.md` | 部署文档导航 |
| **运维指南** | `部署与运维.md` | 容器化、K8s配置 |
| **生产部署** | `../deployment/kubernetes/production-deployment.yaml` | 生产环境配置 |
| **监控配置** | `../deployment/monitoring/` | Prometheus、Grafana配置 |

## 🎯 分阶段文档使用指南

### 🛠️ **M0: 开发环境搭建**
**必读文档**:
- `后端开发计划.md` (M0章节)
- `技术栈.md`
- `开发规范.md`

### 🔐 **M1: 核心数据模型**
**必读文档**:
- `database_design/00_DB_DESIGN_OVERVIEW.md`
- `api_contracts/models/common_models.yaml`
- `开发规范.md` (多租户隔离规范)

### 🏢 **M2: 租户管理**
**必读文档**:
- `api_contracts/saas_platform_api.yaml`
- `功能说明.md` (租户管理功能)
- `测试用例.md` (租户管理测试)

### 💬 **M3: 会话与消息**
**必读文档**:
- `algorithms/session_management/session_allocation.md`
- `运行逻辑（核心流程图）.md`
- `api_contracts/saas_platform_api.yaml` (消息API)

### 🤖 **M4: LLM推理**
**必读文档**:
- `功能说明.md` (LLM推理部分)
- `algorithms/00_ALGORITHMS_OVERVIEW.md`
- `架构说明.md` (LLM编排架构)

### 🔗 **M5: AstrBot实例通信**
**必读文档**:
- `api_contracts/astrbot_webhook_api.yaml`
- `架构说明.md` (实例通信架构)
- `运行逻辑（核心流程图）.md`

### 🛡️ **M6-M8: 权限、分析、部署**
**必读文档**:
- `测试用例.md`
- `部署与运维.md`
- `../deployment/` (完整部署配置)

## 📝 AI开发使用指南

### 🎯 **任务开始前必读**
1. **核心项目规则**: `../../.cursor/rules/core_project_rules.md`
2. **编码规范**: `../../.cursor/rules/coding_conventions.md`
3. **多租户安全**: `../../.cursor/rules/multi_tenancy_guards.md`

### 📖 **标准AI提示词格式**
```markdown
## 任务: 在 {文件路径} 中实现 {具体功能}

## 参考文档:
- @docs/development/{相关设计文档}
- @docs/development/api_contracts/models/common_models.yaml

## 验收标准:
- [ ] 包含tenant_id过滤 (多租户隔离)
- [ ] 使用async/await (异步优先)
- [ ] 完整错误处理和日志
- [ ] 符合API契约规范
```

### 🔄 **迭代开发模式**
1. **明确小任务** (30分钟内完成)
2. **文档优先** (参考相关设计文档)
3. **AI生成代码** (遵循项目规范)
4. **即时验证** (运行测试确认)

---

## 🚨 关键注意事项

### 🔐 **多租户隔离 (CRITICAL)**
- 所有数据模型必须包含`tenant_id`字段
- 所有数据查询必须包含租户过滤条件
- API端点必须验证租户权限

### 📊 **文档一致性**
- 统一数据模型: `api_contracts/models/common_models.yaml`
- API接口定义: `api_contracts/saas_platform_api.yaml`
- 数据库表结构: `database_design/erd_diagram.md`

---

**文档版本**: v2.0 (已补充Webhook API和部署文档)  
**最后更新**: 2025-06-20  
**维护原则**: 文档与代码同步更新，确保AI开发指导的准确性
