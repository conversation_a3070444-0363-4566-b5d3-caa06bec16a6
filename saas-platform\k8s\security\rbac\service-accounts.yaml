# k8s/security/rbac/service-accounts.yaml
# 遵循最小权限原则，为不同服务创建独立的ServiceAccount
# 禁用token自动挂载，提高安全性

apiVersion: v1
kind: ServiceAccount
metadata:
  name: astrbot-saas-api
  namespace: astrbot-saas
  labels:
    component: api-server
automountServiceAccountToken: false
---
apiVersion: v1
kind: ServiceAccount  
metadata:
  name: astrbot-saas-worker
  namespace: astrbot-saas
  labels:
    component: worker
automountServiceAccountToken: false
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: astrbot-saas-cronjob
  namespace: astrbot-saas
  labels:
    component: cronjob
automountServiceAccountToken: false 