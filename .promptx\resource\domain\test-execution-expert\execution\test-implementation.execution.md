<execution>
  <constraint>
    ## 客观技术限制
    - **环境资源约束**：测试环境的计算资源、存储空间、网络带宽限制
    - **时间窗口限制**：CI/CD流水线的时间要求，发布窗口的时间压力
    - **工具兼容性**：现有测试工具栈的技术约束和版本兼容性
    - **数据安全要求**：测试数据的隐私保护和合规性要求
    - **并发执行限制**：测试资源的并发访问限制和竞争条件
  </constraint>

  <rule>
    ## 强制性执行规则
    - **测试优先级**：P0级测试失败必须阻断部署，P1级问题必须当前迭代修复
    - **环境隔离**：测试环境必须与生产环境隔离，避免相互影响
    - **数据清理**：每次测试执行后必须清理测试数据，确保测试独立性
    - **结果记录**：所有测试执行结果必须完整记录，便于问题追踪
    - **安全扫描**：代码变更必须通过安全扫描，高危漏洞零容忍
    - **覆盖率门槛**：核心模块测试覆盖率不得低于80%，整体不低于75%
  </rule>

  <guideline>
    ## 执行指导原则
    - **渐进式验证**：从快速反馈的单元测试开始，逐步扩展到集成和E2E测试
    - **智能化优先**：优先使用智能测试选择，减少不必要的测试执行
    - **并行化执行**：在资源允许的情况下，最大化测试并行执行
    - **快速反馈**：测试结果应在5分钟内反馈给开发者
    - **问题前置**：通过静态分析和代码检查，在测试前发现潜在问题
    - **持续优化**：基于测试执行数据，持续优化测试策略和工具配置
  </guideline>

  <process>
    ## 测试实施执行流程

    ### Phase 1: 测试环境准备 (5-10分钟)
    ```bash
    # 1.1 环境健康检查
    python scripts/health_check.py --env=test
    
    # 1.2 数据库状态验证
    alembic current
    alembic check
    
    # 1.3 依赖服务检查
    docker-compose ps
    curl -f http://localhost:5432 || echo "Database not ready"
    
    # 1.4 测试数据准备
    python scripts/setup_test_data.py --clean --seed
    ```

    ### Phase 2: 智能测试选择 (1-2分钟)
    ```bash
    # 2.1 获取代码变更
    git diff --name-only HEAD~1 HEAD
    
    # 2.2 智能测试选择
    python tests/helpers/smart_test_selector.py --base=main --dry-run
    
    # 2.3 生成执行计划
    python tests/helpers/smart_test_selector.py --base=main > test_plan.txt
    ```

    ### Phase 3: 分层测试执行 (10-30分钟)
    
    #### 3.1 快速验证层 (2-5分钟)
    ```bash
    # 代码风格和静态检查
    python scripts/run_quality_checks.py --quick
    
    # 核心单元测试
    python -m pytest tests/unit/ -x --tb=short --durations=10
    ```
    
    #### 3.2 功能验证层 (5-15分钟)  
    ```bash
    # 集成测试
    python -m pytest tests/integration/ -v --tb=short
    
    # API契约测试
    python -m pytest tests/contract/ -v
    ```
    
    #### 3.3 系统验证层 (10-20分钟)
    ```bash
    # 端到端测试
    python -m pytest tests/e2e/ -v --tb=short
    
    # 性能基准测试
    python tests/performance/baseline_test.py
    ```

    ### Phase 4: 质量分析和报告 (2-5分钟)
    ```bash
    # 4.1 生成覆盖率报告
    python -m pytest --cov=app --cov-report=html --cov-report=json tests/
    
    # 4.2 生成质量报告
    python scripts/run_quality_checks.py --report
    
    # 4.3 趋势分析
    python scripts/quality_trend_analysis.py
    ```

    ### Phase 5: 结果处理和通知 (1-2分钟)
    ```bash
    # 5.1 结果汇总
    python scripts/test_result_aggregator.py
    
    # 5.2 问题分类
    python scripts/issue_classifier.py
    
    # 5.3 通知发送
    python scripts/notification_sender.py --channel=slack,email
    ```

    ## 特殊场景处理流程

    ### 紧急修复验证流程
    ```bash
    # 1. 快速安全检查
    bandit -r app/ -f json | python scripts/security_analyzer.py
    
    # 2. 相关功能测试
    python tests/helpers/smart_test_selector.py --files="修复文件列表"
    
    # 3. 回归风险评估
    python scripts/regression_risk_analyzer.py --change="修复内容"
    
    # 4. 最小验证集执行
    python -m pytest 最小测试集 --tb=short -x
    ```

    ### 性能回归检测流程
    ```bash
    # 1. 基准数据获取
    python tests/performance/get_baseline.py
    
    # 2. 当前性能测试
    locust -f tests/performance/load_test.py --headless -u 50 -r 10 -t 60s
    
    # 3. 性能对比分析
    python scripts/performance_comparator.py
    
    # 4. 回归风险评估
    python scripts/performance_risk_assessor.py
    ```

    ### 安全漏洞响应流程
    ```bash
    # 1. 漏洞扫描
    safety check --json | python scripts/vulnerability_analyzer.py
    
    # 2. 影响范围分析
    python scripts/security_impact_analyzer.py
    
    # 3. 修复验证测试
    python -m pytest tests/security/ -v
    
    # 4. 合规性检查
    python scripts/compliance_checker.py
    ```
  </process>

  <criteria>
    ## 执行质量评价标准

    ### 执行效率指标
    - ✅ 总执行时间 ≤ 30分钟（完整测试套件）
    - ✅ 快速反馈时间 ≤ 5分钟（核心测试）
    - ✅ 智能选择准确率 ≥ 90%（相关测试识别）
    - ✅ 并行化效率 ≥ 70%（资源利用率）

    ### 质量保证指标
    - ✅ 测试通过率 ≥ 95%（稳定性要求）
    - ✅ 覆盖率达标率 = 100%（门槛合规）
    - ✅ 安全漏洞检出率 = 100%（零遗漏）
    - ✅ 性能回归检出率 ≥ 95%（性能保护）

    ### 问题响应指标
    - ✅ P0问题响应时间 ≤ 15分钟
    - ✅ P1问题定位时间 ≤ 2小时
    - ✅ 问题修复验证时间 ≤ 30分钟
    - ✅ 根因分析完成率 = 100%

    ### 流程合规指标
    - ✅ 测试环境隔离率 = 100%
    - ✅ 数据清理执行率 = 100%
    - ✅ 结果记录完整率 = 100%
    - ✅ 通知及时性 ≥ 95%

    ### 持续改进指标
    - ✅ 测试执行时间优化 ≥ 10%/月
    - ✅ 误报率下降 ≥ 5%/月
    - ✅ 工具采纳率 ≥ 80%
    - ✅ 团队满意度 ≥ 85%
  </criteria>
</execution> 