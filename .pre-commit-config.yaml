# 🛠️ AstrBot SaaS Pre-commit 配置
# 确保代码质量和AI协同开发规范

default_install_hook_types: [pre-commit, prepare-commit-msg]
ci:
  autofix_commit_msg: ":balloon: auto fixes by pre-commit hooks"
  autofix_prs: true
  autoupdate_branch: master
  autoupdate_schedule: weekly
  autoupdate_commit_msg: ":balloon: pre-commit autoupdate"
repos:
  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88]

  # Import排序
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: [--profile=black]

  # 代码质量检查
  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.1.9
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]

  # 文件长度检查 (AI协同开发规范)
  - repo: local
    hooks:
      - id: check-file-length
        name: 检查Python文件行数 (AI协同规范)
        entry: python scripts/check_file_length.py
        language: system
        files: \.py$
        args: [saas-platform, --max-lines=500, --ci]
        pass_filenames: false

  # 基础文件检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
        args: [--maxkb=1000]
      - id: check-json

  # Python类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
        args: [--ignore-missing-imports]
        exclude: ^(tests/|migrations/)
