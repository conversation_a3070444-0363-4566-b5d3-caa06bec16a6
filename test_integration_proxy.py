#!/usr/bin/env python3
"""
AstrBot SaaS集成代理测试工具
DevOps执行专家 - 验证集成功能的完整性和正确性
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any
import httpx
import websockets

class IntegrationTester:
    """集成测试器"""
    
    def __init__(self):
        self.proxy_url = "http://localhost:9000"
        self.saas_url = "http://localhost:8000"
        self.astrbot_url = "http://localhost:6185"
        self.http_client = httpx.AsyncClient(timeout=10.0)
        
    def print_section(self, title: str):
        """打印测试章节"""
        print(f"\n{'='*60}")
        print(f"🧪 {title}")
        print(f"{'='*60}")
    
    def print_result(self, test_name: str, success: bool, details: str = ""):
        """打印测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
    
    async def test_proxy_health(self):
        """测试代理服务健康状态"""
        self.print_section("代理服务健康检查")
        
        try:
            response = await self.http_client.get(f"{self.proxy_url}/health")
            success = response.status_code == 200
            
            if success:
                health_data = response.json()
                saas_status = health_data.get("services", {}).get("saas_platform", "unknown")
                astrbot_status = health_data.get("services", {}).get("astrbot", "unknown")
                
                details = f"SaaS: {saas_status}, AstrBot: {astrbot_status}"
                self.print_result("代理服务健康检查", success, details)
                
                return health_data
            else:
                self.print_result("代理服务健康检查", False, f"状态码: {response.status_code}")
                return None
                
        except Exception as e:
            self.print_result("代理服务健康检查", False, f"连接错误: {e}")
            return None
    
    async def test_message_proxy(self):
        """测试消息代理功能"""
        self.print_section("消息代理功能测试")
        
        test_messages = [
            {"tenant_id": "test-tenant-1", "message": "你好，这是测试消息1", "user_id": "user1"},
            {"tenant_id": "test-tenant-2", "message": "Hello, this is test message 2", "user_id": "user2"},
            {"tenant_id": "default", "message": "测试多租户消息路由", "session_id": "session123"}
        ]
        
        results = []
        for i, test_msg in enumerate(test_messages, 1):
            try:
                response = await self.http_client.post(
                    f"{self.proxy_url}/api/v1/proxy/message",
                    json=test_msg,
                    headers={"X-Tenant-ID": test_msg["tenant_id"]}
                )
                
                success = response.status_code == 200
                if success:
                    data = response.json()
                    bot_response = data.get("data", {}).get("response", "")
                    details = f"租户: {test_msg['tenant_id']}, 响应: {bot_response[:50]}..."
                else:
                    details = f"状态码: {response.status_code}"
                
                self.print_result(f"消息代理测试 {i}", success, details)
                results.append(success)
                
            except Exception as e:
                self.print_result(f"消息代理测试 {i}", False, f"错误: {e}")
                results.append(False)
        
        return results
    
    async def test_config_proxy(self):
        """测试配置代理功能"""
        self.print_section("配置代理功能测试")
        
        test_configs = [
            {
                "tenant_id": "test-tenant-1",
                "config": {
                    "llm_provider": "openai",
                    "max_tokens": 1000,
                    "temperature": 0.7
                }
            },
            {
                "tenant_id": "test-tenant-2", 
                "config": {
                    "webhook_url": "https://example.com/webhook",
                    "auto_reply": True
                }
            }
        ]
        
        results = []
        for i, test_config in enumerate(test_configs, 1):
            try:
                response = await self.http_client.post(
                    f"{self.proxy_url}/api/v1/proxy/config",
                    json=test_config["config"],
                    headers={"X-Tenant-ID": test_config["tenant_id"]}
                )
                
                success = response.status_code == 200
                if success:
                    data = response.json()
                    config_keys = data.get("data", {}).get("config_keys", [])
                    details = f"租户: {test_config['tenant_id']}, 配置项: {len(config_keys)}"
                else:
                    details = f"状态码: {response.status_code}"
                
                self.print_result(f"配置代理测试 {i}", success, details)
                results.append(success)
                
            except Exception as e:
                self.print_result(f"配置代理测试 {i}", False, f"错误: {e}")
                results.append(False)
        
        return results
    
    async def test_proxy_status(self):
        """测试代理状态查询"""
        self.print_section("代理状态查询测试")
        
        try:
            response = await self.http_client.get(f"{self.proxy_url}/api/v1/proxy/status")
            success = response.status_code == 200
            
            if success:
                status_data = response.json()
                active_tenants = status_data.get("active_tenants", 0)
                active_sessions = status_data.get("active_sessions", 0)
                details = f"活跃租户: {active_tenants}, 活跃会话: {active_sessions}"
                
                self.print_result("代理状态查询", success, details)
                return status_data
            else:
                self.print_result("代理状态查询", False, f"状态码: {response.status_code}")
                return None
                
        except Exception as e:
            self.print_result("代理状态查询", False, f"错误: {e}")
            return None
    
    async def test_websocket_proxy(self):
        """测试WebSocket代理功能"""
        self.print_section("WebSocket代理功能测试")
        
        try:
            ws_url = f"ws://localhost:9000/ws/proxy/test-tenant-ws"
            
            async with websockets.connect(ws_url, timeout=5) as websocket:
                # 发送测试消息
                test_message = {
                    "type": "chat",
                    "content": "WebSocket测试消息",
                    "session_id": "ws-session-123"
                }
                
                await websocket.send(json.dumps(test_message))
                
                # 接收响应
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                response_data = json.loads(response)
                
                success = response_data.get("type") == "response"
                details = f"响应类型: {response_data.get('type')}, 内容: {response_data.get('content', '')[:50]}..."
                
                self.print_result("WebSocket消息测试", success, details)
                
                # 测试心跳
                ping_message = {"type": "ping"}
                await websocket.send(json.dumps(ping_message))
                
                pong_response = await asyncio.wait_for(websocket.recv(), timeout=3)
                pong_data = json.loads(pong_response)
                
                ping_success = pong_data.get("type") == "pong"
                self.print_result("WebSocket心跳测试", ping_success, f"响应: {pong_data.get('type')}")
                
                return success and ping_success
                
        except Exception as e:
            self.print_result("WebSocket代理测试", False, f"错误: {e}")
            return False
    
    async def test_service_integration(self):
        """测试服务间集成"""
        self.print_section("服务间集成测试")
        
        # 测试SaaS平台直接访问
        try:
            saas_response = await self.http_client.get(f"{self.saas_url}/api/v1/health")
            saas_ok = saas_response.status_code == 200
            self.print_result("SaaS平台直接访问", saas_ok, f"状态码: {saas_response.status_code}")
        except Exception as e:
            self.print_result("SaaS平台直接访问", False, f"错误: {e}")
            saas_ok = False
        
        # 测试AstrBot直接访问
        try:
            astrbot_response = await self.http_client.get(self.astrbot_url)
            astrbot_ok = astrbot_response.status_code in [200, 404]
            self.print_result("AstrBot直接访问", astrbot_ok, f"状态码: {astrbot_response.status_code}")
        except Exception as e:
            self.print_result("AstrBot直接访问", False, f"错误: {e}")
            astrbot_ok = False
        
        # 测试代理服务访问
        try:
            proxy_response = await self.http_client.get(self.proxy_url)
            proxy_ok = proxy_response.status_code == 200
            self.print_result("代理服务直接访问", proxy_ok, f"状态码: {proxy_response.status_code}")
        except Exception as e:
            self.print_result("代理服务直接访问", False, f"错误: {e}")
            proxy_ok = False
        
        return saas_ok and astrbot_ok and proxy_ok
    
    async def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 AstrBot SaaS集成代理综合测试")
        print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 测试目标:")
        print(f"   - 代理服务: {self.proxy_url}")
        print(f"   - SaaS平台: {self.saas_url}")
        print(f"   - AstrBot: {self.astrbot_url}")
        
        # 等待服务稳定
        print("\n⏳ 等待服务稳定...")
        await asyncio.sleep(2)
        
        # 执行测试
        test_results = []
        
        # 1. 健康检查
        health_result = await self.test_proxy_health()
        test_results.append(health_result is not None)
        
        # 2. 服务集成测试
        integration_result = await self.test_service_integration()
        test_results.append(integration_result)
        
        # 3. 消息代理测试
        message_results = await self.test_message_proxy()
        test_results.extend(message_results)
        
        # 4. 配置代理测试
        config_results = await self.test_config_proxy()
        test_results.extend(config_results)
        
        # 5. 状态查询测试
        status_result = await self.test_proxy_status()
        test_results.append(status_result is not None)
        
        # 6. WebSocket测试
        ws_result = await self.test_websocket_proxy()
        test_results.append(ws_result)
        
        # 测试总结
        self.print_section("测试总结")
        passed = sum(test_results)
        total = len(test_results)
        success_rate = (passed / total) * 100 if total > 0 else 0
        
        print(f"📊 测试结果: {passed}/{total} 通过")
        print(f"📈 成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 集成测试通过！代理服务运行正常")
            return True
        else:
            print("⚠️ 集成测试部分失败，需要进一步检查")
            return False
    
    async def cleanup(self):
        """清理资源"""
        await self.http_client.aclose()

async def main():
    """主函数"""
    tester = IntegrationTester()
    
    try:
        success = await tester.run_comprehensive_test()
        return 0 if success else 1
    finally:
        await tester.cleanup()

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
