#!/usr/bin/env python3
"""
AstrBot SaaS - Grafana仪表板生成脚本
=====================================

从YAML配置文件自动生成Grafana仪表板JSON，避免手工维护复杂的JSON配置。

功能:
- 读取YAML配置模板
- 生成标准化的Grafana仪表板JSON
- 支持多种面板类型
- 自动应用主题和样式

使用方法:
    python scripts/generate_dashboards.py
"""

import json
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional
import sys

# 常量定义
GRAFANA_SCHEMA_VERSION = 37
GRAFANA_PLUGIN_VERSION = "10.0.0"
GRID_MAX_WIDTH = 24
DEFAULT_PANEL_WIDTH = 12
DEFAULT_PANEL_HEIGHT = 8
STAT_PANEL_WIDTH = 6

# 支持的面板类型
SUPPORTED_PANEL_TYPES = {"stat", "timeseries"}

# 路径配置
SCRIPT_DIR = Path(__file__).parent
PROJECT_ROOT = SCRIPT_DIR.parent
CONFIG_FILE = PROJECT_ROOT / "monitoring/grafana/dashboards-config.yaml"
OUTPUT_DIR = PROJECT_ROOT / "monitoring/grafana/dashboards"


class DashboardGenerationError(Exception):
    """仪表板生成相关的异常"""

    pass


class ConfigValidationError(Exception):
    """配置验证相关的异常"""

    pass


class GrafanaDashboardGenerator:
    """Grafana仪表板生成器"""

    def __init__(self):
        """初始化生成器"""
        self.config: Optional[Dict[str, Any]] = None
        self.panel_id_counter: int = 1

        # 确保输出目录存在
        OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    def load_config(self) -> Dict[str, Any]:
        """加载YAML配置文件"""
        try:
            with open(CONFIG_FILE, "r", encoding="utf-8") as f:
                self.config = yaml.safe_load(f)
            print(f"✅ 配置文件加载成功: {CONFIG_FILE}")

            # 验证配置
            self._validate_config()
            return self.config

        except FileNotFoundError as e:
            error_msg = f"❌ 配置文件不存在: {CONFIG_FILE}"
            print(error_msg)
            raise ConfigValidationError(error_msg) from e

        except yaml.YAMLError as e:
            error_msg = f"❌ YAML解析错误: {e}"
            print(error_msg)
            raise ConfigValidationError(error_msg) from e

    def _validate_config(self) -> None:
        """验证配置文件内容"""
        if not self.config:
            raise ConfigValidationError("配置为空")

        if "dashboard_templates" not in self.config:
            raise ConfigValidationError("缺少 dashboard_templates 配置")

        dashboard_templates = self.config["dashboard_templates"]
        if not isinstance(dashboard_templates, dict):
            raise ConfigValidationError("dashboard_templates 必须是字典格式")

        if not dashboard_templates:
            raise ConfigValidationError("dashboard_templates 不能为空")

        # 验证每个仪表板配置
        for name, config in dashboard_templates.items():
            self._validate_dashboard_config(name, config)

    def _validate_dashboard_config(self, name: str, config: Dict[str, Any]) -> None:
        """验证单个仪表板配置"""
        if not isinstance(config, dict):
            raise ConfigValidationError(f"仪表板 {name} 配置必须是字典格式")

        # 验证必要字段
        required_fields = ["title", "panels"]
        for field in required_fields:
            if field not in config:
                raise ConfigValidationError(f"仪表板 {name} 缺少必要字段: {field}")

        # 验证面板配置
        panels = config.get("panels", [])
        if not isinstance(panels, list):
            raise ConfigValidationError(f"仪表板 {name} 的 panels 必须是列表格式")

        for i, panel in enumerate(panels):
            self._validate_panel_config(name, i, panel)

    def _validate_panel_config(
        self, dashboard_name: str, panel_index: int, panel: Dict[str, Any]
    ) -> None:
        """验证面板配置"""
        if not isinstance(panel, dict):
            raise ConfigValidationError(
                f"仪表板 {dashboard_name} 面板 {panel_index} 必须是字典格式"
            )

        # 验证面板类型
        panel_type = panel.get("type", "timeseries")
        if panel_type not in SUPPORTED_PANEL_TYPES:
            raise ConfigValidationError(
                f"仪表板 {dashboard_name} 面板 {panel_index} 类型 {panel_type} 不支持。"
                f"支持的类型: {', '.join(SUPPORTED_PANEL_TYPES)}"
            )

        # 验证标题
        if "title" not in panel:
            raise ConfigValidationError(
                f"仪表板 {dashboard_name} 面板 {panel_index} 缺少标题"
            )

        # 验证指标配置
        has_metric = "metric" in panel
        has_metrics = "metrics" in panel

        if not has_metric and not has_metrics:
            raise ConfigValidationError(
                f"仪表板 {dashboard_name} 面板 {panel_index} 必须包含 metric 或 metrics 配置"
            )

        if has_metric and has_metrics:
            raise ConfigValidationError(
                f"仪表板 {dashboard_name} 面板 {panel_index} 不能同时包含 metric 和 metrics 配置"
            )

    def generate_panel(
        self, panel_config: Dict[str, Any], position: Dict[str, int]
    ) -> Dict[str, Any]:
        """生成面板配置"""
        panel_id = self.panel_id_counter
        self.panel_id_counter += 1

        panel_type = panel_config.get("type", "timeseries")
        title = panel_config.get("title", f"Panel {panel_id}")

        # 基础面板结构
        panel = {
            "datasource": {"type": "prometheus", "uid": "prometheus"},
            "fieldConfig": {
                "defaults": {
                    "color": {"mode": "palette-classic"},
                    "mappings": [],
                    "thresholds": {
                        "mode": "absolute",
                        "steps": [{"color": "green", "value": None}],
                    },
                },
                "overrides": [],
            },
            "gridPos": position,
            "id": panel_id,
            "options": {},
            "pluginVersion": GRAFANA_PLUGIN_VERSION,
            "targets": [],
            "title": title,
            "type": panel_type,
        }

        # 根据面板类型配置
        if panel_type == "stat":
            panel["options"] = {
                "colorMode": "background",
                "graphMode": "area",
                "justifyMode": "auto",
                "orientation": "auto",
                "reduceOptions": {
                    "calcs": ["lastNotNull"],
                    "fields": "",
                    "values": False,
                },
                "textMode": "auto",
            }

            # 配置阈值
            thresholds = panel_config.get("thresholds")
            if thresholds and isinstance(thresholds, list) and len(thresholds) >= 2:
                panel["fieldConfig"]["defaults"]["thresholds"]["steps"].extend(
                    [
                        {"color": "yellow", "value": thresholds[0]},
                        {"color": "red", "value": thresholds[1]},
                    ]
                )

        elif panel_type == "timeseries":
            panel["fieldConfig"]["defaults"]["custom"] = {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {"legend": False, "tooltip": False, "vis": False},
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {"type": "linear"},
                "showPoints": "never",
                "spanNulls": False,
                "stacking": {"group": "A", "mode": "none"},
                "thresholdsStyle": {"mode": "off"},
            }

            panel["options"] = {
                "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"},
                "tooltip": {"mode": "single", "sort": "none"},
            }

            # 设置单位
            unit = panel_config.get("unit")
            if unit:
                panel["fieldConfig"]["defaults"]["unit"] = unit

        # 配置查询目标
        targets = self._generate_targets(panel_config, title)
        panel["targets"] = targets

        return panel

    def _generate_targets(
        self, panel_config: Dict[str, Any], title: str
    ) -> List[Dict[str, Any]]:
        """生成查询目标"""
        targets = []
        ref_id_start = ord("A")

        # 单个指标
        metric = panel_config.get("metric")
        if metric:
            targets.append(
                {
                    "datasource": {"type": "prometheus", "uid": "prometheus"},
                    "expr": metric,
                    "legendFormat": title,
                    "refId": chr(ref_id_start),
                }
            )

        # 多个指标
        metrics = panel_config.get("metrics")
        if metrics and isinstance(metrics, list):
            for i, metric in enumerate(metrics):
                targets.append(
                    {
                        "datasource": {"type": "prometheus", "uid": "prometheus"},
                        "expr": metric,
                        "legendFormat": f"指标 {i+1}",
                        "refId": chr(ref_id_start + i),
                    }
                )

        return targets

    def generate_dashboard(
        self, dashboard_name: str, dashboard_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成完整的仪表板"""
        self.panel_id_counter = 1  # 重置面板ID

        # 基础仪表板结构
        dashboard = {
            "annotations": {
                "list": [
                    {
                        "builtIn": 1,
                        "datasource": "prometheus",
                        "enable": True,
                        "hide": True,
                        "iconColor": "rgba(0, 211, 255, 1)",
                        "name": "Annotations & Alerts",
                        "type": "dashboard",
                    }
                ]
            },
            "description": f"AstrBot SaaS - {dashboard_config.get('title', dashboard_name)}",
            "editable": True,
            "fiscalYearStartMonth": 0,
            "graphTooltip": 0,
            "id": None,
            "links": [],
            "liveNow": False,
            "panels": [],
            "refresh": dashboard_config.get("refresh", "30s"),
            "schemaVersion": GRAFANA_SCHEMA_VERSION,
            "style": "dark",
            "tags": dashboard_config.get("tags", ["astrbot", "saas"]),
            "templating": {"list": []},
            "time": {
                "from": f"now-{dashboard_config.get('time_range', '1h')}",
                "to": "now",
            },
            "timepicker": {},
            "timezone": "",
            "title": dashboard_config.get("title", dashboard_name),
            "uid": f"astrbot-{dashboard_name}",
            "version": 1,
        }

        # 生成面板
        panels = dashboard_config.get("panels", [])
        grid_x = 0
        grid_y = 0

        for panel_config in panels:
            # 计算面板位置（简单的网格布局）
            panel_width = (
                STAT_PANEL_WIDTH
                if panel_config.get("type") == "stat"
                else DEFAULT_PANEL_WIDTH
            )
            panel_height = DEFAULT_PANEL_HEIGHT

            position = {"h": panel_height, "w": panel_width, "x": grid_x, "y": grid_y}

            # 生成面板
            panel = self.generate_panel(panel_config, position)
            dashboard["panels"].append(panel)

            # 更新网格位置
            grid_x += panel_width
            if grid_x >= GRID_MAX_WIDTH:  # 一行最多24个单位
                grid_x = 0
                grid_y += panel_height

        return dashboard

    def generate_all_dashboards(self) -> None:
        """生成所有仪表板"""
        if not self.config:
            self.load_config()

        dashboard_templates = self.config.get("dashboard_templates", {})

        print(f"🚀 开始生成 {len(dashboard_templates)} 个仪表板...")

        for dashboard_name, dashboard_config in dashboard_templates.items():
            try:
                # 生成仪表板JSON
                dashboard_json = self.generate_dashboard(
                    dashboard_name, dashboard_config
                )

                # 保存到文件
                output_file = OUTPUT_DIR / f"{dashboard_name}.json"
                with open(output_file, "w", encoding="utf-8") as f:
                    json.dump(dashboard_json, f, indent=2, ensure_ascii=False)

                print(f"✅ 仪表板生成成功: {output_file}")

            except (IOError, OSError) as e:
                print(f"❌ 文件操作失败 {dashboard_name}: {e}")
                raise DashboardGenerationError(
                    f"生成仪表板 {dashboard_name} 时文件操作失败"
                ) from e
            except Exception as e:
                print(f"❌ 生成仪表板 {dashboard_name} 失败: {e}")
                raise DashboardGenerationError(
                    f"生成仪表板 {dashboard_name} 失败"
                ) from e

        print("🎉 所有仪表板生成完成!")

    def validate_dashboards(self) -> None:
        """验证生成的仪表板JSON格式"""
        dashboard_files = list(OUTPUT_DIR.glob("*.json"))

        print(f"🔍 验证 {len(dashboard_files)} 个仪表板文件...")

        validation_errors = []

        for dashboard_file in dashboard_files:
            try:
                with open(dashboard_file, "r", encoding="utf-8") as f:
                    json.load(f)  # 尝试解析JSON
                print(f"✅ {dashboard_file.name} - JSON格式正确")
            except json.JSONDecodeError as e:
                error_msg = f"❌ {dashboard_file.name} - JSON格式错误: {e}"
                print(error_msg)
                validation_errors.append(error_msg)
            except (IOError, OSError) as e:
                error_msg = f"❌ {dashboard_file.name} - 文件读取错误: {e}"
                print(error_msg)
                validation_errors.append(error_msg)

        if validation_errors:
            raise DashboardGenerationError(
                f"验证失败，发现 {len(validation_errors)} 个错误"
            )

        print("🎯 仪表板验证完成!")


def main() -> int:
    """主函数"""
    print("🎨 AstrBot SaaS - Grafana仪表板生成工具")
    print("=" * 50)

    generator = GrafanaDashboardGenerator()

    try:
        # 加载配置
        generator.load_config()

        # 生成所有仪表板
        generator.generate_all_dashboards()

        # 验证生成的文件
        generator.validate_dashboards()

        print("\n📋 生成总结:")
        print(f"📁 输出目录: {OUTPUT_DIR}")
        print("💡 提示: 可以将生成的JSON文件导入到Grafana中使用")

        return 0

    except ConfigValidationError as e:
        print(f"❌ 配置验证失败: {e}")
        return 2
    except DashboardGenerationError as e:
        print(f"❌ 仪表板生成失败: {e}")
        return 3
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
