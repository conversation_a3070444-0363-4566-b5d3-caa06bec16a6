#!/usr/bin/env python3
"""
文档优化工具
自动执行文档合并、清理和重组，减少冗余内容
"""

import os
import shutil
from pathlib import Path
from typing import Dict, List
from datetime import datetime
import json


class DocumentOptimizer:
    """文档优化器"""
    
    def __init__(self, root_path: str):
        self.root_path = Path(root_path)
        self.backup_dir = self.root_path / "docs_backup"
        self.optimized_dir = self.root_path / "docs"
        self.optimization_log = []
        
    def create_backup(self) -> None:
        """创建文档备份"""
        print("💾 创建文档备份...")
        
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        
        self.backup_dir.mkdir(exist_ok=True)
        
        # 备份所有markdown文件
        md_files = list(self.root_path.rglob("*.md"))
        backed_up = 0
        
        for md_file in md_files:
            if "docs_backup" not in str(md_file):
                try:
                    relative_path = md_file.relative_to(self.root_path)
                    backup_path = self.backup_dir / relative_path
                    backup_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(md_file, backup_path)
                    backed_up += 1
                except Exception as e:
                    print(f"⚠️ 备份失败: {md_file} - {e}")
        
        print(f"✅ 已备份 {backed_up} 个文档到 {self.backup_dir}")
        self.log_operation("backup", f"备份了 {backed_up} 个文档")
    
    def setup_optimized_structure(self) -> None:
        """建立优化后的文档结构"""
        print("🏗️ 建立优化后的文档结构...")
        
        # 创建新的文档目录结构
        structure = {
            "modules": "模块文档",
            "api": "API文档", 
            "guides": "用户指南",
            "reports": "重要报告",
            "security": "安全文档",
            "testing": "测试文档",
            "deployment": "部署文档"
        }
        
        for folder, description in structure.items():
            folder_path = self.optimized_dir / folder
            folder_path.mkdir(parents=True, exist_ok=True)
            
            # 创建README说明文件
            readme_path = folder_path / "README.md"
            readme_content = f"# {description}\n\n此目录包含{description}相关内容。\n"
            readme_path.write_text(readme_content, encoding='utf-8')
        
        self.log_operation("structure", "建立了优化后的文档结构")
    
    def merge_comprehensive_docs(self) -> None:
        """合并comprehensive系列文档"""
        print("🔄 合并comprehensive系列文档...")
        
        # 查找所有comprehensive文档
        comprehensive_files = list(self.root_path.glob("*comprehensive*.md"))
        
        if not comprehensive_files:
            print("ℹ️ 未找到comprehensive文档")
            return
        
        # 按类型分组
        groups = {
            'services': [],
            'security': [],
            'api': [],
            'database': [],
            'middleware': [],
            'other': []
        }
        
        for file_path in comprehensive_files:
            file_name = file_path.name.lower()
            if 'service' in file_name or 'tenant' in file_name:
                groups['services'].append(file_path)
            elif 'security' in file_name:
                groups['security'].append(file_path)
            elif 'api' in file_name:
                groups['api'].append(file_path)
            elif 'database' in file_name or 'models' in file_name:
                groups['database'].append(file_path)
            elif 'middleware' in file_name or 'utils' in file_name:
                groups['middleware'].append(file_path)
            else:
                groups['other'].append(file_path)
        
        # 合并每个组
        merged_count = 0
        for group_name, files in groups.items():
            if files:
                merged_file = self.merge_files_by_group(group_name, files)
                if merged_file:
                    merged_count += len(files)
                    # 删除原文件
                    for file_path in files:
                        file_path.unlink()
                        self.log_operation("delete", f"删除了 {file_path.name}")
        
        print(f"✅ 合并了 {merged_count} 个comprehensive文档")
        self.log_operation("merge", f"合并了 {merged_count} 个comprehensive文档")
    
    def merge_files_by_group(self, group_name: str, files: List[Path]) -> Path:
        """按组合并文件"""
        if not files:
            return None
        
        # 确定输出文件名和路径
        output_map = {
            'services': ('modules', 'services_guide.md'),
            'security': ('security', 'security_comprehensive.md'),
            'api': ('api', 'api_comprehensive.md'),
            'database': ('modules', 'database_guide.md'),
            'middleware': ('modules', 'middleware_guide.md'),
            'other': ('modules', 'other_modules.md')
        }
        
        if group_name not in output_map:
            return None
        
        folder, filename = output_map[group_name]
        output_path = self.optimized_dir / folder / filename
        
        # 合并内容
        merged_content = f"# {group_name.title()} 综合文档\n\n"
        merged_content += f"*此文档由以下文件合并而成: {', '.join(f.name for f in files)}*\n\n"
        merged_content += f"*合并时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n"
        merged_content += "---\n\n"
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                merged_content += f"## 来源: {file_path.name}\n\n"
                merged_content += content
                merged_content += "\n\n---\n\n"
                
            except Exception as e:
                print(f"⚠️ 读取文件失败: {file_path} - {e}")
                merged_content += f"## 来源: {file_path.name}\n\n"
                merged_content += f"*读取失败: {e}*\n\n---\n\n"
        
        # 写入合并后的文件
        output_path.write_text(merged_content, encoding='utf-8')
        print(f"✅ 创建合并文档: {output_path}")
        
        return output_path
    
    def cleanup_reports(self) -> None:
        """清理冗余报告"""
        print("🧹 清理冗余报告...")
        
        # 查找所有报告文件
        report_patterns = ["*report*.md", "*报告*.md", "*summary*.md", "*总结*.md"]
        all_reports = []
        
        for pattern in report_patterns:
            all_reports.extend(self.root_path.glob(pattern))
        
        if not all_reports:
            print("ℹ️ 未找到报告文件")
            return
        
        # 按文件大小和修改时间排序，保留最重要的
        def get_file_score(file_path: Path) -> float:
            try:
                stat = file_path.stat()
                # 综合考虑文件大小和修改时间
                size_score = stat.st_size / 1024  # KB
                time_score = stat.st_mtime  # 越新越好
                return size_score + (time_score / 1000000)  # 时间权重较小
            except:
                return 0
        
        sorted_reports = sorted(all_reports, key=get_file_score, reverse=True)
        
        # 保留前5个重要报告
        keep_count = min(5, len(sorted_reports))
        keep_reports = sorted_reports[:keep_count]
        archive_reports = sorted_reports[keep_count:]
        
        # 移动重要报告到reports目录
        for report in keep_reports:
            try:
                new_path = self.optimized_dir / "reports" / report.name
                shutil.move(str(report), str(new_path))
                self.log_operation("move", f"保留重要报告: {report.name}")
            except Exception as e:
                print(f"⚠️ 移动报告失败: {report} - {e}")
        
        # 归档其他报告
        archive_dir = self.backup_dir / "archived_reports"
        archive_dir.mkdir(exist_ok=True)
        
        archived_count = 0
        for report in archive_reports:
            try:
                archive_path = archive_dir / report.name
                shutil.move(str(report), str(archive_path))
                archived_count += 1
                self.log_operation("archive", f"归档报告: {report.name}")
            except Exception as e:
                print(f"⚠️ 归档报告失败: {report} - {e}")
        
        print(f"✅ 保留了 {keep_count} 个重要报告，归档了 {archived_count} 个报告")
    
    def remove_duplicates(self) -> None:
        """删除重复文件"""
        print("🗑️ 删除重复文件...")
        
        # 查找重复的安全报告
        duplicate_pattern = "backup_quality_fix/security_reports/security_report_*.md"
        duplicates = list(self.root_path.glob(duplicate_pattern))
        
        removed_count = 0
        for duplicate in duplicates:
            try:
                duplicate.unlink()
                removed_count += 1
                self.log_operation("delete", f"删除重复文件: {duplicate}")
            except Exception as e:
                print(f"⚠️ 删除重复文件失败: {duplicate} - {e}")
        
        print(f"✅ 删除了 {removed_count} 个重复文件")
    
    def organize_remaining_docs(self) -> None:
        """整理剩余文档"""
        print("📂 整理剩余文档...")
        
        # 查找所有剩余的markdown文件
        remaining_docs = []
        for md_file in self.root_path.rglob("*.md"):
            # 排除已处理的目录
            if any(exclude in str(md_file) for exclude in ['docs_backup', 'docs/', 'backup_quality_fix']):
                continue
            remaining_docs.append(md_file)
        
        organized_count = 0
        
        for doc in remaining_docs:
            doc_name = doc.name.lower()
            target_dir = None
            
            # 根据文件名确定目标目录
            if any(keyword in doc_name for keyword in ['api', 'endpoint']):
                target_dir = self.optimized_dir / "api"
            elif any(keyword in doc_name for keyword in ['test', '测试']):
                target_dir = self.optimized_dir / "testing"
            elif any(keyword in doc_name for keyword in ['security', '安全']):
                target_dir = self.optimized_dir / "security"
            elif any(keyword in doc_name for keyword in ['deploy', '部署']):
                target_dir = self.optimized_dir / "deployment"
            elif any(keyword in doc_name for keyword in ['guide', '指南', 'manual']):
                target_dir = self.optimized_dir / "guides"
            elif doc_name == 'readme.md':
                # 保留在根目录
                continue
            else:
                target_dir = self.optimized_dir / "modules"
            
            if target_dir:
                try:
                    target_path = target_dir / doc.name
                    shutil.move(str(doc), str(target_path))
                    organized_count += 1
                    self.log_operation("organize", f"整理文档: {doc.name} -> {target_dir.name}")
                except Exception as e:
                    print(f"⚠️ 整理文档失败: {doc} - {e}")
        
        print(f"✅ 整理了 {organized_count} 个剩余文档")
    
    def create_master_index(self) -> None:
        """创建主索引文档"""
        print("📋 创建主索引文档...")
        
        index_content = """# AstrBot SaaS Platform - 文档索引

*此索引由文档优化工具自动生成*

## 📁 文档结构

### 🏗️ 模块文档 (`docs/modules/`)
- 核心服务和组件的详细文档
- 数据库模型和架构说明
- 中间件和工具模块文档

### 🔌 API文档 (`docs/api/`)  
- RESTful API接口说明
- 请求/响应格式
- 认证和授权机制

### 📖 用户指南 (`docs/guides/`)
- 部署和配置指南
- 开发者文档
- 最佳实践指南

### 📊 重要报告 (`docs/reports/`)
- 质量检查报告
- 性能测试报告
- 项目里程碑报告

### 🔒 安全文档 (`docs/security/`)
- 安全标准和规范
- 安全加固方案
- 安全审计报告

### 🧪 测试文档 (`docs/testing/`)
- 测试策略和计划
- 测试覆盖率报告
- 质量保证流程

### 🚀 部署文档 (`docs/deployment/`)
- 部署指南和配置
- 环境设置说明
- 运维监控文档

## 📈 优化效果

"""
        
        # 添加优化统计
        if self.optimization_log:
            index_content += "### 🎯 本次优化统计\n\n"
            operation_counts = {}
            for log_entry in self.optimization_log:
                op_type = log_entry['type']
                operation_counts[op_type] = operation_counts.get(op_type, 0) + 1
            
            for op_type, count in operation_counts.items():
                index_content += f"- {op_type}: {count} 项操作\n"
        
        index_content += f"\n*最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n"
        
        # 写入索引文件
        index_path = self.optimized_dir / "README.md"
        index_path.write_text(index_content, encoding='utf-8')
        
        print(f"✅ 创建主索引: {index_path}")
        self.log_operation("create", "创建了文档主索引")
    
    def log_operation(self, operation_type: str, description: str) -> None:
        """记录操作日志"""
        self.optimization_log.append({
            'type': operation_type,
            'description': description,
            'timestamp': datetime.now().isoformat()
        })
    
    def save_optimization_log(self) -> None:
        """保存优化日志"""
        log_path = self.root_path / "docs_optimization_log.json"
        with open(log_path, 'w', encoding='utf-8') as f:
            json.dump(self.optimization_log, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 优化日志已保存: {log_path}")
    
    def generate_summary_report(self) -> None:
        """生成优化总结报告"""
        print("📊 生成优化总结报告...")
        
        # 统计优化前后的情况
        backup_files = list(self.backup_dir.rglob("*.md")) if self.backup_dir.exists() else []
        optimized_files = list(self.optimized_dir.rglob("*.md")) if self.optimized_dir.exists() else []
        
        report_content = f"""# 文档优化总结报告

## 优化时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 优化结果

### 📊 文件数量对比
- **优化前**: {len(backup_files)} 个文档
- **优化后**: {len(optimized_files)} 个文档  
- **减少文件**: {len(backup_files) - len(optimized_files)} 个
- **减少比例**: {((len(backup_files) - len(optimized_files)) / len(backup_files) * 100):.1f}%

### 🎯 主要优化操作
"""
        
        # 统计操作类型
        operation_stats = {}
        for log_entry in self.optimization_log:
            op_type = log_entry['type']
            operation_stats[op_type] = operation_stats.get(op_type, 0) + 1
        
        for op_type, count in operation_stats.items():
            report_content += f"- **{op_type}**: {count} 项操作\n"
        
        report_content += f"""
### 📁 新文档结构
```
docs/
├── modules/          # 模块文档
├── api/             # API文档
├── guides/          # 用户指南
├── reports/         # 重要报告
├── security/        # 安全文档
├── testing/         # 测试文档
├── deployment/      # 部署文档
└── README.md        # 主索引
```

### ✅ 优化效果
1. **消除冗余**: 删除了重复和过时的文档
2. **结构化**: 建立了清晰的文档分类体系
3. **易维护**: 减少了文档维护成本
4. **易查找**: 改善了文档的可发现性

### 🔄 备份说明
所有原始文档已备份到 `docs_backup/` 目录，如需恢复可从备份中获取。

---
*此报告由文档优化工具自动生成*
"""
        
        # 保存报告
        report_path = self.root_path / "DOC_OPTIMIZATION_REPORT.md"
        report_path.write_text(report_content, encoding='utf-8')
        
        print(f"✅ 优化报告已生成: {report_path}")


def main():
    """主函数"""
    print("🚀 AstrBot SaaS Platform - 文档优化工具")
    print("="*60)
    
    optimizer = DocumentOptimizer(".")
    
    try:
        # 1. 创建备份
        optimizer.create_backup()
        
        # 2. 建立优化后的结构
        optimizer.setup_optimized_structure()
        
        # 3. 合并comprehensive文档
        optimizer.merge_comprehensive_docs()
        
        # 4. 清理冗余报告
        optimizer.cleanup_reports()
        
        # 5. 删除重复文件
        optimizer.remove_duplicates()
        
        # 6. 整理剩余文档
        optimizer.organize_remaining_docs()
        
        # 7. 创建主索引
        optimizer.create_master_index()
        
        # 8. 保存日志和生成报告
        optimizer.save_optimization_log()
        optimizer.generate_summary_report()
        
        print("\n" + "="*60)
        print("🎉 文档优化完成！")
        print("="*60)
        print(f"📁 新文档结构: docs/")
        print(f"💾 原文档备份: docs_backup/")
        print(f"📊 优化报告: DOC_OPTIMIZATION_REPORT.md")
        print(f"📋 操作日志: docs_optimization_log.json")
        
    except Exception as e:
        print(f"\n❌ 优化过程中出现错误: {e}")
        print("💡 建议检查备份目录并手动恢复")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main()) 