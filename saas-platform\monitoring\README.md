# 📊 AstrBot SaaS - 监控系统

## 🎯 设计理念

相比传统的手工维护大型JSON配置文件，我们采用了**配置驱动 + 代码生成**的现代化监控配置方案。

### ❌ 传统方案的问题
- 🔻 **文件过大**: 单个JSON文件900+行，难以维护
- 🔻 **复杂度高**: 手工编写JSON容易出错，配置重复
- 🔻 **版本控制困难**: JSON diff不友好，难以review
- 🔻 **缺乏模块化**: 无法复用面板配置

### ✅ 新方案的优势
- ✨ **YAML配置**: 更易读写和维护
- ✨ **代码生成**: 从简洁配置自动生成标准JSON
- ✨ **模块化设计**: 多个小仪表板，各司其职
- ✨ **版本友好**: YAML格式便于版本控制和review

## 📂 目录结构

```
monitoring/
├── grafana/
│   ├── dashboards-config.yaml      # 📋 YAML配置模板
│   ├── dashboards/                 # 📊 生成的JSON仪表板
│   │   ├── business_overview.json  # 🏢 业务总览
│   │   ├── system_performance.json # 🖥️ 系统性能
│   │   ├── database_cache.json     # 🗄️ 数据库&缓存
│   │   └── errors_alerts.json      # ⚠️ 错误&告警
│   └── provisioning/               # ⚙️ Grafana配置
├── prometheus/                     # 📈 Prometheus配置
└── scripts/generate_dashboards.py  # 🛠️ 生成脚本
```

## 🚀 快速开始

### 1. 生成仪表板

```bash
# 从YAML配置生成Grafana仪表板JSON
python scripts/generate_dashboards.py
```

### 2. 验证配置

```bash
# 检查生成的JSON格式
python scripts/generate_dashboards.py --validate
```

### 3. 部署到Grafana

```bash
# 使用docker-compose启动监控堆栈
docker-compose -f monitoring/docker-compose.yml up -d
```

## 📋 仪表板概览

### 🏢 业务总览 (`business_overview`)
- **活跃租户数**: 当前平台活跃的租户数量
- **活跃会话数**: 进行中的用户会话数量  
- **API请求速率**: 实时API调用频率
- **消息处理速率**: 聊天消息处理频率

### 🖥️ 系统性能 (`system_performance`)
- **CPU使用率**: 服务器CPU负载情况
- **内存使用率**: 系统内存占用情况
- **API响应时间**: P50/P95响应时间分布

### 🗄️ 数据库&缓存 (`database_cache`)
- **PostgreSQL操作速率**: 数据库增删改操作频率
- **Redis性能指标**: 缓存命令执行和连接情况

### ⚠️ 错误&告警 (`errors_alerts`)
- **服务错误率**: API和LLM服务的错误率趋势
- **活跃告警状态**: 当前系统告警数量和状态

## ⚙️ 配置说明

### YAML配置模板结构

```yaml
# dashboards-config.yaml
dashboard_templates:
  dashboard_name:
    title: "仪表板标题"
    tags: ["astrbot", "saas", "category"]
    refresh: "30s"
    time_range: "1h"
    panels:
      - type: "stat"              # 面板类型
        title: "面板标题"
        metric: "prometheus_metric"
        thresholds: [1000, 5000]   # 告警阈值
        
      - type: "timeseries"
        title: "时序图标题"
        metrics:
          - "rate(metric1[5m])"
          - "rate(metric2[5m])"
        unit: "percent"
```

### 支持的面板类型

- **`stat`**: 统计数值面板，显示当前值
- **`timeseries`**: 时序图，显示趋势变化

### 关键指标说明

| 指标名称 | 描述 | 数据源 |
|---------|------|-------|
| `astrbot_active_tenants` | 活跃租户数 | 应用指标 |
| `astrbot_active_sessions` | 活跃会话数 | 应用指标 |
| `astrbot_api_requests_total` | API请求总数 | 应用指标 |
| `astrbot_messages_total` | 消息总数 | 应用指标 |
| `node_cpu_seconds_total` | CPU使用时间 | Node Exporter |
| `node_memory_*` | 内存相关指标 | Node Exporter |

## 🔧 自定义和扩展

### 添加新的仪表板

1. 在 `dashboards-config.yaml` 中添加新的仪表板模板
2. 运行生成脚本：`python scripts/generate_dashboards.py`
3. 新的JSON文件会自动生成到 `dashboards/` 目录

### 添加新的面板类型

1. 编辑 `scripts/generate_dashboards.py`
2. 在 `generate_panel()` 方法中添加新类型的处理逻辑
3. 重新生成仪表板

### 自定义告警规则

在 `dashboards-config.yaml` 的 `alerting_rules` 部分添加新规则：

```yaml
alerting_rules:
  - name: "custom_alert"
    condition: "your_prometheus_query > threshold"
    for: "5m"
    severity: "warning"
    message: "告警描述"
```

## 🎯 最佳实践

### 1. 配置管理
- ✅ 只编辑YAML配置文件，不要手工修改JSON
- ✅ 使用有意义的仪表板和面板名称
- ✅ 合理设置告警阈值
- ✅ 定期review和更新配置

### 2. 性能优化
- ✅ 使用适当的查询时间窗口（如5m、1h）
- ✅ 避免过于复杂的Prometheus查询
- ✅ 合理设置仪表板刷新频率

### 3. 维护建议
- ✅ 版本控制YAML配置文件
- ✅ 生成的JSON文件可以不纳入版本控制
- ✅ 定期验证生成的配置格式
- ✅ 测试环境验证后再部署生产

## 🆚 对比传统方案

| 方面 | 传统方案 | 新方案 |
|------|---------|--------|
| **配置文件大小** | 900+ 行JSON | 100行YAML |
| **可读性** | 差，JSON结构复杂 | 好，YAML简洁明了 |
| **维护性** | 难，容易出错 | 易，配置清晰 |
| **版本控制** | 困难，diff不友好 | 友好，易于review |
| **模块化** | 无，单体配置 | 有，分离关注点 |
| **错误率** | 高，手工编写 | 低，自动生成 |

## 📚 参考资料

- [Grafana Dashboard API](https://grafana.com/docs/grafana/latest/http_api/dashboard/)
- [Prometheus Query Examples](https://prometheus.io/docs/prometheus/latest/querying/examples/)
- [Grafana Provisioning](https://grafana.com/docs/grafana/latest/administration/provisioning/)

---

💡 **提示**: 这种配置驱动的方案不仅适用于Grafana仪表板，还可以扩展到其他监控配置（如Prometheus告警规则、Alertmanager配置等）。 