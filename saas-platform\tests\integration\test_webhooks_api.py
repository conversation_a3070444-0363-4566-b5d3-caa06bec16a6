"""
Webhook API集成测试
测试Webhook端点的安全性、数据处理和与下游服务的集成
"""
import hmac
import json
import uuid

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.models.tenant import Tenant
from app.models.message import Message


@pytest.fixture
async def setup_tenant_for_webhook(db_session: AsyncSession) -> Tenant:
    """创建一个用于Webhook测试的租户，并设置webhook secret"""
    secret = "my-super-secret-webhook-key"
    tenant = Tenant(
        name="Webhook Test Tenant",
        email=f"webhook-test-{uuid.uuid4().hex[:6]}@test.com",
        extra_data={"webhook_secret": secret} # 设置secret
    )
    db_session.add(tenant)
    await db_session.commit()
    await db_session.refresh(tenant)
    return tenant


class TestWebhookAPIIntegration:
    """Webhook API集成测试类"""

    @pytest.mark.asyncio
    async def test_process_message_webhook_success(
        self, client: AsyncClient, db_session: AsyncSession, setup_tenant_for_webhook: Tenant
    ):
        """测试成功处理一个带有正确签名的 message.received 事件"""
        tenant = setup_tenant_for_webhook
        secret = tenant.extra_data["webhook_secret"]
        
        # 准备 webhook 数据
        event_data = {
            "event_type": "message.received",
            "data": {
                "session_id": str(uuid.uuid4()),
                "user_id": "webhook-user-1",
                "content": "Message from webhook",
                "platform": "webhook-platform",
            }
        }
        body = json.dumps(event_data)
        signature = hmac.new(secret.encode(), body.encode(), "sha256").hexdigest()

        # 发送请求
        response = await client.post(
            f"/api/v1/webhooks/messages/{tenant.id}",
            content=body,
            headers={
                "X-Webhook-Signature": signature,
                "Content-Type": "application/json"
            }
        )
        
        # 验证响应
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["status"] == "success"
        assert response_data["result"]["action"] == "message_stored"
        
        # 验证数据库中消息已创建
        message_id = response_data["result"]["message_id"]
        message = await db_session.get(Message, int(message_id)) # aiosqlite requires int
        assert message is not None
        assert message.content == "Message from webhook"
        assert message.tenant_id == tenant.id

    @pytest.mark.asyncio
    async def test_process_webhook_invalid_signature(
        self, client: AsyncClient, setup_tenant_for_webhook: Tenant
    ):
        """测试当签名错误时，Webhook请求被拒绝"""
        tenant = setup_tenant_for_webhook
        
        event_data = {"event_type": "message.received", "data": {}}
        body = json.dumps(event_data)

        response = await client.post(
            f"/api/v1/webhooks/messages/{tenant.id}",
            content=body,
            headers={
                "X-Webhook-Signature": "invalid-signature",
                "Content-Type": "application/json"
            }
        )
        
        assert response.status_code == 403
        assert "Invalid signature" in response.text

    @pytest.mark.asyncio
    async def test_process_webhook_missing_signature(
        self, client: AsyncClient, setup_tenant_for_webhook: Tenant
    ):
        """测试当缺少签名时，Webhook请求被拒绝"""
        tenant = setup_tenant_for_webhook
        
        response = await client.post(
            f"/api/v1/webhooks/messages/{tenant.id}",
            json={"event_type": "message.received", "data": {}}
        )
        
        assert response.status_code == 400
        assert "Missing X-Webhook-Signature header" in response.text 