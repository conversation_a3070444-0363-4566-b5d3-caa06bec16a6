# AstrBot SaaS Platform 全局逐项核查计划

## 阶段与核查内容

1. 项目状态评估与记忆建立
   - 检查所有write_memory内容，确保项目状态、风险、目标、进度等信息完整且最新
   - 核查记忆内容是否覆盖项目结构、技术栈、历史遗留问题、优化目标、风险点
   - 如有遗漏，补充write_memory

2. 深度代码分析与问题识别
   - 检查问题清单（如“代码冗余清理计划”“代码质量问题清单”记忆）
   - 复核所有TODO、FIXME、HACK注释，确保无遗漏
   - 检查问题优先级分类是否合理
   - 对比最新代码，确认已识别问题是否已被修复或仍需处理

3. 系统化代码优化
   - 检查异常系统（app/core/exceptions.py）是否为唯一入口，所有异常是否已迁移
   - 检查日志、依赖注入、服务层、API层是否有重复/冗余代码
   - 检查所有已合并/删除的重复测试文件，确认无冗余
   - 复查所有优化commit，确保无遗漏

4. 测试体系完善
   - 检查tests/unit、tests/integration、tests/e2e等目录下所有测试文件
   - 复核每个服务/核心功能是否有对应单元测试、集成测试
   - 检查测试覆盖率（如有coverage报告，需分析）
   - 检查测试规范（命名、结构、注释、断言）是否符合项目标准
   - 检查conftest.py等fixture文件的TODO项

5. 性能与安全强化
   - 检查app/core/config/settings.py、scripts/security_check.py等安全相关代码
   - 检查Token清理、密钥校验、密码策略、CORS、数据库/Redis安全配置
   - 检查安全检查脚本是否能自动发现风险
   - 检查CI/CD是否集成安全检查
   - 检查安全相关文档是否同步

6. 文档与总结
   - 检查docs/api、docs/deployment、docs/guides等文档目录
   - 检查异常系统、安全配置、测试规范、部署清单等文档是否最新
   - 检查交付总结、优化报告是否覆盖所有变更
   - 检查README、CHANGELOG等入口文档是否同步

## 执行要求
- 每完成一阶段，更新记忆并输出核查结论
- 发现遗漏/风险，立即补充记忆并修正代码/文档
- 用serena自动化工具+promptx角色协同推进
- 每阶段输出“核查清单+核查结论”文档化
