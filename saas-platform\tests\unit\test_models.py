"""
模型层测试
测试数据模型的基本功能、属性和方法
"""

import pytest
from uuid import uuid4
from datetime import datetime

from app.models.tenant import Tenant, TenantStatus, TenantPlan
from app.models.user import User
from app.models.session import Session, SessionStatus, ChannelType
from app.models.message import Message, MessageType, SenderType
from app.models.role import Role, Permission


class TestTenantModel:
    """测试Tenant模型"""

    def test_tenant_creation(self):
        """测试租户创建"""
        # Arrange & Act
        tenant = Tenant(name="Test Corporation", email="<EMAIL>")

        # Assert
        assert tenant.name == "Test Corporation"
        assert tenant.email == "<EMAIL>"
        assert tenant.id is not None
        assert tenant.status == TenantStatus.ACTIVE  # 默认状态
        assert tenant.plan == TenantPlan.BASIC  # 默认套餐

    def test_tenant_is_active_property(self):
        """测试租户激活状态属性"""
        # Arrange
        tenant = Tenant(name="Test Corp", email="<EMAIL>")

        # Act & Assert - 默认应该是激活状态
        assert tenant.is_active is True

        # 测试暂停状态
        tenant.suspend()
        assert tenant.is_active is False

    def test_tenant_display_name_property(self):
        """测试显示名称属性"""
        # Arrange
        tenant = Tenant(name="Test Corp", email="<EMAIL>")

        # Act & Assert
        assert tenant.display_name == "Test Corp"

    def test_tenant_generate_api_key(self):
        """测试API密钥生成"""
        # Arrange
        tenant = Tenant(name="Test Corp", email="<EMAIL>")

        # Act
        api_key = tenant.generate_api_key()

        # Assert
        assert api_key is not None
        assert len(api_key) > 0
        assert api_key.startswith("ak_live_")

        # 测试设置API密钥
        tenant.api_key = api_key
        assert tenant.api_key == api_key

    def test_tenant_activate_deactivate(self):
        """测试租户激活/停用操作"""
        # Arrange
        tenant = Tenant(name="Test Corp", email="<EMAIL>")

        # Act & Assert
        tenant.deactivate()  # 不需要参数
        assert tenant.status == TenantStatus.DEACTIVATED
        assert tenant.is_active is False

        tenant.activate()
        assert tenant.status == TenantStatus.ACTIVE
        assert tenant.is_active is True

    def test_tenant_metadata_operations(self):
        """测试元数据操作"""
        # Arrange
        tenant = Tenant(name="Test Corp", email="<EMAIL>")

        # Act
        tenant.update_metadata("theme", "dark")
        tenant.update_metadata("language", "zh-CN")

        # Assert
        assert tenant.get_metadata("theme") == "dark"
        assert tenant.get_metadata("language") == "zh-CN"
        assert tenant.get_metadata("non_existent", "default") == "default"

    def test_tenant_to_dict(self):
        """测试字典转换"""
        # Arrange
        tenant = Tenant(name="Test Corp", email="<EMAIL>")
        tenant.update_metadata("test_key", "test_value")

        # Act
        tenant_dict = tenant.to_dict()

        # Assert
        assert tenant_dict["name"] == "Test Corp"
        assert tenant_dict["email"] == "<EMAIL>"
        assert "metadata" in tenant_dict


class TestUserModel:
    """测试User模型"""

    def test_user_creation(self):
        """测试用户创建"""
        # Arrange
        tenant_id = uuid4()

        # Act - 使用实际的属性名
        user = User(
            id="platform:user123",
            tenant_id=tenant_id,
            platform="test_platform",
            user_id="user123",  # 使用user_id而不是platform_user_id
            nickname="Test User",
        )

        # Assert
        assert user.id == "platform:user123"
        assert user.tenant_id == tenant_id
        assert user.platform == "test_platform"
        assert user.user_id == "user123"
        assert user.nickname == "Test User"

    def test_user_composite_id_creation(self):
        """测试复合ID创建"""
        # Arrange & Act
        composite_id = User.create_user_id("telegram", "123456")

        # Assert
        assert composite_id == "telegram:123456"

    def test_user_composite_id_parsing(self):
        """测试复合ID解析"""
        # Arrange
        user_id = "telegram:123456"

        # Act
        platform, platform_user_id = User.parse_user_id(user_id)

        # Assert
        assert platform == "telegram"
        assert platform_user_id == "123456"

    def test_user_id_parsing_invalid(self):
        """测试无效ID解析"""
        # Arrange & Act & Assert
        with pytest.raises(ValueError, match="Invalid user ID format"):
            User.parse_user_id("invalid_id")

    def test_user_display_name_property(self):
        """测试显示名称属性"""
        # Arrange
        user = User(
            id="platform:user123",
            tenant_id=uuid4(),
            platform="test_platform",
            user_id="user123",
            nickname="testuser",
        )

        # Act & Assert
        assert user.display_name == "testuser"  # 应该返回nickname

    def test_user_platform_user_id_property(self):
        """测试platform_user_id属性"""
        # Arrange
        user = User(
            id="platform:user123",
            tenant_id=uuid4(),
            platform="test_platform",
            user_id="user123",
        )

        # Act & Assert
        assert user.platform_user_id == "user123"  # property读取

    def test_user_metadata_operations(self):
        """测试用户元数据操作"""
        # Arrange
        user = User(
            id="platform:user123",
            tenant_id=uuid4(),
            platform="test_platform",
            user_id="user123",
        )

        # Act
        user.update_metadata("preference", "light_theme")

        # Assert
        assert user.get_metadata("preference") == "light_theme"


class TestSessionModel:
    """测试Session模型"""

    def test_session_creation(self):
        """测试会话创建"""
        # Arrange
        tenant_id = uuid4()
        user_id = "test:user123"

        # Act - 使用实际存在的字段
        session = Session(
            tenant_id=tenant_id,
            user_id=user_id,
            platform="test_platform",
            status=SessionStatus.WAITING,  # 显式设置状态
            channel_type=ChannelType.DIRECT,  # 显式设置渠道类型
        )

        # Assert
        assert session.tenant_id == tenant_id
        assert session.user_id == user_id
        assert session.platform == "test_platform"
        assert session.status == SessionStatus.WAITING
        assert session.channel_type == ChannelType.DIRECT

    def test_session_close(self):
        """测试会话关闭操作"""
        # Arrange
        session = Session(
            tenant_id=uuid4(),
            user_id="test:123",
            platform="test",
            status=SessionStatus.ACTIVE,
        )

        # Act & Assert
        session.close_session()
        assert session.status == SessionStatus.CLOSED

    def test_session_is_active_property(self):
        """测试是否活跃属性"""
        # Arrange
        session = Session(
            tenant_id=uuid4(),
            user_id="test:123",
            platform="test",
            status=SessionStatus.ACTIVE,  # 显式设置为活跃状态
        )

        # Act & Assert - 活跃状态应该是True
        assert session.is_active is True

        # 关闭会话后应该不活跃
        session.close_session()
        assert session.is_active is False


class TestMessageModel:
    """测试Message模型"""

    def test_message_creation(self):
        """测试消息创建"""
        # Arrange
        tenant_id = uuid4()
        session_id = uuid4()

        # Act
        message = Message(
            tenant_id=tenant_id,
            session_id=session_id,
            content="Hello, World!",
            sender_type=SenderType.USER,
            sender_id="user123",
            timestamp=datetime.now(),
            message_type=MessageType.TEXT,  # 显式设置消息类型
        )

        # Assert
        assert message.tenant_id == tenant_id
        assert message.session_id == session_id
        assert message.content == "Hello, World!"
        assert message.sender_type == SenderType.USER
        assert message.sender_id == "user123"
        assert message.message_type == MessageType.TEXT

    def test_message_sender_type_properties(self):
        """测试发送者类型属性"""
        # Arrange
        message = Message(
            tenant_id=uuid4(),
            session_id=uuid4(),
            content="Test",
            sender_type=SenderType.USER,
            sender_id="user123",
            timestamp=datetime.now(),
        )

        # Act & Assert
        assert message.is_from_user is True
        assert message.is_from_staff is False
        assert message.is_system_message is False

    def test_message_attachments(self):
        """测试消息附件"""
        # Arrange
        message = Message(
            tenant_id=uuid4(),
            session_id=uuid4(),
            content="Test with attachment",
            sender_type=SenderType.USER,
            sender_id="user123",
            timestamp=datetime.now(),
            attachments=[{"type": "image", "url": "http://example.com/img.jpg"}],
        )

        # Act & Assert
        assert message.has_attachments is True
        assert message.attachment_count == 1

    def test_message_create_user_message(self):
        """测试创建用户消息的类方法"""
        # Arrange
        tenant_id = uuid4()
        session_id = uuid4()

        # Act
        message = Message.create_user_message(
            tenant_id=tenant_id,
            session_id=session_id,
            sender_id="user123",
            content="Hello from user",
        )

        # Assert
        assert message.tenant_id == tenant_id
        assert message.session_id == session_id
        assert message.sender_type == SenderType.USER
        assert message.content == "Hello from user"

    def test_message_create_system_message(self):
        """测试创建系统消息的类方法"""
        # Arrange
        tenant_id = uuid4()
        session_id = uuid4()

        # Act
        message = Message.create_system_message(
            tenant_id=tenant_id, session_id=session_id, content="System notification"
        )

        # Assert
        assert message.tenant_id == tenant_id
        assert message.session_id == session_id
        assert message.sender_type == SenderType.SYSTEM
        assert message.content == "System notification"


class TestRoleModel:
    """测试Role模型"""

    def test_role_creation(self):
        """测试角色创建"""
        # Arrange
        tenant_id = uuid4()

        # Act
        role = Role(
            name="admin",
            display_name="管理员",
            description="系统管理员角色",
            tenant_id=tenant_id,
            is_active=True,  # 显式设置默认值
        )

        # Assert
        assert role.name == "admin"
        assert role.display_name == "管理员"
        assert role.description == "系统管理员角色"
        assert role.tenant_id == tenant_id
        assert role.is_active is True

    def test_role_has_permission(self):
        """测试角色权限检查"""
        # Arrange
        tenant_id = uuid4()
        role = Role(
            name="admin", display_name="管理员", tenant_id=tenant_id, is_active=True
        )

        # Act & Assert - 空角色没有权限
        assert role.has_permission("tenant", "read") is False


class TestPermissionModel:
    """测试Permission模型"""

    def test_permission_creation(self):
        """测试权限创建"""
        # Act
        permission = Permission(
            name="tenant:read",
            description="读取租户信息",
            resource="tenant",
            action="read",
            is_active=True,  # 显式设置默认值
        )

        # Assert
        assert permission.name == "tenant:read"
        assert permission.description == "读取租户信息"
        assert permission.resource == "tenant"
        assert permission.action == "read"
        assert permission.is_active is True
