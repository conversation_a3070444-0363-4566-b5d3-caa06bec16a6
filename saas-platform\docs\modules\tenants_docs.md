# 📖 技术文档：租户管理API (tenants.py)

## 🎯 1. 模块概述

**功能**：提供租户管理的API端点，包括CRUD操作、状态管理和API密钥管理。

**核心职责**：
- **租户管理**：提供创建、查询、更新、删除租户的API
- **API密钥管理**：提供重新生成和撤销API密钥的API
- **统计信息**：提供获取租户统计数据的API

## 🚀 2. 快速使用

### 2.1 依赖注入

```python
from app.services.tenant_service import TenantService, get_tenant_service

@router.get("/")
async def list_tenants(
    tenant_service: TenantService = Depends(get_tenant_service),
    # ...
):
    # ...
```

### 2.2 核心端点

- `GET /` - 获取租户列表
- `POST /` - 创建新租户
- `GET /{tenant_id}` - 获取租户详情
- `PUT /{tenant_id}` - 更新租户信息

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    A[API Endpoints] --> B(TenantService)
    B --> C(Tenant Model)
    A --> D(TenantRead Schema)
    A --> E(TenantCreate Schema)
```

### 3.2 数据流

**创建租户流程**：
1. **API接收**：接收`POST /`请求
2. **服务调用**：调用`TenantService.create_tenant`
3. **响应返回**：返回`TenantRead` schema

## 🔧 4. API参考

| HTTP动词 | 端点 | 描述 |
|---|---|---|
| `GET` | `/` | 获取租户列表 |
| `POST` | `/` | 创建新租户 |
| `GET` | `/{tenant_id}` | 获取租户详情 |
| `PUT` | `/{tenant_id}` | 更新租户信息 |
| `DELETE` | `/{tenant_id}` | 删除租户 |
| `POST` | `/{tenant_id}/api-key`|重新生成API密钥|
| `DELETE` | `/{tenant_id}/api-key`|撤销API密钥|
| `GET`|`/{tenant_id}/stats`|获取租户统计|

## 🧪 5. 测试策略

- **集成测试**：`tests/integration/test_tenant_api_integration.py`

## 💡 6. 维护与扩展

- **权限控制**：确保所有端点都有正确的权限检查
- **参数验证**：使用Pydantic严格验证所有输入数据
- **日志记录**：为所有关键操作添加详细的日志记录

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 