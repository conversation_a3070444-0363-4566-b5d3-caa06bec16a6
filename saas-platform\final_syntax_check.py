#!/usr/bin/env python3
"""
最终语法检查脚本
检查saas-platform目录下所有Python文件的语法正确性
"""

import os
import sys
import py_compile
from pathlib import Path
from typing import List, <PERSON><PERSON>


def check_python_syntax(file_path: Path) -> Tuple[bool, str]:
    """
    检查单个Python文件的语法
    
    Args:
        file_path: Python文件路径
        
    Returns:
        (是否通过, 错误信息)
    """
    try:
        py_compile.compile(file_path, doraise=True)
        return True, ""
    except py_compile.PyCompileError as e:
        return False, str(e)
    except Exception as e:
        return False, f"未知错误: {e}"


def get_python_files(directory: Path) -> List[Path]:
    """获取目录下所有Python文件"""
    return list(directory.glob("*.py"))


def main():
    """主函数"""
    print("🔍 AstrBot SaaS Platform - 最终语法检查")
    print("=" * 60)
    
    # 获取当前目录
    current_dir = Path(__file__).parent
    
    # 获取所有Python文件
    python_files = get_python_files(current_dir)
    
    if not python_files:
        print("❌ 未找到任何Python文件")
        return 1
    
    print(f"📁 找到 {len(python_files)} 个Python文件")
    print()
    
    # 检查每个文件
    passed = 0
    failed = 0
    failed_files = []
    
    for file_path in sorted(python_files):
        print(f"🔍 检查: {file_path.name}...", end=" ")
        
        success, error_msg = check_python_syntax(file_path)
        
        if success:
            print("✅ 通过")
            passed += 1
        else:
            print("❌ 失败")
            print(f"   错误: {error_msg}")
            failed += 1
            failed_files.append(file_path.name)
    
    # 输出总结
    print()
    print("📊 检查结果总结")
    print("-" * 40)
    print(f"✅ 通过: {passed} 个文件")
    print(f"❌ 失败: {failed} 个文件")
    print(f"📈 通过率: {(passed / len(python_files) * 100):.1f}%")
    
    if failed_files:
        print()
        print("❌ 失败文件列表:")
        for file_name in failed_files:
            print(f"   - {file_name}")
        print()
        print("💡 建议: 请检查以上文件的语法错误并修复")
        return 1
    else:
        print()
        print("🎉 恭喜! 所有Python文件语法检查都通过了!")
        print("✨ 项目代码质量达到语法标准要求")
        return 0


if __name__ == "__main__":
    sys.exit(main()) 