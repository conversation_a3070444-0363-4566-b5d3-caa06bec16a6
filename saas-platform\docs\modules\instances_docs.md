# 📖 技术文档：实例管理API (instances.py)

## 🎯 1. 模块概述

**功能**：提供AstrBot实例的注册、查询、更新和撤销功能。

**核心职责**：
- **实例管理**：实现实例的CRUD操作。
- **配置管理**：管理实例的配置信息。
- **认证管理**：为实例生成和撤销认证Token。

## 🚀 2. 快速使用

### 2.1 依赖注入

```python
from app.services.instance_config_service import InstanceConfigService
from app.services.instance_auth_service import InstanceAuthService

@router.post("/")
async def register_instance(
    # ...
    config_service: InstanceConfigService = Depends(),
    auth_service: InstanceAuthService = Depends(),
):
    # ...
```

### 2.2 核心端点

- `POST /` - 注册新实例
- `GET /` - 获取实例列表
- `GET /{instance_id}` - 获取实例详情
- `PUT /{instance_id}` - 更新实例信息
- `DELETE /{instance_id}` - 撤销实例

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    A[API Endpoints] --> B(InstanceConfigService)
    A --> C(InstanceAuthService)
    B --> D(Tenant Model)
    C --> D
```

### 3.2 数据流

**注册实例流程**：
1. **API接收**：接收注册实例的请求。
2. **服务调用**：
   - 调用`InstanceConfigService.register_instance`创建实例记录。
   - 调用`InstanceAuthService.generate_instance_token`生成认证Token。
3. **响应返回**：返回实例信息和认证Token。

## 🔧 4. API参考

| HTTP动词 | 端点 | 描述 |
|---|---|---|
| `POST` | `/` | 注册新实例 |
| `GET` | `/` | 获取实例列表 |
| `GET` | `/{instance_id}` | 获取实例详情 |
| `PUT` | `/{instance_id}` | 更新实例信息 |
| `DELETE` | `/{instance_id}`| 撤销实例 |
| `POST` | `/{instance_id}/token`| 重新生成认证Token |

## 🧪 5. 测试策略

- **集成测试**：`tests/integration/test_instances_api.py`

## 💡 6. 维护与扩展

- **实例健康检查**：可以添加实例健康状态的上报和查询功能。
- **动态配置**：可以扩展配置管理功能，支持更复杂的动态配置。
- **实例分组**：可以添加实例分组功能，方便管理。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 