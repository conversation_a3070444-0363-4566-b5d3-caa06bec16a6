#!/usr/bin/env python3
"""
阶段一：业务功能测试
DevOps执行专家 - 验证AstrBot SaaS平台的核心业务功能
"""

import asyncio
import json
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List
import httpx
import statistics

class BusinessFunctionTester:
    """业务功能测试器"""
    
    def __init__(self):
        self.proxy_url = "http://localhost:9000"
        self.http_client = httpx.AsyncClient(timeout=30.0)
        self.test_results = []
        self.performance_data = []
        
    def print_section(self, title: str):
        """打印测试章节"""
        print(f"\n{'='*80}")
        print(f"🧪 {title}")
        print(f"{'='*80}")
    
    def print_test(self, test_name: str, success: bool, details: str = "", response_time: float = 0):
        """打印测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        if response_time > 0:
            print(f"   响应时间: {response_time:.3f}s")
        
        # 记录测试结果
        self.test_results.append({
            "test_name": test_name,
            "success": success,
            "details": details,
            "response_time": response_time,
            "timestamp": datetime.now().isoformat()
        })
        
        if response_time > 0:
            self.performance_data.append(response_time)
    
    async def test_multi_tenant_messaging(self):
        """测试多租户消息处理流程"""
        self.print_section("多租户消息处理流程测试")
        
        # 定义测试租户和消息类型
        test_scenarios = [
            {
                "tenant_id": "enterprise-corp",
                "messages": [
                    {"type": "text", "content": "你好，我需要技术支持"},
                    {"type": "command", "content": "/help"},
                    {"type": "multi_turn", "content": "请介绍你的功能"},
                    {"type": "complex", "content": "我想了解产品价格和技术规格，能否提供详细信息？"}
                ]
            },
            {
                "tenant_id": "startup-tech",
                "messages": [
                    {"type": "text", "content": "Hello, I need customer support"},
                    {"type": "command", "content": "/status"},
                    {"type": "multi_turn", "content": "What services do you offer?"},
                    {"type": "technical", "content": "How to integrate your API with our system?"}
                ]
            },
            {
                "tenant_id": "retail-chain",
                "messages": [
                    {"type": "text", "content": "产品咨询"},
                    {"type": "command", "content": "/catalog"},
                    {"type": "multi_turn", "content": "我想退换货"},
                    {"type": "business", "content": "批量采购有什么优惠政策？"}
                ]
            }
        ]
        
        for scenario in test_scenarios:
            tenant_id = scenario["tenant_id"]
            print(f"\n📋 测试租户: {tenant_id}")
            
            for i, message in enumerate(scenario["messages"], 1):
                try:
                    start_time = time.time()
                    
                    # 发送消息
                    response = await self.http_client.post(
                        f"{self.proxy_url}/api/v1/proxy/message",
                        json={
                            "tenant_id": tenant_id,
                            "message": message["content"],
                            "user_id": f"user_{i}",
                            "session_id": f"session_{tenant_id}_{i}"
                        },
                        headers={"X-Tenant-ID": tenant_id}
                    )
                    
                    response_time = time.time() - start_time
                    success = response.status_code == 200
                    
                    if success:
                        data = response.json()
                        bot_response = data.get("data", {}).get("response", "")
                        details = f"类型: {message['type']}, 响应: {bot_response[:50]}..."
                    else:
                        details = f"HTTP {response.status_code}: {response.text[:100]}"
                    
                    self.print_test(
                        f"{tenant_id} - {message['type']}消息",
                        success,
                        details,
                        response_time
                    )
                    
                    # 短暂延迟，模拟真实使用
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    self.print_test(f"{tenant_id} - {message['type']}消息", False, f"异常: {e}")
    
    async def test_configuration_management(self):
        """测试配置管理功能"""
        self.print_section("配置管理功能测试")
        
        # 定义配置测试场景
        config_scenarios = [
            {
                "tenant_id": "enterprise-corp",
                "config": {
                    "llm_provider": "openai",
                    "model": "gpt-4",
                    "max_tokens": 2000,
                    "temperature": 0.7,
                    "system_prompt": "你是企业级客服助手",
                    "auto_reply": True,
                    "response_timeout": 30
                }
            },
            {
                "tenant_id": "startup-tech",
                "config": {
                    "llm_provider": "azure",
                    "model": "gpt-3.5-turbo",
                    "max_tokens": 1000,
                    "temperature": 0.5,
                    "system_prompt": "You are a technical support assistant",
                    "auto_reply": False,
                    "webhook_url": "https://startup-tech.com/webhook"
                }
            },
            {
                "tenant_id": "retail-chain",
                "config": {
                    "llm_provider": "local",
                    "model": "llama2",
                    "max_tokens": 1500,
                    "temperature": 0.8,
                    "system_prompt": "你是零售客服专家",
                    "business_hours": "09:00-21:00",
                    "auto_escalate": True
                }
            }
        ]
        
        for scenario in config_scenarios:
            tenant_id = scenario["tenant_id"]
            config = scenario["config"]
            
            try:
                start_time = time.time()
                
                # 推送配置
                response = await self.http_client.post(
                    f"{self.proxy_url}/api/v1/proxy/config",
                    json=config,
                    headers={"X-Tenant-ID": tenant_id}
                )
                
                response_time = time.time() - start_time
                success = response.status_code == 200
                
                if success:
                    data = response.json()
                    config_keys = data.get("data", {}).get("config_keys", [])
                    details = f"配置项数量: {len(config_keys)}, 应用状态: {data.get('data', {}).get('config_applied', False)}"
                else:
                    details = f"HTTP {response.status_code}: {response.text[:100]}"
                
                self.print_test(
                    f"{tenant_id} - 配置推送",
                    success,
                    details,
                    response_time
                )
                
                # 验证配置生效（模拟）
                if success:
                    await asyncio.sleep(1)  # 等待配置应用
                    
                    # 发送测试消息验证配置
                    test_response = await self.http_client.post(
                        f"{self.proxy_url}/api/v1/proxy/message",
                        json={
                            "tenant_id": tenant_id,
                            "message": "测试配置是否生效",
                            "user_id": "config_test_user"
                        },
                        headers={"X-Tenant-ID": tenant_id}
                    )
                    
                    config_test_success = test_response.status_code == 200
                    self.print_test(
                        f"{tenant_id} - 配置生效验证",
                        config_test_success,
                        "配置应用后消息处理正常" if config_test_success else "配置应用后消息处理异常"
                    )
                
            except Exception as e:
                self.print_test(f"{tenant_id} - 配置管理", False, f"异常: {e}")
    
    async def test_session_management(self):
        """测试会话管理"""
        self.print_section("会话管理功能测试")
        
        # 测试会话创建和维持
        session_tests = [
            {
                "tenant_id": "session-test-1",
                "session_id": f"session_{uuid.uuid4().hex[:8]}",
                "messages": [
                    "开始新会话",
                    "这是第二条消息",
                    "这是第三条消息，测试会话连续性"
                ]
            },
            {
                "tenant_id": "session-test-2", 
                "session_id": f"session_{uuid.uuid4().hex[:8]}",
                "messages": [
                    "另一个租户的会话",
                    "验证会话隔离",
                    "确保数据不会混淆"
                ]
            }
        ]
        
        for test in session_tests:
            tenant_id = test["tenant_id"]
            session_id = test["session_id"]
            
            print(f"\n📋 测试会话: {session_id}")
            
            for i, message in enumerate(test["messages"], 1):
                try:
                    start_time = time.time()
                    
                    response = await self.http_client.post(
                        f"{self.proxy_url}/api/v1/proxy/message",
                        json={
                            "tenant_id": tenant_id,
                            "message": message,
                            "user_id": f"user_{tenant_id}",
                            "session_id": session_id
                        },
                        headers={"X-Tenant-ID": tenant_id}
                    )
                    
                    response_time = time.time() - start_time
                    success = response.status_code == 200
                    
                    details = f"消息 {i}: {message[:30]}..." if success else f"HTTP {response.status_code}"
                    
                    self.print_test(
                        f"会话消息 {i} ({tenant_id})",
                        success,
                        details,
                        response_time
                    )
                    
                except Exception as e:
                    self.print_test(f"会话消息 {i} ({tenant_id})", False, f"异常: {e}")
        
        # 测试会话状态查询
        try:
            status_response = await self.http_client.get(f"{self.proxy_url}/api/v1/proxy/status")
            if status_response.status_code == 200:
                status_data = status_response.json()
                active_sessions = status_data.get("active_sessions", 0)
                self.print_test(
                    "会话状态查询",
                    True,
                    f"活跃会话数: {active_sessions}"
                )
            else:
                self.print_test("会话状态查询", False, f"HTTP {status_response.status_code}")
        except Exception as e:
            self.print_test("会话状态查询", False, f"异常: {e}")
    
    async def test_error_handling(self):
        """测试错误处理"""
        self.print_section("错误处理能力测试")
        
        # 定义错误测试场景
        error_scenarios = [
            {
                "name": "无效租户ID",
                "data": {"tenant_id": "", "message": "测试空租户ID"},
                "expected_error": True
            },
            {
                "name": "超长消息",
                "data": {"tenant_id": "test", "message": "x" * 10000},
                "expected_error": False  # 应该被截断处理
            },
            {
                "name": "特殊字符消息",
                "data": {"tenant_id": "test", "message": "🚀💻🔥 特殊字符测试 @#$%^&*()"},
                "expected_error": False
            },
            {
                "name": "JSON注入测试",
                "data": {"tenant_id": "test", "message": '{"malicious": "payload"}'},
                "expected_error": False
            },
            {
                "name": "SQL注入测试",
                "data": {"tenant_id": "test'; DROP TABLE users; --", "message": "SQL注入测试"},
                "expected_error": False  # 应该被安全处理
            }
        ]
        
        for scenario in error_scenarios:
            try:
                start_time = time.time()
                
                response = await self.http_client.post(
                    f"{self.proxy_url}/api/v1/proxy/message",
                    json=scenario["data"],
                    headers={"X-Tenant-ID": scenario["data"]["tenant_id"]}
                )
                
                response_time = time.time() - start_time
                
                if scenario["expected_error"]:
                    # 期望错误的情况
                    success = response.status_code != 200
                    details = f"正确返回错误状态: {response.status_code}" if success else "未正确处理错误"
                else:
                    # 期望成功处理的情况
                    success = response.status_code == 200
                    details = "正确处理异常输入" if success else f"处理失败: {response.status_code}"
                
                self.print_test(
                    scenario["name"],
                    success,
                    details,
                    response_time
                )
                
            except Exception as e:
                # 对于某些测试，异常也可能是预期的
                if scenario["expected_error"]:
                    self.print_test(scenario["name"], True, f"正确抛出异常: {type(e).__name__}")
                else:
                    self.print_test(scenario["name"], False, f"意外异常: {e}")
    
    async def generate_phase1_report(self):
        """生成阶段一测试报告"""
        self.print_section("阶段一测试报告")
        
        # 统计测试结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        # 性能统计
        if self.performance_data:
            avg_response_time = statistics.mean(self.performance_data)
            min_response_time = min(self.performance_data)
            max_response_time = max(self.performance_data)
            p95_response_time = statistics.quantiles(self.performance_data, n=20)[18]  # 95th percentile
        else:
            avg_response_time = min_response_time = max_response_time = p95_response_time = 0
        
        print(f"📊 测试统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {failed_tests}")
        print(f"   成功率: {success_rate:.1f}%")
        
        print(f"\n⏱️ 性能统计:")
        print(f"   平均响应时间: {avg_response_time:.3f}s")
        print(f"   最快响应时间: {min_response_time:.3f}s")
        print(f"   最慢响应时间: {max_response_time:.3f}s")
        print(f"   95%响应时间: {p95_response_time:.3f}s")
        
        # 保存详细报告
        report = {
            "phase": "业务功能测试",
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": success_rate
            },
            "performance": {
                "avg_response_time": avg_response_time,
                "min_response_time": min_response_time,
                "max_response_time": max_response_time,
                "p95_response_time": p95_response_time
            },
            "test_results": self.test_results
        }
        
        with open("phase1_business_function_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存: phase1_business_function_report.json")
        
        return success_rate >= 80
    
    async def run_all_tests(self):
        """运行所有业务功能测试"""
        print("🚀 阶段一：业务功能测试开始")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 执行各项测试
        await self.test_multi_tenant_messaging()
        await self.test_configuration_management()
        await self.test_session_management()
        await self.test_error_handling()
        
        # 生成报告
        success = await self.generate_phase1_report()
        
        return success
    
    async def cleanup(self):
        """清理资源"""
        await self.http_client.aclose()

async def main():
    """主函数"""
    tester = BusinessFunctionTester()
    
    try:
        success = await tester.run_all_tests()
        return 0 if success else 1
    finally:
        await tester.cleanup()

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
