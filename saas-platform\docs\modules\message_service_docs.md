# 📖 技术文档：消息服务 (MessageService)

## 🎯 1. 模块概述

**功能**：提供消息的存储、检索、处理和相关业务逻辑。

**核心职责**：
- **消息存储**：将消息持久化到数据库。
- **消息检索**：根据会话ID、租户ID等条件检索消息。
- **消息处理**：处理传入的消息，包括创建会话和存储消息。
- **消息搜索**：提供消息内容的全文搜索功能。

## 🚀 2. 快速使用

### 2.1 依赖注入

在API端点中注入`MessageService`：

```python
from app.services.message_service import MessageService, get_message_service

@router.post("/messages")
async def send_message(
    message_data: MessageCreate,
    message_service: MessageService = Depends(get_message_service),
):
    return await message_service.store_message(message_data, tenant_id)
```

### 2.2 核心方法

- **`store_message(message_data, tenant_id)`** - 存储消息
- **`get_session_messages(session_id, tenant_id, ...)`** - 获取会话消息
- **`process_incoming_message(incoming_data, tenant_id)`** - 处理传入的消息
- **`search_messages(tenant_id, search_query, ...)`** - 搜索消息

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    subgraph "核心依赖"
        A[FastAPI] --> B(MessageService)
        C[SQLAlchemy] --> B
        D[Pydantic] --> B
    end

    subgraph "模块交互"
        B --> E(SessionService)
        B --> F(Message Model)
    end

    style B fill:#c8e6c9
```

### 3.2 数据流

**存储消息流程**：
1. **API接收**：接收创建消息的请求。
2. **服务处理**：
   - 验证会话是否存在及权限。
   - 创建`Message`对象。
   - 将消息对象添加到数据库会话。
   - 提交事务。
3. **响应返回**：返回创建的`MessageRead`对象。

## 🔧 4. API参考

| 方法 | HTTP动词 | 端点 | 描述 |
|---|---|---|---|
| `store_message` | `POST` | `/api/v1/sessions/{session_id}/messages` | 发送消息 |
| `get_session_messages`| `GET` | `/api/v1/sessions/{session_id}/messages` | 获取会话消息 |

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_message_service.py`
- **集成测试**：`tests/integration/test_messages_api.py`

## 💡 6. 维护与扩展

- **全文搜索**：当前的搜索功能是基于`LIKE`，可以优化为使用Elasticsearch等全文搜索引擎。
- **消息队列**：可以将消息存储和通知等操作异步化，通过消息队列来处理。
- **附件处理**：可以添加对不同类型附件的处理逻辑，例如图片压缩、文件转码等。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 