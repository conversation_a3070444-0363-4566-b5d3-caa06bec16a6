# 📖 技术文档：数据分析服务 (AnalyticsService)

## 🎯 1. 模块概述

**功能**：提供多维度的数据统计分析功能。

**核心职责**：
- **会话统计**：统计会话总数、状态分布、平均时长等。
- **消息统计**：统计消息总数、类型分布、平均响应时间等。
- **实时监控**：提供活跃会话数、新会话数等实时指标。
- **趋势分析**：对指定时间段内的数据进行趋势分析。

## 🚀 2. 快速使用

### 2.1 依赖注入

在API端点中注入`AnalyticsService`：

```python
from app.services.analytics_service import AnalyticsService

@router.get("/sessions")
async def get_session_stats(
    db: AsyncSession = Depends(get_db),
):
    analytics_service = AnalyticsService(db)
    # ...
```

### 2.2 核心方法

- **`get_session_stats(tenant_id, filters)`** - 获取会话统计数据
- **`get_message_stats(tenant_id, filters)`** - 获取消息统计数据
- **`get_realtime_metrics(tenant_id)`** - 获取实时监控指标
- **`get_trend_analysis(tenant_id, days)`** - 获取趋势分析

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    subgraph "核心依赖"
        A[FastAPI] --> B(AnalyticsService)
        C[SQLAlchemy] --> B
        D[Pydantic] --> B
    end

    subgraph "模块交互"
        B --> E(Session Model)
        B --> F(Message Model)
    end

    style B fill:#c8e6c9
```

### 3.2 数据流

**获取会话统计流程**：
1. **API接收**：接收获取会话统计的请求。
2. **服务处理**：
   - 构建基础查询条件。
   - 分别查询总会话数、状态分布、平均时长等。
   - 获取时间序列数据。
3. **响应返回**：返回`SessionStatsResponse`。

## 🔧 4. API参考

| 方法 | HTTP动词 | 端点 | 描述 |
|---|---|---|---|
| `get_session_stats` | `GET` | `/api/v1/analytics/sessions` | 获取会话统计 |
| `get_message_stats` | `GET` | `/api/v1/analytics/messages` | 获取消息统计 |
| `get_realtime_metrics`|`GET`|`/api/v1/analytics/realtime`|获取实时指标|
| `get_trend_analysis`|`GET`|`/api/v1/analytics/trends`|获取趋势分析|

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_analytics_service.py`
- **集成测试**：`tests/integration/test_analytics_api.py`

## 💡 6. 维护与扩展

- **数据库查询优化**：对于大数据量，可以考虑使用更复杂的SQL查询或物化视图。
- **缓存策略**：对于不经常变化的统计数据，可以添加缓存机制。
- **数据可视化**：可以与Grafana等工具集成，提供更丰富的数据可视化。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 