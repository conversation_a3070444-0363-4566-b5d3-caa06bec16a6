#!/usr/bin/env python3
"""
AstrBot SaaS Platform - 代码冗余分析工具
=====================================

专业的代码冗余检测和分析工具，支持多维度的冗余检查：
1. 文件级冗余检测（重复文件、大文件）
2. 代码级冗余检测（重复代码块、函数）
3. 依赖冗余检测（未使用的导入）
4. 文档冗余检测（重复文档）
5. 配置冗余检测（重复配置项）

作者: Quality Improvement Manager
日期: 2025/01/20
"""

import os
import sys
import json
import hashlib
import ast
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional, Any
from collections import defaultdict, Counter
from dataclasses import dataclass
from datetime import datetime
import difflib

@dataclass
class RedundancyIssue:
    """冗余问题数据类"""
    category: str  # 冗余类别
    severity: str  # 严重程度: CRITICAL, HIGH, MEDIUM, LOW
    description: str  # 问题描述
    files: List[str]  # 涉及的文件
    size_bytes: int  # 冗余大小（字节）
    suggestion: str  # 修复建议
    details: Dict[str, Any]  # 详细信息

class CodeRedundancyAnalyzer:
    """代码冗余分析器"""
    
    def __init__(self, project_root: str):
        """
        初始化分析器
        
        Args:
            project_root: 项目根目录路径
        """
        self.project_root = Path(project_root)
        self.issues: List[RedundancyIssue] = []
        self.file_hashes: Dict[str, str] = {}
        self.file_sizes: Dict[str, int] = {}
        
        # 分析配置
        self.config = {
            'file_extensions': {'.py', '.md', '.json', '.yaml', '.yml', '.txt'},
            'ignore_patterns': {
                '__pycache__', '.git', '.pytest_cache', 'htmlcov', 
                '.coverage', '.ruff_cache', 'node_modules', '.venv'
            },
            'min_code_block_lines': 5,  # 最小重复代码块行数
            'large_file_threshold': 1024 * 1024,  # 1MB
            'max_similar_files': 10,  # 相似文件数量阈值
        }
    
    def analyze_all(self) -> List[RedundancyIssue]:
        """
        执行全面的冗余分析
        
        Returns:
            检测到的冗余问题列表
        """
        print("🔍 开始代码冗余分析...")
        
        # 1. 文件级冗余检测
        print("📁 检测文件级冗余...")
        self._analyze_file_redundancy()
        
        # 2. 代码级冗余检测
        print("💻 检测代码级冗余...")
        self._analyze_code_redundancy()
        
        # 3. 导入冗余检测
        print("📦 检测导入冗余...")
        self._analyze_import_redundancy()
        
        # 4. 文档冗余检测
        print("📄 检测文档冗余...")
        self._analyze_documentation_redundancy()
        
        # 5. 配置冗余检测
        print("⚙️ 检测配置冗余...")
        self._analyze_config_redundancy()
        
        print(f"✅ 分析完成，发现 {len(self.issues)} 个冗余问题")
        return self.issues
    
    def _analyze_file_redundancy(self):
        """分析文件级冗余"""
        # 收集所有文件信息
        file_info = self._collect_file_info()
        
        # 检测重复文件（基于内容哈希）
        self._detect_duplicate_files(file_info)
        
        # 检测大文件
        self._detect_large_files(file_info)
        
        # 检测批量重复文件（如性能报告）
        self._detect_bulk_duplicate_files(file_info)
    
    def _collect_file_info(self) -> Dict[str, Dict]:
        """收集文件信息"""
        file_info = {}
        
        for root, dirs, files in os.walk(self.project_root):
            # 跳过忽略的目录
            dirs[:] = [d for d in dirs if d not in self.config['ignore_patterns']]
            
            for file in files:
                file_path = Path(root) / file
                rel_path = file_path.relative_to(self.project_root)
                
                # 只处理指定扩展名的文件
                if file_path.suffix in self.config['file_extensions']:
                    try:
                        file_stat = file_path.stat()
                        with open(file_path, 'rb') as f:
                            content = f.read()
                        
                        file_info[str(rel_path)] = {
                            'path': file_path,
                            'size': file_stat.st_size,
                            'hash': hashlib.md5(content).hexdigest(),
                            'content': content,
                            'extension': file_path.suffix
                        }
                    except (OSError, UnicodeDecodeError):
                        continue
        
        return file_info
    
    def _detect_duplicate_files(self, file_info: Dict):
        """检测重复文件"""
        hash_to_files = defaultdict(list)
        
        for file_path, info in file_info.items():
            hash_to_files[info['hash']].append((file_path, info))
        
        for file_hash, files in hash_to_files.items():
            if len(files) > 1:
                file_paths = [f[0] for f in files]
                total_size = sum(f[1]['size'] for f in files[1:])  # 除了第一个文件外的冗余大小
                
                self.issues.append(RedundancyIssue(
                    category="文件重复",
                    severity="HIGH",
                    description=f"发现 {len(files)} 个完全相同的文件",
                    files=file_paths,
                    size_bytes=total_size,
                    suggestion=f"保留一个文件，删除其余 {len(files)-1} 个重复文件",
                    details={'hash': file_hash, 'file_size': files[0][1]['size']}
                ))
    
    def _detect_large_files(self, file_info: Dict):
        """检测大文件"""
        for file_path, info in file_info.items():
            if info['size'] > self.config['large_file_threshold']:
                self.issues.append(RedundancyIssue(
                    category="大文件",
                    severity="MEDIUM",
                    description=f"文件大小 {info['size'] / 1024 / 1024:.2f}MB 超过阈值",
                    files=[file_path],
                    size_bytes=info['size'],
                    suggestion="检查是否可以拆分或压缩文件",
                    details={'threshold_mb': self.config['large_file_threshold'] / 1024 / 1024}
                ))
    
    def _detect_bulk_duplicate_files(self, file_info: Dict):
        """检测批量重复文件（如性能报告）"""
        # 按文件名模式分组
        pattern_groups = defaultdict(list)
        
        for file_path, info in file_info.items():
            # 提取文件名模式（去除时间戳等变化部分）
            filename = Path(file_path).name
            
            # 检测常见的时间戳模式
            pattern = re.sub(r'\d{8}_\d{6}', 'TIMESTAMP', filename)
            pattern = re.sub(r'\d{4}-\d{2}-\d{2}', 'DATE', pattern)
            pattern = re.sub(r'\d+', 'NUM', pattern)
            
            pattern_groups[pattern].append((file_path, info))
        
        for pattern, files in pattern_groups.items():
            if len(files) > self.config['max_similar_files']:
                total_size = sum(f[1]['size'] for f in files)
                
                self.issues.append(RedundancyIssue(
                    category="批量重复文件",
                    severity="CRITICAL",
                    description=f"发现 {len(files)} 个相似文件（模式: {pattern}）",
                    files=[f[0] for f in files],
                    size_bytes=total_size,
                    suggestion=f"清理历史文件，仅保留最新的几个文件，或建立归档机制",
                    details={'pattern': pattern, 'file_count': len(files)}
                ))
    
    def _analyze_code_redundancy(self):
        """分析代码级冗余"""
        python_files = self._get_python_files()
        
        # 检测重复函数
        self._detect_duplicate_functions(python_files)
        
        # 检测重复代码块
        self._detect_duplicate_code_blocks(python_files)
    
    def _get_python_files(self) -> List[str]:
        """获取所有Python文件"""
        python_files = []
        
        for root, dirs, files in os.walk(self.project_root):
            dirs[:] = [d for d in dirs if d not in self.config['ignore_patterns']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = Path(root) / file
                    rel_path = file_path.relative_to(self.project_root)
                    python_files.append(str(rel_path))
        
        return python_files
    
    def _detect_duplicate_functions(self, python_files: List[str]):
        """检测重复函数"""
        function_signatures = defaultdict(list)
        
        for file_path in python_files:
            try:
                full_path = self.project_root / file_path
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        # 生成函数签名
                        args = [arg.arg for arg in node.args.args]
                        signature = f"{node.name}({', '.join(args)})"
                        
                        # 获取函数体的标准化形式
                        func_body = ast.get_source_segment(content, node)
                        if func_body:
                            # 移除空白和注释来标准化比较
                            normalized_body = re.sub(r'#.*', '', func_body)
                            normalized_body = re.sub(r'\s+', ' ', normalized_body).strip()
                            
                            function_signatures[signature].append({
                                'file': file_path,
                                'line': node.lineno,
                                'body': normalized_body,
                                'length': len(func_body.split('\n'))
                            })
            
            except (SyntaxError, UnicodeDecodeError, OSError):
                continue
        
        # 查找重复函数
        for signature, functions in function_signatures.items():
            if len(functions) > 1:
                # 按函数体分组
                body_groups = defaultdict(list)
                for func in functions:
                    body_groups[func['body']].append(func)
                
                for body, func_list in body_groups.items():
                    if len(func_list) > 1:
                        self.issues.append(RedundancyIssue(
                            category="重复函数",
                            severity="HIGH",
                            description=f"函数 {signature} 在 {len(func_list)} 个文件中重复定义",
                            files=[f['file'] for f in func_list],
                            size_bytes=sum(len(f['body']) for f in func_list),
                            suggestion="将重复函数提取到公共模块中",
                            details={'signature': signature, 'locations': func_list}
                        ))
    
    def _detect_duplicate_code_blocks(self, python_files: List[str]):
        """检测重复代码块"""
        code_blocks = defaultdict(list)
        
        for file_path in python_files:
            try:
                full_path = self.project_root / file_path
                with open(full_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 滑动窗口检测重复代码块
                for i in range(len(lines) - self.config['min_code_block_lines'] + 1):
                    block = lines[i:i + self.config['min_code_block_lines']]
                    
                    # 标准化代码块（移除空白和注释）
                    normalized_block = []
                    for line in block:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            # 移除字符串和数字常量来增加匹配概率
                            line = re.sub(r'"[^"]*"', '""', line)
                            line = re.sub(r"'[^']*'", "''", line)
                            line = re.sub(r'\b\d+\b', 'NUM', line)
                            normalized_block.append(line)
                    
                    if len(normalized_block) >= self.config['min_code_block_lines']:
                        block_hash = hashlib.md5('\n'.join(normalized_block).encode()).hexdigest()
                        code_blocks[block_hash].append({
                            'file': file_path,
                            'start_line': i + 1,
                            'end_line': i + len(block),
                            'content': ''.join(block)
                        })
            
            except (UnicodeDecodeError, OSError):
                continue
        
        # 查找重复代码块
        for block_hash, blocks in code_blocks.items():
            if len(blocks) > 1:
                total_size = sum(len(b['content']) for b in blocks)
                
                self.issues.append(RedundancyIssue(
                    category="重复代码块",
                    severity="MEDIUM",
                    description=f"发现 {len(blocks)} 个相似的代码块（{self.config['min_code_block_lines']}+ 行）",
                    files=[b['file'] for b in blocks],
                    size_bytes=total_size,
                    suggestion="提取重复代码到公共函数或方法中",
                    details={'locations': blocks, 'block_hash': block_hash}
                ))
    
    def _analyze_import_redundancy(self):
        """分析导入冗余"""
        python_files = self._get_python_files()
        
        for file_path in python_files:
            try:
                full_path = self.project_root / file_path
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                # 收集所有导入
                imports = []
                used_names = set()
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    elif isinstance(node, ast.ImportFrom):
                        for alias in node.names:
                            imports.append(alias.name)
                    elif isinstance(node, ast.Name):
                        used_names.add(node.id)
                
                # 检测未使用的导入
                unused_imports = []
                for imp in imports:
                    base_name = imp.split('.')[0]
                    if base_name not in used_names:
                        unused_imports.append(imp)
                
                if unused_imports:
                    self.issues.append(RedundancyIssue(
                        category="未使用导入",
                        severity="LOW",
                        description=f"发现 {len(unused_imports)} 个未使用的导入",
                        files=[file_path],
                        size_bytes=sum(len(imp) for imp in unused_imports),
                        suggestion="移除未使用的导入以减少依赖和提高加载速度",
                        details={'unused_imports': unused_imports}
                    ))
            
            except (SyntaxError, UnicodeDecodeError, OSError):
                continue
    
    def _analyze_documentation_redundancy(self):
        """分析文档冗余"""
        # 查找markdown文件
        md_files = []
        for root, dirs, files in os.walk(self.project_root):
            dirs[:] = [d for d in dirs if d not in self.config['ignore_patterns']]
            
            for file in files:
                if file.endswith('.md'):
                    file_path = Path(root) / file
                    rel_path = file_path.relative_to(self.project_root)
                    md_files.append(str(rel_path))
        
        # 检测重复文档内容
        doc_contents = {}
        for file_path in md_files:
            try:
                full_path = self.project_root / file_path
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                
                # 标准化内容（移除空白）
                normalized = re.sub(r'\s+', ' ', content)
                doc_contents[file_path] = normalized
            
            except (UnicodeDecodeError, OSError):
                continue
        
        # 查找相似文档
        similar_docs = defaultdict(list)
        processed = set()
        
        for file1, content1 in doc_contents.items():
            if file1 in processed:
                continue
            
            similar_group = [file1]
            for file2, content2 in doc_contents.items():
                if file1 != file2 and file2 not in processed:
                    # 计算相似度
                    similarity = difflib.SequenceMatcher(None, content1, content2).ratio()
                    if similarity > 0.8:  # 80%相似度阈值
                        similar_group.append(file2)
                        processed.add(file2)
            
            if len(similar_group) > 1:
                processed.add(file1)
                total_size = sum(len(doc_contents[f]) for f in similar_group)
                
                self.issues.append(RedundancyIssue(
                    category="重复文档",
                    severity="MEDIUM",
                    description=f"发现 {len(similar_group)} 个相似的文档文件",
                    files=similar_group,
                    size_bytes=total_size,
                    suggestion="合并相似文档或创建文档模板",
                    details={'similarity_threshold': 0.8}
                ))
    
    def _analyze_config_redundancy(self):
        """分析配置冗余"""
        config_files = []
        
        # 查找配置文件
        config_extensions = {'.json', '.yaml', '.yml', '.toml', '.ini'}
        for root, dirs, files in os.walk(self.project_root):
            dirs[:] = [d for d in dirs if d not in self.config['ignore_patterns']]
            
            for file in files:
                file_path = Path(root) / file
                if file_path.suffix in config_extensions:
                    rel_path = file_path.relative_to(self.project_root)
                    config_files.append(str(rel_path))
        
        # 分析配置文件内容
        config_data = {}
        for file_path in config_files:
            try:
                full_path = self.project_root / file_path
                
                if file_path.endswith('.json'):
                    with open(full_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    config_data[file_path] = data
                # 可以扩展支持其他配置格式
                
            except (json.JSONDecodeError, UnicodeDecodeError, OSError):
                continue
        
        # 检测重复配置项
        all_keys = defaultdict(list)
        for file_path, data in config_data.items():
            self._extract_config_keys(data, file_path, '', all_keys)
        
        duplicate_configs = []
        for key, files in all_keys.items():
            if len(files) > 1:
                duplicate_configs.append((key, files))
        
        if duplicate_configs:
            total_keys = len(duplicate_configs)
            affected_files = list(set(f for _, files in duplicate_configs for f in files))
            
            self.issues.append(RedundancyIssue(
                category="重复配置",
                severity="MEDIUM",
                description=f"发现 {total_keys} 个重复的配置项",
                files=affected_files,
                size_bytes=total_keys * 50,  # 估算大小
                suggestion="创建共享配置文件或配置继承机制",
                details={'duplicate_keys': duplicate_configs[:10]}  # 只显示前10个
            ))
    
    def _extract_config_keys(self, data: Any, file_path: str, prefix: str, all_keys: Dict):
        """递归提取配置键"""
        if isinstance(data, dict):
            for key, value in data.items():
                full_key = f"{prefix}.{key}" if prefix else key
                all_keys[full_key].append(file_path)
                self._extract_config_keys(value, file_path, full_key, all_keys)
    
    def generate_report(self) -> str:
        """生成分析报告"""
        report = []
        report.append("=" * 80)
        report.append("🔍 AstrBot SaaS Platform - 代码冗余分析报告")
        report.append("=" * 80)
        report.append(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"📁 项目路径: {self.project_root}")
        report.append(f"🎯 发现问题: {len(self.issues)} 个")
        report.append("")
        
        # 按严重程度分组
        issues_by_severity = defaultdict(list)
        total_size = 0
        
        for issue in self.issues:
            issues_by_severity[issue.severity].append(issue)
            total_size += issue.size_bytes
        
        # 概览统计
        report.append("📊 问题概览")
        report.append("-" * 40)
        for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']:
            count = len(issues_by_severity[severity])
            if count > 0:
                report.append(f"🔴 {severity}: {count} 个问题")
        
        report.append(f"💾 估计冗余大小: {total_size / 1024 / 1024:.2f} MB")
        report.append("")
        
        # 详细问题列表
        for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']:
            if not issues_by_severity[severity]:
                continue
            
            report.append(f"🚨 {severity} 级别问题")
            report.append("-" * 40)
            
            for i, issue in enumerate(issues_by_severity[severity], 1):
                report.append(f"{i}. 【{issue.category}】{issue.description}")
                report.append(f"   📄 涉及文件: {len(issue.files)} 个")
                if len(issue.files) <= 5:
                    for file in issue.files:
                        report.append(f"      - {file}")
                else:
                    for file in issue.files[:3]:
                        report.append(f"      - {file}")
                    report.append(f"      ... 还有 {len(issue.files) - 3} 个文件")
                
                report.append(f"   💾 冗余大小: {issue.size_bytes / 1024:.1f} KB")
                report.append(f"   💡 建议: {issue.suggestion}")
                report.append("")
        
        # 优先处理建议
        report.append("🎯 优先处理建议")
        report.append("-" * 40)
        critical_high = issues_by_severity['CRITICAL'] + issues_by_severity['HIGH']
        critical_high.sort(key=lambda x: x.size_bytes, reverse=True)
        
        for i, issue in enumerate(critical_high[:5], 1):
            size_mb = issue.size_bytes / 1024 / 1024
            report.append(f"{i}. {issue.category} - 可节省 {size_mb:.2f}MB")
            report.append(f"   {issue.suggestion}")
        
        return "\n".join(report)
    
    def save_detailed_report(self, output_path: str):
        """保存详细报告到JSON文件"""
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'project_root': str(self.project_root),
            'summary': {
                'total_issues': len(self.issues),
                'total_redundant_size_bytes': sum(issue.size_bytes for issue in self.issues),
                'issues_by_severity': {
                    severity: len([i for i in self.issues if i.severity == severity])
                    for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']
                }
            },
            'issues': [
                {
                    'category': issue.category,
                    'severity': issue.severity,
                    'description': issue.description,
                    'files': issue.files,
                    'size_bytes': issue.size_bytes,
                    'suggestion': issue.suggestion,
                    'details': issue.details
                }
                for issue in self.issues
            ]
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)

def main():
    """主函数"""
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = "."
    
    analyzer = CodeRedundancyAnalyzer(project_root)
    issues = analyzer.analyze_all()
    
    # 生成控制台报告
    report = analyzer.generate_report()
    print(report)
    
    # 保存详细报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    json_report_path = f"code_redundancy_report_{timestamp}.json"
    analyzer.save_detailed_report(json_report_path)
    
    # 保存文本报告
    txt_report_path = f"code_redundancy_report_{timestamp}.txt"
    with open(txt_report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📋 详细报告已保存:")
    print(f"   📄 文本报告: {txt_report_path}")
    print(f"   📊 JSON报告: {json_report_path}")
    
    return len(issues)

if __name__ == "__main__":
    exit_code = main()
    sys.exit(min(exit_code, 255))  # 限制退出码在有效范围内 