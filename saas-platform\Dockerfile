# 多阶段构建的生产级Dockerfile
# Stage 1: 构建阶段
FROM python:3.11-slim AS builder

# 设置构建环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt ./
COPY saas-platform/pyproject.toml ./
COPY LICENSE ./
COPY saas-platform/README.md ./

# 安装Python依赖
RUN pip install --upgrade pip && \
    pip install -r requirements.txt && \
    pip install ".[dev]"

# 复制应用代码
COPY ./saas-platform .

# 运行代码质量检查
# RUN python -m black --check app/ && \
#     python -m ruff check app/ && \
#     python -m mypy app/

# Stage 2: 生产阶段
FROM python:3.11-slim AS production

# 设置生产环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    ENVIRONMENT=production \
    LOG_LEVEL=INFO

# 创建非root用户
RUN groupadd -r astrbot && useradd -r -g astrbot astrbot

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    postgresql-client \
    redis-tools \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 从构建阶段复制依赖
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# 复制应用代码
COPY --chown=astrbot:astrbot ./saas-platform .

# 创建必要的目录
RUN mkdir -p /app/logs /app/static && \
    chown -R astrbot:astrbot /app

# 切换到非root用户
USER astrbot

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
