{"summary": {"total_app_files": 47, "total_test_files": 29, "app_structure": {"models": ["models\\message.py", "models\\role.py", "models\\session.py", "models\\tenant.py", "models\\user.py"], "schemas": ["schemas\\analytics.py", "schemas\\auth.py", "schemas\\common.py", "schemas\\message.py", "schemas\\session.py", "schemas\\tenant.py", "schemas\\user.py"], "services": ["services\\agent_suggestion_service.py", "services\\analytics_service.py", "services\\auth_service.py", "services\\auto_reply_service.py", "services\\context_manager.py", "services\\instance_auth_service.py", "services\\instance_config_service.py", "services\\message_service.py", "services\\rbac_service.py", "services\\session_service.py", "services\\session_summary_service.py", "services\\tenant_service.py", "services\\webhook_service.py", "services\\llm\\base_provider.py", "services\\llm\\dify_provider.py", "services\\llm\\mock_provider.py", "services\\llm\\openai_provider.py", "services\\session_summary\\summary_analyzer.py"], "api": ["api\\deps.py", "api\\v1\\ai_features.py", "api\\v1\\analytics.py", "api\\v1\\instances.py", "api\\v1\\messages.py", "api\\v1\\rbac.py", "api\\v1\\sessions.py", "api\\v1\\tenants.py", "api\\v1\\user_roles.py", "api\\v1\\webhooks.py", "api\\v1\\websocket.py"], "core": ["core\\database.py", "core\\middleware.py", "core\\permissions.py", "core\\security.py", "core\\config\\settings.py"], "utils": ["utils\\logging.py"]}, "test_structure": {"unit": ["unit\\test_agent_suggestion_service.py", "unit\\test_ai_features.py", "unit\\test_analytics.py", "unit\\test_analytics_service.py", "unit\\test_auth.py", "unit\\test_auth_service.py", "unit\\test_auto_reply_service.py", "unit\\test_common.py", "unit\\test_config.py", "unit\\test_database.py", "unit\\test_deps.py", "unit\\test_instances.py", "unit\\test_logging.py", "unit\\test_message.py", "unit\\test_messages.py", "unit\\test_message_service.py", "unit\\test_middleware.py", "unit\\test_permissions.py", "unit\\test_role.py", "unit\\test_security.py", "unit\\test_session.py", "unit\\test_tenant.py", "unit\\test_tenant_model.py", "unit\\test_user_model.py"], "integration": ["integration\\test_basic_integration.py", "integration\\test_tenant_api_integration.py"], "e2e": ["e2e\\test_business_flows.py", "e2e\\test_customer_service_flow.py"], "performance": ["performance\\test_performance_suite.py"]}, "missing_tests": {"models": ["models\\user.py"], "schemas": ["schemas\\user.py"], "services": ["services\\context_manager.py", "services\\instance_auth_service.py", "services\\instance_config_service.py", "services\\rbac_service.py", "services\\session_service.py", "services\\session_summary_service.py", "services\\tenant_service.py", "services\\webhook_service.py", "services\\llm\\base_provider.py", "services\\llm\\dify_provider.py", "services\\llm\\mock_provider.py", "services\\llm\\openai_provider.py", "services\\session_summary\\summary_analyzer.py"], "api": ["api\\v1\\rbac.py", "api\\v1\\sessions.py", "api\\v1\\tenants.py", "api\\v1\\user_roles.py", "api\\v1\\webhooks.py", "api\\v1\\websocket.py"], "core": ["core\\config\\settings.py"], "utils": []}}, "recommendations": ["为models模块创建测试: 1个文件需要测试", "  - models\\user.py", "为schemas模块创建测试: 1个文件需要测试", "  - schemas\\user.py", "为services模块创建测试: 13个文件需要测试", "  - services\\context_manager.py", "  - services\\instance_auth_service.py", "  - services\\instance_config_service.py", "  ... 还有10个文件", "为api模块创建测试: 6个文件需要测试", "  - api\\v1\\rbac.py", "  - api\\v1\\sessions.py", "  - api\\v1\\tenants.py", "  ... 还有3个文件", "为core模块创建测试: 1个文件需要测试", "  - core\\config\\settings.py"]}