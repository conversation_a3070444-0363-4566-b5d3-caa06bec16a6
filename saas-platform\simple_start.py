#!/usr/bin/env python3
"""
简单的AstrBot SaaS Platform启动脚本 (不使用reload)
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    print("🚀 启动 AstrBot SaaS Platform...")
    
    # 设置基本环境变量
    os.environ.setdefault('DATABASE_URL', 'postgresql+asyncpg://astrbot:astrbot123@localhost:5432/astrbot_saas')
    os.environ.setdefault('REDIS_URL', 'redis://:redis123@localhost:6379/0')
    os.environ.setdefault('SECRET_KEY', 'dev-secret-key')
    
    try:
        # 导入并验证应用
        from app.main import app
        print(f"✅ 应用加载成功: {app.title}")
        
        # 使用uvicorn启动 (不使用reload)
        import uvicorn
        print("🌐 启动服务器在 http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        print("🔍 健康检查: http://localhost:8000/health")
        print("\n按 Ctrl+C 停止服务器")
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True
        )
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 