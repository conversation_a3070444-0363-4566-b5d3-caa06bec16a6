{"phase": "业务功能测试", "timestamp": "2025-06-25T13:42:23.152571", "summary": {"total_tests": 30, "passed_tests": 29, "failed_tests": 1, "success_rate": 96.66666666666667}, "performance": {"avg_response_time": 0.004368818723238432, "min_response_time": 0.0034432411193847656, "max_response_time": 0.00786137580871582, "p95_response_time": 0.007747137546539306}, "test_results": [{"test_name": "enterprise-corp - text消息", "success": true, "details": "类型: text, 响应: AstrBot处理消息: 你好，我需要技术支持...", "response_time": 0.00786137580871582, "timestamp": "2025-06-25T13:42:14.017883"}, {"test_name": "enterprise-corp - command消息", "success": true, "details": "类型: command, 响应: AstrBot处理消息: /help...", "response_time": 0.0036911964416503906, "timestamp": "2025-06-25T13:42:14.521714"}, {"test_name": "enterprise-corp - multi_turn消息", "success": true, "details": "类型: multi_turn, 响应: AstrBot处理消息: 请介绍你的功能...", "response_time": 0.0036220550537109375, "timestamp": "2025-06-25T13:42:15.026802"}, {"test_name": "enterprise-corp - complex消息", "success": true, "details": "类型: complex, 响应: AstrBot处理消息: 我想了解产品价格和技术规格，能否提供详细信息？...", "response_time": 0.004181385040283203, "timestamp": "2025-06-25T13:42:15.531840"}, {"test_name": "startup-tech - text消息", "success": true, "details": "类型: text, 响应: AstrBot处理消息: Hello, I need customer support...", "response_time": 0.003609180450439453, "timestamp": "2025-06-25T13:42:16.036750"}, {"test_name": "startup-tech - command消息", "success": true, "details": "类型: command, 响应: AstrBot处理消息: /status...", "response_time": 0.0043087005615234375, "timestamp": "2025-06-25T13:42:16.541698"}, {"test_name": "startup-tech - multi_turn消息", "success": true, "details": "类型: multi_turn, 响应: AstrBot处理消息: What services do you offer?...", "response_time": 0.005067348480224609, "timestamp": "2025-06-25T13:42:17.047051"}, {"test_name": "startup-tech - technical消息", "success": true, "details": "类型: technical, 响应: AstrBot处理消息: How to integrate your API with our sy...", "response_time": 0.0035517215728759766, "timestamp": "2025-06-25T13:42:17.551560"}, {"test_name": "retail-chain - text消息", "success": true, "details": "类型: text, 响应: AstrBot处理消息: 产品咨询...", "response_time": 0.005937814712524414, "timestamp": "2025-06-25T13:42:18.057990"}, {"test_name": "retail-chain - command消息", "success": true, "details": "类型: command, 响应: AstrBot处理消息: /catalog...", "response_time": 0.005490779876708984, "timestamp": "2025-06-25T13:42:18.563845"}, {"test_name": "retail-chain - multi_turn消息", "success": true, "details": "类型: multi_turn, 响应: AstrBot处理消息: 我想退换货...", "response_time": 0.0034432411193847656, "timestamp": "2025-06-25T13:42:19.067497"}, {"test_name": "retail-chain - business消息", "success": true, "details": "类型: business, 响应: AstrBot处理消息: 批量采购有什么优惠政策？...", "response_time": 0.0035943984985351562, "timestamp": "2025-06-25T13:42:19.571867"}, {"test_name": "enterprise-corp - 配置推送", "success": true, "details": "配置项数量: 7, 应用状态: True", "response_time": 0.004014492034912109, "timestamp": "2025-06-25T13:42:20.077997"}, {"test_name": "enterprise-corp - 配置生效验证", "success": true, "details": "配置应用后消息处理正常", "response_time": 0, "timestamp": "2025-06-25T13:42:21.081838"}, {"test_name": "startup-tech - 配置推送", "success": true, "details": "配置项数量: 7, 应用状态: True", "response_time": 0.0036797523498535156, "timestamp": "2025-06-25T13:42:21.086022"}, {"test_name": "startup-tech - 配置生效验证", "success": true, "details": "配置应用后消息处理正常", "response_time": 0, "timestamp": "2025-06-25T13:42:22.090958"}, {"test_name": "retail-chain - 配置推送", "success": true, "details": "配置项数量: 7, 应用状态: True", "response_time": 0.005057096481323242, "timestamp": "2025-06-25T13:42:22.096015"}, {"test_name": "retail-chain - 配置生效验证", "success": true, "details": "配置应用后消息处理正常", "response_time": 0, "timestamp": "2025-06-25T13:42:23.100081"}, {"test_name": "会话消息 1 (session-test-1)", "success": true, "details": "消息 1: 开始新会话...", "response_time": 0.004017829895019531, "timestamp": "2025-06-25T13:42:23.104099"}, {"test_name": "会话消息 2 (session-test-1)", "success": true, "details": "消息 2: 这是第二条消息...", "response_time": 0.004268646240234375, "timestamp": "2025-06-25T13:42:23.108368"}, {"test_name": "会话消息 3 (session-test-1)", "success": true, "details": "消息 3: 这是第三条消息，测试会话连续性...", "response_time": 0.007534980773925781, "timestamp": "2025-06-25T13:42:23.115903"}, {"test_name": "会话消息 1 (session-test-2)", "success": true, "details": "消息 1: 另一个租户的会话...", "response_time": 0.004315376281738281, "timestamp": "2025-06-25T13:42:23.120218"}, {"test_name": "会话消息 2 (session-test-2)", "success": true, "details": "消息 2: 验证会话隔离...", "response_time": 0.003596782684326172, "timestamp": "2025-06-25T13:42:23.123815"}, {"test_name": "会话消息 3 (session-test-2)", "success": true, "details": "消息 3: 确保数据不会混淆...", "response_time": 0.004086732864379883, "timestamp": "2025-06-25T13:42:23.129414"}, {"test_name": "会话状态查询", "success": true, "details": "活跃会话数: 20", "response_time": 0, "timestamp": "2025-06-25T13:42:23.132405"}, {"test_name": "无效租户ID", "success": false, "details": "未正确处理错误", "response_time": 0.004057168960571289, "timestamp": "2025-06-25T13:42:23.136462"}, {"test_name": "超长消息", "success": true, "details": "正确处理异常输入", "response_time": 0.0037689208984375, "timestamp": "2025-06-25T13:42:23.140735"}, {"test_name": "特殊字符消息", "success": true, "details": "正确处理异常输入", "response_time": 0.003560304641723633, "timestamp": "2025-06-25T13:42:23.144295"}, {"test_name": "JSON注入测试", "success": true, "details": "正确处理异常输入", "response_time": 0.0038182735443115234, "timestamp": "2025-06-25T13:42:23.148114"}, {"test_name": "SQL注入测试", "success": true, "details": "正确处理异常输入", "response_time": 0.0034537315368652344, "timestamp": "2025-06-25T13:42:23.151567"}]}