实际参考# 📖 技术文档：安全模块 (security.py)

## 🎯 1. 模块概述

**功能**：提供密码哈希、JWT令牌管理和安全相关的工具函数。

**核心职责**：
- **密码安全**：提供密码哈希和验证功能。
- **JWT令牌**：创建和验证访问令牌和刷新令牌。
- **令牌管理**：从请求头中提取令牌，从令牌中获取主体和租户ID。
- **令牌黑名单**：实现用户登出时的令牌黑名单机制。

## 🚀 2. 快速使用

### 2.1 密码哈希

```python
from app.core.security import get_password_hash, verify_password

hashed_password = get_password_hash("password123")
is_valid = verify_password("password123", hashed_password)
```

### 2.2 JWT令牌

```python
from app.core.security import create_access_token, verify_token

access_token = create_access_token(data={"sub": "<EMAIL>"})
payload = verify_token(access_token)
```

## 🏗️ 3. 架构设计

### 3.1 关键组件

- **`pwd_context`**: `passlib`的密码哈希上下文。
- **`create_access_token()`**: 创建访问令牌。
- **`create_refresh_token()`**: 创建刷新令牌。
- **`verify_token()`**: 验证JWT令牌。
- **`TOKEN_BLACKLIST`**: 用于存储已登出的令牌。

### 3.2 令牌结构

JWT payload包含以下标准和自定义声明：
- `exp`: 过期时间
- `iat`: 签发时间
- `sub`: 主体（用户ID）
- `tenant_id`: 租户ID
- `type`: 令牌类型（`access`或`refresh`）

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_security.py`

## 💡 6. 维护与扩展

- **密钥轮换**：可以添加`SECRET_KEY`的轮换机制。
- **加密算法**：可以升级为更强大的加密算法，如`EdDSA`。
- **分布式黑名单**：可以将令牌黑名单存储在Redis中，以支持多节点部署。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 