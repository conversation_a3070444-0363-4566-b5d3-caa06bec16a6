#!/usr/bin/env python3
"""
AstrBot SaaS 质量检查自动化脚本
执行完整的质量门禁检查流程
"""

import subprocess
import sys
import json
import time
from pathlib import Path
from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum


class CheckStatus(Enum):
    PASSED = "PASSED"
    FAILED = "FAILED"
    SKIPPED = "SKIPPED"
    WARNING = "WARNING"


@dataclass
class CheckResult:
    name: str
    status: CheckStatus
    duration: float
    details: str = ""
    coverage: float = 0.0


class QualityChecker:
    """质量检查执行器"""

    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.results: List[CheckResult] = []

    def run_all_checks(self) -> Dict[str, Any]:
        """执行所有质量检查"""
        print("🚀 开始执行质量检查...")

        # 执行各项检查
        self.check_code_style()
        self.check_type_hints()
        self.check_security()
        self.run_unit_tests()
        self.run_integration_tests()
        self.check_coverage()

        # 生成报告
        return self.generate_report()

    def check_code_style(self):
        """代码风格检查"""
        print("📝 检查代码风格...")
        start_time = time.time()

        try:
            result = subprocess.run(
                ["black", "--check", "app/", "tests/"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
            )

            duration = time.time() - start_time

            if result.returncode == 0:
                self.results.append(
                    CheckResult(
                        name="代码风格检查",
                        status=CheckStatus.PASSED,
                        duration=duration,
                        details="所有文件符合Black格式规范",
                    )
                )
            else:
                self.results.append(
                    CheckResult(
                        name="代码风格检查",
                        status=CheckStatus.FAILED,
                        duration=duration,
                        details=result.stdout + result.stderr,
                    )
                )

        except FileNotFoundError:
            self.results.append(
                CheckResult(
                    name="代码风格检查",
                    status=CheckStatus.SKIPPED,
                    duration=0,
                    details="Black未安装",
                )
            )

    def check_type_hints(self):
        """类型检查"""
        print("🔍 检查类型注解...")
        start_time = time.time()

        try:
            # 明确指定根目录下的 mypy.ini 配置文件
            config_file_path = "../mypy.ini"
            result = subprocess.run(
                ["mypy", "app/", "--config-file", config_file_path],
                cwd=self.project_root,
                capture_output=True,
                text=True,
            )

            duration = time.time() - start_time

            if result.returncode == 0:
                self.results.append(
                    CheckResult(
                        name="类型检查",
                        status=CheckStatus.PASSED,
                        duration=duration,
                        details="类型检查通过",
                    )
                )
            else:
                self.results.append(
                    CheckResult(
                        name="类型检查",
                        status=CheckStatus.FAILED,
                        duration=duration,
                        details=result.stdout + result.stderr,
                    )
                )

        except FileNotFoundError:
            self.results.append(
                CheckResult(
                    name="类型检查",
                    status=CheckStatus.SKIPPED,
                    duration=0,
                    details="MyPy未安装",
                )
            )

    def check_security(self):
        """安全检查"""
        print("🛡️ 执行安全扫描...")
        start_time = time.time()

        try:
            result = subprocess.run(
                ["bandit", "-r", "app/", "-f", "json"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
            )

            duration = time.time() - start_time

            if result.returncode == 0:
                # 解析Bandit输出
                try:
                    bandit_data = json.loads(result.stdout)
                    high_severity = len(
                        [
                            r
                            for r in bandit_data.get("results", [])
                            if r.get("issue_severity") == "HIGH"
                        ]
                    )

                    if high_severity == 0:
                        self.results.append(
                            CheckResult(
                                name="安全检查",
                                status=CheckStatus.PASSED,
                                duration=duration,
                                details=f"发现0个高危安全问题",
                            )
                        )
                    else:
                        self.results.append(
                            CheckResult(
                                name="安全检查",
                                status=CheckStatus.FAILED,
                                duration=duration,
                                details=f"发现{high_severity}个高危安全问题",
                            )
                        )
                except json.JSONDecodeError:
                    self.results.append(
                        CheckResult(
                            name="安全检查",
                            status=CheckStatus.WARNING,
                            duration=duration,
                            details="无法解析安全扫描结果",
                        )
                    )
            else:
                self.results.append(
                    CheckResult(
                        name="安全检查",
                        status=CheckStatus.FAILED,
                        duration=duration,
                        details=result.stderr,
                    )
                )

        except FileNotFoundError:
            self.results.append(
                CheckResult(
                    name="安全检查",
                    status=CheckStatus.SKIPPED,
                    duration=0,
                    details="Bandit未安装",
                )
            )

    def run_unit_tests(self):
        """单元测试"""
        print("🧪 执行单元测试...")
        start_time = time.time()

        try:
            # 移除覆盖率报告参数，只收集数据
            result = subprocess.run(
                ["python", "-m", "pytest", "tests/unit/", "-v", "--tb=short", "--cov=app"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
            )

            duration = time.time() - start_time

            # 解析pytest输出
            if "failed" not in result.stdout.lower():
                self.results.append(
                    CheckResult(
                        name="单元测试",
                        status=CheckStatus.PASSED,
                        duration=duration,
                        details="所有单元测试通过",
                    )
                )
            else:
                self.results.append(
                    CheckResult(
                        name="单元测试",
                        status=CheckStatus.FAILED,
                        duration=duration,
                        details=result.stdout + result.stderr,
                    )
                )

        except Exception as e:
            self.results.append(
                CheckResult(
                    name="单元测试",
                    status=CheckStatus.FAILED,
                    duration=time.time() - start_time,
                    details=str(e),
                )
            )

    def run_integration_tests(self):
        """集成测试"""
        print("🔗 执行集成测试...")
        start_time = time.time()

        try:
            # 移除覆盖率报告参数，只追加收集数据
            result = subprocess.run(
                ["python", "-m", "pytest", "tests/integration/", "-v", "--tb=short", "--cov=app", "--cov-append"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
            )

            duration = time.time() - start_time

            if "failed" not in result.stdout.lower():
                self.results.append(
                    CheckResult(
                        name="集成测试",
                        status=CheckStatus.PASSED,
                        duration=duration,
                        details="所有集成测试通过",
                    )
                )
            else:
                self.results.append(
                    CheckResult(
                        name="集成测试",
                        status=CheckStatus.WARNING,  # 集成测试失败不阻断
                        duration=duration,
                        details=result.stdout + result.stderr,
                    )
                )

        except Exception as e:
            self.results.append(
                CheckResult(
                    name="集成测试",
                    status=CheckStatus.WARNING,
                    duration=time.time() - start_time,
                    details=str(e),
                )
            )

    def check_coverage(self):
        """检查测试覆盖率"""
        print("📊 检查测试覆盖率...")
        start_time = time.time()

        try:
            # 强制从 .coverage 文件生成 coverage.json
            report_result = subprocess.run(
                ["coverage", "json"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
            )
            if report_result.returncode != 0:
                self.results.append(
                    CheckResult(
                        name="测试覆盖率",
                        status=CheckStatus.WARNING,
                        duration=time.time() - start_time,
                        details=f"生成coverage.json失败: {report_result.stderr}",
                    )
                )
                return

            coverage_file = self.project_root / "coverage.json"
            if not coverage_file.exists():
                self.results.append(
                    CheckResult(
                        name="测试覆盖率",
                        status=CheckStatus.WARNING,
                        duration=time.time() - start_time,
                        details="coverage.json 文件未找到",
                    )
                )
                return

            with open(coverage_file, "r") as f:
                coverage_data = json.load(f)

            total_coverage = coverage_data.get("totals", {}).get("percent_covered", 0.0)

            if total_coverage >= 80:
                status = CheckStatus.PASSED
            elif total_coverage >= 70:
                status = CheckStatus.WARNING
            else:
                status = CheckStatus.FAILED

            self.results.append(
                CheckResult(
                    name="测试覆盖率",
                    status=status,
                    duration=time.time() - start_time,
                    details=f"测试覆盖率: {total_coverage:.1f}%",
                    coverage=total_coverage,
                )
            )
        except (FileNotFoundError, json.JSONDecodeError):
            self.results.append(
                CheckResult(
                    name="测试覆盖率",
                    status=CheckStatus.FAILED,
                    duration=time.time() - start_time,
                    details="无法读取覆盖率报告",
                )
            )

    def generate_report(self) -> Dict[str, Any]:
        """生成质量报告"""
        total_checks = len(self.results)
        passed_checks = len([r for r in self.results if r.status == CheckStatus.PASSED])
        failed_checks = len([r for r in self.results if r.status == CheckStatus.FAILED])
        warning_checks = len(
            [r for r in self.results if r.status == CheckStatus.WARNING]
        )

        overall_status = "PASSED"
        if failed_checks > 0:
            overall_status = "FAILED"
        elif warning_checks > 0:
            overall_status = "WARNING"

        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "overall_status": overall_status,
            "summary": {
                "total_checks": total_checks,
                "passed": passed_checks,
                "failed": failed_checks,
                "warnings": warning_checks,
                "success_rate": (
                    f"{(passed_checks / total_checks * 100):.1f}%"
                    if total_checks > 0
                    else "0%"
                ),
            },
            "checks": [
                {
                    "name": r.name,
                    "status": r.status.value,
                    "duration": f"{r.duration:.2f}s",
                    "details": r.details,
                    "coverage": r.coverage if r.coverage > 0 else None,
                }
                for r in self.results
            ],
        }

        return report

    def print_report(self, report: Dict[str, Any]):
        """打印质量报告"""
        print("\n" + "=" * 60)
        print("📋 质量检查报告")
        print("=" * 60)

        print(f"⏰ 执行时间: {report['timestamp']}")
        print(f"🎯 总体状态: {report['overall_status']}")
        print(f"📊 成功率: {report['summary']['success_rate']}")
        print()

        print("详细结果:")
        for check in report["checks"]:
            status_icon = {
                "PASSED": "✅",
                "FAILED": "❌",
                "WARNING": "⚠️",
                "SKIPPED": "⏭️",
            }.get(check["status"], "❓")

            print(
                f"  {status_icon} {check['name']}: {check['status']} ({check['duration']})"
            )
            if check["details"]:
                print(f"     {check['details']}")
            if check["coverage"]:
                print(f"     覆盖率: {check['coverage']:.1f}%")

        print("\n" + "=" * 60)

        # 根据结果设置退出码
        if report["overall_status"] == "FAILED":
            sys.exit(1)
        elif report["overall_status"] == "WARNING":
            sys.exit(2)
        else:
            sys.exit(0)


def main():
    """主函数"""
    project_root = Path(__file__).parent.parent

    checker = QualityChecker(project_root)
    report = checker.run_all_checks()

    # 保存报告
    report_file = project_root / "reports" / "quality_report.json"
    report_file.parent.mkdir(exist_ok=True)

    with open(report_file, "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)

    # 打印报告
    checker.print_report(report)


if __name__ == "__main__":
    main()
