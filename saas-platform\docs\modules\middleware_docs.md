# 📖 技术文档：中间件模块 (middleware.py)

## 🎯 1. 模块概述

**功能**：提供FastAPI中间件，用于处理所有传入的请求和传出的响应。

**核心职责**：
- **CORS处理**：处理跨源资源共享（CORS）请求。
- **请求日志**：记录所有传入请求的详细信息。
- **错误处理**：捕获和处理未处理的异常。
- **租户上下文**：从请求中提取租户信息并设置到上下文中。

## 🚀 2. 快速使用

在`app/main.py`中注册中间件：

```python
from app.core.middleware import (
    CORSMiddleware,
    RequestLoggingMiddleware,
    TenantContextMiddleware,
)

app.add_middleware(
    CORSMiddleware,
    # ...
)
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(TenantContextMiddleware)
```

## 🏗️ 3. 架构设计

### 3.1 关键组件

- **`CORSMiddleware`**: 处理CORS请求。
- **`RequestLoggingMiddleware`**: 记录请求和响应信息。
- **`TenantContextMiddleware`**: 设置租户上下文。
- **`ExceptionMiddleware`**: 捕获和处理全局异常。

### 3.2 数据流

**请求处理流程**：
1. **`CORSMiddleware`**: 处理CORS预检请求。
2. **`RequestLoggingMiddleware`**: 记录请求信息。
3. **`TenantContextMiddleware`**: 从请求头或Token中解析租户ID。
4. **API端点处理**：执行业务逻辑。
5. **`ExceptionMiddleware`**: 如果发生异常，捕获并返回标准错误响应。

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_middleware.py`

## 💡 6. 维护与扩展

- **GZip压缩**：可以添加`GZipMiddleware`来压缩响应体。
- **请求限流**：可以添加中间件来实现基于IP或用户的请求限流。
- **国际化**：可以添加中间件来处理`Accept-Language`头，实现国际化。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 