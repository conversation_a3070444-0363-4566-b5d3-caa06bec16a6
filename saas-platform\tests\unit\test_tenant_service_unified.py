"""
TenantService 统一测试文件

合并三个版本的最佳实践：
- 改进版的健壮Mock策略
- 基础版的完整功能覆盖  
- 简化版的清晰业务逻辑测试

作者: AstrBot团队
版本: v1.0 - 统一版
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from uuid import UUID, uuid4
from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import Select
from datetime import datetime
from typing import List, Optional

# 导入被测试的模块
from app.models.tenant import Tenant, TenantPlan, TenantStatus
from app.services.tenant_service import TenantService
from app.schemas.tenant import TenantCreate, TenantRead, TenantUpdate, TenantStatusUpdate
from app.core.exceptions import TenantNotFoundError, TenantConflictError


class TestTenantServiceUnified:
    """
    TenantService 统一测试类
    
    采用AAA模式（Arrange-Act-Assert）进行测试结构化
    """

    @pytest.fixture
    def mock_db_session(self):
        """
        创建健壮的Mock异步数据库会话
        
        基于改进版的策略，提供完整的数据库操作Mock
        """
        session = AsyncMock(spec=AsyncSession)
        
        # 创建Mock查询结果对象
        execute_result = MagicMock()
        execute_result.scalar_one_or_none.return_value = None  # 默认无结果
        execute_result.scalars.return_value.all.return_value = []  # 默认空列表
        execute_result.scalars.return_value.first.return_value = None  # 默认无首个结果
        
        # 配置数据库操作方法
        session.execute = AsyncMock(return_value=execute_result)
        session.add = Mock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.refresh = AsyncMock()
        session.delete = AsyncMock()
        session.close = AsyncMock()
        
        return session

    @pytest.fixture
    def tenant_service(self, mock_db_session):
        """创建TenantService实例"""
        return TenantService(db=mock_db_session)

    @pytest.fixture
    def sample_tenant_create(self):
        """标准租户创建数据"""
        return TenantCreate(
            name="Test Corporation",
            email="<EMAIL>",
            plan="basic",
            metadata={"industry": "technology", "size": "startup"}
        )

    @pytest.fixture
    def sample_tenant_update(self):
        """标准租户更新数据"""
        return TenantUpdate(
            name="Updated Corporation",
            plan="professional",
            metadata={"industry": "fintech", "size": "medium"}
        )

    @pytest.fixture
    def mock_tenant_instance(self):
        """
        创建完整的Mock Tenant实例
        
        包含所有必需属性，确保Pydantic序列化成功
        """
        tenant = Mock(spec=Tenant)
        tenant.id = uuid4()
        tenant.name = "Test Corporation"
        tenant.email = "<EMAIL>"
        tenant.plan = TenantPlan.BASIC
        tenant.status = TenantStatus.ACTIVE
        tenant.api_key = "ak_test_" + str(uuid4()).replace("-", "")[:16]
        tenant.created_at = datetime.utcnow()
        tenant.updated_at = datetime.utcnow()
        tenant.is_active = True
        tenant.display_name = "Test Corporation"
        tenant.metadata = {"industry": "technology"}
        tenant.extra_data = {}
        
        return tenant

    @pytest.fixture
    def mock_tenant_list(self):
        """创建Mock租户列表用于列表查询测试"""
        tenants = []
        for i in range(3):
            tenant = Mock(spec=Tenant)
            tenant.id = uuid4()
            tenant.name = f"Test Company {i+1}"
            tenant.email = f"test{i+1}@example.com"
            tenant.plan = TenantPlan.BASIC
            tenant.status = TenantStatus.ACTIVE
            tenant.created_at = datetime.utcnow()
            tenant.updated_at = datetime.utcnow()
            tenants.append(tenant)
        return tenants

    # =================== 创建租户测试 ===================

    @pytest.mark.asyncio
    async def test_create_tenant_success(
        self, tenant_service, mock_db_session, sample_tenant_create, mock_tenant_instance
    ):
        """测试成功创建租户的完整流程"""
        # Arrange - 模拟邮箱和名称都不存在
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

        with patch("app.services.tenant_service.Tenant") as MockTenant, \
             patch("app.services.tenant_service.TenantRead.model_validate") as mock_validate:
            
            MockTenant.return_value = mock_tenant_instance
            MockTenant.generate_api_key = Mock(return_value="ak_test_123456789")
            mock_validate.return_value = TenantRead(
                id=mock_tenant_instance.id,
                name=mock_tenant_instance.name,
                email=mock_tenant_instance.email,
                plan=mock_tenant_instance.plan,
                status=mock_tenant_instance.status,
                api_key=mock_tenant_instance.api_key,
                created_at=mock_tenant_instance.created_at,
                updated_at=mock_tenant_instance.updated_at,
                is_active=mock_tenant_instance.is_active,
                display_name=mock_tenant_instance.display_name,
                metadata=mock_tenant_instance.metadata
            )

            # Act - 执行创建操作
            result = await tenant_service.create_tenant(sample_tenant_create)

            # Assert - 验证结果和调用
            assert result is not None
            assert isinstance(result, TenantRead)
            assert result.name == sample_tenant_create.name
            assert result.email == sample_tenant_create.email
            assert result.plan == sample_tenant_create.plan
            
            # 验证数据库操作调用
            mock_db_session.add.assert_called_once()
            mock_db_session.commit.assert_called_once()
            mock_db_session.refresh.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_tenant_duplicate_email(
        self, tenant_service, mock_db_session, sample_tenant_create, mock_tenant_instance
    ):
        """测试邮箱重复创建租户的异常处理"""
        # Arrange - 模拟邮箱已存在
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant_instance

        # Act & Assert - 验证抛出正确异常
        with pytest.raises(HTTPException) as exc_info:
            await tenant_service.create_tenant(sample_tenant_create)
        
        assert exc_info.value.status_code == 409
        assert "邮箱" in exc_info.value.detail and "已被其他租户使用" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_create_tenant_duplicate_name(
        self, tenant_service, mock_db_session, sample_tenant_create, mock_tenant_instance
    ):
        """测试企业名称重复创建租户的异常处理"""
        # Arrange - 模拟名称已存在（邮箱不存在）
        def mock_execute_side_effect(*args, **kwargs):
            # 第一次调用（检查邮箱）返回None，第二次调用（检查名称）返回已存在的租户
            if not hasattr(mock_execute_side_effect, 'call_count'):
                mock_execute_side_effect.call_count = 0
            mock_execute_side_effect.call_count += 1
            
            result = MagicMock()
            if mock_execute_side_effect.call_count == 1:
                result.scalar_one_or_none.return_value = None  # 邮箱不存在
            else:
                result.scalar_one_or_none.return_value = mock_tenant_instance  # 名称已存在
            return result

        mock_db_session.execute.side_effect = mock_execute_side_effect

        # Act & Assert - 验证抛出正确异常
        with pytest.raises(HTTPException) as exc_info:
            await tenant_service.create_tenant(sample_tenant_create)
        
        assert exc_info.value.status_code == 400
        assert "企业名称已存在" in exc_info.value.detail

    # =================== 获取租户测试 ===================

    @pytest.mark.asyncio
    async def test_get_tenant_success(
        self, tenant_service, mock_db_session, mock_tenant_instance
    ):
        """测试通过ID成功获取租户"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant_instance

        with patch("app.services.tenant_service.TenantRead.model_validate") as mock_validate:
            mock_validate.return_value = TenantRead(
                id=mock_tenant_instance.id,
                name=mock_tenant_instance.name,
                email=mock_tenant_instance.email,
                plan=mock_tenant_instance.plan,
                status=mock_tenant_instance.status,
                api_key=mock_tenant_instance.api_key,
                created_at=mock_tenant_instance.created_at,
                updated_at=mock_tenant_instance.updated_at,
                is_active=mock_tenant_instance.is_active,
                display_name=mock_tenant_instance.display_name,
                metadata=mock_tenant_instance.metadata
            )

            # Act
            result = await tenant_service.get_tenant(tenant_id)

            # Assert
            assert result is not None
            assert isinstance(result, TenantRead)
            assert result.id == tenant_id
            assert result.name == mock_tenant_instance.name
            mock_validate.assert_called_once_with(mock_tenant_instance)

    @pytest.mark.asyncio
    async def test_get_tenant_not_found(
        self, tenant_service, mock_db_session
    ):
        """测试获取不存在的租户返回None"""
        # Arrange
        tenant_id = uuid4()
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

        # Act
        result = await tenant_service.get_tenant(tenant_id)

        # Assert
        assert result is None

    @pytest.mark.asyncio
    async def test_get_tenant_by_id_with_verification_success(
        self, tenant_service, mock_tenant_instance
    ):
        """测试获取租户并验证存在成功"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        
        with patch.object(tenant_service, "get_tenant") as mock_get_tenant:
            mock_tenant_read = TenantRead(
                id=mock_tenant_instance.id,
                name=mock_tenant_instance.name,
                email=mock_tenant_instance.email,
                plan=mock_tenant_instance.plan,
                status=mock_tenant_instance.status,
                api_key=mock_tenant_instance.api_key,
                created_at=mock_tenant_instance.created_at,
                updated_at=mock_tenant_instance.updated_at,
                is_active=mock_tenant_instance.is_active,
                display_name=mock_tenant_instance.display_name,
                metadata=mock_tenant_instance.metadata
            )
            mock_get_tenant.return_value = mock_tenant_read

            # Act
            result = await tenant_service.get_tenant_by_id_with_verification(tenant_id)

            # Assert
            assert result is not None
            assert result.id == tenant_id
            mock_get_tenant.assert_called_once_with(tenant_id)

    @pytest.mark.asyncio
    async def test_get_tenant_by_id_with_verification_not_found(
        self, tenant_service
    ):
        """测试获取租户并验证存在失败抛出异常"""
        # Arrange
        tenant_id = uuid4()
        
        with patch.object(tenant_service, "get_tenant", return_value=None):
            # Act & Assert
            with pytest.raises(HTTPException) as exc_info:
                await tenant_service.get_tenant_by_id_with_verification(tenant_id)
            
            assert exc_info.value.status_code == 404
            assert "租户不存在" in exc_info.value.detail

    # =================== 更新租户测试 ===================

    @pytest.mark.asyncio
    async def test_update_tenant_success(
        self, tenant_service, mock_db_session, mock_tenant_instance, sample_tenant_update
    ):
        """测试成功更新租户信息"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant_instance

        with patch("app.services.tenant_service.TenantRead.model_validate") as mock_validate:
            # 模拟更新后的租户
            updated_tenant_read = TenantRead(
                id=mock_tenant_instance.id,
                name=sample_tenant_update.name,  # 使用更新后的名称
                email=mock_tenant_instance.email,
                plan=sample_tenant_update.plan,  # 使用更新后的计划
                status=mock_tenant_instance.status,
                api_key=mock_tenant_instance.api_key,
                created_at=mock_tenant_instance.created_at,
                updated_at=datetime.utcnow(),
                is_active=mock_tenant_instance.is_active,
                display_name=sample_tenant_update.name,
                metadata=sample_tenant_update.metadata
            )
            mock_validate.return_value = updated_tenant_read

            # Act
            result = await tenant_service.update_tenant(tenant_id, sample_tenant_update)

            # Assert
            assert result is not None
            assert isinstance(result, TenantRead)
            assert result.name == sample_tenant_update.name
            assert result.plan == sample_tenant_update.plan
            mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_tenant_not_found(
        self, tenant_service, mock_db_session, sample_tenant_update
    ):
        """测试更新不存在的租户抛出异常"""
        # Arrange
        tenant_id = uuid4()
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await tenant_service.update_tenant(tenant_id, sample_tenant_update)
        
        assert exc_info.value.status_code == 404
        assert "租户不存在" in exc_info.value.detail

    # =================== 删除租户测试 ===================

    @pytest.mark.asyncio
    async def test_delete_tenant_soft_delete_success(
        self, tenant_service, mock_db_session, mock_tenant_instance
    ):
        """测试软删除租户成功（存在用户时）"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant_instance
        
        with patch.object(tenant_service, "_count_tenant_users", return_value=5):
            # Act
            result = await tenant_service.delete_tenant(tenant_id)

            # Assert
            assert result is True
            assert mock_tenant_instance.status == TenantStatus.DEACTIVATED
            mock_db_session.commit.assert_called_once()
            # 验证没有调用硬删除
            mock_db_session.delete.assert_not_called()

    @pytest.mark.asyncio
    async def test_delete_tenant_hard_delete_success(
        self, tenant_service, mock_db_session, mock_tenant_instance
    ):
        """测试硬删除租户成功（无用户时）"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant_instance
        
        with patch.object(tenant_service, "_count_tenant_users", return_value=0):
            # Act
            result = await tenant_service.delete_tenant(tenant_id)

            # Assert
            assert result is True
            mock_db_session.delete.assert_called_once_with(mock_tenant_instance)
            mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_tenant_not_found(
        self, tenant_service, mock_db_session
    ):
        """测试删除不存在的租户抛出异常"""
        # Arrange
        tenant_id = uuid4()
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await tenant_service.delete_tenant(tenant_id)
        
        assert exc_info.value.status_code == 404
        assert "租户不存在" in exc_info.value.detail

    # =================== 列出租户测试 ===================

    @pytest.mark.asyncio
    async def test_list_tenants_success(
        self, tenant_service, mock_db_session, mock_tenant_list
    ):
        """测试成功列出租户"""
        # Arrange
        mock_db_session.execute.return_value.scalars.return_value.all.return_value = mock_tenant_list

        with patch("app.services.tenant_service.TenantRead.model_validate") as mock_validate:
            # 为每个租户创建对应的TenantRead对象
            tenant_reads = []
            for tenant in mock_tenant_list:
                tenant_read = TenantRead(
                    id=tenant.id,
                    name=tenant.name,
                    email=tenant.email,
                    plan=tenant.plan,
                    status=tenant.status,
                    api_key="ak_test_123",
                    created_at=tenant.created_at,
                    updated_at=tenant.updated_at,
                    is_active=True,
                    display_name=tenant.name,
                    metadata={}
                )
                tenant_reads.append(tenant_read)
            
            mock_validate.side_effect = tenant_reads

            # Act
            result = await tenant_service.list_tenants()

            # Assert
            assert isinstance(result, list)
            assert len(result) == len(mock_tenant_list)
            assert all(isinstance(tenant, TenantRead) for tenant in result)
            assert mock_validate.call_count == len(mock_tenant_list)

    @pytest.mark.asyncio
    async def test_list_tenants_with_search(
        self, tenant_service, mock_db_session, mock_tenant_list
    ):
        """测试带搜索条件的租户列表"""
        # Arrange
        search_term = "Test Company 1"
        filtered_tenants = [tenant for tenant in mock_tenant_list if search_term in tenant.name]
        mock_db_session.execute.return_value.scalars.return_value.all.return_value = filtered_tenants

        with patch("app.services.tenant_service.TenantRead.model_validate") as mock_validate:
            tenant_read = TenantRead(
                id=filtered_tenants[0].id,
                name=filtered_tenants[0].name,
                email=filtered_tenants[0].email,
                plan=filtered_tenants[0].plan,
                status=filtered_tenants[0].status,
                api_key="ak_test_123",
                created_at=filtered_tenants[0].created_at,
                updated_at=filtered_tenants[0].updated_at,
                is_active=True,
                display_name=filtered_tenants[0].name,
                metadata={}
            )
            mock_validate.return_value = tenant_read

            # Act
            result = await tenant_service.list_tenants(search=search_term)

            # Assert
            assert isinstance(result, list)
            assert len(result) == len(filtered_tenants)

    # =================== 租户状态管理测试 ===================

    @pytest.mark.asyncio
    async def test_update_tenant_status_success(
        self, tenant_service, mock_db_session, mock_tenant_instance
    ):
        """测试成功更新租户状态"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        status_update = TenantStatusUpdate(status=TenantStatus.SUSPENDED)
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant_instance

        with patch("app.services.tenant_service.TenantRead.model_validate") as mock_validate:
            updated_tenant_read = TenantRead(
                id=mock_tenant_instance.id,
                name=mock_tenant_instance.name,
                email=mock_tenant_instance.email,
                plan=mock_tenant_instance.plan,
                status=TenantStatus.SUSPENDED,  # 更新后的状态
                api_key=mock_tenant_instance.api_key,
                created_at=mock_tenant_instance.created_at,
                updated_at=datetime.utcnow(),
                is_active=False,  # 状态为暂停时不活跃
                display_name=mock_tenant_instance.display_name,
                metadata=mock_tenant_instance.metadata
            )
            mock_validate.return_value = updated_tenant_read

            # Act
            result = await tenant_service.update_tenant_status(tenant_id, status_update)

            # Assert
            assert result is not None
            assert result.status == TenantStatus.SUSPENDED
            assert result.is_active is False
            mock_db_session.commit.assert_called_once()

    # =================== API密钥管理测试 ===================

    @pytest.mark.asyncio
    async def test_regenerate_api_key_success(
        self, tenant_service, mock_db_session, mock_tenant_instance
    ):
        """测试成功重新生成API密钥"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        new_api_key = "ak_new_" + str(uuid4()).replace("-", "")[:16]
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant_instance

        with patch("app.services.tenant_service.Tenant.generate_api_key", return_value=new_api_key), \
             patch("app.services.tenant_service.TenantRead.model_validate") as mock_validate:
            
            updated_tenant_read = TenantRead(
                id=mock_tenant_instance.id,
                name=mock_tenant_instance.name,
                email=mock_tenant_instance.email,
                plan=mock_tenant_instance.plan,
                status=mock_tenant_instance.status,
                api_key=new_api_key,  # 新的API密钥
                created_at=mock_tenant_instance.created_at,
                updated_at=datetime.utcnow(),
                is_active=mock_tenant_instance.is_active,
                display_name=mock_tenant_instance.display_name,
                metadata=mock_tenant_instance.metadata
            )
            mock_validate.return_value = updated_tenant_read

            # Act
            result = await tenant_service.regenerate_api_key(tenant_id)

            # Assert
            assert result is not None
            assert result.api_key == new_api_key
            assert result.api_key != mock_tenant_instance.api_key
            mock_db_session.commit.assert_called_once()

    # =================== 租户统计测试 ===================

    @pytest.mark.asyncio
    async def test_get_tenant_statistics_success(
        self, tenant_service, mock_db_session
    ):
        """测试获取租户统计信息成功"""
        # Arrange
        mock_stats = {
            "total_tenants": 100,
            "active_tenants": 85,
            "suspended_tenants": 10,
            "deactivated_tenants": 5
        }
        
        with patch.object(tenant_service, "_calculate_tenant_statistics", return_value=mock_stats):
            # Act
            result = await tenant_service.get_tenant_statistics()

            # Assert
            assert isinstance(result, dict)
            assert result["total_tenants"] == 100
            assert result["active_tenants"] == 85
            assert result["suspended_tenants"] == 10
            assert result["deactivated_tenants"] == 5

    # =================== 服务初始化测试 ===================

    def test_service_initialization(self, mock_db_session):
        """测试TenantService正确初始化"""
        # Act
        service = TenantService(db=mock_db_session)

        # Assert
        assert service.db is mock_db_session
        assert hasattr(service, 'db')

    # =================== 错误处理测试 ===================

    @pytest.mark.asyncio
    async def test_database_error_handling(
        self, tenant_service, mock_db_session, sample_tenant_create
    ):
        """测试数据库错误的处理"""
        # Arrange
        mock_db_session.execute.side_effect = Exception("Database connection error")

        # Act & Assert
        with pytest.raises(Exception) as exc_info:
            await tenant_service.create_tenant(sample_tenant_create)
        
        assert "Database connection error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_rollback_on_commit_failure(
        self, tenant_service, mock_db_session, sample_tenant_create, mock_tenant_instance
    ):
        """测试提交失败时的回滚操作"""
        # Arrange
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = None
        mock_db_session.commit.side_effect = Exception("Commit failed")

        with patch("app.services.tenant_service.Tenant", return_value=mock_tenant_instance):
            # Act & Assert
            with pytest.raises(Exception):
                await tenant_service.create_tenant(sample_tenant_create)
            
            # 验证添加操作被调用，但由于提交失败会触发异常
            mock_db_session.add.assert_called_once() 