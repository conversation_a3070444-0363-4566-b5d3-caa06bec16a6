# AstrBot SaaS Platform Kubernetes部署配置
apiVersion: v1
kind: Namespace
metadata:
  name: astrbot-saas
  labels:
    name: astrbot-saas

---
# ConfigMap - 应用配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: astrbot-config
  namespace: astrbot-saas
data:
  # 应用配置
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  CORS_ORIGINS: '["http://localhost:3000", "https://app.astrbot.com"]'

  # 数据库配置
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "astrbot_saas"
  DATABASE_USER: "astrbot"

  # Redis配置
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"

  # JWT配置
  JWT_ALGORITHM: "HS256"
  ACCESS_TOKEN_EXPIRE_MINUTES: "30"

  # 监控配置
  PROMETHEUS_ENABLED: "true"
  JAEGER_ENABLED: "true"
  JAEGER_AGENT_HOST: "jaeger-agent"

---
# Secret - 敏感配置
apiVersion: v1
kind: Secret
metadata:
  name: astrbot-secrets
  namespace: astrbot-saas
type: Opaque
data:
  # Base64编码的敏感信息
  SECRET_KEY: eW91ci1zdXBlci1zZWNyZXQta2V5LWNoYW5nZS1pbi1wcm9kdWN0aW9u  # your-super-secret-key-change-in-production
  JWT_SECRET_KEY: and0LXNlY3JldC1jaGFuZ2UtaW4tcHJvZHVjdGlvbg==  # jwt-secret-change-in-production
  DATABASE_PASSWORD: YXN0cmJvdDEyMw==  # astrbot123
  REDIS_PASSWORD: cmVkaXMxMjM=  # redis123
  POSTGRES_PASSWORD: YXN0cmJvdDEyMw==  # astrbot123
  OPENAI_API_KEY: ""  # OpenAI API密钥（需要填入）
  DIFY_API_KEY: ""  # Dify API密钥（需要填入）

---
# PostgreSQL Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: astrbot-saas
  labels:
    app: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: astrbot-config
              key: DATABASE_NAME
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: astrbot-config
              key: DATABASE_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: astrbot-secrets
              key: POSTGRES_PASSWORD
        - name: POSTGRES_INITDB_ARGS
          value: "--encoding=UTF8 --locale=C"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: init-db-script
          mountPath: /docker-entrypoint-initdb.d
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - "pg_isready -U $POSTGRES_USER -d $POSTGRES_DB"
          initialDelaySeconds: 15
          periodSeconds: 5
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - "pg_isready -U $POSTGRES_USER -d $POSTGRES_DB"
          initialDelaySeconds: 30
          periodSeconds: 10
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
      - name: init-db-script
        configMap:
          name: init-db-config
          defaultMode: 0755

---
# PostgreSQL Service
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: astrbot-saas
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP

---
# PostgreSQL PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: astrbot-saas
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd

---
# Redis Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: astrbot-saas
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command:
        - redis-server
        - --appendonly
        - "yes"
        - --requirepass
        - "$(REDIS_PASSWORD)"
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: astrbot-secrets
              key: REDIS_PASSWORD
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        - name: redis-config
          mountPath: /usr/local/etc/redis/
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 3
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc
      - name: redis-config
        configMap:
          name: redis-config

---
# Redis Service
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: astrbot-saas
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP

---
# Redis PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: astrbot-saas
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: fast-ssd

---
# AstrBot SaaS Application Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: astrbot-saas
  namespace: astrbot-saas
  labels:
    app: astrbot-saas
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: astrbot-saas
  template:
    metadata:
      labels:
        app: astrbot-saas
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: astrbot-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      initContainers:
      # 数据库迁移初始化容器
      - name: migrate-db
        image: astrbot/saas-platform:latest
        command: ["python", "-m", "alembic", "upgrade", "head"]
        envFrom:
        - configMapRef:
            name: astrbot-config
        - secretRef:
            name: astrbot-secrets
        env:
        - name: DATABASE_URL
          value: "postgresql+asyncpg://$(DATABASE_USER):$(DATABASE_PASSWORD)@$(DATABASE_HOST):$(DATABASE_PORT)/$(DATABASE_NAME)"
      containers:
      - name: astrbot-saas
        image: astrbot/saas-platform:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        - containerPort: 8080
          name: metrics
        envFrom:
        - configMapRef:
            name: astrbot-config
        - secretRef:
            name: astrbot-secrets
        env:
        - name: DATABASE_URL
          value: "postgresql+asyncpg://$(DATABASE_USER):$(DATABASE_PASSWORD)@$(DATABASE_HOST):$(DATABASE_PORT)/$(DATABASE_NAME)"
        - name: REDIS_URL
          value: "redis://:$(REDIS_PASSWORD)@$(REDIS_HOST):$(REDIS_PORT)/0"
        volumeMounts:
        - name: app-logs
          mountPath: /app/logs
        - name: static-files
          mountPath: /app/static
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 10
          failureThreshold: 3
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: app-logs
        emptyDir: {}
      - name: static-files
        persistentVolumeClaim:
          claimName: static-files-pvc
      restartPolicy: Always
      terminationGracePeriodSeconds: 30

---
# AstrBot SaaS Service
apiVersion: v1
kind: Service
metadata:
  name: astrbot-saas-service
  namespace: astrbot-saas
  labels:
    app: astrbot-saas
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
spec:
  selector:
    app: astrbot-saas
  ports:
  - name: http
    port: 80
    targetPort: 8000
    protocol: TCP
  - name: metrics
    port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP

---
# Ingress配置
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: astrbot-saas-ingress
  namespace: astrbot-saas
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.astrbot.com
    secretName: astrbot-tls-secret
  rules:
  - host: api.astrbot.com
    http:
      paths:
      - path: /api/(.*)
        pathType: Prefix
        backend:
          service:
            name: astrbot-saas-service
            port:
              number: 80
      - path: /docs
        pathType: Prefix
        backend:
          service:
            name: astrbot-saas-service
            port:
              number: 80
      - path: /redoc
        pathType: Prefix
        backend:
          service:
            name: astrbot-saas-service
            port:
              number: 80

---
# HorizontalPodAutoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: astrbot-saas-hpa
  namespace: astrbot-saas
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: astrbot-saas
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Pods
        value: 2
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Pods
        value: 4
        periodSeconds: 60

---
# ServiceAccount
apiVersion: v1
kind: ServiceAccount
metadata:
  name: astrbot-service-account
  namespace: astrbot-saas

---
# NetworkPolicy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: astrbot-network-policy
  namespace: astrbot-saas
spec:
  podSelector:
    matchLabels:
      app: astrbot-saas
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80

---
# PodDisruptionBudget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: astrbot-pdb
  namespace: astrbot-saas
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: astrbot-saas

---
# Static Files PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: static-files-pvc
  namespace: astrbot-saas
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  storageClassName: nfs-storage
