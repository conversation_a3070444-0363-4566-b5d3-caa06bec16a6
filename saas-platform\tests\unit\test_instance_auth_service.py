"""
实例认证服务单元测试
"""
import uuid
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock

import pytest

from app.models.tenant import Tenant
from app.services.instance_auth_service import InstanceAuthService


@pytest.fixture
def mock_db_session():
    """创建一个 mock 的异步数据库会话"""
    mock = AsyncMock()
    
    # 修复：确保execute返回的是一个同步的MagicMock，其方法也是同步的
    mock_result = MagicMock()
    mock_result.scalar_one_or_none.return_value = None # 默认返回
    mock.execute.return_value = mock_result
    
    return mock


@pytest.fixture
def instance_auth_service(mock_db_session):
    """创建一个带有 mock 数据库的 InstanceAuthService 实例"""
    return InstanceAuthService(mock_db_session)


@pytest.fixture
def mock_tenant():
    """创建一个 mock 的 Tenant 对象"""
    tenant = Tenant(
        id=uuid.uuid4(),
        name="Test Tenant",
        extra_data={"instances": {}} # 初始为空
    )
    return tenant


class TestInstanceAuthService:
    """测试 InstanceAuthService"""

    TENANT_ID = uuid.uuid4()
    INSTANCE_ID = "test-instance-123"

    @pytest.mark.asyncio
    async def test_generate_instance_token_success(
        self, instance_auth_service, mock_db_session, mock_tenant
    ):
        """测试 generate_instance_token 成功生成并保存token"""
        # Arrange
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant

        # Act
        result = await instance_auth_service.generate_instance_token(
            mock_tenant.id, self.INSTANCE_ID
        )

        # Assert
        assert result["status"] == "success"
        assert "api_token" in result
        assert "webhook_secret" in result
        
        # 验证数据是否被写入 mock_tenant 的 extra_data
        instance_data = mock_tenant.extra_data["instances"][self.INSTANCE_ID]
        assert instance_data["token"] == result["api_token"]
        
        mock_db_session.commit.assert_awaited_once()

    @pytest.mark.asyncio
    async def test_validate_instance_token_success(
        self, instance_auth_service, mock_db_session, mock_tenant
    ):
        """测试 validate_instance_token 成功验证有效token"""
        # Arrange
        token = "test_token_for_testing"
        mock_tenant.extra_data["instances"][self.INSTANCE_ID] = {
            "token": token,
            "status": "active",
            "expires_at": (datetime.utcnow() + timedelta(days=1)).isoformat()
        }
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant

        # Act
        is_valid = await instance_auth_service.validate_instance_token(
            mock_tenant.id, self.INSTANCE_ID, token
        )

        # Assert
        assert is_valid is True

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "token_data_override, expected_result",
        [
            ({"token": "test_token_for_testing"}, False), # Token不匹配
            ({"status": "revoked"}, False),     # 状态无效
            ({"expires_at": (datetime.utcnow() - timedelta(days=1)).isoformat()}, False) # 已过期
        ]
    )
    async def test_validate_instance_token_failures(
        self, instance_auth_service, mock_db_session, mock_tenant, token_data_override, expected_result
    ):
        """测试 validate_instance_token 的各种失败场景"""
        # Arrange
        valid_token = "test_token_for_testing"
        base_data = {
            "token": valid_token,
            "status": "active",
            "expires_at": (datetime.utcnow() + timedelta(days=1)).isoformat()
        }
        base_data.update(token_data_override)
        mock_tenant.extra_data["instances"][self.INSTANCE_ID] = base_data
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant
        
        # Act
        is_valid = await instance_auth_service.validate_instance_token(
            mock_tenant.id, self.INSTANCE_ID, valid_token
        )

        # Assert
        assert is_valid is expected_result

    @pytest.mark.asyncio
    async def test_revoke_instance_token_success(
        self, instance_auth_service, mock_db_session, mock_tenant
    ):
        """测试 revoke_instance_token 成功撤销token"""
        # Arrange
        mock_tenant.extra_data["instances"][self.INSTANCE_ID] = {
            "token": "test_token_for_testing", "status": "active"
        }
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant

        # Act
        result = await instance_auth_service.revoke_instance_token(
            mock_tenant.id, self.INSTANCE_ID
        )

        # Assert
        assert result["status"] == "success"
        instance_data = mock_tenant.extra_data["instances"][self.INSTANCE_ID]
        assert instance_data["status"] == "revoked"
        mock_db_session.commit.assert_awaited_once()
