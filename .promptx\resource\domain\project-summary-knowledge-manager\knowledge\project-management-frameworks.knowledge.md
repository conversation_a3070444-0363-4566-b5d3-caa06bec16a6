# 项目管理框架知识体系

## 1. 传统项目管理框架

### 1.1 PMBOK指南 (PMI)
- **核心理念**：结构化、阶段性的项目管理方法
- **五大过程组**：启动、规划、执行、监控、收尾
- **十大知识领域**：整合、范围、进度、成本、质量、资源、沟通、风险、采购、干系人
- **适用场景**：大型、复杂、规范性要求高的项目

### 1.2 PRINCE2 (英国政府标准)
- **核心原则**：持续的业务论证、经验学习、角色和职责定义
- **七大主题**：业务论证、组织、质量、计划、风险、变更、进展
- **七大流程**：项目准备、项目启动、阶段管理、阶段边界管理等
- **特点**：重视业务价值，强调风险管理

### 1.3 瀑布模型 (Waterfall)
- **特征**：线性、顺序化的开发过程
- **阶段**：需求分析→系统设计→实现→测试→部署→维护
- **优点**：结构清晰，文档完整，易于管理
- **缺点**：灵活性差，对变更响应慢

## 2. 敏捷项目管理框架

### 2.1 Scrum框架
- **核心理念**：迭代式增量开发，持续改进
- **三大角色**：Product Owner、Scrum Master、Development Team
- **五大事件**：Sprint、Sprint Planning、Daily Scrum、Sprint Review、Sprint Retrospective
- **三大工件**：Product Backlog、Sprint Backlog、Increment
- **价值观**：个体互动、工作软件、客户协作、响应变化

### 2.2 看板方法 (Kanban)
- **核心原则**：可视化工作流、限制在制品、管理流动、持续改进
- **关键实践**：看板可视化、WIP限制、周期时间测量
- **适用场景**：持续交付、运维工作、支持性工作

### 2.3 极限编程 (XP)
- **核心实践**：结对编程、测试驱动开发、持续集成、小版本发布
- **价值观**：沟通、简单、反馈、勇气、尊重

### 2.4 精益开发 (Lean)
- **核心原则**：消除浪费、增强学习、尽量延迟决定、快速交付
- **七大浪费**：不必要的功能、等待、移交、重复工作等

## 3. 混合项目管理方法

### 3.1 SAFe (规模化敏捷框架)
- **层次结构**：投资组合层、价值流层、项目群层、团队层
- **核心实践**：PI规划、架构跑道、持续交付流水线
- **适用场景**：大型企业的敏捷转型

### 3.2 PMBOK + 敏捷混合
- **结合方式**：传统规划 + 敏捷执行
- **适用场景**：需要严格治理但希望提高灵活性的项目
- **关键平衡**：预测性规划与适应性执行

### 3.3 设计思维 + 敏捷
- **阶段**：共情→定义→构思→原型→测试 + 敏捷实施
- **适用场景**：创新性产品开发、用户体验重要的项目

## 4. 特定领域框架

### 4.1 DevOps/持续交付
- **核心理念**：开发与运维一体化，持续集成持续交付
- **关键实践**：自动化部署、基础设施即代码、监控告警
- **工具链**：版本控制、CI/CD工具、容器化、监控系统

### 4.2 项目组合管理 (PPM)
- **目标**：确保项目与战略一致性，优化资源配置
- **关键活动**：项目筛选、优先级排序、资源分配、绩效监控

### 4.3 变更管理框架
- **ADKAR模型**：Awareness、Desire、Knowledge、Ability、Reinforcement
- **8步变更过程**：紧迫感→联盟→愿景→沟通→授权→短期胜利→持续推进→固化

## 5. 项目成功评估框架

### 5.1 铁三角模型
- **三大约束**：范围、时间、成本
- **平衡策略**：固定一个，平衡另外两个
- **现代扩展**：加入质量、风险、价值等维度

### 5.2 价值实现框架
- **价值类型**：财务价值、战略价值、社会价值
- **测量方法**：ROI、NPV、平衡计分卡
- **实现路径**：价值识别→价值规划→价值交付→价值实现

### 5.3 成熟度模型
- **CMMI**：初始级→已管理级→已定义级→量化管理级→优化级
- **OPM3**：标准化→测量→控制→持续改进
- **应用**：组织能力评估，改进路径规划

## 6. 风险管理体系

### 6.1 风险管理过程
- **识别**：专家判断、头脑风暴、检查清单、SWOT分析
- **分析**：定性分析（概率影响矩阵）、定量分析（蒙特卡洛模拟）
- **应对**：规避、减轻、转移、接受
- **监控**：风险登记册更新、风险审计、定期评估

### 6.2 风险分类体系
- **技术风险**：技术不成熟、性能不达标、集成困难
- **管理风险**：资源不足、进度延误、质量问题
- **外部风险**：政策变化、市场变化、供应商风险
- **组织风险**：人员流失、技能不足、沟通不畅

## 7. 质量管理体系

### 7.1 全面质量管理 (TQM)
- **核心原则**：客户导向、全员参与、持续改进、基于事实决策
- **关键工具**：PDCA循环、六西格玛、精益生产

### 7.2 项目质量保证
- **质量规划**：质量标准、质量目标、质量检查点
- **质量保证**：质量审计、过程改进、最佳实践
- **质量控制**：质量检查、缺陷修复、验收测试

## 8. 沟通与干系人管理

### 8.1 沟通管理计划
- **沟通需求分析**：谁需要什么信息、何时需要、如何获得
- **沟通方法**：正式/非正式、书面/口头、内部/外部
- **沟通技术**：面对面、电话会议、协作工具、报告系统

### 8.2 干系人管理策略
- **识别分析**：权力-利益矩阵、影响-态度矩阵
- **参与策略**：不知晓→抵制→中立→支持→领导
- **管理方法**：定期沟通、期望管理、冲突解决

## 9. 资源与成本管理

### 9.1 资源管理
- **资源类型**：人力资源、物理资源、团队资源
- **获取策略**：预分派、谈判、招募、虚拟团队
- **团队发展**：形成期→风暴期→规范期→成就期→解散期

### 9.2 成本管理
- **成本估算**：类比估算、参数估算、自下而上估算、三点估算
- **预算制定**：成本汇总、资金限制平衡、成本基准
- **成本控制**：挣值管理、成本变更控制、绩效分析

## 10. 采购与合同管理

### 10.1 采购管理过程
- **采购规划**：自制/外购分析、采购策略、合同类型选择
- **采购实施**：供应商选择、合同谈判、合同签署
- **采购控制**：合同管理、供应商绩效监控、变更控制

### 10.2 合同类型
- **固定价格合同**：总价固定、单价固定、激励费用
- **成本补偿合同**：成本加费用、成本加激励费、成本加奖励费
- **工料合同**：时间材料、单价合同

## 应用指导

### 框架选择原则
1. **项目特征匹配**：规模、复杂度、不确定性、创新程度
2. **组织环境适应**：企业文化、成熟度、资源状况
3. **干系人期望**：客户需求、管理层要求、团队能力
4. **行业特点**：监管要求、标准规范、最佳实践

### 混合方法设计
1. **需求分析**：明确项目目标、约束条件、成功标准
2. **框架整合**：选择合适的框架组合，定义接口和转换点
3. **裁剪适配**：根据项目实际情况调整框架内容
4. **试点验证**：小范围试点，收集反馈，持续优化

### 持续改进机制
1. **定期评估**：项目回顾、框架有效性评估
2. **经验萃取**：最佳实践识别、教训学习
3. **知识分享**：培训推广、经验交流、标准化
4. **创新发展**：新方法探索、工具升级、能力提升 