[{"type": "backup", "description": "备份了 80 个文档", "timestamp": "2025-06-20T16:06:41.787379"}, {"type": "structure", "description": "建立了优化后的文档结构", "timestamp": "2025-06-20T16:06:41.790656"}, {"type": "move", "description": "保留重要报告: AstrBot项目文档优化总结报告.md", "timestamp": "2025-06-20T16:06:41.795661"}, {"type": "move", "description": "保留重要报告: PROJECT_MANAGEMENT_SUMMARY.md", "timestamp": "2025-06-20T16:06:41.796660"}, {"type": "move", "description": "保留重要报告: TEST_COVERAGE_REPORT.md", "timestamp": "2025-06-20T16:06:41.796660"}, {"type": "move", "description": "保留重要报告: POSTGRESQL_TEST_REPORT.md", "timestamp": "2025-06-20T16:06:41.797660"}, {"type": "archive", "description": "归档报告: M8_DevOps_Completion_Report.md", "timestamp": "2025-06-20T16:06:41.821402"}, {"type": "archive", "description": "归档报告: DEPLOYMENT_SUMMARY.md", "timestamp": "2025-06-20T16:06:41.821402"}, {"type": "archive", "description": "归档报告: PROJECT_STATUS_REPORT.md", "timestamp": "2025-06-20T16:06:41.822403"}, {"type": "archive", "description": "归档报告: final_redundancy_cleanup_report.md", "timestamp": "2025-06-20T16:06:41.822403"}, {"type": "archive", "description": "归档报告: CODE_QUALITY_SUMMARY.md", "timestamp": "2025-06-20T16:06:41.823404"}, {"type": "archive", "description": "归档报告: DEPLOYMENT_TEST_REPORT.md", "timestamp": "2025-06-20T16:06:41.823404"}, {"type": "archive", "description": "归档报告: SYNTAX_FIX_SUMMARY.md", "timestamp": "2025-06-20T16:06:41.823404"}, {"type": "delete", "description": "删除重复文件: backup_quality_fix\\security_reports\\security_report_20250616_135437.md", "timestamp": "2025-06-20T16:06:41.824402"}, {"type": "organize", "description": "整理文档: DEPLOYMENT_GUIDE.md -> deployment", "timestamp": "2025-06-20T16:06:41.833918"}, {"type": "organize", "description": "整理文档: M8_deployment_preparation.md -> deployment", "timestamp": "2025-06-20T16:06:41.833918"}, {"type": "organize", "description": "整理文档: M8_Role_Specifications.md -> modules", "timestamp": "2025-06-20T16:06:41.834918"}, {"type": "organize", "description": "整理文档: M8_Security_Hardening_Plan.md -> security", "timestamp": "2025-06-20T16:06:41.834918"}, {"type": "organize", "description": "整理文档: SECURITY_GUIDE.md -> security", "timestamp": "2025-06-20T16:06:41.834918"}, {"type": "organize", "description": "整理文档: SERVICE_MANUAL.md -> guides", "timestamp": "2025-06-20T16:06:41.835916"}, {"type": "organize", "description": "整理文档: TEST_COVERAGE_IMPROVEMENT_PLAN.md -> testing", "timestamp": "2025-06-20T16:06:41.835916"}, {"type": "organize", "description": "整理文档: 文档管理最佳实践指南.md -> guides", "timestamp": "2025-06-20T16:06:41.835916"}, {"type": "organize", "description": "整理文档: 测试文档整理计划.md -> testing", "timestamp": "2025-06-20T16:06:41.835916"}, {"type": "organize", "description": "整理文档: 测试文档清理执行方案.md -> testing", "timestamp": "2025-06-20T16:06:41.836916"}, {"type": "organize", "description": "整理文档: 质量管理与持续改进机制.md -> modules", "timestamp": "2025-06-20T16:06:41.836916"}, {"type": "organize", "description": "整理文档: main_module_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.837916"}, {"type": "organize", "description": "整理文档: security_report_20250616_135437.md -> security", "timestamp": "2025-06-20T16:06:41.837916"}, {"type": "organize", "description": "整理文档: TESTING_GUIDE.md -> testing", "timestamp": "2025-06-20T16:06:41.837916"}, {"type": "organize", "description": "整理文档: test_strategy.md -> testing", "timestamp": "2025-06-20T16:06:41.837916"}, {"type": "organize", "description": "整理文档: 性能测试完成报告.md -> testing", "timestamp": "2025-06-20T16:06:41.838916"}, {"type": "organize", "description": "整理文档: 测试全面完成总结.md -> testing", "timestamp": "2025-06-20T16:06:41.838916"}, {"type": "organize", "description": "整理文档: 测试状态快照.md -> testing", "timestamp": "2025-06-20T16:06:41.838916"}, {"type": "organize", "description": "整理文档: 测试综合报告与质量提升记录.md -> testing", "timestamp": "2025-06-20T16:06:41.838916"}, {"type": "organize", "description": "整理文档: 集成测试完成报告.md -> testing", "timestamp": "2025-06-20T16:06:41.839917"}, {"type": "organize", "description": "整理文档: multi_tenancy_guards.md -> modules", "timestamp": "2025-06-20T16:06:41.839917"}, {"type": "organize", "description": "整理文档: project_overview.md -> modules", "timestamp": "2025-06-20T16:06:41.840422"}, {"type": "organize", "description": "整理文档: deps_comprehensive_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.840422"}, {"type": "organize", "description": "整理文档: database_comprehensive_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.840422"}, {"type": "organize", "description": "整理文档: database_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.840422"}, {"type": "organize", "description": "整理文档: middleware_comprehensive_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.840422"}, {"type": "organize", "description": "整理文档: middleware_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.841428"}, {"type": "organize", "description": "整理文档: permissions_comprehensive_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.841428"}, {"type": "organize", "description": "整理文档: permissions_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.841428"}, {"type": "organize", "description": "整理文档: security_comprehensive_docs.md -> security", "timestamp": "2025-06-20T16:06:41.842432"}, {"type": "organize", "description": "整理文档: security_docs.md -> security", "timestamp": "2025-06-20T16:06:41.842432"}, {"type": "organize", "description": "整理文档: message_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.842432"}, {"type": "organize", "description": "整理文档: models_comprehensive_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.842432"}, {"type": "organize", "description": "整理文档: role_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.842432"}, {"type": "organize", "description": "整理文档: session_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.843431"}, {"type": "organize", "description": "整理文档: tenant_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.843431"}, {"type": "organize", "description": "整理文档: user_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.843431"}, {"type": "organize", "description": "整理文档: schemas_comprehensive_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.843431"}, {"type": "organize", "description": "整理文档: analytics_service_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.844434"}, {"type": "organize", "description": "整理文档: auth_service_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.867442"}, {"type": "organize", "description": "整理文档: auto_reply_service_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.867946"}, {"type": "organize", "description": "整理文档: message_service_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.867946"}, {"type": "organize", "description": "整理文档: services_comprehensive_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.868960"}, {"type": "organize", "description": "整理文档: session_service_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.868960"}, {"type": "organize", "description": "整理文档: tenant_service_comprehensive_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.868960"}, {"type": "organize", "description": "整理文档: tenant_service_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.869952"}, {"type": "organize", "description": "整理文档: webhook_service_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.869952"}, {"type": "organize", "description": "整理文档: utils_module_technical_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.869952"}, {"type": "organize", "description": "整理文档: ai_features_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.869952"}, {"type": "organize", "description": "整理文档: analytics_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.869952"}, {"type": "organize", "description": "整理文档: api_v1_comprehensive_docs.md -> api", "timestamp": "2025-06-20T16:06:41.871180"}, {"type": "organize", "description": "整理文档: instances_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.871180"}, {"type": "organize", "description": "整理文档: messages_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.871180"}, {"type": "organize", "description": "整理文档: rbac_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.872184"}, {"type": "organize", "description": "整理文档: sessions_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.872184"}, {"type": "organize", "description": "整理文档: tenants_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.872184"}, {"type": "organize", "description": "整理文档: user_roles_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.872184"}, {"type": "organize", "description": "整理文档: webhooks_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.873184"}, {"type": "organize", "description": "整理文档: websocket_docs.md -> modules", "timestamp": "2025-06-20T16:06:41.873184"}, {"type": "organize", "description": "整理文档: 00_PROJECT_OVERVIEW_AI.md -> modules", "timestamp": "2025-06-20T16:06:41.873184"}, {"type": "organize", "description": "整理文档: M8_Completion_Report.md -> modules", "timestamp": "2025-06-20T16:06:41.874185"}, {"type": "organize", "description": "整理文档: M8_Progress_Report.md -> modules", "timestamp": "2025-06-20T16:06:41.874185"}, {"type": "organize", "description": "整理文档: M9_Completion_Report.md -> modules", "timestamp": "2025-06-20T16:06:41.874185"}, {"type": "organize", "description": "整理文档: AstrBot项目文档优化总结报告.md -> modules", "timestamp": "2025-06-20T16:06:41.874185"}, {"type": "organize", "description": "整理文档: POSTGRESQL_TEST_REPORT.md -> testing", "timestamp": "2025-06-20T16:06:41.875192"}, {"type": "organize", "description": "整理文档: PROJECT_MANAGEMENT_SUMMARY.md -> modules", "timestamp": "2025-06-20T16:06:41.875192"}, {"type": "organize", "description": "整理文档: TEST_COVERAGE_REPORT.md -> testing", "timestamp": "2025-06-20T16:06:41.876207"}, {"type": "organize", "description": "整理文档: M8.4_Security_Standards.md -> security", "timestamp": "2025-06-20T16:06:41.877188"}, {"type": "organize", "description": "整理文档: M8.4_Security_Validation_Report.md -> security", "timestamp": "2025-06-20T16:06:41.877188"}, {"type": "create", "description": "创建了文档主索引", "timestamp": "2025-06-20T16:06:41.878200"}]