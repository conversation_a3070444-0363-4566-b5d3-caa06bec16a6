#!/usr/bin/env python3
"""
AstrBot SaaS Platform 启动问题诊断和修复工具
用于识别和解决应用启动过程中的常见问题
"""

import os
import sys
import json
import subprocess
import traceback
from pathlib import Path
from typing import Dict, Any, List, Optional

class StartupDiagnostic:
    """启动问题诊断类"""
    
    def __init__(self):
        self.issues = []
        self.fixes_applied = []
        self.project_root = Path(__file__).parent
        self.env_file = self.project_root / ".env"
    
    def log_issue(self, issue: str) -> None:
        """记录发现的问题"""
        self.issues.append(issue)
        print(f"❌ 发现问题: {issue}")
    
    def log_fix(self, fix: str) -> None:
        """记录应用的修复"""
        self.fixes_applied.append(fix)
        print(f"✅ 已修复: {fix}")
    
    def check_env_file_encoding(self) -> bool:
        """检查.env文件编码"""
        print("\n🔍 检查.env文件编码...")
        
        if not self.env_file.exists():
            self.log_issue(".env文件不存在")
            return False
        
        try:
            # 尝试UTF-8读取
            with open(self.env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            print(f"✅ .env文件UTF-8编码正常 (长度: {len(content)})")
            return True
        except UnicodeDecodeError as e:
            self.log_issue(f".env文件编码错误: {e}")
            return False
    
    def validate_env_variables(self) -> Dict[str, Any]:
        """验证环境变量格式"""
        print("\n🔍 验证环境变量格式...")
        
        env_vars = {}
        problematic_vars = []
        
        try:
            with open(self.env_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key] = value
                        
                        # 检查JSON格式的变量
                        if key in ['BACKEND_CORS_ORIGINS'] and value:
                            try:
                                json.loads(value)
                            except json.JSONDecodeError as e:
                                problematic_vars.append(f"{key} (行{line_num}): {e}")
        except Exception as e:
            self.log_issue(f"读取.env文件失败: {e}")
            return {}
        
        if problematic_vars:
            for var_error in problematic_vars:
                self.log_issue(f"JSON格式错误: {var_error}")
        else:
            print("✅ 环境变量格式验证通过")
        
        return env_vars
    
    def fix_cors_configuration(self) -> None:
        """修复CORS配置"""
        print("\n🔧 修复CORS配置...")
        
        try:
            with open(self.env_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            fixed_lines = []
            cors_fixed = False
            
            for line in lines:
                if line.strip().startswith('BACKEND_CORS_ORIGINS='):
                    # 正确的JSON格式
                    fixed_line = 'BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000"]\n'
                    fixed_lines.append(fixed_line)
                    cors_fixed = True
                else:
                    fixed_lines.append(line)
            
            # 如果没有找到CORS配置，添加它
            if not cors_fixed:
                fixed_lines.append('BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000"]\n')
                cors_fixed = True
            
            if cors_fixed:
                with open(self.env_file, 'w', encoding='utf-8') as f:
                    f.writelines(fixed_lines)
                self.log_fix("CORS配置JSON格式")
        
        except Exception as e:
            self.log_issue(f"修复CORS配置失败: {e}")
    
    def create_minimal_env(self) -> None:
        """创建最小可用的.env文件"""
        print("\n🔧 创建最小可用环境配置...")
        
        minimal_config = '''# AstrBot SaaS Platform - 最小配置
# 数据库配置
DATABASE_URL=postgresql+asyncpg://astrbot:astrbot123@localhost:5432/astrbot_saas
REDIS_URL=redis://:redis123@localhost:6379/0

# 安全配置
SECRET_KEY=dev-secret-key-please-change-in-production
ALGORITHM=HS256

# CORS配置 (注意JSON格式)
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000"]

# 应用配置
PROJECT_NAME=AstrBot SaaS Platform
API_V1_STR=/api/v1
ENVIRONMENT=development

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000

# 日志配置
LOG_LEVEL=INFO
'''
        
        try:
            with open(self.env_file, 'w', encoding='utf-8') as f:
                f.write(minimal_config)
            self.log_fix("创建最小环境配置文件")
        except Exception as e:
            self.log_issue(f"创建环境配置文件失败: {e}")
    
    def test_settings_import(self) -> bool:
        """测试设置导入"""
        print("\n🔍 测试应用设置导入...")
        
        try:
            # 临时添加项目路径到sys.path
            if str(self.project_root) not in sys.path:
                sys.path.insert(0, str(self.project_root))
            
            from app.core.config import settings
            print(f"✅ Settings导入成功")
            print(f"   项目名称: {getattr(settings, 'PROJECT_NAME', 'N/A')}")
            print(f"   数据库URL: {settings.DATABASE_URL}")
            print(f"   CORS源: {settings.BACKEND_CORS_ORIGINS}")
            return True
        except Exception as e:
            self.log_issue(f"Settings导入失败: {e}")
            print(f"详细错误:\n{traceback.format_exc()}")
            return False
    
    def test_app_import(self) -> bool:
        """测试FastAPI应用导入"""
        print("\n🔍 测试FastAPI应用导入...")
        
        try:
            if str(self.project_root) not in sys.path:
                sys.path.insert(0, str(self.project_root))
            
            from app.main import app
            print(f"✅ FastAPI应用导入成功")
            print(f"   应用标题: {app.title}")
            return True
        except Exception as e:
            self.log_issue(f"FastAPI应用导入失败: {e}")
            print(f"详细错误:\n{traceback.format_exc()}")
            return False
    
    def check_dependencies(self) -> None:
        """检查依赖包"""
        print("\n🔍 检查关键依赖包...")
        
        required_packages = [
            'fastapi', 'uvicorn', 'pydantic', 'pydantic-settings',
            'sqlalchemy', 'asyncpg', 'redis', 'python-dotenv'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                print(f"✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"❌ {package} (缺失)")
        
        if missing_packages:
            print(f"\n⚠️ 缺失依赖包: {', '.join(missing_packages)}")
            print("请运行: pip install " + " ".join(missing_packages))
    
    def generate_diagnostic_report(self) -> None:
        """生成诊断报告"""
        print("\n" + "="*60)
        print("🔍 AstrBot SaaS Platform 启动诊断报告")
        print("="*60)
        
        if self.issues:
            print(f"\n❌ 发现 {len(self.issues)} 个问题:")
            for i, issue in enumerate(self.issues, 1):
                print(f"   {i}. {issue}")
        
        if self.fixes_applied:
            print(f"\n✅ 已应用 {len(self.fixes_applied)} 个修复:")
            for i, fix in enumerate(self.fixes_applied, 1):
                print(f"   {i}. {fix}")
        
        if not self.issues:
            print("\n🎉 没有发现问题，应用配置正常！")
    
    def run_diagnostic(self) -> bool:
        """运行完整诊断"""
        print("🔍 开始 AstrBot SaaS Platform 启动诊断...")
        
        # 1. 检查依赖
        self.check_dependencies()
        
        # 2. 检查.env文件编码
        if not self.check_env_file_encoding():
            # 创建新的环境文件
            self.create_minimal_env()
        
        # 3. 验证环境变量
        env_vars = self.validate_env_variables()
        if not env_vars:
            self.create_minimal_env()
        
        # 4. 修复CORS配置
        self.fix_cors_configuration()
        
        # 5. 测试设置导入
        settings_ok = self.test_settings_import()
        
        # 6. 测试应用导入
        app_ok = settings_ok and self.test_app_import()
        
        # 7. 生成报告
        self.generate_diagnostic_report()
        
        return app_ok

def main():
    """主函数"""
    print("🚀 AstrBot SaaS Platform 启动问题诊断工具")
    print("="*50)
    
    diagnostic = StartupDiagnostic()
    success = diagnostic.run_diagnostic()
    
    if success:
        print("\n🎉 诊断完成，应用可以正常启动！")
        print("建议使用以下命令启动:")
        print("   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")
    else:
        print("\n⚠️ 仍有问题需要解决，请查看上述错误信息。")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main()) 