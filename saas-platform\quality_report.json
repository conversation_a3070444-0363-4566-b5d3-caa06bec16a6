{"timestamp": "2025-06-20 15:50:05", "code_quality": {"file_count": 192, "total_lines": 57469, "python_files": [{"path": "cleanup_redundancy.py", "lines": 273, "functions": 7, "classes": 1, "complexity": 17, "has_docstring": true, "imports": 8}, {"path": "code_redundancy_analyzer.py", "lines": 639, "functions": 18, "classes": 2, "complexity": 90, "has_docstring": true, "imports": 12}, {"path": "debug_api.py", "lines": 72, "functions": 1, "classes": 0, "complexity": 4, "has_docstring": true, "imports": 7}, {"path": "debug_tenant_auth.py", "lines": 60, "functions": 0, "classes": 0, "complexity": 4, "has_docstring": true, "imports": 6}, {"path": "deploy_test.py", "lines": 426, "functions": 14, "classes": 1, "complexity": 37, "has_docstring": true, "imports": 11}, {"path": "deploy_test_windows.py", "lines": 453, "functions": 16, "classes": 1, "complexity": 39, "has_docstring": true, "imports": 11}, {"path": "fix_startup_issues.py", "lines": 282, "functions": 13, "classes": 1, "complexity": 41, "has_docstring": true, "imports": 9}, {"path": "install_postgresql_windows.py", "lines": 0, "functions": 0, "classes": 0, "complexity": 0, "has_docstring": false, "imports": 0}, {"path": "integration_db_test.py", "lines": 585, "functions": 4, "classes": 1, "complexity": 19, "has_docstring": true, "imports": 13}, {"path": "integration_db_test_windows.py", "lines": 579, "functions": 4, "classes": 1, "complexity": 20, "has_docstring": true, "imports": 10}, {"path": "optimize_test_code.py", "lines": 645, "functions": 12, "classes": 1, "complexity": 42, "has_docstring": true, "imports": 7}, {"path": "quality_check.py", "lines": 807, "functions": 33, "classes": 1, "complexity": 106, "has_docstring": true, "imports": 11}, {"path": "quality_fix.py", "lines": 550, "functions": 19, "classes": 1, "complexity": 68, "has_docstring": true, "imports": 8}, {"path": "quick_db_test.py", "lines": 325, "functions": 5, "classes": 1, "complexity": 24, "has_docstring": true, "imports": 7}, {"path": "run_tests.py", "lines": 0, "functions": 0, "classes": 0, "complexity": 0, "has_docstring": false, "imports": 0}, {"path": "simple_start.py", "lines": 51, "functions": 1, "classes": 0, "complexity": 3, "has_docstring": true, "imports": 6}, {"path": "start_server.py", "lines": 113, "functions": 4, "classes": 0, "complexity": 7, "has_docstring": true, "imports": 10}, {"path": "test_api.py", "lines": 99, "functions": 1, "classes": 0, "complexity": 11, "has_docstring": true, "imports": 2}, {"path": "app\\main.py", "lines": 47, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 4}, {"path": "app\\__init__.py", "lines": 9, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 0}, {"path": "backup_quality_fix\\debug_api.py", "lines": 72, "functions": 1, "classes": 0, "complexity": 4, "has_docstring": true, "imports": 7}, {"path": "backup_quality_fix\\debug_tenant_auth.py", "lines": 60, "functions": 0, "classes": 0, "complexity": 4, "has_docstring": true, "imports": 6}, {"path": "backup_quality_fix\\deploy_test_windows.py", "lines": 453, "functions": 16, "classes": 1, "complexity": 39, "has_docstring": true, "imports": 11}, {"path": "backup_quality_fix\\install_postgresql_windows.py", "lines": 511, "functions": 10, "classes": 1, "complexity": 42, "has_docstring": true, "imports": 13}, {"path": "backup_quality_fix\\quality_check.py", "lines": 807, "functions": 33, "classes": 1, "complexity": 106, "has_docstring": true, "imports": 11}, {"path": "backup_quality_fix\\run_tests.py", "lines": 95, "functions": 2, "classes": 0, "complexity": 12, "has_docstring": true, "imports": 3}, {"path": "scripts\\analyze_api_docs.py", "lines": 267, "functions": 9, "classes": 1, "complexity": 44, "has_docstring": true, "imports": 5}, {"path": "scripts\\analyze_warnings.py", "lines": 416, "functions": 12, "classes": 1, "complexity": 41, "has_docstring": true, "imports": 6}, {"path": "scripts\\check_api_docs_simple.py", "lines": 136, "functions": 3, "classes": 0, "complexity": 30, "has_docstring": true, "imports": 4}, {"path": "scripts\\code_quality_check.py", "lines": 776, "functions": 24, "classes": 4, "complexity": 92, "has_docstring": true, "imports": 10}, {"path": "scripts\\comprehensive_api_check.py", "lines": 345, "functions": 13, "classes": 1, "complexity": 54, "has_docstring": true, "imports": 4}, {"path": "scripts\\coverage_analysis.py", "lines": 366, "functions": 10, "classes": 1, "complexity": 66, "has_docstring": true, "imports": 7}, {"path": "scripts\\debug_api_detection.py", "lines": 77, "functions": 4, "classes": 1, "complexity": 16, "has_docstring": true, "imports": 2}, {"path": "scripts\\fix_integration_tests.py", "lines": 215, "functions": 5, "classes": 0, "complexity": 20, "has_docstring": true, "imports": 4}, {"path": "scripts\\fix_type_annotations.py", "lines": 178, "functions": 8, "classes": 1, "complexity": 24, "has_docstring": true, "imports": 4}, {"path": "scripts\\generate_dashboards.py", "lines": 462, "functions": 11, "classes": 3, "complexity": 42, "has_docstring": true, "imports": 5}, {"path": "scripts\\optimize_async_functions.py", "lines": 172, "functions": 8, "classes": 1, "complexity": 30, "has_docstring": true, "imports": 4}, {"path": "scripts\\run_quality_checks.py", "lines": 478, "functions": 11, "classes": 3, "complexity": 28, "has_docstring": true, "imports": 8}, {"path": "scripts\\security_check.py", "lines": 501, "functions": 9, "classes": 1, "complexity": 47, "has_docstring": true, "imports": 9}, {"path": "scripts\\security_scan.py", "lines": 869, "functions": 6, "classes": 1, "complexity": 80, "has_docstring": true, "imports": 13}, {"path": "tests\\conftest.py", "lines": 514, "functions": 16, "classes": 0, "complexity": 6, "has_docstring": true, "imports": 37}, {"path": "app\\api\\deps.py", "lines": 457, "functions": 9, "classes": 4, "complexity": 30, "has_docstring": true, "imports": 10}, {"path": "app\\api\\__init__.py", "lines": 1, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 0}, {"path": "app\\core\\database.py", "lines": 187, "functions": 2, "classes": 1, "complexity": 9, "has_docstring": true, "imports": 7}, {"path": "app\\core\\middleware.py", "lines": 449, "functions": 5, "classes": 4, "complexity": 17, "has_docstring": true, "imports": 15}, {"path": "app\\core\\permissions.py", "lines": 482, "functions": 20, "classes": 3, "complexity": 19, "has_docstring": true, "imports": 12}, {"path": "app\\core\\security.py", "lines": 423, "functions": 18, "classes": 4, "complexity": 22, "has_docstring": true, "imports": 8}, {"path": "app\\core\\__init__.py", "lines": 1, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 0}, {"path": "app\\models\\message.py", "lines": 421, "functions": 16, "classes": 4, "complexity": 12, "has_docstring": true, "imports": 13}, {"path": "app\\models\\role.py", "lines": 160, "functions": 4, "classes": 2, "complexity": 7, "has_docstring": true, "imports": 9}, {"path": "app\\models\\session.py", "lines": 370, "functions": 16, "classes": 3, "complexity": 10, "has_docstring": true, "imports": 14}, {"path": "app\\models\\tenant.py", "lines": 269, "functions": 16, "classes": 3, "complexity": 18, "has_docstring": true, "imports": 14}, {"path": "app\\models\\user.py", "lines": 332, "functions": 16, "classes": 1, "complexity": 24, "has_docstring": true, "imports": 10}, {"path": "app\\models\\__init__.py", "lines": 0, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": false, "imports": 0}, {"path": "app\\schemas\\analytics.py", "lines": 142, "functions": 0, "classes": 11, "complexity": 1, "has_docstring": true, "imports": 4}, {"path": "app\\schemas\\auth.py", "lines": 332, "functions": 3, "classes": 15, "complexity": 7, "has_docstring": true, "imports": 4}, {"path": "app\\schemas\\common.py", "lines": 140, "functions": 1, "classes": 6, "complexity": 1, "has_docstring": true, "imports": 3}, {"path": "app\\schemas\\message.py", "lines": 214, "functions": 5, "classes": 13, "complexity": 1, "has_docstring": true, "imports": 5}, {"path": "app\\schemas\\session.py", "lines": 85, "functions": 0, "classes": 6, "complexity": 1, "has_docstring": true, "imports": 5}, {"path": "app\\schemas\\tenant.py", "lines": 203, "functions": 0, "classes": 10, "complexity": 1, "has_docstring": true, "imports": 5}, {"path": "app\\schemas\\user.py", "lines": 275, "functions": 1, "classes": 10, "complexity": 4, "has_docstring": true, "imports": 4}, {"path": "app\\schemas\\__init__.py", "lines": 0, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": false, "imports": 0}, {"path": "app\\services\\agent_suggestion_service.py", "lines": 709, "functions": 7, "classes": 1, "complexity": 35, "has_docstring": true, "imports": 11}, {"path": "app\\services\\analytics_service.py", "lines": 570, "functions": 2, "classes": 1, "complexity": 43, "has_docstring": true, "imports": 11}, {"path": "app\\services\\auth_service.py", "lines": 452, "functions": 3, "classes": 3, "complexity": 29, "has_docstring": true, "imports": 11}, {"path": "app\\services\\auto_reply_service.py", "lines": 410, "functions": 6, "classes": 1, "complexity": 28, "has_docstring": true, "imports": 13}, {"path": "app\\services\\context_manager.py", "lines": 422, "functions": 7, "classes": 1, "complexity": 26, "has_docstring": true, "imports": 9}, {"path": "app\\services\\instance_auth_service.py", "lines": 623, "functions": 3, "classes": 1, "complexity": 42, "has_docstring": true, "imports": 11}, {"path": "app\\services\\instance_config_service.py", "lines": 619, "functions": 3, "classes": 1, "complexity": 47, "has_docstring": true, "imports": 8}, {"path": "app\\services\\message_service.py", "lines": 687, "functions": 2, "classes": 1, "complexity": 39, "has_docstring": true, "imports": 14}, {"path": "app\\services\\rbac_service.py", "lines": 589, "functions": 1, "classes": 1, "complexity": 33, "has_docstring": true, "imports": 8}, {"path": "app\\services\\session_service.py", "lines": 508, "functions": 3, "classes": 1, "complexity": 29, "has_docstring": true, "imports": 12}, {"path": "app\\services\\session_summary_service.py", "lines": 781, "functions": 18, "classes": 1, "complexity": 39, "has_docstring": true, "imports": 15}, {"path": "app\\services\\tenant_service.py", "lines": 485, "functions": 2, "classes": 1, "complexity": 28, "has_docstring": true, "imports": 12}, {"path": "app\\services\\webhook_service.py", "lines": 327, "functions": 2, "classes": 2, "complexity": 25, "has_docstring": true, "imports": 15}, {"path": "app\\services\\__init__.py", "lines": 0, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": false, "imports": 0}, {"path": "app\\utils\\context_vars.py", "lines": 47, "functions": 3, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 3}, {"path": "app\\utils\\logging.py", "lines": 163, "functions": 12, "classes": 2, "complexity": 10, "has_docstring": true, "imports": 7}, {"path": "app\\utils\\__init__.py", "lines": 0, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": false, "imports": 0}, {"path": "app\\api\\deps\\__init__.py", "lines": 273, "functions": 0, "classes": 0, "complexity": 19, "has_docstring": true, "imports": 14}, {"path": "app\\api\\v1\\ai_features.py", "lines": 476, "functions": 2, "classes": 5, "complexity": 18, "has_docstring": true, "imports": 17}, {"path": "app\\api\\v1\\analytics.py", "lines": 475, "functions": 0, "classes": 0, "complexity": 12, "has_docstring": true, "imports": 13}, {"path": "app\\api\\v1\\instances.py", "lines": 555, "functions": 0, "classes": 4, "complexity": 12, "has_docstring": true, "imports": 12}, {"path": "app\\api\\v1\\messages.py", "lines": 427, "functions": 0, "classes": 0, "complexity": 7, "has_docstring": true, "imports": 11}, {"path": "app\\api\\v1\\rbac.py", "lines": 417, "functions": 0, "classes": 0, "complexity": 9, "has_docstring": true, "imports": 12}, {"path": "app\\api\\v1\\sessions.py", "lines": 537, "functions": 1, "classes": 0, "complexity": 23, "has_docstring": true, "imports": 14}, {"path": "app\\api\\v1\\tenants.py", "lines": 608, "functions": 1, "classes": 0, "complexity": 30, "has_docstring": true, "imports": 14}, {"path": "app\\api\\v1\\user_roles.py", "lines": 313, "functions": 0, "classes": 0, "complexity": 12, "has_docstring": true, "imports": 12}, {"path": "app\\api\\v1\\webhooks.py", "lines": 352, "functions": 1, "classes": 0, "complexity": 19, "has_docstring": true, "imports": 12}, {"path": "app\\api\\v1\\websocket.py", "lines": 353, "functions": 6, "classes": 1, "complexity": 30, "has_docstring": true, "imports": 11}, {"path": "app\\api\\v1\\__init__.py", "lines": 74, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 12}, {"path": "app\\core\\auth\\__init__.py", "lines": 0, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": false, "imports": 0}, {"path": "app\\core\\config\\settings.py", "lines": 291, "functions": 17, "classes": 1, "complexity": 24, "has_docstring": true, "imports": 6}, {"path": "app\\core\\config\\__init__.py", "lines": 5, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 1}, {"path": "app\\services\\llm\\base_provider.py", "lines": 265, "functions": 10, "classes": 8, "complexity": 10, "has_docstring": true, "imports": 5}, {"path": "app\\services\\llm\\dify_provider.py", "lines": 400, "functions": 11, "classes": 1, "complexity": 38, "has_docstring": true, "imports": 9}, {"path": "app\\services\\llm\\mock_provider.py", "lines": 187, "functions": 8, "classes": 1, "complexity": 6, "has_docstring": true, "imports": 5}, {"path": "app\\services\\llm\\openai_provider.py", "lines": 406, "functions": 10, "classes": 1, "complexity": 36, "has_docstring": true, "imports": 11}, {"path": "app\\services\\llm\\__init__.py", "lines": 11, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 3}, {"path": "app\\services\\session_summary\\summary_analyzer.py", "lines": 464, "functions": 11, "classes": 1, "complexity": 54, "has_docstring": true, "imports": 5}, {"path": "backup_quality_fix\\tests\\conftest.py", "lines": 514, "functions": 16, "classes": 0, "complexity": 6, "has_docstring": true, "imports": 37}, {"path": "backup_quality_fix\\tests\\integration\\test_tenant_api_integration.py", "lines": 642, "functions": 0, "classes": 1, "complexity": 13, "has_docstring": true, "imports": 26}, {"path": "backup_quality_fix\\tests\\performance\\performance_test_suite.py", "lines": 688, "functions": 12, "classes": 4, "complexity": 45, "has_docstring": true, "imports": 14}, {"path": "backup_quality_fix\\tests\\unit\\test_api_endpoints.py", "lines": 625, "functions": 42, "classes": 9, "complexity": 20, "has_docstring": true, "imports": 12}, {"path": "backup_quality_fix\\tests\\unit\\test_api_layers.py", "lines": 291, "functions": 17, "classes": 7, "complexity": 10, "has_docstring": true, "imports": 8}, {"path": "backup_quality_fix\\tests\\unit\\test_api_layer_comprehensive.py", "lines": 319, "functions": 18, "classes": 8, "complexity": 7, "has_docstring": true, "imports": 18}, {"path": "backup_quality_fix\\tests\\unit\\test_coverage_boost.py", "lines": 448, "functions": 19, "classes": 8, "complexity": 8, "has_docstring": true, "imports": 24}, {"path": "backup_quality_fix\\tests\\unit\\test_coverage_boost_services.py", "lines": 484, "functions": 24, "classes": 9, "complexity": 15, "has_docstring": true, "imports": 20}, {"path": "backup_quality_fix\\tests\\unit\\test_instance_auth_service.py", "lines": 148, "functions": 3, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 6}, {"path": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "lines": 634, "functions": 14, "classes": 4, "complexity": 10, "has_docstring": true, "imports": 19}, {"path": "backup_quality_fix\\tests\\unit\\test_service_layer_fixes.py", "lines": 307, "functions": 4, "classes": 1, "complexity": 5, "has_docstring": true, "imports": 20}, {"path": "backup_quality_fix\\tests\\unit\\test_tenant_model.py", "lines": 196, "functions": 12, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 2}, {"path": "backup_quality_fix\\tests\\unit\\test_tenant_service.py", "lines": 511, "functions": 4, "classes": 4, "complexity": 21, "has_docstring": true, "imports": 10}, {"path": "backup_quality_fix\\tests\\unit\\test_tenant_service_improved.py", "lines": 301, "functions": 5, "classes": 1, "complexity": 5, "has_docstring": true, "imports": 7}, {"path": "backup_quality_fix\\tests\\unit\\_test_auth_service.py", "lines": 405, "functions": 7, "classes": 1, "complexity": 17, "has_docstring": true, "imports": 8}, {"path": "backup_quality_fix\\tests\\unit\\_test_high_value_services.py", "lines": 419, "functions": 11, "classes": 6, "complexity": 11, "has_docstring": true, "imports": 16}, {"path": "tests\\e2e\\business_process_test.py", "lines": 801, "functions": 4, "classes": 6, "complexity": 36, "has_docstring": true, "imports": 11}, {"path": "tests\\e2e\\test_business_flows.py", "lines": 633, "functions": 1, "classes": 1, "complexity": 22, "has_docstring": true, "imports": 15}, {"path": "tests\\e2e\\test_customer_service_flow.py", "lines": 584, "functions": 0, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 9}, {"path": "tests\\e2e\\__init__.py", "lines": 51, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 1}, {"path": "tests\\helpers\\assertion_helpers.py", "lines": 80, "functions": 10, "classes": 1, "complexity": 4, "has_docstring": true, "imports": 3}, {"path": "tests\\helpers\\mock_helpers.py", "lines": 120, "functions": 13, "classes": 2, "complexity": 1, "has_docstring": true, "imports": 3}, {"path": "tests\\helpers\\mock_services.py", "lines": 326, "functions": 13, "classes": 6, "complexity": 15, "has_docstring": true, "imports": 5}, {"path": "tests\\helpers\\smart_test_selector.py", "lines": 345, "functions": 14, "classes": 2, "complexity": 70, "has_docstring": true, "imports": 7}, {"path": "tests\\helpers\\test_data_factory.py", "lines": 455, "functions": 19, "classes": 1, "complexity": 6, "has_docstring": true, "imports": 10}, {"path": "tests\\integration\\test_analytics_api.py", "lines": 139, "functions": 0, "classes": 1, "complexity": 5, "has_docstring": true, "imports": 10}, {"path": "tests\\integration\\test_basic_integration.py", "lines": 29, "functions": 0, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 2}, {"path": "tests\\integration\\test_instances_api.py", "lines": 93, "functions": 0, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 6}, {"path": "tests\\integration\\test_messages_api.py", "lines": 104, "functions": 0, "classes": 1, "complexity": 2, "has_docstring": true, "imports": 9}, {"path": "tests\\integration\\test_sessions_api.py", "lines": 128, "functions": 0, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 7}, {"path": "tests\\integration\\test_tenant_api_integration.py", "lines": 642, "functions": 0, "classes": 1, "complexity": 13, "has_docstring": true, "imports": 26}, {"path": "tests\\integration\\test_webhooks_api.py", "lines": 115, "functions": 0, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 9}, {"path": "tests\\performance\\baseline_manager.py", "lines": 392, "functions": 17, "classes": 5, "complexity": 26, "has_docstring": true, "imports": 9}, {"path": "tests\\performance\\basic_performance_test.py", "lines": 0, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": false, "imports": 0}, {"path": "tests\\performance\\load_test.py", "lines": 320, "functions": 18, "classes": 5, "complexity": 19, "has_docstring": true, "imports": 2}, {"path": "tests\\performance\\performance_baseline_test.py", "lines": 314, "functions": 11, "classes": 1, "complexity": 13, "has_docstring": true, "imports": 8}, {"path": "tests\\performance\\performance_test_suite.py", "lines": 688, "functions": 12, "classes": 4, "complexity": 45, "has_docstring": true, "imports": 14}, {"path": "tests\\performance\\test_performance_suite.py", "lines": 175, "functions": 6, "classes": 1, "complexity": 14, "has_docstring": true, "imports": 4}, {"path": "tests\\performance\\__init__.py", "lines": 37, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 1}, {"path": "tests\\security\\test_k8s_security_policies.py", "lines": 125, "functions": 8, "classes": 1, "complexity": 14, "has_docstring": false, "imports": 3}, {"path": "tests\\unit\\test_additional_services.py", "lines": 454, "functions": 20, "classes": 5, "complexity": 9, "has_docstring": true, "imports": 13}, {"path": "tests\\unit\\test_agent_suggestion_service.py", "lines": 17, "functions": 1, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 3}, {"path": "tests\\unit\\test_ai_features.py", "lines": 23, "functions": 2, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 4}, {"path": "tests\\unit\\test_analytics.py", "lines": 22, "functions": 2, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 3}, {"path": "tests\\unit\\test_analytics_service.py", "lines": 144, "functions": 3, "classes": 1, "complexity": 6, "has_docstring": true, "imports": 6}, {"path": "tests\\unit\\test_api_endpoints.py", "lines": 625, "functions": 42, "classes": 9, "complexity": 20, "has_docstring": true, "imports": 12}, {"path": "tests\\unit\\test_api_layers.py", "lines": 291, "functions": 17, "classes": 7, "complexity": 10, "has_docstring": true, "imports": 8}, {"path": "tests\\unit\\test_api_layer_comprehensive.py", "lines": 319, "functions": 18, "classes": 8, "complexity": 7, "has_docstring": true, "imports": 18}, {"path": "tests\\unit\\test_auth.py", "lines": 22, "functions": 2, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 3}, {"path": "tests\\unit\\test_auto_reply_service.py", "lines": 156, "functions": 3, "classes": 1, "complexity": 6, "has_docstring": true, "imports": 6}, {"path": "tests\\unit\\test_common.py", "lines": 22, "functions": 2, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 3}, {"path": "tests\\unit\\test_config.py", "lines": 236, "functions": 15, "classes": 2, "complexity": 14, "has_docstring": true, "imports": 5}, {"path": "tests\\unit\\test_context_manager.py", "lines": 17, "functions": 1, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 3}, {"path": "tests\\unit\\test_coverage_boost.py", "lines": 448, "functions": 19, "classes": 8, "complexity": 8, "has_docstring": true, "imports": 24}, {"path": "tests\\unit\\test_coverage_boost_services.py", "lines": 484, "functions": 24, "classes": 9, "complexity": 15, "has_docstring": true, "imports": 20}, {"path": "tests\\unit\\test_database.py", "lines": 6, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 2}, {"path": "tests\\unit\\test_deps.py", "lines": 24, "functions": 2, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 4}, {"path": "tests\\unit\\test_instances.py", "lines": 23, "functions": 2, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 4}, {"path": "tests\\unit\\test_instance_auth_service.py", "lines": 148, "functions": 3, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 6}, {"path": "tests\\unit\\test_logging.py", "lines": 6, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 2}, {"path": "tests\\unit\\test_message.py", "lines": 401, "functions": 13, "classes": 1, "complexity": 3, "has_docstring": true, "imports": 5}, {"path": "tests\\unit\\test_messages.py", "lines": 23, "functions": 2, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 4}, {"path": "tests\\unit\\test_message_service.py", "lines": 529, "functions": 7, "classes": 1, "complexity": 13, "has_docstring": true, "imports": 11}, {"path": "tests\\unit\\test_middleware.py", "lines": 6, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 2}, {"path": "tests\\unit\\test_mock_strategies.py", "lines": 343, "functions": 2, "classes": 1, "complexity": 7, "has_docstring": true, "imports": 18}, {"path": "tests\\unit\\test_models.py", "lines": 418, "functions": 25, "classes": 6, "complexity": 2, "has_docstring": true, "imports": 8}, {"path": "tests\\unit\\test_permissions.py", "lines": 6, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 2}, {"path": "tests\\unit\\test_rbac.py", "lines": 23, "functions": 2, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 4}, {"path": "tests\\unit\\test_rbac_service.py", "lines": 57, "functions": 3, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 5}, {"path": "tests\\unit\\test_role.py", "lines": 22, "functions": 2, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 3}, {"path": "tests\\unit\\test_schemas.py", "lines": 396, "functions": 21, "classes": 7, "complexity": 12, "has_docstring": true, "imports": 11}, {"path": "tests\\unit\\test_scripts.py", "lines": 218, "functions": 18, "classes": 7, "complexity": 18, "has_docstring": true, "imports": 17}, {"path": "tests\\unit\\test_security.py", "lines": 6, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 2}, {"path": "tests\\unit\\test_services_realistic.py", "lines": 634, "functions": 14, "classes": 4, "complexity": 10, "has_docstring": true, "imports": 19}, {"path": "tests\\unit\\test_service_layer_fixes.py", "lines": 307, "functions": 4, "classes": 1, "complexity": 5, "has_docstring": true, "imports": 20}, {"path": "tests\\unit\\test_session.py", "lines": 22, "functions": 2, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 3}, {"path": "tests\\unit\\test_sessions.py", "lines": 23, "functions": 2, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 4}, {"path": "tests\\unit\\test_session_service.py", "lines": 253, "functions": 6, "classes": 3, "complexity": 11, "has_docstring": true, "imports": 8}, {"path": "tests\\unit\\test_session_summary_service_professional.py", "lines": 314, "functions": 8, "classes": 2, "complexity": 17, "has_docstring": true, "imports": 10}, {"path": "tests\\unit\\test_session_summary_service_refactored.py", "lines": 409, "functions": 9, "classes": 3, "complexity": 14, "has_docstring": true, "imports": 11}, {"path": "tests\\unit\\test_settings.py", "lines": 6, "functions": 0, "classes": 0, "complexity": 1, "has_docstring": true, "imports": 2}, {"path": "tests\\unit\\test_tenant_model.py", "lines": 196, "functions": 12, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 2}, {"path": "tests\\unit\\test_tenant_service.py", "lines": 511, "functions": 4, "classes": 4, "complexity": 21, "has_docstring": true, "imports": 10}, {"path": "tests\\unit\\test_tenant_service_improved.py", "lines": 301, "functions": 5, "classes": 1, "complexity": 5, "has_docstring": true, "imports": 7}, {"path": "tests\\unit\\test_tenant_service_simplified.py", "lines": 253, "functions": 4, "classes": 1, "complexity": 10, "has_docstring": true, "imports": 7}, {"path": "tests\\unit\\test_user.py", "lines": 22, "functions": 2, "classes": 1, "complexity": 1, "has_docstring": true, "imports": 3}, {"path": "tests\\unit\\test_user_model.py", "lines": 293, "functions": 12, "classes": 1, "complexity": 5, "has_docstring": true, "imports": 3}, {"path": "tests\\unit\\test_utils.py", "lines": 234, "functions": 12, "classes": 4, "complexity": 2, "has_docstring": true, "imports": 7}, {"path": "tests\\unit\\test_webhook_service.py", "lines": 295, "functions": 4, "classes": 2, "complexity": 12, "has_docstring": true, "imports": 7}, {"path": "tests\\unit\\_test_auth_service.py", "lines": 405, "functions": 7, "classes": 1, "complexity": 17, "has_docstring": true, "imports": 8}, {"path": "tests\\unit\\_test_high_value_services.py", "lines": 419, "functions": 11, "classes": 6, "complexity": 11, "has_docstring": true, "imports": 16}], "complexity_issues": [{"file": "cleanup_redundancy.py", "complexity": 17}, {"file": "code_redundancy_analyzer.py", "complexity": 90}, {"file": "deploy_test.py", "complexity": 37}, {"file": "deploy_test_windows.py", "complexity": 39}, {"file": "fix_startup_issues.py", "complexity": 41}, {"file": "integration_db_test.py", "complexity": 19}, {"file": "integration_db_test_windows.py", "complexity": 20}, {"file": "optimize_test_code.py", "complexity": 42}, {"file": "quality_check.py", "complexity": 106}, {"file": "quality_fix.py", "complexity": 68}, {"file": "quick_db_test.py", "complexity": 24}, {"file": "test_api.py", "complexity": 11}, {"file": "backup_quality_fix\\deploy_test_windows.py", "complexity": 39}, {"file": "backup_quality_fix\\install_postgresql_windows.py", "complexity": 42}, {"file": "backup_quality_fix\\quality_check.py", "complexity": 106}, {"file": "backup_quality_fix\\run_tests.py", "complexity": 12}, {"file": "scripts\\analyze_api_docs.py", "complexity": 44}, {"file": "scripts\\analyze_warnings.py", "complexity": 41}, {"file": "scripts\\check_api_docs_simple.py", "complexity": 30}, {"file": "scripts\\code_quality_check.py", "complexity": 92}, {"file": "scripts\\comprehensive_api_check.py", "complexity": 54}, {"file": "scripts\\coverage_analysis.py", "complexity": 66}, {"file": "scripts\\debug_api_detection.py", "complexity": 16}, {"file": "scripts\\fix_integration_tests.py", "complexity": 20}, {"file": "scripts\\fix_type_annotations.py", "complexity": 24}, {"file": "scripts\\generate_dashboards.py", "complexity": 42}, {"file": "scripts\\optimize_async_functions.py", "complexity": 30}, {"file": "scripts\\run_quality_checks.py", "complexity": 28}, {"file": "scripts\\security_check.py", "complexity": 47}, {"file": "scripts\\security_scan.py", "complexity": 80}, {"file": "app\\api\\deps.py", "complexity": 30}, {"file": "app\\core\\middleware.py", "complexity": 17}, {"file": "app\\core\\permissions.py", "complexity": 19}, {"file": "app\\core\\security.py", "complexity": 22}, {"file": "app\\models\\message.py", "complexity": 12}, {"file": "app\\models\\tenant.py", "complexity": 18}, {"file": "app\\models\\user.py", "complexity": 24}, {"file": "app\\services\\agent_suggestion_service.py", "complexity": 35}, {"file": "app\\services\\analytics_service.py", "complexity": 43}, {"file": "app\\services\\auth_service.py", "complexity": 29}, {"file": "app\\services\\auto_reply_service.py", "complexity": 28}, {"file": "app\\services\\context_manager.py", "complexity": 26}, {"file": "app\\services\\instance_auth_service.py", "complexity": 42}, {"file": "app\\services\\instance_config_service.py", "complexity": 47}, {"file": "app\\services\\message_service.py", "complexity": 39}, {"file": "app\\services\\rbac_service.py", "complexity": 33}, {"file": "app\\services\\session_service.py", "complexity": 29}, {"file": "app\\services\\session_summary_service.py", "complexity": 39}, {"file": "app\\services\\tenant_service.py", "complexity": 28}, {"file": "app\\services\\webhook_service.py", "complexity": 25}, {"file": "app\\api\\deps\\__init__.py", "complexity": 19}, {"file": "app\\api\\v1\\ai_features.py", "complexity": 18}, {"file": "app\\api\\v1\\analytics.py", "complexity": 12}, {"file": "app\\api\\v1\\instances.py", "complexity": 12}, {"file": "app\\api\\v1\\sessions.py", "complexity": 23}, {"file": "app\\api\\v1\\tenants.py", "complexity": 30}, {"file": "app\\api\\v1\\user_roles.py", "complexity": 12}, {"file": "app\\api\\v1\\webhooks.py", "complexity": 19}, {"file": "app\\api\\v1\\websocket.py", "complexity": 30}, {"file": "app\\core\\config\\settings.py", "complexity": 24}, {"file": "app\\services\\llm\\dify_provider.py", "complexity": 38}, {"file": "app\\services\\llm\\openai_provider.py", "complexity": 36}, {"file": "app\\services\\session_summary\\summary_analyzer.py", "complexity": 54}, {"file": "backup_quality_fix\\tests\\integration\\test_tenant_api_integration.py", "complexity": 13}, {"file": "backup_quality_fix\\tests\\performance\\performance_test_suite.py", "complexity": 45}, {"file": "backup_quality_fix\\tests\\unit\\test_api_endpoints.py", "complexity": 20}, {"file": "backup_quality_fix\\tests\\unit\\test_coverage_boost_services.py", "complexity": 15}, {"file": "backup_quality_fix\\tests\\unit\\test_tenant_service.py", "complexity": 21}, {"file": "backup_quality_fix\\tests\\unit\\_test_auth_service.py", "complexity": 17}, {"file": "backup_quality_fix\\tests\\unit\\_test_high_value_services.py", "complexity": 11}, {"file": "tests\\e2e\\business_process_test.py", "complexity": 36}, {"file": "tests\\e2e\\test_business_flows.py", "complexity": 22}, {"file": "tests\\helpers\\mock_services.py", "complexity": 15}, {"file": "tests\\helpers\\smart_test_selector.py", "complexity": 70}, {"file": "tests\\integration\\test_tenant_api_integration.py", "complexity": 13}, {"file": "tests\\performance\\baseline_manager.py", "complexity": 26}, {"file": "tests\\performance\\load_test.py", "complexity": 19}, {"file": "tests\\performance\\performance_baseline_test.py", "complexity": 13}, {"file": "tests\\performance\\performance_test_suite.py", "complexity": 45}, {"file": "tests\\performance\\test_performance_suite.py", "complexity": 14}, {"file": "tests\\security\\test_k8s_security_policies.py", "complexity": 14}, {"file": "tests\\unit\\test_api_endpoints.py", "complexity": 20}, {"file": "tests\\unit\\test_config.py", "complexity": 14}, {"file": "tests\\unit\\test_coverage_boost_services.py", "complexity": 15}, {"file": "tests\\unit\\test_message_service.py", "complexity": 13}, {"file": "tests\\unit\\test_schemas.py", "complexity": 12}, {"file": "tests\\unit\\test_scripts.py", "complexity": 18}, {"file": "tests\\unit\\test_session_service.py", "complexity": 11}, {"file": "tests\\unit\\test_session_summary_service_professional.py", "complexity": 17}, {"file": "tests\\unit\\test_session_summary_service_refactored.py", "complexity": 14}, {"file": "tests\\unit\\test_tenant_service.py", "complexity": 21}, {"file": "tests\\unit\\test_webhook_service.py", "complexity": 12}, {"file": "tests\\unit\\_test_auth_service.py", "complexity": 17}, {"file": "tests\\unit\\_test_high_value_services.py", "complexity": 11}], "style_issues": [{"file": "code_redundancy_analyzer.py", "issue": "文件过长 (639 行)"}, {"file": "integration_db_test.py", "issue": "文件过长 (585 行)"}, {"file": "integration_db_test_windows.py", "issue": "文件过长 (579 行)"}, {"file": "optimize_test_code.py", "issue": "文件过长 (645 行)"}, {"file": "quality_check.py", "issue": "文件过长 (807 行)"}, {"file": "quality_fix.py", "issue": "文件过长 (550 行)"}, {"file": "backup_quality_fix\\install_postgresql_windows.py", "issue": "文件过长 (511 行)"}, {"file": "backup_quality_fix\\quality_check.py", "issue": "文件过长 (807 行)"}, {"file": "scripts\\code_quality_check.py", "issue": "文件过长 (776 行)"}, {"file": "scripts\\security_check.py", "issue": "文件过长 (501 行)"}, {"file": "scripts\\security_scan.py", "issue": "文件过长 (869 行)"}, {"file": "tests\\conftest.py", "issue": "文件过长 (514 行)"}, {"file": "app\\services\\agent_suggestion_service.py", "issue": "文件过长 (709 行)"}, {"file": "app\\services\\analytics_service.py", "issue": "文件过长 (570 行)"}, {"file": "app\\services\\instance_auth_service.py", "issue": "文件过长 (623 行)"}, {"file": "app\\services\\instance_config_service.py", "issue": "文件过长 (619 行)"}, {"file": "app\\services\\message_service.py", "issue": "文件过长 (687 行)"}, {"file": "app\\services\\rbac_service.py", "issue": "文件过长 (589 行)"}, {"file": "app\\services\\session_service.py", "issue": "文件过长 (508 行)"}, {"file": "app\\services\\session_summary_service.py", "issue": "文件过长 (781 行)"}, {"file": "app\\api\\v1\\instances.py", "issue": "文件过长 (555 行)"}, {"file": "app\\api\\v1\\sessions.py", "issue": "文件过长 (537 行)"}, {"file": "app\\api\\v1\\tenants.py", "issue": "文件过长 (608 行)"}, {"file": "backup_quality_fix\\tests\\conftest.py", "issue": "文件过长 (514 行)"}, {"file": "backup_quality_fix\\tests\\integration\\test_tenant_api_integration.py", "issue": "文件过长 (642 行)"}, {"file": "backup_quality_fix\\tests\\performance\\performance_test_suite.py", "issue": "文件过长 (688 行)"}, {"file": "backup_quality_fix\\tests\\unit\\test_api_endpoints.py", "issue": "文件过长 (625 行)"}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "issue": "文件过长 (634 行)"}, {"file": "backup_quality_fix\\tests\\unit\\test_tenant_service.py", "issue": "文件过长 (511 行)"}, {"file": "tests\\e2e\\business_process_test.py", "issue": "文件过长 (801 行)"}, {"file": "tests\\e2e\\test_business_flows.py", "issue": "文件过长 (633 行)"}, {"file": "tests\\e2e\\test_customer_service_flow.py", "issue": "文件过长 (584 行)"}, {"file": "tests\\integration\\test_tenant_api_integration.py", "issue": "文件过长 (642 行)"}, {"file": "tests\\performance\\performance_test_suite.py", "issue": "文件过长 (688 行)"}, {"file": "tests\\unit\\test_api_endpoints.py", "issue": "文件过长 (625 行)"}, {"file": "tests\\unit\\test_message_service.py", "issue": "文件过长 (529 行)"}, {"file": "tests\\unit\\test_services_realistic.py", "issue": "文件过长 (634 行)"}, {"file": "tests\\unit\\test_tenant_service.py", "issue": "文件过长 (511 行)"}], "import_issues": [], "score": 0}, "security": {"secret_leaks": [{"file": "debug_api.py", "type": "API密钥", "line": 26}, {"file": "debug_tenant_auth.py", "type": "API密钥", "line": 26}, {"file": "quality_fix.py", "type": "硬编码密码", "line": 152}, {"file": "quality_fix.py", "type": "API密钥", "line": 140}, {"file": "quality_fix.py", "type": "API密钥", "line": 141}, {"file": "quality_fix.py", "type": "令牌", "line": 163}, {"file": "backup_quality_fix\\debug_api.py", "type": "API密钥", "line": 26}, {"file": "backup_quality_fix\\debug_tenant_auth.py", "type": "API密钥", "line": 26}, {"file": "security_reports\\security_report_20250616_135437.md", "type": "API密钥", "line": 38}, {"file": "security_reports\\security_report_20250616_135437.md", "type": "API密钥", "line": 39}, {"file": "tests\\conftest.py", "type": "API密钥", "line": 127}, {"file": "tests\\conftest.py", "type": "API密钥", "line": 285}, {"file": "tests\\conftest.py", "type": "API密钥", "line": 343}, {"file": "tests\\conftest.py", "type": "密钥", "line": 283}, {"file": "backup_quality_fix\\security_reports\\security_report_20250616_135437.md", "type": "API密钥", "line": 38}, {"file": "backup_quality_fix\\security_reports\\security_report_20250616_135437.md", "type": "API密钥", "line": 39}, {"file": "backup_quality_fix\\tests\\conftest.py", "type": "硬编码密码", "line": 195}, {"file": "backup_quality_fix\\tests\\conftest.py", "type": "API密钥", "line": 127}, {"file": "backup_quality_fix\\tests\\conftest.py", "type": "API密钥", "line": 285}, {"file": "backup_quality_fix\\tests\\conftest.py", "type": "API密钥", "line": 343}, {"file": "backup_quality_fix\\tests\\conftest.py", "type": "密钥", "line": 283}, {"file": "backup_quality_fix\\tests\\integration\\test_tenant_api_integration.py", "type": "API密钥", "line": 88}, {"file": "backup_quality_fix\\tests\\integration\\test_tenant_api_integration.py", "type": "API密钥", "line": 136}, {"file": "backup_quality_fix\\tests\\integration\\test_tenant_api_integration.py", "type": "API密钥", "line": 214}, {"file": "backup_quality_fix\\tests\\integration\\test_tenant_api_integration.py", "type": "API密钥", "line": 333}, {"file": "backup_quality_fix\\tests\\integration\\test_tenant_api_integration.py", "type": "API密钥", "line": 403}, {"file": "backup_quality_fix\\tests\\integration\\test_tenant_api_integration.py", "type": "API密钥", "line": 526}, {"file": "backup_quality_fix\\tests\\performance\\performance_test_suite.py", "type": "API密钥", "line": 234}, {"file": "backup_quality_fix\\tests\\unit\\test_api_endpoints.py", "type": "API密钥", "line": 76}, {"file": "backup_quality_fix\\tests\\unit\\test_api_layers.py", "type": "硬编码密码", "line": 27}, {"file": "backup_quality_fix\\tests\\unit\\test_api_layers.py", "type": "硬编码密码", "line": 40}, {"file": "backup_quality_fix\\tests\\unit\\test_api_layers.py", "type": "硬编码密码", "line": 52}, {"file": "backup_quality_fix\\tests\\unit\\test_api_layers.py", "type": "硬编码密码", "line": 53}, {"file": "backup_quality_fix\\tests\\unit\\test_api_layers.py", "type": "令牌", "line": 106}, {"file": "backup_quality_fix\\tests\\unit\\test_api_layer_comprehensive.py", "type": "硬编码密码", "line": 292}, {"file": "backup_quality_fix\\tests\\unit\\test_api_layer_comprehensive.py", "type": "令牌", "line": 22}, {"file": "backup_quality_fix\\tests\\unit\\test_coverage_boost.py", "type": "硬编码密码", "line": 73}, {"file": "backup_quality_fix\\tests\\unit\\test_coverage_boost_services.py", "type": "API密钥", "line": 141}, {"file": "backup_quality_fix\\tests\\unit\\test_instance_auth_service.py", "type": "令牌", "line": 80}, {"file": "backup_quality_fix\\tests\\unit\\test_instance_auth_service.py", "type": "令牌", "line": 110}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 127}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 153}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 176}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 191}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 192}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 220}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 221}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 276}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 277}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 278}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 284}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 582}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 583}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 584}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "硬编码密码", "line": 587}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "API密钥", "line": 47}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "API密钥", "line": 84}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "API密钥", "line": 169}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "API密钥", "line": 332}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "令牌", "line": 261}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "type": "令牌", "line": 555}, {"file": "backup_quality_fix\\tests\\unit\\test_service_layer_fixes.py", "type": "硬编码密码", "line": 215}, {"file": "backup_quality_fix\\tests\\unit\\test_service_layer_fixes.py", "type": "硬编码密码", "line": 252}, {"file": "backup_quality_fix\\tests\\unit\\test_service_layer_fixes.py", "type": "硬编码密码", "line": 253}, {"file": "backup_quality_fix\\tests\\unit\\test_tenant_model.py", "type": "API密钥", "line": 91}, {"file": "backup_quality_fix\\tests\\unit\\test_tenant_model.py", "type": "API密钥", "line": 159}, {"file": "backup_quality_fix\\tests\\unit\\test_tenant_service.py", "type": "API密钥", "line": 273}, {"file": "backup_quality_fix\\tests\\unit\\test_tenant_service_improved.py", "type": "API密钥", "line": 66}, {"file": "backup_quality_fix\\tests\\unit\\_test_auth_service.py", "type": "硬编码密码", "line": 70}, {"file": "backup_quality_fix\\tests\\unit\\_test_auth_service.py", "type": "硬编码密码", "line": 77}, {"file": "backup_quality_fix\\tests\\unit\\_test_auth_service.py", "type": "硬编码密码", "line": 78}, {"file": "backup_quality_fix\\tests\\unit\\_test_auth_service.py", "type": "硬编码密码", "line": 279}, {"file": "backup_quality_fix\\tests\\unit\\_test_auth_service.py", "type": "硬编码密码", "line": 280}, {"file": "backup_quality_fix\\tests\\unit\\_test_auth_service.py", "type": "硬编码密码", "line": 281}, {"file": "backup_quality_fix\\tests\\unit\\_test_auth_service.py", "type": "硬编码密码", "line": 286}, {"file": "backup_quality_fix\\tests\\unit\\_test_auth_service.py", "type": "API密钥", "line": 62}, {"file": "backup_quality_fix\\tests\\unit\\_test_auth_service.py", "type": "令牌", "line": 245}, {"file": "backup_quality_fix\\tests\\unit\\_test_auth_service.py", "type": "令牌", "line": 391}, {"file": "backup_quality_fix\\tests\\unit\\_test_high_value_services.py", "type": "硬编码密码", "line": 49}, {"file": "backup_quality_fix\\tests\\unit\\_test_high_value_services.py", "type": "硬编码密码", "line": 88}, {"file": "backup_quality_fix\\tests\\unit\\_test_high_value_services.py", "type": "硬编码密码", "line": 113}, {"file": "backup_quality_fix\\tests\\unit\\_test_high_value_services.py", "type": "硬编码密码", "line": 114}, {"file": "tests\\integration\\test_tenant_api_integration.py", "type": "API密钥", "line": 88}, {"file": "tests\\integration\\test_tenant_api_integration.py", "type": "API密钥", "line": 136}, {"file": "tests\\integration\\test_tenant_api_integration.py", "type": "API密钥", "line": 214}, {"file": "tests\\integration\\test_tenant_api_integration.py", "type": "API密钥", "line": 333}, {"file": "tests\\integration\\test_tenant_api_integration.py", "type": "API密钥", "line": 403}, {"file": "tests\\integration\\test_tenant_api_integration.py", "type": "API密钥", "line": 526}, {"file": "tests\\performance\\performance_test_suite.py", "type": "API密钥", "line": 234}, {"file": "tests\\unit\\test_api_endpoints.py", "type": "API密钥", "line": 76}, {"file": "tests\\unit\\test_api_layers.py", "type": "令牌", "line": 106}, {"file": "tests\\unit\\test_api_layer_comprehensive.py", "type": "令牌", "line": 22}, {"file": "tests\\unit\\test_coverage_boost_services.py", "type": "API密钥", "line": 141}, {"file": "tests\\unit\\test_instance_auth_service.py", "type": "令牌", "line": 80}, {"file": "tests\\unit\\test_instance_auth_service.py", "type": "令牌", "line": 110}, {"file": "tests\\unit\\test_services_realistic.py", "type": "API密钥", "line": 47}, {"file": "tests\\unit\\test_services_realistic.py", "type": "API密钥", "line": 84}, {"file": "tests\\unit\\test_services_realistic.py", "type": "API密钥", "line": 169}, {"file": "tests\\unit\\test_services_realistic.py", "type": "API密钥", "line": 332}, {"file": "tests\\unit\\test_services_realistic.py", "type": "令牌", "line": 261}, {"file": "tests\\unit\\test_services_realistic.py", "type": "令牌", "line": 555}, {"file": "tests\\unit\\test_tenant_model.py", "type": "API密钥", "line": 91}, {"file": "tests\\unit\\test_tenant_model.py", "type": "API密钥", "line": 159}, {"file": "tests\\unit\\test_tenant_service.py", "type": "API密钥", "line": 273}, {"file": "tests\\unit\\test_tenant_service_improved.py", "type": "API密钥", "line": 66}, {"file": "tests\\unit\\_test_auth_service.py", "type": "API密钥", "line": 62}, {"file": "tests\\unit\\_test_auth_service.py", "type": "令牌", "line": 245}, {"file": "tests\\unit\\_test_auth_service.py", "type": "令牌", "line": 391}], "insecure_patterns": [{"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 102}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 103}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 112}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 113}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 169}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 170}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 181}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 182}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 191}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 192}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 237}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 238}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 270}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 271}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 286}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 287}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 300}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 301}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 432}, {"file": "deploy_test_windows.py", "pattern": "使用shell=True", "line": 432}, {"file": "deploy_test_windows.py", "pattern": "使用input()可能不安全", "line": 441}, {"file": "deploy_test_windows.py", "pattern": "使用input()可能不安全", "line": 449}, {"file": "install_postgresql_windows.py", "pattern": "使用input()可能不安全", "line": 76}, {"file": "quality_check.py", "pattern": "使用eval()函数", "line": 320}, {"file": "quality_check.py", "pattern": "使用exec()函数", "line": 321}, {"file": "quality_check.py", "pattern": "使用shell=True", "line": 322}, {"file": "quality_check.py", "pattern": "使用input()可能不安全", "line": 323}, {"file": "quality_fix.py", "pattern": "使用eval()函数", "line": 280}, {"file": "quality_fix.py", "pattern": "使用eval()函数", "line": 281}, {"file": "quality_fix.py", "pattern": "使用eval()函数", "line": 318}, {"file": "quality_fix.py", "pattern": "使用eval()函数", "line": 322}, {"file": "quality_fix.py", "pattern": "使用exec()函数", "line": 280}, {"file": "quality_fix.py", "pattern": "使用exec()函数", "line": 281}, {"file": "quality_fix.py", "pattern": "使用exec()函数", "line": 282}, {"file": "quality_fix.py", "pattern": "使用exec()函数", "line": 317}, {"file": "quality_fix.py", "pattern": "使用exec()函数", "line": 318}, {"file": "quality_fix.py", "pattern": "使用exec()函数", "line": 328}, {"file": "quality_fix.py", "pattern": "使用shell=True", "line": 275}, {"file": "quality_fix.py", "pattern": "使用shell=True", "line": 276}, {"file": "quality_fix.py", "pattern": "使用shell=True", "line": 303}, {"file": "quality_fix.py", "pattern": "使用shell=True", "line": 305}, {"file": "quality_fix.py", "pattern": "使用shell=True", "line": 312}, {"file": "quality_fix.py", "pattern": "使用shell=True", "line": 313}, {"file": "quality_fix.py", "pattern": "使用input()可能不安全", "line": 285}, {"file": "quality_fix.py", "pattern": "使用input()可能不安全", "line": 286}, {"file": "quality_fix.py", "pattern": "使用input()可能不安全", "line": 287}, {"file": "quality_fix.py", "pattern": "使用input()可能不安全", "line": 334}, {"file": "quality_fix.py", "pattern": "使用input()可能不安全", "line": 335}, {"file": "run_tests.py", "pattern": "使用shell=True", "line": 17}, {"file": "run_tests.py", "pattern": "使用shell=True", "line": 17}, {"file": "backup_quality_fix\\deploy_test_windows.py", "pattern": "使用shell=True", "line": 102}, {"file": "backup_quality_fix\\deploy_test_windows.py", "pattern": "使用shell=True", "line": 112}, {"file": "backup_quality_fix\\deploy_test_windows.py", "pattern": "使用shell=True", "line": 169}, {"file": "backup_quality_fix\\deploy_test_windows.py", "pattern": "使用shell=True", "line": 181}, {"file": "backup_quality_fix\\deploy_test_windows.py", "pattern": "使用shell=True", "line": 191}, {"file": "backup_quality_fix\\deploy_test_windows.py", "pattern": "使用shell=True", "line": 237}, {"file": "backup_quality_fix\\deploy_test_windows.py", "pattern": "使用shell=True", "line": 270}, {"file": "backup_quality_fix\\deploy_test_windows.py", "pattern": "使用shell=True", "line": 286}, {"file": "backup_quality_fix\\deploy_test_windows.py", "pattern": "使用shell=True", "line": 300}, {"file": "backup_quality_fix\\deploy_test_windows.py", "pattern": "使用shell=True", "line": 432}, {"file": "backup_quality_fix\\deploy_test_windows.py", "pattern": "使用input()可能不安全", "line": 441}, {"file": "backup_quality_fix\\deploy_test_windows.py", "pattern": "使用input()可能不安全", "line": 449}, {"file": "backup_quality_fix\\install_postgresql_windows.py", "pattern": "使用input()可能不安全", "line": 75}, {"file": "backup_quality_fix\\quality_check.py", "pattern": "使用eval()函数", "line": 320}, {"file": "backup_quality_fix\\quality_check.py", "pattern": "使用exec()函数", "line": 321}, {"file": "backup_quality_fix\\quality_check.py", "pattern": "使用shell=True", "line": 322}, {"file": "backup_quality_fix\\quality_check.py", "pattern": "使用input()可能不安全", "line": 323}, {"file": "backup_quality_fix\\run_tests.py", "pattern": "使用shell=True", "line": 17}], "dependency_issues": [{"file": "pyproject.toml", "suggestion": "建议定期更新依赖并检查安全漏洞"}], "permission_issues": [{"file": ".env", "suggestion": "敏感配置文件应设置适当权限"}, {"file": "app\\core\\config\\settings.py", "suggestion": "敏感配置文件应设置适当权限"}], "score": 0}, "documentation": {"readme_quality": {"exists": true, "quality_score": 100, "sections": ["安装", "使用", "配置", "贡献", "API", "示例"]}, "api_docs": {"swagger_exists": false, "doc_files": ["docs\\ai_guides\\00_PROJECT_OVERVIEW_AI.md", "docs\\progress\\M8_Completion_Report.md", "docs\\progress\\M8_Progress_Report.md", "docs\\progress\\M9_Completion_Report.md", "docs\\security\\M8.4_Security_Standards.md"]}, "code_comments": {"total_lines": 58076, "comment_lines": 7800, "ratio": 13.430677043873546}, "docstring_coverage": 95.35529972211195, "score": 77}, "testing": {"test_files": ["test_api.py", "backup_quality_fix\\tests\\integration\\test_tenant_api_integration.py", "backup_quality_fix\\tests\\unit\\test_api_endpoints.py", "backup_quality_fix\\tests\\unit\\test_api_layers.py", "backup_quality_fix\\tests\\unit\\test_api_layer_comprehensive.py", "backup_quality_fix\\tests\\unit\\test_coverage_boost.py", "backup_quality_fix\\tests\\unit\\test_coverage_boost_services.py", "backup_quality_fix\\tests\\unit\\test_instance_auth_service.py", "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "backup_quality_fix\\tests\\unit\\test_service_layer_fixes.py", "backup_quality_fix\\tests\\unit\\test_tenant_model.py", "backup_quality_fix\\tests\\unit\\test_tenant_service.py", "backup_quality_fix\\tests\\unit\\test_tenant_service_improved.py", "tests\\e2e\\test_business_flows.py", "tests\\e2e\\test_customer_service_flow.py", "tests\\helpers\\test_data_factory.py", "tests\\integration\\test_analytics_api.py", "tests\\integration\\test_basic_integration.py", "tests\\integration\\test_instances_api.py", "tests\\integration\\test_messages_api.py", "tests\\integration\\test_sessions_api.py", "tests\\integration\\test_tenant_api_integration.py", "tests\\integration\\test_webhooks_api.py", "tests\\performance\\test_performance_suite.py", "tests\\security\\test_k8s_security_policies.py", "tests\\unit\\test_additional_services.py", "tests\\unit\\test_agent_suggestion_service.py", "tests\\unit\\test_ai_features.py", "tests\\unit\\test_analytics.py", "tests\\unit\\test_analytics_service.py", "tests\\unit\\test_api_endpoints.py", "tests\\unit\\test_api_layers.py", "tests\\unit\\test_api_layer_comprehensive.py", "tests\\unit\\test_auth.py", "tests\\unit\\test_auto_reply_service.py", "tests\\unit\\test_common.py", "tests\\unit\\test_config.py", "tests\\unit\\test_context_manager.py", "tests\\unit\\test_coverage_boost.py", "tests\\unit\\test_coverage_boost_services.py", "tests\\unit\\test_database.py", "tests\\unit\\test_deps.py", "tests\\unit\\test_instances.py", "tests\\unit\\test_instance_auth_service.py", "tests\\unit\\test_logging.py", "tests\\unit\\test_message.py", "tests\\unit\\test_messages.py", "tests\\unit\\test_message_service.py", "tests\\unit\\test_middleware.py", "tests\\unit\\test_mock_strategies.py", "tests\\unit\\test_models.py", "tests\\unit\\test_permissions.py", "tests\\unit\\test_rbac.py", "tests\\unit\\test_rbac_service.py", "tests\\unit\\test_role.py", "tests\\unit\\test_schemas.py", "tests\\unit\\test_scripts.py", "tests\\unit\\test_security.py", "tests\\unit\\test_services_realistic.py", "tests\\unit\\test_service_layer_fixes.py", "tests\\unit\\test_session.py", "tests\\unit\\test_sessions.py", "tests\\unit\\test_session_service.py", "tests\\unit\\test_session_summary_service_professional.py", "tests\\unit\\test_session_summary_service_refactored.py", "tests\\unit\\test_settings.py", "tests\\unit\\test_tenant_model.py", "tests\\unit\\test_tenant_service.py", "tests\\unit\\test_tenant_service_improved.py", "tests\\unit\\test_tenant_service_simplified.py", "tests\\unit\\test_user.py", "tests\\unit\\test_user_model.py", "tests\\unit\\test_utils.py", "tests\\unit\\test_webhook_service.py", "deploy_test.py", "integration_db_test.py", "quick_db_test.py", "tests\\e2e\\business_process_test.py", "tests\\performance\\basic_performance_test.py", "tests\\performance\\load_test.py", "tests\\performance\\performance_baseline_test.py", "tests\\conftest.py", "tests\\e2e\\business_process_test.py", "tests\\e2e\\test_business_flows.py", "tests\\e2e\\test_customer_service_flow.py", "tests\\e2e\\__init__.py", "tests\\helpers\\assertion_helpers.py", "tests\\helpers\\mock_helpers.py", "tests\\helpers\\mock_services.py", "tests\\helpers\\smart_test_selector.py", "tests\\helpers\\test_data_factory.py", "tests\\integration\\test_analytics_api.py", "tests\\integration\\test_basic_integration.py", "tests\\integration\\test_instances_api.py", "tests\\integration\\test_messages_api.py", "tests\\integration\\test_sessions_api.py", "tests\\integration\\test_tenant_api_integration.py", "tests\\integration\\test_webhooks_api.py", "tests\\performance\\baseline_manager.py", "tests\\performance\\basic_performance_test.py", "tests\\performance\\load_test.py", "tests\\performance\\performance_baseline_test.py", "tests\\performance\\performance_test_suite.py", "tests\\performance\\test_performance_suite.py", "tests\\performance\\__init__.py", "tests\\security\\test_k8s_security_policies.py", "tests\\unit\\test_additional_services.py", "tests\\unit\\test_agent_suggestion_service.py", "tests\\unit\\test_ai_features.py", "tests\\unit\\test_analytics.py", "tests\\unit\\test_analytics_service.py", "tests\\unit\\test_api_endpoints.py", "tests\\unit\\test_api_layers.py", "tests\\unit\\test_api_layer_comprehensive.py", "tests\\unit\\test_auth.py", "tests\\unit\\test_auto_reply_service.py", "tests\\unit\\test_common.py", "tests\\unit\\test_config.py", "tests\\unit\\test_context_manager.py", "tests\\unit\\test_coverage_boost.py", "tests\\unit\\test_coverage_boost_services.py", "tests\\unit\\test_database.py", "tests\\unit\\test_deps.py", "tests\\unit\\test_instances.py", "tests\\unit\\test_instance_auth_service.py", "tests\\unit\\test_logging.py", "tests\\unit\\test_message.py", "tests\\unit\\test_messages.py", "tests\\unit\\test_message_service.py", "tests\\unit\\test_middleware.py", "tests\\unit\\test_mock_strategies.py", "tests\\unit\\test_models.py", "tests\\unit\\test_permissions.py", "tests\\unit\\test_rbac.py", "tests\\unit\\test_rbac_service.py", "tests\\unit\\test_role.py", "tests\\unit\\test_schemas.py", "tests\\unit\\test_scripts.py", "tests\\unit\\test_security.py", "tests\\unit\\test_services_realistic.py", "tests\\unit\\test_service_layer_fixes.py", "tests\\unit\\test_session.py", "tests\\unit\\test_sessions.py", "tests\\unit\\test_session_service.py", "tests\\unit\\test_session_summary_service_professional.py", "tests\\unit\\test_session_summary_service_refactored.py", "tests\\unit\\test_settings.py", "tests\\unit\\test_tenant_model.py", "tests\\unit\\test_tenant_service.py", "tests\\unit\\test_tenant_service_improved.py", "tests\\unit\\test_tenant_service_simplified.py", "tests\\unit\\test_user.py", "tests\\unit\\test_user_model.py", "tests\\unit\\test_utils.py", "tests\\unit\\test_webhook_service.py", "tests\\unit\\_test_auth_service.py", "tests\\unit\\_test_high_value_services.py"], "test_coverage": 58.52272727272727, "test_types": {"unit": 111, "integration": 16, "e2e": 7, "performance": 11}, "score": 98}, "performance": {"large_files": [{"file": "code_redundancy_analyzer.py", "lines": 639}, {"file": "install_postgresql_windows.py", "lines": 512}, {"file": "integration_db_test.py", "lines": 585}, {"file": "integration_db_test_windows.py", "lines": 579}, {"file": "optimize_test_code.py", "lines": 645}, {"file": "quality_check.py", "lines": 807}, {"file": "quality_fix.py", "lines": 550}, {"file": "backup_quality_fix\\install_postgresql_windows.py", "lines": 511}, {"file": "backup_quality_fix\\quality_check.py", "lines": 807}, {"file": "scripts\\code_quality_check.py", "lines": 776}, {"file": "scripts\\security_check.py", "lines": 501}, {"file": "scripts\\security_scan.py", "lines": 869}, {"file": "tests\\conftest.py", "lines": 514}, {"file": "app\\services\\agent_suggestion_service.py", "lines": 709}, {"file": "app\\services\\analytics_service.py", "lines": 570}, {"file": "app\\services\\instance_auth_service.py", "lines": 623}, {"file": "app\\services\\instance_config_service.py", "lines": 619}, {"file": "app\\services\\message_service.py", "lines": 687}, {"file": "app\\services\\rbac_service.py", "lines": 589}, {"file": "app\\services\\session_service.py", "lines": 508}, {"file": "app\\services\\session_summary_service.py", "lines": 781}, {"file": "app\\api\\v1\\instances.py", "lines": 555}, {"file": "app\\api\\v1\\sessions.py", "lines": 537}, {"file": "app\\api\\v1\\tenants.py", "lines": 608}, {"file": "backup_quality_fix\\tests\\conftest.py", "lines": 514}, {"file": "backup_quality_fix\\tests\\integration\\test_tenant_api_integration.py", "lines": 642}, {"file": "backup_quality_fix\\tests\\performance\\performance_test_suite.py", "lines": 688}, {"file": "backup_quality_fix\\tests\\unit\\test_api_endpoints.py", "lines": 625}, {"file": "backup_quality_fix\\tests\\unit\\test_services_realistic.py", "lines": 634}, {"file": "backup_quality_fix\\tests\\unit\\test_tenant_service.py", "lines": 511}, {"file": "tests\\e2e\\business_process_test.py", "lines": 801}, {"file": "tests\\e2e\\test_business_flows.py", "lines": 633}, {"file": "tests\\e2e\\test_customer_service_flow.py", "lines": 584}, {"file": "tests\\integration\\test_tenant_api_integration.py", "lines": 642}, {"file": "tests\\performance\\performance_test_suite.py", "lines": 688}, {"file": "tests\\unit\\test_api_endpoints.py", "lines": 625}, {"file": "tests\\unit\\test_message_service.py", "lines": 529}, {"file": "tests\\unit\\test_services_realistic.py", "lines": 634}, {"file": "tests\\unit\\test_tenant_service.py", "lines": 511}], "complex_functions": [{"file": "code_redundancy_analyzer.py", "function": "_detect_duplicate_functions", "complexity": 12}, {"file": "code_redundancy_analyzer.py", "function": "_detect_duplicate_code_blocks", "complexity": 11}, {"file": "code_redundancy_analyzer.py", "function": "_analyze_import_redundancy", "complexity": 13}, {"file": "code_redundancy_analyzer.py", "function": "_analyze_documentation_redundancy", "complexity": 14}, {"file": "code_redundancy_analyzer.py", "function": "_analyze_config_redundancy", "complexity": 12}, {"file": "code_redundancy_analyzer.py", "function": "generate_report", "complexity": 11}, {"file": "fix_startup_issues.py", "function": "validate_env_variables", "complexity": 12}, {"file": "optimize_test_code.py", "function": "_analyze_single_test_file", "complexity": 14}, {"file": "quality_check.py", "function": "_analyze_test_types", "complexity": 12}, {"file": "quality_fix.py", "function": "_fix_file_secrets", "complexity": 12}, {"file": "quality_fix.py", "function": "_fix_file_insecure_patterns", "complexity": 11}, {"file": "backup_quality_fix\\quality_check.py", "function": "_analyze_test_types", "complexity": 12}, {"file": "scripts\\analyze_api_docs.py", "function": "_analyze_route_decorator", "complexity": 17}, {"file": "scripts\\check_api_docs_simple.py", "function": "analyze_api_file", "complexity": 12}, {"file": "scripts\\check_api_docs_simple.py", "function": "check_route_documentation", "complexity": 13}, {"file": "scripts\\code_quality_check.py", "function": "_check_tenant_isolation", "complexity": 14}, {"file": "scripts\\code_quality_check.py", "function": "generate_report", "complexity": 15}, {"file": "scripts\\comprehensive_api_check.py", "function": "_is_route_function", "complexity": 11}, {"file": "scripts\\coverage_analysis.py", "function": "analyze_app_structure", "complexity": 18}, {"file": "scripts\\coverage_analysis.py", "function": "analyze_test_structure", "complexity": 14}, {"file": "scripts\\debug_api_detection.py", "function": "debug_api_file", "complexity": 13}, {"file": "scripts\\security_check.py", "function": "check_sensitive_files", "complexity": 18}, {"file": "app\\services\\analytics_service.py", "function": "get_session_stats", "complexity": 12}, {"file": "app\\services\\auto_reply_service.py", "function": "generate_reply", "complexity": 11}, {"file": "app\\services\\instance_auth_service.py", "function": "cleanup_expired_tokens", "complexity": 11}, {"file": "app\\services\\instance_config_service.py", "function": "_validate_config_updates", "complexity": 17}, {"file": "app\\services\\message_service.py", "function": "store_message", "complexity": 18}, {"file": "app\\api\\v1\\sessions.py", "function": "get_tenant_from_auth", "complexity": 14}, {"file": "app\\api\\v1\\sessions.py", "function": "_get_tenant", "complexity": 14}, {"file": "app\\services\\llm\\dify_provider.py", "function": "_build_chat_request", "complexity": 12}, {"file": "backup_quality_fix\\tests\\performance\\performance_test_suite.py", "function": "_run_scenario", "complexity": 15}, {"file": "tests\\e2e\\business_process_test.py", "function": "execute_scenario", "complexity": 11}, {"file": "tests\\helpers\\smart_test_selector.py", "function": "analyze_dependencies", "complexity": 13}, {"file": "tests\\helpers\\smart_test_selector.py", "function": "_file_imports_module", "complexity": 12}, {"file": "tests\\performance\\performance_test_suite.py", "function": "_run_scenario", "complexity": 15}], "import_cycles": [], "optimization_suggestions": ["建议将大文件拆分为更小的模块", "建议重构复杂函数，提高可读性", "定期进行代码评审和重构", "使用代码分析工具持续监控代码质量"], "score": 0}, "overall_score": 35}