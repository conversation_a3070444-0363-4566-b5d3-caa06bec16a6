# 📖 技术文档：用户模型 (user.py)

## 🎯 1. 模块概述

**功能**：定义`User`数据模型，对应数据库中的`users`表。

**核心职责**：
- **数据结构**：定义用户的核心字段，如`platform`, `user_id`, `nickname`。
- **关系**：定义与`Tenant`和`Role`模型的关系。
- **业务逻辑**：包含与用户相关的业务逻辑，如`has_permission`方法。

## 🚀 2. 快速使用

### 2.1 创建用户

```python
from app.models.user import User

new_user = User(
    tenant_id=tenant.id,
    platform="web",
    user_id="user123",
    nickname="Test User",
)
db.add(new_user)
await db.commit()
```

### 2.2 查询用户

```python
from sqlalchemy import select

stmt = select(User).where(User.nickname == "Test User")
result = await db.execute(stmt)
user = result.scalar_one_or_none()
```

## 🏗️ 3. 架构设计

### 3.1 关键字段

- **`id`**: `str` - 主键，格式为`platform:user_id`
- **`tenant_id`**: `UUID` - 所属租户ID
- **`platform`**: `str` - 来源平台
- **`user_id`**: `str` - 平台唯一用户ID
- **`nickname`**: `str` - 昵称
- **`extra_data`**: `JSON` - 扩展元数据

### 3.2 关系

```mermaid
erDiagram
    USER }o--|| TENANT : "belongs to"
    USER }o--o{ ROLE : "has"
```

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_user_model.py`

## 💡 6. 维护与扩展

- **用户画像**：可以通过`extra_data`字段来存储更丰富的用户画像信息。
- **用户合并**：可以添加用户合并功能，处理同一用户在不同平台下的账户。
- **用户标签**：可以添加用户标签功能，方便对用户进行分类和筛选。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 