"""
日志记录工具模块

提供结构化日志记录功能，支持多租户环境的日志管理。
"""

import json
import logging
import sys
from datetime import datetime
from typing import Any

from app.core.config import settings


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""

    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为结构化JSON格式"""

        # 基础日志信息
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)

        # 添加额外字段
        if hasattr(record, "tenant_id"):
            log_data["tenant_id"] = record.tenant_id
        if hasattr(record, "user_id"):
            log_data["user_id"] = record.user_id
        if hasattr(record, "session_id"):
            log_data["session_id"] = record.session_id
        if hasattr(record, "extra_data"):
            log_data.update(record.extra_data)

        return json.dumps(log_data, ensure_ascii=False)


def setup_logging() -> None:
    """设置应用日志配置"""

    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))

    # 清除现有处理器
    root_logger.handlers.clear()

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))

    # 设置格式化器
    if settings.is_development:
        # 开发环境使用简单格式
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
    else:
        # 生产环境使用结构化格式
        formatter = StructuredFormatter()

    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # 设置第三方库的日志级别
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)


class ContextLogger:
    """带上下文的日志记录器"""

    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.context: dict[str, Any] = {}

    def set_context(self, **kwargs) -> None:
        """设置日志上下文"""
        self.context.update(kwargs)

    def clear_context(self) -> None:
        """清除日志上下文"""
        self.context.clear()

    def _log(self, level: int, message: str, **kwargs) -> None:
        """内部日志记录方法"""
        extra_data = {**self.context, **kwargs}
        
        # 处理 exc_info 参数
        exc_info = extra_data.pop('exc_info', None)
        
        # 如果 exc_info 为 True，获取当前异常信息
        if exc_info is True:
            import sys
            exc_info = sys.exc_info()
        
        # 创建LogRecord并添加额外字段
        record = self.logger.makeRecord(
            self.logger.name, level, "", 0, message, (), exc_info
        )

        # 添加上下文数据
        for key, value in extra_data.items():
            setattr(record, key, value)

        self.logger.handle(record)

    def debug(self, message: str, **kwargs) -> None:
        """记录调试信息"""
        self._log(logging.DEBUG, message, **kwargs)

    def info(self, message: str, **kwargs) -> None:
        """记录信息"""
        self._log(logging.INFO, message, **kwargs)

    def warning(self, message: str, **kwargs) -> None:
        """记录警告"""
        self._log(logging.WARNING, message, **kwargs)

    def error(self, message: str, **kwargs) -> None:
        """记录错误"""
        self._log(logging.ERROR, message, **kwargs)

    def critical(self, message: str, **kwargs) -> None:
        """记录严重错误"""
        self._log(logging.CRITICAL, message, **kwargs)


# 全局日志记录器缓存
_loggers: dict[str, ContextLogger] = {}


def get_logger(name: str) -> ContextLogger:
    """
    获取日志记录器

    Args:
        name: 日志记录器名称，通常使用 __name__

    Returns:
        ContextLogger: 带上下文的日志记录器实例
    """
    if name not in _loggers:
        _loggers[name] = ContextLogger(name)

    return _loggers[name]

# ================================
# 缓存工具模块
# ================================

import json
import pickle
from typing import Any, Optional, Union
from functools import wraps
import redis.asyncio as redis
from app.core.config import settings

class CacheManager:
    """高性能Redis缓存管理器"""
    
    def __init__(self):
        self._redis_client: Optional[redis.Redis] = None
    
    async def get_redis_client(self) -> redis.Redis:
        """获取Redis客户端（懒加载）"""
        if self._redis_client is None:
            self._redis_client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True,
                max_connections=20,  # 连接池大小
                retry_on_timeout=True
            )
        return self._redis_client
    
    async def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        try:
            client = await self.get_redis_client()
            value = await client.get(key)
            if value is None:
                return default
            # 尝试JSON解析，失败则返回原始字符串
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        except Exception as e:
            logger.error(f"Cache get error for key {key}", exc_info=e)
            return default
    
    async def set(self, key: str, value: Any, expire: int = 3600) -> bool:
        """设置缓存值"""
        try:
            client = await self.get_redis_client()
            # 序列化值
            if isinstance(value, (dict, list, tuple)):
                serialized_value = json.dumps(value, ensure_ascii=False)
            else:
                serialized_value = str(value)
            
            await client.set(key, serialized_value, ex=expire)
            return True
        except Exception as e:
            logger.error(f"Cache set error for key {key}", exc_info=e)
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            client = await self.get_redis_client()
            await client.delete(key)
            return True
        except Exception as e:
            logger.error(f"Cache delete error for key {key}", exc_info=e)
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            client = await self.get_redis_client()
            return await client.exists(key) > 0
        except Exception as e:
            logger.error(f"Cache exists error for key {key}", exc_info=e)
            return False
    
    async def close(self):
        """关闭Redis连接"""
        if self._redis_client:
            await self._redis_client.close()

# 全局缓存管理器实例
cache_manager = CacheManager()

def cache_result(expire: int = 3600, key_prefix: str = ""):
    """缓存装饰器，用于缓存函数结果"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试从缓存获取
            cached_result = await cache_manager.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {cache_key}")
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            await cache_manager.set(cache_key, result, expire)
            logger.debug(f"Cache set for {cache_key}")
            return result
        return wrapper
    return decorator

async def get_cache_manager() -> CacheManager:
    """获取缓存管理器实例（依赖注入使用）"""
    return cache_manager

# 初始化日志配置
setup_logging()
