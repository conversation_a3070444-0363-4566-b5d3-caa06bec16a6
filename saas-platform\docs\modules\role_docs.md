# 📖 技术文档：角色模型 (role.py)

## 🎯 1. 模块概述

**功能**：定义`Role`和`Permission`数据模型，用于实现基于角色的访问控制（RBAC）。

**核心职责**：
- **`Role`模型**：定义角色的核心字段，如`name`, `display_name`, `description`。
- **`Permission`模型**：定义权限的核心字段，如`name`, `resource`, `action`。
- **关系**：定义`Role`与`Permission`之间的多对多关系。

## 🚀 2. 快速使用

### 2.1 创建角色和权限

```python
from app.models.role import Role, Permission

new_role = Role(name="admin", display_name="Administrator")
new_permission = Permission(name="create_user", resource="user", action="create")
new_role.permissions.append(new_permission)

db.add(new_role)
await db.commit()
```

### 2.2 查询角色和权限

```python
from sqlalchemy import select

stmt = select(Role).where(Role.name == "admin")
result = await db.execute(stmt)
role = result.scalar_one_or_none()
```

## 🏗️ 3. 架构设计

### 3.1 关键字段

- **`Role.name`**: `str` - 角色名称，用于代码中的唯一标识。
- **`Role.display_name`**: `str` - 显示名称，用于UI展示。
- **`Permission.name`**: `str` - 权限名称，格式为`resource:action`。
- **`Permission.resource`**: `str` - 权限作用的资源。
- **`Permission.action`**: `str` - 对资源执行的操作。

### 3.2 关系

```mermaid
erDiagram
    ROLE ||--|{ ROLE_PERMISSION : "has"
    ROLE_PERMISSION }|--|| PERMISSION : "is"
```

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_role_model.py`

## 💡 6. 维护与扩展

- **权限分组**：可以为权限添加分组字段，方便管理。
- **系统角色**：`is_system_role`字段用于区分系统预置角色和租户自定义角色。
- **默认角色**：可以为新用户自动分配默认角色。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 