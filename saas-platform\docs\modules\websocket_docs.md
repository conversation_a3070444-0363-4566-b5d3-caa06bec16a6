# 📖 技术文档：WebSocket API (websocket.py)

## 🎯 1. 模块概述

**功能**：提供实时双向通信功能，用于客服和用户之间的实时消息传递。

**核心职责**：
- **WebSocket连接**：管理客户端的WebSocket连接
- **消息广播**：向指定会话的客户端广播消息
- **认证授权**：验证WebSocket连接的合法性

## 🚀 2. 快速使用

### 2.1 连接端点

客户端可以通过以下端点建立WebSocket连接：

```
ws://localhost:8000/api/v1/ws/{session_id}?token={jwt_token}
```

### 2.2 消息格式

- **发送消息**
  ```json
  {
    "action": "send_message",
    "payload": {
      "content": "Hello, world!"
    }
  }
  ```

- **接收消息**
  ```json
  {
    "event": "new_message",
    "data": {
      "id": 123,
      "content": "Hello, world!",
      "sender_type": "user"
    }
  }
  ```

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    A[WebSocket Endpoint] --> B(ConnectionManager)
    A --> C(MessageService)
    A --> D(get_current_tenant_from_token)
```

### 3.2 数据流

**消息发送流程**：
1. **客户端连接**：客户端通过WebSocket连接到服务器。
2. **认证**：服务器验证token，确认租户和会话权限。
3. **接收消息**：服务器接收客户端发送的消息。
4. **服务处理**：调用`MessageService`存储消息。
5. **广播消息**：通过`ConnectionManager`向会话中的其他客户端广播新消息。

## 🔧 4. API参考

| 端点 | 描述 |
|---|---|
| `ws/{session_id}` | WebSocket连接端点 |

## 🧪 5. 测试策略

- **集成测试**：`tests/integration/test_websocket_api.py`

## 💡 6. 维护与扩展

- **心跳机制**：可以添加心跳机制来检测和关闭断开的连接。
- **消息确认**：可以添加消息确认机制，确保消息送达。
- **多节点部署**：在多节点部署时，需要使用Redis等外部消息队列来广播消息。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 