#!/usr/bin/env python3
"""
AstrBot SaaS Platform - 冗余清理执行工具
========================================

自动化清理代码冗余问题，包括：
1. 清理重复的性能基准报告文件
2. 清理MyPy缓存冗余
3. 生成清理报告和建议

作者: Quality Improvement Manager  
日期: 2025/01/20
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict
import re

class RedundancyCleanup:
    """冗余清理执行器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.cleanup_report = {
            'timestamp': datetime.now().isoformat(),
            'actions': [],
            'space_saved_bytes': 0,
            'files_removed': 0
        }
    
    def execute_cleanup(self, simulate: bool = True):
        """
        执行冗余清理
        
        Args:
            simulate: 是否仅模拟清理（默认True，安全模式）
        """
        print("🧹 开始执行代码冗余清理...")
        print(f"📁 项目根目录: {self.project_root}")
        print(f"🔧 执行模式: {'模拟模式' if simulate else '真实清理'}")
        print()
        
        # 1. 清理性能基准报告文件
        self._cleanup_performance_reports(simulate)
        
        # 2. 清理MyPy缓存
        self._cleanup_mypy_cache(simulate)
        
        # 3. 清理临时文件
        self._cleanup_temp_files(simulate)
        
        # 4. 生成清理报告
        self._generate_cleanup_report()
    
    def _cleanup_performance_reports(self, simulate: bool):
        """清理性能基准报告文件"""
        print("📊 清理性能基准报告文件...")
        
        pattern = "performance_baseline_report_*.json"
        report_files = list(self.project_root.glob(pattern))
        
        if not report_files:
            print("   ✅ 未发现性能报告文件")
            return
        
        # 按时间排序，保留最新的5个
        report_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        files_to_keep = report_files[:5]
        files_to_remove = report_files[5:]
        
        space_saved = 0
        for file_path in files_to_remove:
            file_size = file_path.stat().st_size
            space_saved += file_size
            
            if not simulate:
                file_path.unlink()
                
            print(f"   🗑️ {'[模拟]' if simulate else ''}删除: {file_path.name} ({file_size/1024:.1f}KB)")
        
        self.cleanup_report['actions'].append({
            'category': 'performance_reports',
            'files_processed': len(files_to_remove),
            'files_kept': len(files_to_keep),
            'space_saved_bytes': space_saved,
            'description': f"保留最新5个性能报告，清理{len(files_to_remove)}个历史文件"
        })
        
        self.cleanup_report['space_saved_bytes'] += space_saved
        self.cleanup_report['files_removed'] += len(files_to_remove)
        
        print(f"   📈 发现文件: {len(report_files)} 个")
        print(f"   🔒 保留文件: {len(files_to_keep)} 个")
        print(f"   🗑️ 清理文件: {len(files_to_remove)} 个")
        print(f"   💾 节省空间: {space_saved/1024:.1f} KB")
        print()
    
    def _cleanup_mypy_cache(self, simulate: bool):
        """清理MyPy缓存冗余"""
        print("🔍 清理MyPy缓存...")
        
        mypy_cache_dir = self.project_root / ".mypy_cache"
        
        if not mypy_cache_dir.exists():
            print("   ✅ 未发现MyPy缓存目录")
            return
        
        # 计算缓存目录大小
        total_size = 0
        file_count = 0
        
        for root, dirs, files in os.walk(mypy_cache_dir):
            for file in files:
                file_path = Path(root) / file
                total_size += file_path.stat().st_size
                file_count += 1
        
        if not simulate:
            shutil.rmtree(mypy_cache_dir)
            print(f"   🗑️ 删除整个MyPy缓存目录")
        else:
            print(f"   🗑️ [模拟]删除整个MyPy缓存目录")
        
        self.cleanup_report['actions'].append({
            'category': 'mypy_cache',
            'files_processed': file_count,
            'space_saved_bytes': total_size,
            'description': f"清理MyPy类型检查缓存，包含{file_count}个文件"
        })
        
        self.cleanup_report['space_saved_bytes'] += total_size
        self.cleanup_report['files_removed'] += file_count
        
        print(f"   📁 缓存文件: {file_count} 个")
        print(f"   💾 节省空间: {total_size/1024/1024:.2f} MB")
        print()
    
    def _cleanup_temp_files(self, simulate: bool):
        """清理临时文件"""
        print("🧽 清理临时文件...")
        
        temp_patterns = [
            "*.tmp",
            "*.temp", 
            "*~",
            ".DS_Store",
            "Thumbs.db",
            "*.log.*",  # 排除主要的log文件
        ]
        
        space_saved = 0
        files_removed = 0
        
        for pattern in temp_patterns:
            temp_files = list(self.project_root.rglob(pattern))
            
            for file_path in temp_files:
                if file_path.is_file():
                    file_size = file_path.stat().st_size
                    space_saved += file_size
                    files_removed += 1
                    
                    if not simulate:
                        file_path.unlink()
                    
                    print(f"   🗑️ {'[模拟]' if simulate else ''}删除临时文件: {file_path.relative_to(self.project_root)}")
        
        if files_removed == 0:
            print("   ✅ 未发现临时文件")
        else:
            self.cleanup_report['actions'].append({
                'category': 'temp_files',
                'files_processed': files_removed,
                'space_saved_bytes': space_saved,
                'description': f"清理各类临时文件"
            })
            
            self.cleanup_report['space_saved_bytes'] += space_saved
            self.cleanup_report['files_removed'] += files_removed
            
            print(f"   💾 节省空间: {space_saved/1024:.1f} KB")
        print()
    
    def _generate_cleanup_report(self):
        """生成清理报告"""
        print("📋 生成清理报告...")
        
        report = []
        report.append("=" * 80)
        report.append("🧹 AstrBot SaaS Platform - 冗余清理执行报告")
        report.append("=" * 80)
        report.append(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"📁 项目路径: {self.project_root}")
        report.append("")
        
        # 总体统计
        total_space_mb = self.cleanup_report['space_saved_bytes'] / 1024 / 1024
        report.append("📊 清理统计")
        report.append("-" * 40)
        report.append(f"🗑️ 清理文件数: {self.cleanup_report['files_removed']} 个")
        report.append(f"💾 节省空间: {total_space_mb:.2f} MB")
        report.append(f"🎯 执行操作: {len(self.cleanup_report['actions'])} 类")
        report.append("")
        
        # 详细操作列表
        report.append("🔧 清理操作详情")
        report.append("-" * 40)
        for i, action in enumerate(self.cleanup_report['actions'], 1):
            space_mb = action['space_saved_bytes'] / 1024 / 1024
            report.append(f"{i}. 【{action['category']}】{action['description']}")
            report.append(f"   📁 处理文件: {action['files_processed']} 个")
            report.append(f"   💾 节省空间: {space_mb:.2f} MB")
            report.append("")
        
        # 后续建议
        report.append("💡 后续优化建议")
        report.append("-" * 40)
        report.append("1. 建立自动化清理机制:")
        report.append("   - 添加Git hooks自动清理临时文件")
        report.append("   - 配置CI/CD自动清理历史报告")
        report.append("   - 设置MyPy缓存定期清理")
        report.append("")
        report.append("2. 代码级优化:")
        report.append("   - 提取测试代码中的公共工具函数")
        report.append("   - 创建测试数据工厂类")
        report.append("   - 统一测试断言和Mock模式")
        report.append("")
        report.append("3. 文档级优化:")
        report.append("   - 合并相似的文档文件")
        report.append("   - 建立文档模板")
        report.append("   - 清理重复的API文档")
        
        report_content = "\n".join(report)
        
        # 保存报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = self.project_root / f"cleanup_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        # 保存JSON报告
        json_report_file = self.project_root / f"cleanup_report_{timestamp}.json"
        with open(json_report_file, 'w', encoding='utf-8') as f:
            json.dump(self.cleanup_report, f, indent=2, ensure_ascii=False)
        
        print(report_content)
        print()
        print(f"📄 报告已保存:")
        print(f"   📋 文本报告: {report_file}")
        print(f"   📊 JSON报告: {json_report_file}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AstrBot SaaS Platform 冗余清理工具")
    parser.add_argument("--execute", action="store_true", 
                       help="真实执行清理（默认为模拟模式）")
    parser.add_argument("--project", default=".", 
                       help="项目根目录路径")
    
    args = parser.parse_args()
    
    cleanup = RedundancyCleanup(args.project)
    cleanup.execute_cleanup(simulate=not args.execute)

if __name__ == "__main__":
    main() 