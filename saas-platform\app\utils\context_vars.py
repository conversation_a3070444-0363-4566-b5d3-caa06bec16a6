"""
上下文变量管理模块

用于安全地在异步任务中传递和访问请求级别的数据，如租户ID。
"""

from contextvars import ContextVar
from typing import Optional
from uuid import UUID


class TenantContext:
    """
    租户上下文管理器

    封装了tenant_id的ContextVar，提供类型安全的get/set/clear方法。
    """

    _tenant_id_cv: ContextVar[Optional[UUID]] = ContextVar("tenant_id_cv", default=None)

    def set_tenant_id(self, tenant_id: UUID) -> None:
        """
        设置当前上下文的租户ID

        Args:
            tenant_id: 要设置的租户ID
        """
        self._tenant_id_cv.set(tenant_id)

    def get_tenant_id(self) -> Optional[UUID]:
        """
        获取当前上下文的租户ID

        Returns:
            Optional[UUID]: 当前租户ID，如果未设置则为None
        """
        return self._tenant_id_cv.get()

    def clear(self) -> None:
        """
        清空当前上下文的租户ID
        """
        self._tenant_id_cv.set(None)


# 全局租户上下文实例
tenant_context = TenantContext()
