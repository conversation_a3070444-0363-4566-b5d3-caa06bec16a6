"""
基础集成测试
验证API基本功能
"""

import pytest
from httpx import AsyncClient


class TestBasicIntegration:
    """基础集成测试类"""

    @pytest.mark.asyncio
    async def test_health_check(self, client: AsyncClient):
        """测试健康检查端点"""
        response = await client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "AstrBot SaaS Platform API" in data["message"]

    @pytest.mark.asyncio
    async def test_api_v1_health(self, client: AsyncClient):
        """测试API v1健康检查"""
        response = await client.get("/api/v1/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["version"] == "v1"
