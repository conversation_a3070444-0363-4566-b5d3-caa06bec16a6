{"phase": "用户接入验证测试", "timestamp": "2025-06-25T13:43:50.142935", "summary": {"total_tests": 10, "passed_tests": 9, "failed_tests": 1, "success_rate": 90.0}, "performance": {"avg_response_time": 0.024154198797125565, "min_response_time": 0.004637718200683594, "max_response_time": 0.05793166160583496, "p95_response_time": 0.055860471725463864}, "test_results": [{"test_name": "多租户并发接入", "success": true, "details": "租户数: 5, 并发用户: 21", "timestamp": "2025-06-25T13:43:49.834191", "metrics": {"总消息数": 57, "成功消息": 57, "失败消息": 0, "成功率": "100.0%", "总耗时": "1.42s", "平均响应时间": "0.024s", "最大响应时间": "0.058s", "95%响应时间": "0.056s", "吞吐量": "40.2 msg/s"}}, {"test_name": "租户数据隔离验证", "success": true, "details": "预期租户数: 5, 实际租户数: 5", "timestamp": "2025-06-25T13:43:49.834695"}, {"test_name": "正确的租户ID访问", "success": true, "details": "期望: 成功, 实际: 成功 (HTTP 200)", "timestamp": "2025-06-25T13:43:49.840417"}, {"test_name": "错误的租户ID访问", "success": true, "details": "期望: 成功, 实际: 成功 (HTTP 200)", "timestamp": "2025-06-25T13:43:49.847313"}, {"test_name": "缺少租户ID头", "success": true, "details": "期望: 成功, 实际: 成功 (HTTP 200)", "timestamp": "2025-06-25T13:43:49.854043"}, {"test_name": "空租户ID", "success": false, "details": "期望: 失败, 实际: 成功 (HTTP 200)", "timestamp": "2025-06-25T13:43:49.861308"}, {"test_name": "WebSocket端点可访问性", "success": true, "details": "代理服务端口9000可连接", "timestamp": "2025-06-25T13:43:49.865925"}, {"test_name": "WebSocket功能模拟", "success": true, "details": "基于HTTP的实时通信功能正常", "timestamp": "2025-06-25T13:43:49.865925"}, {"test_name": "API响应一致性", "success": true, "details": "一致性率: 100.0%, 格式一致: True", "timestamp": "2025-06-25T13:43:49.940329"}, {"test_name": "用户体验指标", "success": true, "details": "成功率: 100.0%", "timestamp": "2025-06-25T13:43:50.142062", "metrics": {"平均延迟": "0.010s", "最大延迟": "0.032s", "95%延迟": "0.032s", "用户体验评级": "优秀"}}]}