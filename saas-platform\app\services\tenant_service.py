"""
租户管理服务层
实现租户的CRUD操作、配置管理和业务逻辑
"""

from typing import Any, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from sqlalchemy import func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.models.tenant import Tenant, TenantStatus
from app.schemas.tenant import TenantCreate, TenantRead, TenantUpdate
from app.utils.logging import get_logger, cache_result, cache_manager

logger = get_logger(__name__)


class TenantService:
    """租户管理服务"""

    def __init__(self, db: AsyncSession) -> None:
        """初始化租户服务

        Args:
            db: 数据库异步会话
        """
        self.db = db

    async def create_tenant(self, tenant_data: TenantCreate) -> TenantRead:
        """创建新租户

        Args:
            tenant_data: 租户创建数据

        Returns:
            TenantRead: 创建的租户信息

        Raises:
            HTTPException: 当邮箱或企业名称已存在时
        """
        try:
            # 检查邮箱是否已存在（防止重复）
            existing_tenant = await self._get_tenant_by_email(tenant_data.email)
            if existing_tenant:
                logger.warning("租户邮箱已存在", email=tenant_data.email)
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="该邮箱已被其他租户使用",
                )

            # 检查企业名称唯一性
            existing_name = await self._get_tenant_by_name(tenant_data.name)
            if existing_name:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="企业名称已存在"
                )

            # 创建新租户实例
            new_tenant = Tenant(
                name=tenant_data.name,
                email=tenant_data.email,
                plan=tenant_data.plan,
                extra_data=tenant_data.metadata or {},
                api_key=Tenant.generate_api_key(),  # 自动生成API密钥
            )

            # 保存到数据库
            self.db.add(new_tenant)
            await self.db.commit()
            await self.db.refresh(new_tenant)

            logger.info(
                "租户创建成功",
                tenant_id=str(new_tenant.id),
                tenant_name=new_tenant.name,
                email=new_tenant.email,
            )

            return TenantRead.model_validate(new_tenant)

        except HTTPException:
            # For HTTP exceptions (like 409), just re-raise
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error(
                "租户创建失败", error=str(e), tenant_data=tenant_data.model_dump()
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="租户创建失败，请稍后重试",
            )

    @cache_result(expire=300, key_prefix="tenant")  # 缓存5分钟
    async def get_tenant(self, tenant_id: UUID) -> Optional[TenantRead]:
            """根据ID获取租户信息（带缓存优化）
    
            Args:
                tenant_id: 租户ID
    
            Returns:
                Optional[TenantRead]: 租户信息，不存在则返回None
            """
            try:
                # 查询优化：只选择需要的字段
                query = select(Tenant).where(Tenant.id == tenant_id)
                result = await self.db.execute(query)
                tenant = result.scalar_one_or_none()
    
                if not tenant:
                    return None
    
                return TenantRead.model_validate(tenant)
    
            except Exception as e:
                logger.error("获取租户信息失败", tenant_id=str(tenant_id), error=str(e))
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="获取租户信息失败",
                )


    async def get_tenant_by_id_with_verification(self, tenant_id: UUID) -> TenantRead:
        """获取租户信息并验证存在性

        Args:
            tenant_id: 租户ID

        Returns:
            TenantRead: 租户信息

        Raises:
            HTTPException: 当租户不存在时
        """
        tenant = await self.get_tenant(tenant_id)
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="租户不存在"
            )
        return tenant

    async def update_tenant(
        self, tenant_id: UUID, tenant_data: TenantUpdate
    ) -> TenantRead:
        """更新租户信息

        Args:
            tenant_id: 租户ID
            tenant_data: 更新数据

        Returns:
            TenantRead: 更新后的租户信息

        Raises:
            HTTPException: 当租户不存在或更新失败时
        """
        try:
            # 1. 获取现有租户
            query = select(Tenant).where(Tenant.id == tenant_id)
            result = await self.db.execute(query)
            tenant = result.scalar_one_or_none()

            if not tenant:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="租户不存在"
                )

            # 2. 检查邮箱唯一性（如果邮箱有变更）
            if tenant_data.email and tenant_data.email != tenant.email:
                existing_tenant = await self._get_tenant_by_email(tenant_data.email)
                if existing_tenant:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="邮箱已被其他租户使用",
                    )

            # 3. 更新字段
            update_data = tenant_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(tenant, field, value)

            await self.db.commit()
            await self.db.refresh(tenant)

            logger.info(
                "租户信息更新成功",
                tenant_id=str(tenant_id),
                updated_fields=list(update_data.keys()),
            )

            return TenantRead.model_validate(tenant)

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error("租户更新失败", tenant_id=str(tenant_id), error=str(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="租户更新失败"
            )

    async def delete_tenant(self, tenant_id: UUID) -> bool:
        """删除租户

        Args:
            tenant_id: 租户ID

        Returns:
            bool: 删除是否成功

        Raises:
            HTTPException: 当租户不存在或删除失败时
        """
        try:
            # 1. 检查租户是否存在
            query = select(Tenant).where(Tenant.id == tenant_id)
            result = await self.db.execute(query)
            tenant = result.scalar_one_or_none()

            if not tenant:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="租户不存在"
                )

            # 2. 检查是否有关联用户（软删除保护）
            user_count = await self._count_tenant_users(tenant_id)
            if user_count > 0:
                # 执行软删除 (将状态设置为DEACTIVATED)
                tenant.status = TenantStatus.DEACTIVATED
                await self.db.commit()

                logger.info(
                    "租户软删除成功（用户数据保护）",
                    tenant_id=str(tenant_id),
                    user_count=user_count,
                )
            else:
                # 执行硬删除
                await self.db.delete(tenant)
                await self.db.commit()

                logger.info("租户硬删除成功", tenant_id=str(tenant_id))

            return True

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error("租户删除失败", tenant_id=str(tenant_id), error=str(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="租户删除失败"
            )

    async def list_tenants(
            self,
            skip: int = 0,
            limit: int = 100,
            search: Optional[str] = None,
            is_active: Optional[bool] = None,
        ) -> list[TenantRead]:
            """获取租户列表（管理员功能，带查询优化）
    
            Args:
                skip: 跳过数量
                limit: 限制数量（最大500）
                search: 搜索关键词
                is_active: 是否激活状态过滤
    
            Returns:
                List[TenantRead]: 租户列表
            """
            try:
                # 限制最大查询数量防止性能问题
                limit = min(limit, 500)
                
                # 创建缓存键
                cache_key = f"tenant_list:{skip}:{limit}:{search or ''}:{is_active or ''}"
                
                # 尝试从缓存获取
                cached_result = await cache_manager.get(cache_key)
                if cached_result is not None:
                    logger.debug(f"租户列表缓存命中: {cache_key}")
                    return [TenantRead.model_validate(item) for item in cached_result]
                
                # 构建查询，只选择必要字段以优化性能
                query = select(Tenant)
    
                # 添加搜索条件
                if search:
                    search_pattern = f"%{search}%"
                    query = query.where(
                        or_(
                            Tenant.name.ilike(search_pattern),
                            Tenant.email.ilike(search_pattern),
                        )
                    )
    
                # 添加状态过滤
                if is_active is not None:
                    if is_active:
                        query = query.where(Tenant.status == TenantStatus.ACTIVE)
                    else:
                        query = query.where(
                            Tenant.status.in_(
                                [TenantStatus.SUSPENDED, TenantStatus.DEACTIVATED]
                            )
                        )
    
                # 添加分页和排序优化
                query = query.offset(skip).limit(limit).order_by(Tenant.created_at.desc())
    
                result = await self.db.execute(query)
                tenants = result.scalars().all()
    
                # 转换为返回格式
                tenant_list = [TenantRead.model_validate(tenant) for tenant in tenants]
                
                # 缓存结果（短时间缓存，因为列表会频繁变化）
                await cache_manager.set(
                    cache_key, 
                    [tenant.model_dump() for tenant in tenant_list], 
                    expire=60  # 1分钟缓存
                )
    
                return tenant_list
    
            except Exception as e:
                logger.error(
                    "获取租户列表失败", error=str(e), skip=skip, limit=limit, search=search
                )
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="获取租户列表失败",
                )


    async def get_tenant_statistics(self, tenant_id: UUID) -> dict[str, Any]:
        """获取租户统计信息

        Args:
            tenant_id: 租户ID

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 验证租户存在
            await self.get_tenant_by_id_with_verification(tenant_id)

            # 统计用户数量
            user_count = await self._count_tenant_users(tenant_id)

            # TODO: 添加更多统计信息
            # - 会话数量
            # - 消息数量
            # - 存储使用量
            # - 活跃度指标

            return {
                "tenant_id": str(tenant_id),
                "user_count": user_count,
                "sessions_count": 0,  # 待实现
                "messages_count": 0,  # 待实现
                "storage_usage": 0,  # 待实现
                "last_activity": None,  # 待实现
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error("获取租户统计失败", tenant_id=str(tenant_id), error=str(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取租户统计失败",
            )

    # 私有辅助方法
    async def _get_tenant_by_email(self, email: str) -> Optional[Tenant]:
        """根据邮箱获取租户(内部使用)"""
        query = select(Tenant).where(Tenant.email == email)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def _get_tenant_by_name(self, name: str) -> Optional[Tenant]:
        """根据名称获取租户(内部使用)"""
        query = select(Tenant).where(Tenant.name == name)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def _count_tenant_users(self, tenant_id: UUID) -> int:
        """计算租户下的用户数量"""
        from ..models.user import User  # 延迟导入避免循环依赖

        query = select(func.count(User.id)).where(User.tenant_id == tenant_id)
        result = await self.db.execute(query)
        return result.scalar() or 0

    async def update_tenant_status(
        self, tenant_id: UUID, is_active: bool
    ) -> Optional[TenantRead]:
        """更新租户状态

        Args:
            tenant_id: 租户ID
            is_active: 新的激活状态

        Returns:
            Optional[TenantRead]: 更新后的租户信息，如果租户不存在则返回None

        Raises:
            HTTPException: 当更新失败时
        """
        try:
            # 1. 检查租户是否存在
            query = select(Tenant).where(Tenant.id == tenant_id)
            result = await self.db.execute(query)
            tenant = result.scalar_one_or_none()

            if not tenant:
                return None

            # 2. 更新状态 (使用status字段而不是is_active属性)
            if is_active:
                tenant.status = TenantStatus.ACTIVE
            else:
                tenant.status = TenantStatus.SUSPENDED

            # 更新时间戳会通过onupdate自动处理

            await self.db.commit()
            await self.db.refresh(tenant)

            logger.info(
                "租户状态更新成功",
                tenant_id=str(tenant_id),
                new_status=tenant.status.value,
            )

            return TenantRead.model_validate(tenant)

        except Exception as e:
            await self.db.rollback()
            logger.error("租户状态更新失败", tenant_id=str(tenant_id), error=str(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="租户状态更新失败",
            )

    async def regenerate_api_key(self, tenant_id: UUID) -> Optional[str]:
        """重新生成租户API密钥

        Args:
            tenant_id: 租户ID

        Returns:
            Optional[str]: 新的API密钥，如果租户不存在则返回None

        Raises:
            HTTPException: 当生成失败时
        """
        try:
            # 1. 检查租户是否存在
            query = select(Tenant).where(Tenant.id == tenant_id)
            result = await self.db.execute(query)
            tenant = result.scalar_one_or_none()

            if not tenant:
                return None

            # 2. 生成新的API密钥
            import hashlib
            import secrets

            # 生成随机字符串
            raw_key = secrets.token_urlsafe(32)

            # 创建带前缀的API密钥
            new_api_key = f"astr_{tenant.name}_{raw_key[:16]}"

            # 对API密钥进行哈希存储
            api_key_hash = hashlib.sha256(new_api_key.encode()).hexdigest()

            # 3. 更新租户的API密钥
            tenant.api_key = api_key_hash
            tenant.updated_at = func.now()

            await self.db.commit()

            logger.info("租户API密钥重生成成功", tenant_id=str(tenant_id))

            # 返回明文API密钥（仅此一次）
            return new_api_key

        except Exception as e:
            await self.db.rollback()
            logger.error(
                "租户API密钥重生成失败", tenant_id=str(tenant_id), error=str(e)
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="API密钥重生成失败",
            )


# 依赖注入函数
def get_tenant_service(db: AsyncSession = Depends(get_db)) -> TenantService:
    """获取租户服务实例"""
    return TenantService(db)
