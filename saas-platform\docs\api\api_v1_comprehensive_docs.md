# AstrBot SaaS API v1 技术文档

## 📋 模块概览

### 🎯 模块定位
`app/api/v1/` 模块是 AstrBot SaaS 平台版本 1 的核心API层。它负责定义所有的HTTP端点，处理客户端请求，并通过依赖注入系统与服务层进行交互，最终向客户端返回结构化的响应。

### ✨ 核心功能
- **RESTful API 设计** - 遵循 RESTful 原则，提供清晰、一致的资源操作接口。
- **模块化路由** - 按资源（如租户、用户、会话）组织路由，便于管理和扩展。
- **请求验证** - 使用 Pydantic `schemas` 对请求体和查询参数进行严格验证。
- **响应序列化** - 使用 `response_model` 确保响应数据格式的统一和规范。
- **依赖注入** - 大量使用 `deps.py` 中的依赖项来处理认证、授权和租户隔离。
- **自动化API文档** - 利用 FastAPI 的特性自动生成交互式的 OpenAPI (Swagger) 和 ReDoc 文档。

### 🏗️ API 路由架构

```mermaid
graph TD
    A[FastAPI 应用实例] --> B(主API路由器 /api/v1)
    
    subgraph "app.api.v1"
        B -- include --> C[/tenants]
        B -- include --> D[/sessions]
        B -- include --> E[/messages]
        B -- include --> F[/rbac]
        B -- include --> G[...]
        B -- include --> H[/health]
    end
    
    subgraph "路由模块 (e.g., tenants.py)"
        C -- GET /{tenant_id} --> C1(get_tenant)
        C -- POST / --> C2(create_tenant)
        C -- PUT /{tenant_id} --> C3(update_tenant)
    end
    
    subgraph "依赖注入 (deps.py)"
        I(get_current_user)
        J(get_tenant_service)
        K(require_api_key)
    end
    
    C1 -- Depends --> I
    C2 -- Depends --> J
    C3 -- Depends --> I & J

    style B fill:#bde4ff,stroke:#6a8ebf,stroke-width:2px
    style C fill:#cde4ff,stroke:#6a8ebf,stroke-width:2px
```
**架构说明:**
1.  `app/api/v1/__init__.py` 中创建了一个主路由器 `api_router`。
2.  每个资源（如 `tenants.py`）都定义了自己的 `APIRouter`，并带有 `prefix`（如 `/tenants`）和 `tags`。
3.  主路由器 `api_router` 使用 `include_router` 将所有资源路由聚合起来。
4.  每个API端点函数都通过`Depends`关键字声明其依赖项，如获取当前用户、获取服务实例等。

## 🔧 核心路由分析

### 1. `tenants.py` - 租户管理API

- **`POST /tenants` (create_tenant)**: 创建新租户，这是一个公开接口，用于用户注册。
- **`GET /tenants/{tenant_id}` (get_tenant)**: 获取租户详情。此接口使用了自定义的混合认证依赖 `get_tenant_from_mixed_auth`，同时支持 `Bearer Token` 和 `X-API-Key` 两种认证方式，并强制执行租户隔离检查，确保用户只能访问自己的租户信息。
- **`PUT /tenants/{tenant_id}` (update_tenant)**: 更新租户信息，同样强制租户隔离。
- **`DELETE /tenants/{tenant_id}` (delete_tenant)**: 软删除租户。
- **`GET /tenants` (list_tenants)**: 获取租户列表。此接口做了特殊处理，对于普通用户，即使是请求列表，也只返回其自身的租户信息，实现了严格的数据隔离。
- **`GET /tenants/{tenant_id}/stats` (get_tenant_statistics)**: 获取特定租户的统计数据。

### 2. `sessions.py` & `messages.py` - 核心业务API

- 这两个模块处理会话和消息的CRUD操作。
- **核心模式**:
  - 所有端点都强制依赖 `get_current_user` 和 `get_current_tenant` 来确保请求已通过认证且在正确的租户上下文中执行。
  - 所有数据库操作都通过注入的 `SessionService` 或 `MessageService` 完成，保持了API层和服务层的清晰分离。
  - 使用 `PaginatedResponse` schema 来处理列表查询的响应。

### 3. `websocket.py` - 实时通信

- **`WS /websocket/{session_id}`**: 建立WebSocket连接，用于实时消息收发。
- **认证方式**: WebSocket连接的认证通过在连接URL的查询参数中传递 `token` 来完成。在连接建立时，服务器会验证此Token的有效性。

### 4. `webhooks.py` - 外部集成

- **`POST /webhooks/{provider}`**: 接收来自第三方服务（如支付、消息平台）的Webhook回调。
- **认证方式**: 通常使用 `require_api_key` 依赖进行认证，或者在URL中包含一个安全的、唯一的标识符。

### 5. `rbac.py` & `user_roles.py` - 权限管理

- 提供对角色和权限的增删改查接口。
- **访问控制**: 这些路由通常受到严格的角色限制，例如，只有 `admin` 角色的用户才能调用，通过 `dependencies=[Depends(require_admin_role)]` 来实现。

## 🚀 使用和开发指南

### 1. API设计规范

- **URL路径**: 使用小写字母、连字符 `-`，并清晰地表示资源层级，例如 `/sessions/{session_id}/messages`。
- **HTTP动词**:
  - `GET`: 读取资源。
  - `POST`: 创建新资源。
  - `PUT`: 完整替换资源。
  - `PATCH`: 部分更新资源。
  - `DELETE`: 删除资源。
- **状态码**:
  - `200 OK`: 请求成功。
  - `201 Created`: 资源创建成功。
  - `204 No Content`: 请求成功，但无返回内容（如DELETE）。
  - `400 Bad Request`: 请求体验证失败。
  - `401 Unauthorized`: 认证失败。
  - `403 Forbidden`: 授权失败（无权限）。
  - `404 Not Found`: 资源不存在。
- **命名**:
  - **路由模块**: 复数形式，如 `tenants.py`。
  - **Tags**: 中文描述，用于API文档分组，如 `["租户管理"]`。
  - **路由函数**: 清晰的动词+名词，如 `create_tenant`。

### 2. 添加新API模块

1.  在 `app/api/v1/` 目录下创建一个新文件，例如 `products.py`。
2.  在 `products.py` 中，创建一个新的 `APIRouter` 实例：
    ```python
    from fastapi import APIRouter
    
    router = APIRouter(prefix="/products", tags=["商品管理"])
    ```
3.  定义你的API端点，并使用正确的依赖注入和服务。
    ```python
    from app.schemas.product import ProductRead, ProductCreate
    from app.services.product_service import ProductService
    from app.api.deps import get_current_user
    
    @router.post("/", response_model=ProductRead)
    async def create_product(
        product_data: ProductCreate,
        # 注入服务和当前用户
        product_service: ProductService = Depends(),
        user: User = Depends(get_current_user)
    ):
        # ... 调用服务层方法
        new_product = await product_service.create(product_data, user)
        return new_product
    ```
4.  在 `app/api/v1/__init__.py` 中导入并注册新的路由器。
    ```python
    # in __init__.py
    from .products import router as products_router
    
    # ...
    api_router.include_router(products_router)
    ```

### 3. API的安全性

- **始终进行认证**: 除非API是完全公开的，否则每个端点都应至少依赖 `get_current_user`。
- **强制租户隔离**: 任何涉及特定资源的操作，都必须验证该资源是否属于当前登录用户所在的租户。
- **服务层处理业务逻辑**: API路由层应保持轻量，只负责处理HTTP请求和响应，所有业务逻辑、数据库操作都应委托给服务层。
- **使用 `response_model`**: 明确定义响应模型可以防止意外泄露敏感的内部数据。

## 🔮 扩展规划

- **API版本控制**: 当前为 `v1`。未来如果出现不兼容的API变更，可以创建一个新的 `v2` 目录，将旧的 `v1` 路由标记为弃用，从而实现平滑过渡。
- **GraphQL接口**: 可以考虑在 `v1` 旁边添加一个并行的GraphQL接口，为需要复杂数据查询的客户端提供更灵活的选择。
- **更高级的速率限制**: 可以将速率限制从中间件层移到路由层，使用 `Depends` 实现对不同API、不同用户套餐的精细化速率限制。

---

## 📝 总结

`app/api/v1/` 模块通过模块化的设计、强大的依赖注入和标准化的数据契约，构建了一个清晰、安全且易于维护的API层。它成功地将HTTP协议的复杂性与核心业务逻辑分离开来，为整个应用提供了一个稳定可靠的入口。 