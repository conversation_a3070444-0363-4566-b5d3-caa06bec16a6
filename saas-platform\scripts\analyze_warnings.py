#!/usr/bin/env python3
"""
警告类型分析脚本 - 分析代码质量检查中的警告分布情况
"""

import ast
import argparse
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Any
import json


class WarningAnalyzer:
    """警告分析器"""

    def __init__(self):
        self.warning_stats = defaultdict(list)
        self.file_stats = {}

    def analyze_warnings(self, root_path: Path) -> Dict[str, Any]:
        """分析警告分布"""
        print("🔍 分析代码质量警告...")

        # 分析API模块
        api_warnings = self._analyze_api_warnings(root_path / "app" / "api")

        # 分析Services模块
        services_warnings = self._analyze_services_warnings(
            root_path / "app" / "services"
        )

        # 汇总结果
        summary = {
            "api_warnings": api_warnings,
            "services_warnings": services_warnings,
            "total_warnings": api_warnings["total"] + services_warnings["total"],
            "priority_actions": self._generate_priority_actions(
                api_warnings, services_warnings
            ),
        }

        return summary

    def _analyze_api_warnings(self, api_path: Path) -> Dict[str, Any]:
        """分析API模块的警告"""
        warnings = {
            "missing_response_model": [],
            "missing_summary": [],
            "missing_description": [],
            "missing_type_annotations": [],
            "missing_error_handling": [],
            "total": 0,
        }

        if not api_path.exists():
            return warnings

        for py_file in api_path.rglob("*.py"):
            if py_file.name.startswith("__"):
                continue

            try:
                content = py_file.read_text(encoding="utf-8")
                file_warnings = self._check_api_file(py_file, content)

                for warning_type, items in file_warnings.items():
                    if warning_type != "total":
                        warnings[warning_type].extend(items)

                warnings["total"] += file_warnings["total"]

            except Exception as e:
                print(f"⚠️ 分析文件失败: {py_file}: {e}")

        return warnings

    def _analyze_services_warnings(self, services_path: Path) -> Dict[str, Any]:
        """分析Services模块的警告"""
        warnings = {
            "missing_type_annotations": [],
            "missing_docstrings": [],
            "async_without_await": [],
            "missing_error_handling": [],
            "total": 0,
        }

        if not services_path.exists():
            return warnings

        for py_file in services_path.rglob("*.py"):
            if py_file.name.startswith("__"):
                continue

            try:
                content = py_file.read_text(encoding="utf-8")
                file_warnings = self._check_service_file(py_file, content)

                for warning_type, items in file_warnings.items():
                    if warning_type != "total":
                        warnings[warning_type].extend(items)

                warnings["total"] += file_warnings["total"]

            except Exception as e:
                print(f"⚠️ 分析文件失败: {py_file}: {e}")

        return warnings

    def _check_api_file(self, file_path: Path, content: str) -> Dict[str, Any]:
        """检查API文件的警告"""
        warnings = {
            "missing_response_model": [],
            "missing_summary": [],
            "missing_description": [],
            "missing_type_annotations": [],
            "missing_error_handling": [],
            "total": 0,
        }

        try:
            tree = ast.parse(content)

            for node in ast.walk(tree):
                # 检查FastAPI路由装饰器
                if isinstance(node, ast.FunctionDef):
                    self._check_api_function(node, file_path, warnings)

        except SyntaxError:
            pass

        return warnings

    def _check_service_file(self, file_path: Path, content: str) -> Dict[str, Any]:
        """检查Service文件的警告"""
        warnings = {
            "missing_type_annotations": [],
            "missing_docstrings": [],
            "async_without_await": [],
            "missing_error_handling": [],
            "total": 0,
        }

        try:
            tree = ast.parse(content)

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    self._check_service_function(node, file_path, warnings)
                elif isinstance(node, ast.AsyncFunctionDef):
                    self._check_async_function(node, file_path, warnings)

        except SyntaxError:
            pass

        return warnings

    def _check_api_function(
        self, node: ast.FunctionDef, file_path: Path, warnings: Dict
    ):
        """检查API函数"""
        # 检查是否有路由装饰器
        has_route_decorator = any(
            isinstance(d, ast.Call)
            and isinstance(d.func, ast.Attribute)
            and d.func.attr in ["get", "post", "put", "delete", "patch"]
            for d in node.decorator_list
        )

        if not has_route_decorator:
            return

        # 检查response_model
        has_response_model = any(
            isinstance(d, ast.Call)
            and any(
                kw.arg == "response_model" for kw in d.keywords if hasattr(kw, "arg")
            )
            for d in node.decorator_list
        )

        if not has_response_model:
            warnings["missing_response_model"].append(
                {
                    "file": str(file_path.relative_to(Path.cwd())),
                    "function": node.name,
                    "line": node.lineno,
                }
            )
            warnings["total"] += 1

        # 检查summary
        has_summary = any(
            isinstance(d, ast.Call)
            and any(kw.arg == "summary" for kw in d.keywords if hasattr(kw, "arg"))
            for d in node.decorator_list
        )

        if not has_summary:
            warnings["missing_summary"].append(
                {
                    "file": str(file_path.relative_to(Path.cwd())),
                    "function": node.name,
                    "line": node.lineno,
                }
            )
            warnings["total"] += 1

        # 检查类型注解
        if not node.returns:
            warnings["missing_type_annotations"].append(
                {
                    "file": str(file_path.relative_to(Path.cwd())),
                    "function": node.name,
                    "line": node.lineno,
                    "type": "return_type",
                }
            )
            warnings["total"] += 1

    def _check_service_function(
        self, node: ast.FunctionDef, file_path: Path, warnings: Dict
    ):
        """检查Service函数"""
        # 检查类型注解
        if not node.returns:
            warnings["missing_type_annotations"].append(
                {
                    "file": str(file_path.relative_to(Path.cwd())),
                    "function": node.name,
                    "line": node.lineno,
                    "type": "return_type",
                }
            )
            warnings["total"] += 1

        # 检查文档字符串
        if not ast.get_docstring(node):
            warnings["missing_docstrings"].append(
                {
                    "file": str(file_path.relative_to(Path.cwd())),
                    "function": node.name,
                    "line": node.lineno,
                }
            )
            warnings["total"] += 1

    def _check_async_function(
        self, node: ast.AsyncFunctionDef, file_path: Path, warnings: Dict
    ):
        """检查异步函数"""
        # 检查是否有await调用
        has_await = any(isinstance(child, ast.Await) for child in ast.walk(node))

        if not has_await:
            warnings["async_without_await"].append(
                {
                    "file": str(file_path.relative_to(Path.cwd())),
                    "function": node.name,
                    "line": node.lineno,
                }
            )
            warnings["total"] += 1

        # 检查类型注解
        if not node.returns:
            warnings["missing_type_annotations"].append(
                {
                    "file": str(file_path.relative_to(Path.cwd())),
                    "function": node.name,
                    "line": node.lineno,
                    "type": "return_type",
                }
            )
            warnings["total"] += 1

    def _generate_priority_actions(
        self, api_warnings: Dict, services_warnings: Dict
    ) -> List[Dict]:
        """生成优化优先级建议"""
        actions = []

        # API优化建议
        if api_warnings["missing_response_model"]:
            actions.append(
                {
                    "priority": 1,
                    "category": "API文档",
                    "action": "添加response_model",
                    "count": len(api_warnings["missing_response_model"]),
                    "files": list(
                        set(w["file"] for w in api_warnings["missing_response_model"])
                    ),
                }
            )

        if api_warnings["missing_summary"]:
            actions.append(
                {
                    "priority": 1,
                    "category": "API文档",
                    "action": "添加summary描述",
                    "count": len(api_warnings["missing_summary"]),
                    "files": list(
                        set(w["file"] for w in api_warnings["missing_summary"])
                    ),
                }
            )

        # Services优化建议
        if services_warnings["missing_type_annotations"]:
            actions.append(
                {
                    "priority": 1,
                    "category": "类型注解",
                    "action": "添加函数返回类型",
                    "count": len(services_warnings["missing_type_annotations"]),
                    "files": list(
                        set(
                            w["file"]
                            for w in services_warnings["missing_type_annotations"]
                        )
                    ),
                }
            )

        if services_warnings["async_without_await"]:
            actions.append(
                {
                    "priority": 2,
                    "category": "异步优化",
                    "action": "检查异步函数必要性",
                    "count": len(services_warnings["async_without_await"]),
                    "files": list(
                        set(w["file"] for w in services_warnings["async_without_await"])
                    ),
                }
            )

        return sorted(actions, key=lambda x: (x["priority"], -x["count"]))

    def generate_report(self, analysis: Dict[str, Any]) -> str:
        """生成分析报告"""
        report = []
        report.append("📊 代码质量警告分析报告")
        report.append("=" * 50)
        report.append("")

        # 总体统计
        report.append("📈 总体统计")
        report.append("-" * 20)
        report.append(f"总警告数: {analysis['total_warnings']}")
        report.append(f"API模块警告: {analysis['api_warnings']['total']}")
        report.append(f"Services模块警告: {analysis['services_warnings']['total']}")
        report.append("")

        # API模块详情
        api_warnings = analysis["api_warnings"]
        report.append("🌐 API模块警告分布")
        report.append("-" * 25)
        report.append(
            f"缺少response_model: {len(api_warnings['missing_response_model'])}"
        )
        report.append(f"缺少summary: {len(api_warnings['missing_summary'])}")
        report.append(f"缺少类型注解: {len(api_warnings['missing_type_annotations'])}")
        report.append("")

        # Services模块详情
        services_warnings = analysis["services_warnings"]
        report.append("🔧 Services模块警告分布")
        report.append("-" * 28)
        report.append(
            f"缺少类型注解: {len(services_warnings['missing_type_annotations'])}"
        )
        report.append(f"缺少文档字符串: {len(services_warnings['missing_docstrings'])}")
        report.append(
            f"异步函数无await: {len(services_warnings['async_without_await'])}"
        )
        report.append("")

        # 优先级建议
        report.append("🎯 优化优先级建议")
        report.append("-" * 22)
        for i, action in enumerate(analysis["priority_actions"][:5], 1):
            report.append(
                f"{i}. [{action['category']}] {action['action']} ({action['count']}个)"
            )
            report.append(f"   涉及文件: {len(action['files'])}个")

        return "\n".join(report)


def main():
    parser = argparse.ArgumentParser(description="分析代码质量警告")
    parser.add_argument("--output", help="输出报告文件")
    args = parser.parse_args()

    analyzer = WarningAnalyzer()
    analysis = analyzer.analyze_warnings(Path.cwd())

    report = analyzer.generate_report(analysis)
    print(report)

    if args.output:
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(report)
        print(f"\n📄 报告已保存: {args.output}")

    # 保存详细数据
    with open("warning_analysis_data.json", "w", encoding="utf-8") as f:
        json.dump(analysis, f, ensure_ascii=False, indent=2)
    print("📊 详细数据已保存: warning_analysis_data.json")


if __name__ == "__main__":
    main()
