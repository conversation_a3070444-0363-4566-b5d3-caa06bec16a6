"""
认证服务测试
测试AuthService的登录、注册、Token管理等功能
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from uuid import uuid4
from datetime import datetime
from fastapi import HTTPException

from app.services.auth_service import (
    AuthService,
    AuthenticationError,
    RegistrationError,
)
from app.models.tenant import Tenant, TenantStatus, TenantPlan
from app.schemas.auth import (
    LoginRequest,
    RegisterRequest,
    RefreshTokenRequest,
    ChangePasswordRequest,
    LoginResponse,
    RegisterResponse,
)


class TestAuthService:
    """测试AuthService服务"""

    @pytest.fixture
    def mock_db():
        """A robust mock for the SQLAlchemy AsyncSession."""
        session = AsyncMock(spec=AsyncSession)
        
        # This mock represents the result of `db.execute(...)`
        execute_result = MagicMock()
        
        # This is the final value from `result.scalar_one_or_none()`
        execute_result.scalar_one_or_none.return_value = None 
        
        # Set the awaitable `execute` method to return the synchronous result object
        session.execute = AsyncMock(return_value=execute_result)
        
        return session

    @pytest.fixture
    def auth_service(self, mock_db):
        """创建AuthService实例"""
        return AuthService(mock_db)

    @pytest.fixture
    def sample_tenant(self):
        """示例租户实例"""
        tenant = Tenant(
            name="Test Company",
            email="<EMAIL>",
            status=TenantStatus.ACTIVE,
            plan=TenantPlan.BASIC,
        )
        tenant.id = uuid4()
        tenant.API_KEY = "test_api_key_for_testing"
        tenant.created_at = datetime.utcnow()
        tenant.updated_at = datetime.utcnow()
        return tenant

    @pytest.fixture
    def login_request(self):
        """登录请求数据"""
        return LoginRequest(email="<EMAIL>", password = os.getenv("TEST_PASSWORD", "test_password"))

    @pytest.fixture
    def register_request(self):
        """注册请求数据"""
        return RegisterRequest(
            email="<EMAIL>",
            password = os.getenv("TEST_PASSWORD", "test_password"),
            confirm_password = os.getenv("TEST_PASSWORD", "test_password"),
            tenant_name="New Company",
        )

    @pytest.mark.asyncio
    async def test_authenticate_user_success(
        self, auth_service, mock_db, sample_tenant
    ):
        """测试用户认证成功"""
        # Arrange
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_tenant

        # Act
        result = await auth_service.authenticate_user("<EMAIL>", "{REPLACE_WITH_ENV_VAR}")

        # Assert
        assert result == sample_tenant
        mock_db.execute.assert_awaited_once()

    @pytest.mark.asyncio
    async def test_authenticate_user_not_found(self, auth_service, mock_db):
        """测试用户认证失败 - 用户不存在"""
        # Arrange
        mock_db.execute.return_value.scalar_one_or_none.return_value = None

        # Act
        result = await auth_service.authenticate_user(
            "<EMAIL>", "{REPLACE_WITH_ENV_VAR}"
        )

        # Assert
        assert result is None

    @pytest.mark.asyncio
    async def test_authenticate_user_database_error(self, auth_service, mock_db):
        """测试用户认证 - 数据库错误"""
        # Arrange
        mock_db.execute.side_effect = Exception("Database error")

        # Act
        result = await auth_service.authenticate_user("<EMAIL>", "{REPLACE_WITH_ENV_VAR}")

        # Assert
        assert result is None

    @pytest.mark.asyncio
    async def test_login_success(
        self, auth_service, mock_db, sample_tenant, login_request
    ):
        """测试登录成功"""
        # Arrange
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_tenant

        # Mock Token生成
        mock_tokens = {
            "access_token": "access_token_123",
            "refresh_token": "refresh_token_123",
            "token_type": "Bearer",
        }

        with patch(
            "app.services.auth_service.create_auth_tokens", return_value=mock_tokens
        ):
            # Act
            result = await auth_service.login(login_request)

            # Assert
            assert isinstance(result, LoginResponse)
            assert result.access_token == "access_token_123"
            assert result.refresh_token == "refresh_token_123"
            assert result.tenant_id == sample_tenant.id
            assert result.user_id == f"tenant:{sample_tenant.id}"

    @pytest.mark.asyncio
    async def test_login_user_not_found(self, auth_service, mock_db, login_request):
        """测试登录失败 - 用户不存在"""
        # Arrange
        mock_db.execute.return_value.scalar_one_or_none.return_value = None

        # Act & Assert
        with pytest.raises(AuthenticationError) as exc_info:
            await auth_service.login(login_request)

        assert "Invalid email or password" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_login_inactive_account(
        self, auth_service, mock_db, sample_tenant, login_request
    ):
        """测试登录失败 - 账户未激活"""
        # Arrange
        sample_tenant.status = TenantStatus.SUSPENDED
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_tenant

        # Act & Assert
        with pytest.raises(AuthenticationError) as exc_info:
            await auth_service.login(login_request)

        assert "Account is not active" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_register_success(self, auth_service, mock_db, register_request):
        """测试注册成功"""
        # Arrange
        # Mock邮箱不存在
        mock_db.execute.return_value.scalar_one_or_none.return_value = None

        # Mock新租户
        new_tenant = Tenant(
            name=register_request.tenant_name,
            email=register_request.email,
            status=TenantStatus.ACTIVE,
            plan=TenantPlan.BASIC,
        )
        new_tenant.id = uuid4()

        with patch("app.services.auth_service.Tenant", return_value=new_tenant):
            with patch.object(Tenant, "generate_api_key", return_value="new_api_key"):
                # Act
                result = await auth_service.register(register_request)

                # Assert
                assert isinstance(result, RegisterResponse)
                assert result.tenant_id == new_tenant.id
                assert result.user_id == f"tenant:{new_tenant.id}"
                assert "successful" in result.message

                # 验证数据库操作
                mock_db.add.assert_called_once()
                mock_db.commit.assert_called_once()
                mock_db.refresh.assert_called_once()

    @pytest.mark.asyncio
    async def test_register_email_exists(
        self, auth_service, mock_db, sample_tenant, register_request
    ):
        """测试注册失败 - 邮箱已存在"""
        # Arrange
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_tenant

        # Act & Assert
        with pytest.raises(RegistrationError) as exc_info:
            await auth_service.register(register_request)

        assert "Email already registered" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_register_database_error(
        self, auth_service, mock_db, register_request
    ):
        """测试注册失败 - 数据库错误"""
        # Arrange
        mock_db.execute.return_value.scalar_one_or_none.return_value = None
        mock_db.commit.side_effect = Exception("Database commit failed")

        with patch("app.services.auth_service.Tenant"):
            # Act & Assert
            with pytest.raises(RegistrationError) as exc_info:
                await auth_service.register(register_request)

            assert "Registration failed" in str(exc_info.value)
            mock_db.rollback.assert_called_once()

    @pytest.mark.asyncio
    async def test_refresh_token_success(self, auth_service):
        """测试Token刷新成功"""
        # Arrange
        refresh_request = RefreshTokenRequest(refresh_token = "test_token_for_testing")

        # Mock Token验证和生成
        mock_payload = {
            "user_id": "tenant:123",
            "tenant_id": "tenant_id_123",
            "email": "<EMAIL>",
            "role": "{REPLACE_WITH_ENV_VAR}",
        }
        mock_new_tokens = {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "token_type": "Bearer",
        }

        with patch("app.services.auth_service.verify_token", return_value=mock_payload):
            with patch(
                "app.services.auth_service.create_auth_tokens",
                return_value=mock_new_tokens,
            ):
                # Act
                result = await auth_service.refresh_token(refresh_request)

                # Assert
                assert result.access_token == "new_access_token"
                assert result.refresh_token == "new_refresh_token"

    @pytest.mark.asyncio
    async def test_change_password_success(
        self, auth_service, mock_db, sample_tenant
    ):
        """测试修改密码成功"""
        # Arrange
        change_request = ChangePasswordRequest(
            current_password = os.getenv("TEST_PASSWORD", "test_password"),
            new_password = os.getenv("TEST_PASSWORD", "test_password"),
            confirm_new_password = os.getenv("TEST_PASSWORD", "test_password"),
        )
        # The service needs a user-like object with a hashed_password
        user_mock = MagicMock()
        user_mock.id = uuid4()
        user_mock.hashed_password = os.getenv("TEST_PASSWORD", "test_password")
        # The service also needs a tenant object for the user
        user_mock.tenant = sample_tenant
        
        mock_db.execute.return_value.scalar_one_or_none.return_value = user_mock

        with patch("app.services.auth_service.verify_password", return_value=True) as mock_verify:
            with patch(
                "app.services.auth_service.get_password_hash", return_value="hashed_new_password"
            ) as mock_hash:
                # Act
                result = await auth_service.change_password(
                    user_id=str(user_mock.id), change_data=change_request
                )

                # Assert
                assert result == {"message": "Password changed successfully"}
                mock_verify.assert_called_once_with("old_password", "hashed_old_password")
                mock_hash.assert_called_once_with("new_password_123")
                assert user_mock.hashed_password == "hashed_new_password"
                mock_db.commit.assert_awaited_once()

    @pytest.mark.asyncio
    async def test_verify_api_key_success(self, auth_service, mock_db, sample_tenant):
        """测试API密钥验证成功"""
        # Arrange
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_tenant

        # Act
        result = await auth_service.verify_api_key(sample_tenant.api_key)

        # Assert
        assert result == sample_tenant

    @pytest.mark.asyncio
    async def test_verify_api_key_invalid(self, auth_service, mock_db):
        """测试API密钥验证失败"""
        # Arrange
        mock_db.execute.return_value.scalar_one_or_none.return_value = None

        # Act
        result = await auth_service.verify_api_key("invalid_api_key")

        # Assert
        assert result is None

    @pytest.mark.asyncio
    async def test_generate_api_key_success(self, auth_service, mock_db, sample_tenant):
        """测试生成API密钥成功"""
        # Arrange
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_tenant

        with patch.object(Tenant, "generate_api_key", return_value="new_generated_key"):
            # Act
            result = await auth_service.generate_api_key(sample_tenant.id)

            # Assert
            assert result == "new_generated_key"
            mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_api_key_tenant_not_found(self, auth_service, mock_db):
        """测试生成API密钥失败 - 租户不存在"""
        # Arrange
        mock_db.execute.return_value.scalar_one_or_none.return_value = None

        # Act & Assert
        with pytest.raises(Exception):
            await auth_service.generate_api_key(uuid4())

    @pytest.mark.asyncio
    async def test_get_user_by_token_success(self, auth_service):
        """测试通过Token获取用户信息成功"""
        # Arrange
        mock_payload = {
            "user_id": "tenant:123",
            "tenant_id": "tenant_id_123",
            "email": "<EMAIL>",
            "role": "{REPLACE_WITH_ENV_VAR}",
        }

        with patch("app.services.auth_service.verify_token", return_value=mock_payload):
            # Act
            result = await auth_service.get_user_by_token("valid_token")

            # Assert
            assert result == mock_payload

    @pytest.mark.asyncio
    async def test_get_user_by_token_invalid(self, auth_service):
        """测试通过Token获取用户信息失败"""
        # Arrange
        with patch(
            "app.services.auth_service.verify_token",
            side_effect=Exception("Invalid token"),
        ):
            # Act
            result = await auth_service.get_user_by_token("invalid_token")

            # Assert
            assert result is None

    def test_logout_success(self, auth_service):
        """测试登出成功"""
        # Arrange
        token = "test_token_for_testing"

        # Act
        result = auth_service.logout(token)

        # Assert
        assert result == {"message": "Logout successful"}

    def test_service_initialization(self, mock_db):
        """测试服务初始化"""
        # Act
        service = AuthService(mock_db)

        # Assert
        assert service.db == mock_db
