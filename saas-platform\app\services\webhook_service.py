"""
Webhook接收服务

处理AstrBot实例的消息上报、状态同步和事件通知
"""

import hashlib
import hmac
import json
from datetime import datetime
from typing import Any, Optional
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.exceptions import SecurityError
from app.models.message import SenderType
from app.models.tenant import Tenant
from app.schemas.message import MessageCreate
from app.schemas.session import SessionStatusUpdate, SessionCreate
from app.services.message_service import MessageService
from app.services.session_service import SessionService
from app.utils.logging import get_logger

logger = get_logger(__name__)


class WebhookService:
    """Webhook接收服务"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.session_service = SessionService(db)
        self.message_service = MessageService(db, self.session_service)

    async def process_message_webhook(
        self,
        tenant_id: UUID,
        webhook_data: dict[str, Any],
        signature: Optional[str] = None,
        raw_body: Optional[bytes] = None,
    ) -> dict[str, Any]:
        # 首先检查签名，这应该在业务逻辑验证之前
        if signature is None:
            raise SecurityError("Missing X-Webhook-Signature header")
        
        await self._verify_webhook_signature(tenant_id, webhook_data, signature, raw_body)

        event_type = webhook_data.get("event_type")
        event_data = webhook_data.get("data", {})

        handler_map = {
            "message.received": self._handle_message_received,
            "message.sent": self._handle_message_sent,
            "session.created": self._handle_session_created,
            "session.closed": self._handle_session_closed,
        }

        if not event_type or not isinstance(event_type, str):
            handler = self._handle_unknown_event
        else:
            handler = handler_map.get(event_type, self._handle_unknown_event)

        # type: ignore
        result = await handler(tenant_id, event_data)

        logger.info(
            "webhook_processed_successfully",
            tenant_id=tenant_id,
            event_type=event_type,
            result=result,
        )

        return {
            "status": "success",
            "event_type": event_type,
            "result": result,
            "processed_at": datetime.utcnow().isoformat(),
        }

    async def process_status_webhook(
        self,
        tenant_id: UUID,
        webhook_data: dict[str, Any],
        signature: Optional[str] = None,
    ) -> dict[str, Any]:
        if signature:
            await self._verify_webhook_signature(tenant_id, webhook_data, signature)

        instance_id = webhook_data.get("instance_id")
        status = webhook_data.get("status")
        metadata = webhook_data.get("metadata", {})

        if not isinstance(instance_id, str) or not isinstance(status, str):
            raise ValueError("instance_id and status are required and must be strings")

        await self._update_instance_status(
            tenant_id=tenant_id,
            instance_id=instance_id,
            status=status,
            metadata=metadata,
        )

        logger.info(
            "status_webhook_processed",
            tenant_id=tenant_id,
            instance_id=instance_id,
            status=status,
        )

        return {
            "status": "success",
            "instance_id": instance_id,
            "updated_status": status,
            "processed_at": datetime.utcnow().isoformat(),
        }

    async def _handle_message_received(
        self, tenant_id: UUID, event_data: dict[str, Any]
    ) -> dict[str, Any]:
        session_id_str = event_data.get("session_id")
        user_id = event_data.get("user_id")
        content = event_data.get("content")
        platform = event_data.get("platform")
        message_metadata = event_data.get("metadata", {})

        if not all([session_id_str, user_id, content]):
            raise ValueError("Missing required fields: session_id, user_id, content")

        if not isinstance(user_id, str):
            raise ValueError("user_id must be a string")

        session_uuid = UUID(session_id_str)

        platform_str = platform or "unknown"

        session_create_data = SessionCreate(
            id=session_uuid, user_id=user_id, platform=platform_str
        )

        session = await self.session_service.create_or_get_session(
            user_id=user_id,
            platform=platform_str,
            tenant_id=tenant_id,
            session_data=session_create_data,
        )

        message_create = MessageCreate(
            session_id=session.id,
            content=content,
            sender_type=SenderType.USER,
            sender_id=user_id,
            metadata={**message_metadata, "platform": platform},
        )
        message = await self.message_service.store_message(message_create, tenant_id)

        return {
            "action": "message_stored",
            "message_id": str(message.id),
            "session_id": str(message.session_id),
            "created_at": message.created_at.isoformat(),
        }

    async def _handle_message_sent(
        self, tenant_id: UUID, event_data: dict[str, Any]
    ) -> dict[str, Any]:
        session_id_str = event_data.get("session_id")
        user_id = event_data.get("user_id")  # agent ID
        content = event_data.get("content")
        message_metadata = event_data.get("metadata", {})

        if not all([session_id_str, user_id, content]):
            raise ValueError("Missing required fields: session_id, user_id, content")

        if not isinstance(user_id, str):
            raise ValueError("user_id must be a string")

        session_uuid = UUID(session_id_str)

        message_create = MessageCreate(
            session_id=session_uuid,
            content=content,
            sender_type=SenderType.STAFF,
            sender_id=user_id,
            metadata=message_metadata,
        )
        message = await self.message_service.store_message(message_create, tenant_id)

        return {
            "message_id": str(message.id),
            "session_id": str(message.session_id),
            "created_at": message.created_at.isoformat(),
        }

    async def _handle_session_created(
        self, tenant_id: UUID, event_data: dict[str, Any]
    ) -> dict[str, Any]:
        user_id = event_data.get("user_id")
        platform = event_data.get("platform")
        session_id_str = event_data.get("session_id")
        metadata = event_data.get("metadata", {})

        if not all([user_id, platform, session_id_str]):
            raise ValueError("Missing required fields: user_id, platform, session_id")

        if not isinstance(user_id, str):
            raise ValueError("user_id must be a string")

        if not isinstance(platform, str):
            raise ValueError("platform must be a string")

        session_create = SessionCreate(
            id=UUID(session_id_str),
            user_id=user_id,
            platform=platform,
            metadata=metadata,
        )
        session = await self.session_service.create_or_get_session(
            user_id=user_id,
            platform=platform,
            tenant_id=tenant_id,
            session_data=session_create,
        )
        return {
            "action": "session_ensured",
            "session_id": str(session.id),
            "created_at": session.created_at.isoformat(),
        }

    async def _handle_session_closed(
        self, tenant_id: UUID, event_data: dict[str, Any]
    ) -> dict[str, Any]:
        session_id_str = event_data.get("session_id")
        reason = event_data.get("reason", "unknown")

        if not session_id_str or not isinstance(session_id_str, str):
            raise ValueError("Missing or invalid session_id")

        session_uuid = UUID(session_id_str)
        update_data = SessionStatusUpdate(status="closed", reason=reason)
        await self.session_service.update_session_status(
            session_uuid, tenant_id, update_data
        )

        return {
            "session_id": session_id_str,
            "status": "closed",
            "reason": reason,
            "closed_at": datetime.utcnow().isoformat(),
        }

    async def _handle_unknown_event(
        self, tenant_id: UUID, event_data: dict[str, Any]
    ) -> dict[str, Any]:
        event_type = event_data.get("event_type")
        logger.warning(
            "unknown_webhook_event_received",
            tenant_id=tenant_id,
            event_type=event_type,
            data=event_data,
        )
        return {"status": "ignored", "reason": f"Unknown event type: {event_type}"}

    async def _update_instance_status(
        self, tenant_id: UUID, instance_id: str, status: str, metadata: dict[str, Any]
    ) -> None:
        logger.info(
            "instance_status_updated_mock",
            tenant_id=tenant_id,
            instance_id=instance_id,
            status=status,
            metadata=metadata,
        )

    async def _verify_webhook_signature(
        self, tenant_id: UUID, webhook_data: dict[str, Any], signature: str, raw_body: Optional[bytes] = None
    ) -> None:
        secret = await self._get_tenant_webhook_secret(tenant_id)
        if not secret:
            logger.error(
                f"Webhook secret not configured for tenant {tenant_id}. Cannot verify signature."
            )
            raise SecurityError(
                f"Webhook secret not configured for tenant {tenant_id}"
            )

        if raw_body:
            message_bytes = raw_body
        else:
            message_bytes = json.dumps(webhook_data, separators=(",", ":")).encode("utf-8")

        computed_sig = hmac.new(
            secret.encode("utf-8"), message_bytes, hashlib.sha256
        ).hexdigest()

        if not hmac.compare_digest(computed_sig, signature):
            logger.warning(f"Invalid webhook signature for tenant {tenant_id}")
            raise SecurityError("Invalid signature")

    async def _get_tenant_webhook_secret(self, tenant_id: UUID) -> Optional[str]:
        stmt = select(Tenant).where(Tenant.id == tenant_id)
        tenant = await self.db.scalar(stmt)
        
        if not tenant or not tenant.extra_data:
            return None
            
        return tenant.extra_data.get("webhook_secret")

    def validate_webhook_data(self, webhook_data: dict[str, Any]) -> bool:
        """
        Validates the basic structure of webhook data.
        """
        if "event_type" not in webhook_data or "data" not in webhook_data:
            raise ValueError(
                "Webhook data must contain 'event_type' and 'data' fields."
            )

        if not isinstance(webhook_data["data"], dict):
            raise ValueError("'data' field must be a dictionary.")

        return True
