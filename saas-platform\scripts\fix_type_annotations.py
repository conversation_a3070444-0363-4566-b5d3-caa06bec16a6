#!/usr/bin/env python3
"""
类型注解修复脚本 - 批量添加缺失的类型注解
"""

import ast
import re
from pathlib import Path
from typing import List, Tuple


class TypeAnnotationFixer:
    """类型注解修复器"""

    def __init__(self):
        self.fixes_applied = 0

    def fix_file(self, file_path: Path) -> bool:
        """修复单个文件的类型注解"""
        try:
            content = file_path.read_text(encoding="utf-8")
            original_content = content

            # 修复__init__方法
            content = self._fix_init_methods(content)

            # 修复_close_client方法
            content = self._fix_close_client_methods(content)

            # 修复__del__方法
            content = self._fix_del_methods(content)

            # 修复其他常见的返回None的方法
            content = self._fix_void_methods(content)

            # 如果有修改，写回文件
            if content != original_content:
                file_path.write_text(content, encoding="utf-8")
                print(f"✅ 修复文件: {file_path}")
                return True
            else:
                print(f"⏭️  无需修复: {file_path}")
                return False

        except Exception as e:
            print(f"❌ 修复文件失败 {file_path}: {e}")
            return False

    def _fix_init_methods(self, content: str) -> str:
        """修复__init__方法的返回类型注解"""
        # 匹配 def __init__(self, ...): 但没有 -> None 的情况
        pattern = r"(def __init__\(self[^)]*\)):"
        replacement = r"\1 -> None:"

        # 只有当方法没有返回类型注解时才添加
        lines = content.split("\n")
        new_lines = []

        for line in lines:
            if re.search(r"def __init__\(.*\):", line) and " -> " not in line:
                # 添加返回类型注解
                new_line = re.sub(pattern, replacement, line)
                new_lines.append(new_line)
                self.fixes_applied += 1
            else:
                new_lines.append(line)

        return "\n".join(new_lines)

    def _fix_close_client_methods(self, content: str) -> str:
        """修复_close_client方法的返回类型注解"""
        pattern = r"(async def _close_client\(self[^)]*\)):"
        replacement = r"\1 -> None:"

        lines = content.split("\n")
        new_lines = []

        for line in lines:
            if (
                re.search(r"async def _close_client\(.*\):", line)
                and " -> " not in line
            ):
                new_line = re.sub(pattern, replacement, line)
                new_lines.append(new_line)
                self.fixes_applied += 1
            else:
                new_lines.append(line)

        return "\n".join(new_lines)

    def _fix_del_methods(self, content: str) -> str:
        """修复__del__方法的返回类型注解"""
        pattern = r"(def __del__\(self[^)]*\)):"
        replacement = r"\1 -> None:"

        lines = content.split("\n")
        new_lines = []

        for line in lines:
            if re.search(r"def __del__\(.*\):", line) and " -> " not in line:
                new_line = re.sub(pattern, replacement, line)
                new_lines.append(new_line)
                self.fixes_applied += 1
            else:
                new_lines.append(line)

        return "\n".join(new_lines)

    def _fix_void_methods(self, content: str) -> str:
        """修复其他返回None的常见方法"""
        void_method_patterns = [
            r"(def close\(self[^)]*\)):",  # close方法
            r"(async def logout\(self[^)]*\)):",  # logout方法
        ]

        lines = content.split("\n")
        new_lines = []

        for line in lines:
            modified = False
            if " -> " not in line:  # 只处理没有返回类型注解的行
                for pattern in void_method_patterns:
                    if re.search(pattern.replace(":", ""), line):
                        replacement = pattern.replace(":", "") + r" -> None:"
                        new_line = re.sub(pattern, replacement, line)
                        if new_line != line:
                            new_lines.append(new_line)
                            self.fixes_applied += 1
                            modified = True
                            break

            if not modified:
                new_lines.append(line)

        return "\n".join(new_lines)

    def fix_services_directory(self, services_path: Path) -> Tuple[int, int]:
        """修复services目录下的所有文件"""
        fixed_files = 0
        total_files = 0

        for py_file in services_path.rglob("*.py"):
            if py_file.name.startswith("__"):
                continue

            total_files += 1
            if self.fix_file(py_file):
                fixed_files += 1

        return fixed_files, total_files


def main():
    """主函数"""
    print("🔧 开始修复类型注解...")

    fixer = TypeAnnotationFixer()
    services_path = Path("app/services")

    if not services_path.exists():
        print(f"❌ 目录不存在: {services_path}")
        return

    fixed_files, total_files = fixer.fix_services_directory(services_path)

    print(f"\n📊 修复统计:")
    print(f"检查文件数: {total_files}")
    print(f"修复文件数: {fixed_files}")
    print(f"应用修复数: {fixer.fixes_applied}")

    if fixed_files > 0:
        print(f"\n🎉 成功修复 {fixed_files} 个文件的类型注解！")
    else:
        print(f"\n✅ 所有文件的类型注解都是完整的！")


if __name__ == "__main__":
    main()
