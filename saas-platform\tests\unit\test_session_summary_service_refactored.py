"""
重构后的SessionSummaryService测试
使用标准Mock策略和依赖注入，遵循后端开发最佳实践
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4, UUID
from datetime import datetime
from typing import List

from app.services.session_summary_service import SessionSummaryService
from app.services.llm.base_provider import (
    BaseLLMProvider,
    LLMMessage,
    LLMResponse,
    LLMConfig,
)
from app.models.session import Session, SessionStatus
from app.models.message import Message, SenderType
from app.schemas.message import MessageRead
from app.schemas.session import SessionRead


class MockLLMProvider(BaseLLMProvider):
    """专业Mock LLM提供商，用于测试"""

    def __init__(self, config: LLMConfig, api_key: str):
        super().__init__(config, api_key)
        self.call_count = 0
        self.last_context = None

    async def generate_response(self, context: List[LLMMessage]) -> LLMResponse:
        """模拟LLM响应生成"""
        self.call_count += 1
        self.last_context = context

        # 根据输入内容生成不同类型的响应
        user_message = context[-1].content if context else ""

        if "简要总结" in user_message:
            content = "用户咨询产品问题，客服提供了专业解答，问题得到解决。"
        elif "详细总结" in user_message:
            content = """
1. 用户咨询背景：关于产品功能的询问
2. 主要问题和需求：了解产品特性和使用方法
3. 客服处理过程：耐心解答并提供详细说明
4. 解决方案：提供了完整的使用指南
5. 最终结果：用户问题得到满意解决
6. 用户满意度预估：较高
            """.strip()
        elif "深度分析" in user_message:
            content = """
1. 用户情绪变化趋势：从疑惑到理解，情绪积极
2. 问题复杂程度评估：中等复杂度，需要专业解答
3. 客服专业度表现：表现专业，回答准确
4. 沟通效率分析：沟通高效，响应及时
5. 改进建议：可以进一步优化响应速度
6. 风险点识别：无明显风险点
            """.strip()
        else:
            content = "模拟LLM响应内容"

        return LLMResponse(
            content=content,
            model=self.config.model,
            usage={"prompt_tokens": 100, "completion_tokens": 50, "total_tokens": 150},
            metadata={},
        )

    @property
    def provider_name(self) -> str:
        return "mock"

    def supported_models(self) -> List[str]:
        return ["test-model", "integration-test"]

    def validate_config(self) -> None:
        pass

    async def get_token_count(self, text: str) -> int:
        return len(text.split())

    async def generate_stream_response(self, context: List[LLMMessage]):
        if False:  # This is a mock, so we don't need a real implementation
            yield


class TestSessionSummaryServiceRefactored:
    """重构后的SessionSummaryService测试类"""

    @pytest.fixture
    def mock_db_session(self):
        """标准数据库会话Mock"""
        mock_db = AsyncMock()

        # 配置数据库操作Mock
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none = AsyncMock()
        mock_result.scalars = AsyncMock()
        mock_result.scalars.return_value.all = AsyncMock(return_value=[])

        mock_db.execute = AsyncMock(return_value=mock_result)
        mock_db.add = MagicMock()
        mock_db.commit = AsyncMock()
        mock_db.rollback = AsyncMock()
        mock_db.refresh = AsyncMock()

        return mock_db

    @pytest.fixture
    def mock_llm_provider(self):
        """Mock LLM提供商"""
        config = LLMConfig(model="test-model")
        return MockLLMProvider(config, "test-api-key")

    @pytest.fixture
    def sample_session(self):
        """示例会话对象"""
        session = Session(
            id=uuid4(),
            tenant_id=uuid4(),
            user_id="test_user_123",
            platform="telegram",
            status=SessionStatus.ACTIVE,
        )
        session.created_at = datetime(2024, 1, 1, 10, 0, 0)
        session.updated_at = datetime(2024, 1, 1, 11, 30, 0)
        return session

    @pytest.fixture
    def sample_messages(self):
        """示例消息列表"""
        base_time = datetime(2024, 1, 1, 10, 0, 0)
        session_id = uuid4()
        tenant_id = uuid4()

        messages = [
            MessageRead(
                id=1,
                tenant_id=tenant_id,
                session_id=session_id,
                content="你好，我想了解一下产品功能",
                sender_type=SenderType.USER,
                sender_id="test_user_123",
                message_type="text",
                timestamp=base_time,
                created_at=base_time,
                metadata={},
            ),
            MessageRead(
                id=2,
                tenant_id=tenant_id,
                session_id=session_id,
                content="您好！我很乐意为您介绍我们的产品功能。我们的产品主要包括...",
                sender_type=SenderType.STAFF,
                sender_id="agent_001",
                message_type="text",
                timestamp=base_time.replace(minute=5),
                created_at=base_time.replace(minute=5),
                metadata={},
            ),
            MessageRead(
                id=3,
                tenant_id=tenant_id,
                session_id=session_id,
                content="明白了，谢谢你的详细解答！",
                sender_type=SenderType.USER,
                sender_id="test_user_123",
                message_type="text",
                timestamp=base_time.replace(minute=10),
                created_at=base_time.replace(minute=10),
                metadata={},
            ),
        ]

        return messages

    @pytest.fixture
    def summary_service(self, mock_db_session, mock_llm_provider):
        """使用依赖注入的SessionSummaryService实例"""
        return SessionSummaryService(db=mock_db_session, llm_provider=mock_llm_provider)

    # @pytest.mark.asyncio
    # async def test_generate_session_summary_with_dependency_injection(
    #     self,
    #     summary_service,
    #     mock_db_session,
    #     mock_llm_provider,
    #     sample_session,
    #     sample_messages,
    # ):
    #     """测试使用依赖注入的会话总结生成"""
    #     session_id = sample_session.id
    #     tenant_id = sample_session.tenant_id

    #     # Mock会话服务返回
    #     with patch.object(
    #         summary_service.session_service, "get_session"
    #     ) as mock_get_session:
    #         mock_get_session.return_value = sample_session

    #         # Mock消息获取
    #         with patch.object(
    #             summary_service, "_get_session_messages"
    #         ) as mock_get_messages:
    #             mock_get_messages.return_value = sample_messages

    #             # 执行测试
    #             result = await summary_service.generate_session_summary(
    #                 session_id=session_id, tenant_id=tenant_id, summary_type="detailed"
    #             )

    #             # 验证结果结构
    #             assert result is not None
    #             assert result["session_id"] == str(session_id)
    #             assert result["tenant_id"] == str(tenant_id)
    #             assert result["summary_type"] == "detailed"
    #             assert "generated_at" in result
    #             assert "basic_stats" in result
    #             assert "summary_content" in result
    #             assert "behavior_analysis" in result
    #             assert "quality_assessment" in result
    #             assert "session_info" in result

    #             # 验证基础统计
    #             basic_stats = result["basic_stats"]
    #             assert basic_stats["total_messages"] == 3
    #             assert basic_stats["user_messages"] == 2
    #             assert basic_stats["agent_messages"] == 1

    #             # 验证LLM提供商被调用
    #             assert mock_llm_provider.call_count > 0

    #             # 验证会话信息
    #             session_info = result["session_info"]
    #             assert session_info["user_id"] == "test_user_123"
    #             assert session_info["status"] == sample_session.status

    @pytest.mark.asyncio
    async def test_session_not_found_error(self, summary_service):
        """测试会话不存在时，服务应抛出ValueError"""
        non_existent_session_id = uuid4()
        tenant_id = uuid4()

        with patch.object(
            summary_service.session_service, "get_session"
        ) as mock_get_session:
            mock_get_session.return_value = None

            with pytest.raises(ValueError, match="Session .* not found"):
                await summary_service.generate_session_summary(
                    session_id=non_existent_session_id, tenant_id=tenant_id
                )

    @pytest.mark.asyncio
    async def test_empty_messages_handling(self, summary_service, sample_session):
        """测试空消息列表的处理"""
        with patch.object(
            summary_service.session_service, "get_session"
        ) as mock_get_session:
            mock_get_session.return_value = sample_session

            with patch.object(
                summary_service, "_get_session_messages"
            ) as mock_get_messages:
                mock_get_messages.return_value = []

                result = await summary_service.generate_session_summary(
                    session_id=sample_session.id, tenant_id=sample_session.tenant_id
                )

                assert result["summary_type"] == "empty"
                assert result["reason"] == "无对话内容"
                assert result["basic_stats"]["total_messages"] == 0

    @pytest.mark.asyncio
    async def test_invalid_summary_type_error(
        self, summary_service, sample_session, sample_messages
    ):
        """测试无效总结类型的错误处理"""
        with patch.object(
            summary_service.session_service, "get_session"
        ) as mock_get_session:
            mock_get_session.return_value = sample_session

            with patch.object(
                summary_service, "_get_session_messages"
            ) as mock_get_messages:
                mock_get_messages.return_value = sample_messages

                with pytest.raises(ValueError, match="Unsupported summary type"):
                    await summary_service.generate_session_summary(
                        session_id=sample_session.id,
                        tenant_id=sample_session.tenant_id,
                        summary_type="invalid_type",
                    )

    @pytest.mark.asyncio
    async def test_llm_provider_fallback_mechanism(self, mock_db_session):
        """测试在没有显式提供LLM Provider时，服务能够正确初始化"""
        service = SessionSummaryService(db=mock_db_session)  # 不注入LLM提供商
        # 此测试仅验证初始化，不进行LLM调用
        assert service is not None
        assert service.llm_provider is None

    # def test_calculate_basic_stats(
    #     self, summary_service, sample_messages, sample_session
    # ):
    #     """测试基础统计计算"""
    #     stats = summary_service._calculate_basic_stats(sample_messages, sample_session)

    #     assert stats["total_messages"] == 3
    #     assert stats["user_messages"] == 2
    #     assert stats["agent_messages"] == 1
    #     assert stats["duration_minutes"] > 0
    #     assert stats["avg_response_time_seconds"] > 0

    # def test_analyze_user_behavior(self, summary_service, sample_messages):
    #     """测试用户行为分析"""
    #     analysis = summary_service._analyze_user_behavior(sample_messages, uuid4())
    #     assert "emotion_indicators" in analysis
    #     assert "overall_tone" in analysis

    # def test_assess_service_quality(self, summary_service, sample_messages):
    #     """测试服务质量评估"""
    #     tenant_id = uuid4()
    #     assessment = summary_service._assess_service_quality(sample_messages, tenant_id)

    #     assert "response_timeliness" in assessment
    #     assert "problem_resolution" in assessment


class TestSessionSummaryServiceIntegration:
    """SessionSummaryService集成测试"""

    @pytest.mark.asyncio
    async def test_real_workflow_simulation(self):
        """模拟真实工作流程的集成测试"""
        # 这个测试不使用依赖注入，而是测试实际的组件集成
        mock_db = AsyncMock()

        # 配置真实的Mock Provider
        config = LLMConfig(model="integration-test")
        mock_llm = MockLLMProvider(config, "test-key")

        service = SessionSummaryService(db=mock_db, llm_provider=mock_llm)

        # 创建测试数据
        session_id = uuid4()
        tenant_id = uuid4()

        # Mock依赖服务
        sample_session = Session(
            id=session_id,
            tenant_id=tenant_id,
            user_id="integration_test_user",
            platform="test_platform",
            status=SessionStatus.CLOSED,
        )
        sample_session.created_at = datetime.utcnow()
        sample_session.updated_at = datetime.utcnow()

        sample_messages = [
            MessageRead(
                id=1,
                tenant_id=tenant_id,
                session_id=session_id,
                content="集成测试用户消息",
                sender_type="user",
                sender_id="integration_test_user",
                message_type="text",
                timestamp=datetime.utcnow(),
                created_at=datetime.utcnow(),
                metadata={},
            )
        ]

        with patch.object(service.session_service, "get_session") as mock_get_session:
            mock_get_session.return_value = sample_session

            with patch.object(service, "_get_session_messages") as mock_get_messages:
                mock_get_messages.return_value = sample_messages

                # 执行完整流程
                result = await service.generate_session_summary(
                    session_id=session_id, tenant_id=tenant_id, summary_type="detailed"
                )

                # 验证完整结果
                assert result is not None
                assert all(
                    key in result
                    for key in [
                        "session_id",
                        "tenant_id",
                        "summary_type",
                        "generated_at",
                        "basic_stats",
                        "summary_content",
                        "behavior_analysis",
                        "quality_assessment",
                        "session_info",
                    ]
                )

                # 验证LLM被正确调用
                assert mock_llm.call_count > 0
                assert mock_llm.last_context is not None
