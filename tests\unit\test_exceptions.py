"""
统一异常管理系统测试

测试 app.core.exceptions 模块中的所有异常类和工具函数
"""

import pytest
from fastapi import status
from unittest.mock import Mock

from app.core.exceptions import (
    AstrBotBaseException,
    AstrBotHTTPException,
    AuthenticationError,
    AuthorizationError,
    PermissionError,
    SecurityError,
    TokenExpiredError,
    InvalidTokenError,
    TenantAccessError,
    TenantNotFoundError,
    BusinessLogicError,
    RegistrationError,
    ResourceNotFoundError,
    ResourceConflictError,
    LLMProviderError,
    LLMRateLimitError,
    LLMQuotaExceededError,
    LLMInvalidRequestError,
    ValidationError,
    create_http_exception_from_base,
    get_exception_details
)


class TestAstrBotBaseException:
    """测试基础异常类"""

    def test_basic_exception_creation(self):
        """测试基础异常的创建"""
        message = "Test error message"
        exception = AstrBotBaseException(message)
        
        assert exception.message == message
        assert exception.error_code == "AstrBotBaseException"
        assert exception.details == {}
        assert str(exception) == message

    def test_exception_with_error_code(self):
        """测试带错误代码的异常"""
        message = "Custom error"
        error_code = "CUSTOM_ERROR"
        exception = AstrBotBaseException(message, error_code=error_code)
        
        assert exception.message == message
        assert exception.error_code == error_code
        assert exception.details == {}

    def test_exception_with_details(self):
        """测试带详细信息的异常"""
        message = "Error with details"
        details = {"field": "username", "value": "invalid"}
        exception = AstrBotBaseException(message, details=details)
        
        assert exception.message == message
        assert exception.details == details


class TestAstrBotHTTPException:
    """测试HTTP异常基类"""

    def test_http_exception_creation(self):
        """测试HTTP异常的创建"""
        detail = "HTTP error"
        exception = AstrBotHTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail
        )
        
        assert exception.status_code == 400
        assert exception.detail == detail
        assert exception.error_code == "AstrBotHTTPException"

    def test_http_exception_with_headers(self):
        """测试带请求头的HTTP异常"""
        detail = "Auth error"
        headers = {"WWW-Authenticate": "Bearer"}
        exception = AstrBotHTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers=headers
        )
        
        assert exception.status_code == 401
        assert exception.detail == detail
        assert exception.headers == headers


class TestAuthenticationErrors:
    """测试认证相关异常"""

    def test_authentication_error_default(self):
        """测试默认认证异常"""
        exception = AuthenticationError()
        
        assert exception.status_code == 401
        assert "credentials" in exception.detail.lower()
        assert exception.error_code == "AUTHENTICATION_FAILED"
        assert "WWW-Authenticate" in exception.headers

    def test_authentication_error_custom(self):
        """测试自定义认证异常"""
        detail = "Invalid token format"
        exception = AuthenticationError(detail)
        
        assert exception.status_code == 401
        assert exception.detail == detail
        assert exception.error_code == "AUTHENTICATION_FAILED"

    def test_authorization_error_default(self):
        """测试默认授权异常"""
        exception = AuthorizationError()
        
        assert exception.status_code == 403
        assert "permissions" in exception.detail.lower()
        assert exception.error_code == "AUTHORIZATION_FAILED"

    def test_permission_error_default(self):
        """测试默认权限异常"""
        exception = PermissionError()
        
        assert exception.status_code == 403
        assert "denied" in exception.detail.lower()
        assert exception.error_code == "PERMISSION_DENIED"


class TestSecurityErrors:
    """测试安全相关异常"""

    def test_security_error_creation(self):
        """测试安全异常创建"""
        message = "Security violation detected"
        exception = SecurityError(message)
        
        assert exception.message == message
        assert exception.error_code == "SecurityError"

    def test_token_expired_error(self):
        """测试Token过期异常"""
        exception = TokenExpiredError()
        
        assert "expired" in exception.message.lower()
        assert exception.error_code == "TOKEN_EXPIRED"

    def test_invalid_token_error(self):
        """测试无效Token异常"""
        exception = InvalidTokenError()
        
        assert "invalid" in exception.message.lower()
        assert exception.error_code == "INVALID_TOKEN"

    def test_invalid_token_error_custom_message(self):
        """测试自定义消息的无效Token异常"""
        message = "Token format is incorrect"
        exception = InvalidTokenError(message)
        
        assert exception.message == message
        assert exception.error_code == "INVALID_TOKEN"


class TestTenantErrors:
    """测试租户相关异常"""

    def test_tenant_access_error_default(self):
        """测试默认租户访问异常"""
        exception = TenantAccessError()
        
        assert exception.status_code == 403
        assert "tenant access denied" in exception.detail.lower()
        assert exception.error_code == "TENANT_ACCESS_DENIED"

    def test_tenant_not_found_error_without_id(self):
        """测试不带ID的租户不存在异常"""
        exception = TenantNotFoundError()
        
        assert exception.status_code == 404
        assert "tenant not found" in exception.detail.lower()
        assert exception.error_code == "TENANT_NOT_FOUND"

    def test_tenant_not_found_error_with_id(self):
        """测试带ID的租户不存在异常"""
        tenant_id = "tenant_123"
        exception = TenantNotFoundError(tenant_id)
        
        assert exception.status_code == 404
        assert tenant_id in exception.detail
        assert exception.error_code == "TENANT_NOT_FOUND"


class TestBusinessLogicErrors:
    """测试业务逻辑异常"""

    def test_business_logic_error(self):
        """测试业务逻辑异常"""
        detail = "Business rule violation"
        exception = BusinessLogicError(detail)
        
        assert exception.status_code == 422
        assert exception.detail == detail
        assert exception.error_code == "BUSINESS_LOGIC_ERROR"

    def test_business_logic_error_custom_code(self):
        """测试自定义错误代码的业务逻辑异常"""
        detail = "Validation failed"
        error_code = "VALIDATION_FAILED"
        exception = BusinessLogicError(detail, error_code)
        
        assert exception.status_code == 422
        assert exception.detail == detail
        assert exception.error_code == error_code

    def test_registration_error_default(self):
        """测试默认注册异常"""
        exception = RegistrationError()
        
        assert exception.status_code == 422
        assert "registration failed" in exception.detail.lower()
        assert exception.error_code == "REGISTRATION_FAILED"

    def test_registration_error_custom(self):
        """测试自定义注册异常"""
        detail = "Email already exists"
        exception = RegistrationError(detail)
        
        assert exception.status_code == 422
        assert exception.detail == detail
        assert exception.error_code == "REGISTRATION_FAILED"


class TestResourceErrors:
    """测试资源相关异常"""

    def test_resource_not_found_error_basic(self):
        """测试基本资源不存在异常"""
        resource_type = "User"
        exception = ResourceNotFoundError(resource_type)
        
        assert exception.status_code == 404
        assert resource_type in exception.detail
        assert "not found" in exception.detail
        assert exception.error_code == "RESOURCE_NOT_FOUND"

    def test_resource_not_found_error_with_id(self):
        """测试带ID的资源不存在异常"""
        resource_type = "Session"
        resource_id = "session_456"
        exception = ResourceNotFoundError(resource_type, resource_id)
        
        assert exception.status_code == 404
        assert resource_type in exception.detail
        assert resource_id in exception.detail
        assert "not found" in exception.detail

    def test_resource_conflict_error_default(self):
        """测试默认资源冲突异常"""
        exception = ResourceConflictError()
        
        assert exception.status_code == 409
        assert "already exists" in exception.detail.lower()
        assert exception.error_code == "RESOURCE_CONFLICT"

    def test_resource_conflict_error_custom(self):
        """测试自定义资源冲突异常"""
        detail = "Username already taken"
        exception = ResourceConflictError(detail)
        
        assert exception.status_code == 409
        assert exception.detail == detail
        assert exception.error_code == "RESOURCE_CONFLICT"


class TestLLMErrors:
    """测试LLM服务异常"""

    def test_llm_provider_error(self):
        """测试LLM提供商异常"""
        message = "LLM service unavailable"
        exception = LLMProviderError(message)
        
        assert exception.message == message
        assert exception.error_code == "LLMProviderError"

    def test_llm_rate_limit_error_default(self):
        """测试默认LLM频率限制异常"""
        exception = LLMRateLimitError()
        
        assert "rate limit" in exception.message.lower()
        assert exception.error_code == "LLM_RATE_LIMIT"

    def test_llm_quota_exceeded_error_default(self):
        """测试默认LLM配额超限异常"""
        exception = LLMQuotaExceededError()
        
        assert "quota" in exception.message.lower()
        assert exception.error_code == "LLM_QUOTA_EXCEEDED"

    def test_llm_invalid_request_error_default(self):
        """测试默认LLM无效请求异常"""
        exception = LLMInvalidRequestError()
        
        assert "invalid request" in exception.message.lower()
        assert exception.error_code == "LLM_INVALID_REQUEST"

    def test_llm_errors_inheritance(self):
        """测试LLM异常继承关系"""
        rate_limit_error = LLMRateLimitError()
        quota_error = LLMQuotaExceededError()
        request_error = LLMInvalidRequestError()
        
        assert isinstance(rate_limit_error, LLMProviderError)
        assert isinstance(quota_error, LLMProviderError)
        assert isinstance(request_error, LLMProviderError)


class TestValidationError:
    """测试验证异常"""

    def test_validation_error_default(self):
        """测试默认验证异常"""
        exception = ValidationError()
        
        assert exception.status_code == 422
        assert "validation failed" in exception.detail.lower()
        assert exception.error_code == "VALIDATION_ERROR"

    def test_validation_error_with_field(self):
        """测试带字段名的验证异常"""
        detail = "Invalid format"
        field = "email"
        exception = ValidationError(detail, field)
        
        assert exception.status_code == 422
        assert detail in exception.detail
        assert field in exception.detail
        assert exception.error_code == "VALIDATION_ERROR"


class TestExceptionUtilityFunctions:
    """测试异常工具函数"""

    def test_create_http_exception_from_base(self):
        """测试基础异常转HTTP异常"""
        base_exception = AstrBotBaseException("Test error", "TEST_ERROR")
        http_exception = create_http_exception_from_base(base_exception)
        
        assert http_exception.status_code == 500
        assert http_exception.detail == "Test error"
        assert http_exception.error_code == "TEST_ERROR"

    def test_create_http_exception_from_base_custom_status(self):
        """测试自定义状态码的基础异常转HTTP异常"""
        base_exception = AstrBotBaseException("Custom error", "CUSTOM_ERROR")
        http_exception = create_http_exception_from_base(base_exception, 422)
        
        assert http_exception.status_code == 422
        assert http_exception.detail == "Custom error"
        assert http_exception.error_code == "CUSTOM_ERROR"

    def test_get_exception_details_base_exception(self):
        """测试获取基础异常详情"""
        details_data = {"field": "username", "value": "test"}
        exception = AstrBotBaseException(
            "Test error", 
            "TEST_ERROR", 
            details_data
        )
        
        details = get_exception_details(exception)
        
        assert details["type"] == "AstrBotBaseException"
        assert details["message"] == "Test error"
        assert details["error_code"] == "TEST_ERROR"
        assert details["details"] == details_data

    def test_get_exception_details_http_exception(self):
        """测试获取HTTP异常详情"""
        exception = AuthenticationError("Invalid credentials")
        
        details = get_exception_details(exception)
        
        assert details["type"] == "AuthenticationError"
        assert details["message"] == "Invalid credentials"
        assert details["error_code"] == "AUTHENTICATION_FAILED"
        assert details["status_code"] == 401

    def test_get_exception_details_standard_exception(self):
        """测试获取标准异常详情"""
        exception = ValueError("Standard error")
        
        details = get_exception_details(exception)
        
        assert details["type"] == "ValueError"
        assert details["message"] == "Standard error"
        assert "error_code" not in details
        assert "status_code" not in details


class TestExceptionIntegration:
    """测试异常系统集成"""

    def test_exception_chaining(self):
        """测试异常链"""
        try:
            try:
                raise InvalidTokenError("Token malformed")
            except InvalidTokenError as e:
                raise AuthenticationError("Auth failed") from e
        except AuthenticationError as auth_error:
            assert auth_error.detail == "Auth failed"
            assert isinstance(auth_error.__cause__, InvalidTokenError)

    def test_exception_with_context_manager(self):
        """测试异常在上下文管理器中的使用"""
        with pytest.raises(TenantNotFoundError) as exc_info:
            raise TenantNotFoundError("tenant_123")
        
        exception = exc_info.value
        assert exception.status_code == 404
        assert "tenant_123" in exception.detail 