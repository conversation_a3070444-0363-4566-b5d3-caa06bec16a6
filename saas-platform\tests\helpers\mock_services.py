# Mock服务类
"""
端到端测试Mock服务
模拟：LLM服务、ASR服务、外部API调用
确保：测试独立性、可预测性、隔离性
"""

from unittest.mock import MagicMock, AsyncMock
from typing import Dict, Any, List, Optional
import asyncio
import json
from datetime import datetime


class MockLLMService:
    """模拟LLM服务"""
    
    def __init__(self):
        self.call_count = 0
        self.responses = []
    
    async def generate_reply(self, context: List[Dict], **kwargs) -> Dict[str, Any]:
        """模拟生成AI回复"""
        self.call_count += 1
        
        # 根据用户输入生成不同的模拟回复
        user_message = context[-1].get("content", "") if context else ""
        
        if "价格" in user_message or "多少钱" in user_message:
            reply = "关于价格，我们有多种套餐可选，基础版299元/月，专业版599元/月，企业版请联系销售获取报价。"
        elif "技术支持" in user_message or "客服" in user_message:
            reply = "我们提供7x24小时技术支持服务，包括电话、邮件和在线客服。技术支持热线：400-xxx-xxxx"
        elif "功能" in user_message or "产品" in user_message:
            reply = "我们的产品主要功能包括：智能客服、多渠道接入、会话管理、数据分析等。"
        else:
            reply = "您好！很高兴为您服务。请问有什么可以帮助您的吗？"
        
        response = {
            "reply": reply,
            "confidence": 0.88,
            "model": "mock-gpt-3.5",
            "token_usage": {
                "prompt_tokens": len(str(context)),
                "completion_tokens": len(reply),
                "total_tokens": len(str(context)) + len(reply)
            },
            "processing_time": 1.2,
            "timestamp": datetime.now().isoformat()
        }
        
        self.responses.append(response)
        return response
    
    async def get_reply_suggestions(self, context: List[Dict]) -> Dict[str, Any]:
        """模拟获取回复建议"""
        suggestions = [
            "我们提供7x24小时技术支持服务，包括电话、邮件和在线客服。",
            "技术支持服务包括软件安装指导、问题排查和定期维护。",
            "您可以通过我们的技术支持热线400-xxx-xxxx联系工程师。"
        ]
        
        return {
            "suggestions": suggestions,
            "confidence": 0.85,
            "generated_at": datetime.now().isoformat()
        }
    
    async def generate_session_summary(self, messages: List[Dict]) -> Dict[str, Any]:
        """模拟生成会话总结"""
        return {
            "summary": "客户咨询产品功能和技术支持服务，已详细介绍相关信息，客户表示满意。",
            "resolution_status": "resolved",
            "customer_satisfaction": "satisfied",
            "key_points": [
                "客户关心产品功能特性",
                "详细介绍了技术支持服务",
                "提供了联系方式"
            ],
            "follow_up_needed": False,
            "tags": ["产品咨询", "技术支持", "已解决"],
            "generated_at": datetime.now().isoformat()
        }


class MockASRService:
    """模拟ASR语音识别服务"""
    
    def __init__(self):
        self.processing_tasks = {}
        self.results = {}
    
    async def process_voice_async(self, voice_file_url: str, **kwargs) -> Dict[str, Any]:
        """模拟异步语音识别"""
        task_id = f"asr_task_{len(self.processing_tasks) + 1}"
        
        # 模拟异步处理
        self.processing_tasks[task_id] = {
            "status": "processing",
            "voice_file_url": voice_file_url,
            "started_at": datetime.now().isoformat()
        }
        
        return {
            "task_id": task_id,
            "status": "processing",
            "estimated_time": 3.0
        }
    
    async def get_asr_result(self, task_id: str) -> Dict[str, Any]:
        """模拟获取ASR结果"""
        if task_id not in self.processing_tasks:
            return {"error": "Task not found"}
        
        # 模拟处理完成
        result = {
            "task_id": task_id,
            "status": "success",
            "result": {
                "text": "你好，我想了解一下你们的产品价格",
                "confidence": 0.94,
                "language": "zh-CN",
                "duration": 12.5,
                "segments": [
                    {
                        "start": 0.0,
                        "end": 12.5,
                        "text": "你好，我想了解一下你们的产品价格",
                        "confidence": 0.94
                    }
                ]
            },
            "processing_time": 2.8,
            "completed_at": datetime.now().isoformat()
        }
        
        self.results[task_id] = result
        return result
    
    def simulate_asr_callback(self, task_id: str, text: str = None) -> Dict[str, Any]:
        """模拟ASR回调结果"""
        if text is None:
            text = "你好，我想了解一下你们的产品功能"
        
        return {
            "task_id": task_id,
            "status": "success",
            "result": {
                "text": text,
                "confidence": 0.95,
                "language": "zh-CN",
                "duration": 10.5
            },
            "timestamp": datetime.now().isoformat()
        }


class MockAstrBotClient:
    """模拟AstrBot实例客户端"""
    
    def __init__(self):
        self.processed_messages = []
        self.config_updates = []
        self.blacklist_cache = set()
    
    async def process_message(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """模拟处理消息"""
        message_id = f"msg_{len(self.processed_messages) + 1}"
        session_id = f"session_{message_data.get('user_id', 'unknown')}"
        
        # 检查黑名单
        user_key = f"{message_data['user_id']}_{message_data['platform']}"
        if user_key in self.blacklist_cache:
            return {
                "status": "blocked",
                "reason": "blacklist",
                "message_id": message_id
            }
        
        result = {
            "status": "processed",
            "message_id": message_id,
            "session_id": session_id,
            "processed_at": datetime.now().isoformat()
        }
        
        self.processed_messages.append(result)
        return result
    
    async def update_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """模拟配置更新"""
        self.config_updates.append({
            "config_type": config_data.get("config_type"),
            "action": config_data.get("action"),
            "data": config_data.get("data"),
            "updated_at": datetime.now().isoformat()
        })
        
        # 如果是黑名单更新，同步到本地缓存
        if config_data.get("config_type") == "blacklist":
            action = config_data.get("action")
            data = config_data.get("data", {})
            user_key = f"{data.get('user_id')}_{data.get('platform')}"
            
            if action == "add":
                self.blacklist_cache.add(user_key)
            elif action == "remove":
                self.blacklist_cache.discard(user_key)
        
        return {
            "status": "success",
            "updated_configs": len(self.config_updates)
        }
    
    async def check_blacklist(self, user_id: str, platform: str) -> Dict[str, Any]:
        """模拟黑名单检查"""
        user_key = f"{user_id}_{platform}"
        return {
            "is_blocked": user_key in self.blacklist_cache,
            "reason": "blacklist" if user_key in self.blacklist_cache else None
        }


class MockStorageService:
    """模拟对象存储服务"""
    
    def __init__(self):
        self.uploaded_files = {}
    
    async def upload_voice_file(self, file_data: bytes, tenant_id: str, **kwargs) -> Dict[str, Any]:
        """模拟语音文件上传"""
        file_id = f"voice_{len(self.uploaded_files) + 1}"
        file_url = f"https://storage.example.com/voices/{tenant_id}/{file_id}.amr"
        
        self.uploaded_files[file_id] = {
            "file_id": file_id,
            "file_size": len(file_data),
            "tenant_id": tenant_id,
            "uploaded_at": datetime.now().isoformat()
        }
        
        return {
            "file_id": file_id,
            "signed_url": file_url,
            "expires_in": 3600
        }
    
    async def generate_signed_url(self, file_id: str, expires_in: int = 3600) -> str:
        """模拟生成签名URL"""
        return f"https://storage.example.com/signed/{file_id}?expires={expires_in}"


class MockWebSocketManager:
    """模拟WebSocket连接管理器"""
    
    def __init__(self):
        self.connections = {}
        self.messages = []
    
    async def connect(self, websocket, tenant_id: str, user_id: str):
        """模拟WebSocket连接"""
        connection_id = f"{tenant_id}_{user_id}"
        self.connections[connection_id] = {
            "websocket": websocket,
            "tenant_id": tenant_id,
            "user_id": user_id,
            "connected_at": datetime.now().isoformat()
        }
    
    async def disconnect(self, tenant_id: str, user_id: str):
        """模拟WebSocket断开"""
        connection_id = f"{tenant_id}_{user_id}"
        if connection_id in self.connections:
            del self.connections[connection_id]
    
    async def send_message(self, tenant_id: str, message_data: Dict[str, Any]):
        """模拟发送WebSocket消息"""
        message = {
            "tenant_id": tenant_id,
            "message_data": message_data,
            "sent_at": datetime.now().isoformat()
        }
        self.messages.append(message)
        
        return {"status": "sent", "recipient_count": 1}
    
    def get_sent_messages(self, tenant_id: str = None) -> List[Dict[str, Any]]:
        """获取已发送的消息"""
        if tenant_id:
            return [msg for msg in self.messages if msg["tenant_id"] == tenant_id]
        return self.messages


# Mock服务工厂
class MockServiceFactory:
    """Mock服务工厂"""
    
    @staticmethod
    def create_llm_service() -> MockLLMService:
        return MockLLMService()
    
    @staticmethod
    def create_asr_service() -> MockASRService:
        return MockASRService()
    
    @staticmethod
    def create_astrbot_client() -> MockAstrBotClient:
        return MockAstrBotClient()
    
    @staticmethod
    def create_storage_service() -> MockStorageService:
        return MockStorageService()
    
    @staticmethod
    def create_websocket_manager() -> MockWebSocketManager:
        return MockWebSocketManager()
    
    @staticmethod
    def create_all_mocks() -> Dict[str, Any]:
        """创建所有Mock服务"""
        return {
            "llm_service": MockServiceFactory.create_llm_service(),
            "asr_service": MockServiceFactory.create_asr_service(),
            "astrbot_client": MockServiceFactory.create_astrbot_client(),
            "storage_service": MockServiceFactory.create_storage_service(),
            "websocket_manager": MockServiceFactory.create_websocket_manager()
        } 