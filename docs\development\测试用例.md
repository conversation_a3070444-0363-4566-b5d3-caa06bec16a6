# 测试计划与用例文档

## 📑 目录
- [1. 测试策略概览](#1-测试策略概览)
- [2. 单元测试](#2-单元测试)
- [3. 集成测试](#3-集成测试)
- [4. 端到端测试](#4-端到端测试)
- [5. 性能测试](#5-性能测试)
- [6. 安全测试](#6-安全测试)
- [7. 可用性与容错测试](#7-可用性与容错测试)
- [8. 灰度与回滚测试](#8-灰度与回滚测试)
- [9. 测试覆盖率要求](#9-测试覆盖率要求)

---

## 1. 测试策略概览

### 1.1 测试金字塔

```
        /\
       /  \
      / E2E \     < 10%
     /______\
    /        \
   /  集成测试 \   < 30%
  /______________\
 /                \
 /    单元测试     \   > 60%
 /__________________\
```

### 1.2 测试目标

| 测试类型 | 覆盖率目标 | 重点验证 |
|---------|------------|----------|
| **单元测试** | > 80% | 业务逻辑正确性 |
| **集成测试** | > 70% | 模块间交互 |
| **API测试** | 100% | 接口契约 |
| **E2E测试** | 核心流程 | 用户体验 |

---

## 2. 单元测试

### 2.1 SaaS主平台单元测试

#### 🔧 Service层测试
```python
# tests/unit/services/test_tenant_service.py
import pytest
from unittest.mock import Mock, patch
from app.services.tenant_service import TenantService
from app.exceptions import TenantNotFoundError, DuplicateTenantError

class TestTenantService:
    @pytest.fixture
    def service(self):
        return TenantService()

    def test_create_tenant_success(self, service):
        """测试创建租户 - 成功场景"""
        tenant_data = {
            "name": "Test Company",
            "email": "<EMAIL>",
            "plan": "standard"
        }

        with patch.object(service.repository, 'create') as mock_create:
            mock_create.return_value = {"id": "tenant_123", **tenant_data}

            result = service.create_tenant(tenant_data)

            assert result["id"] == "tenant_123"
            assert result["name"] == tenant_data["name"]
            mock_create.assert_called_once()

    def test_create_tenant_duplicate_email(self, service):
        """测试创建租户 - 邮箱重复场景"""
        tenant_data = {"email": "<EMAIL>"}

        with patch.object(service.repository, 'get_by_email') as mock_get:
            mock_get.return_value = {"id": "existing_tenant"}

            with pytest.raises(DuplicateTenantError):
                service.create_tenant(tenant_data)
```

#### 💬 Message Service测试
```python
# tests/unit/services/test_message_service.py
class TestMessageService:
    def test_create_message_with_tenant_isolation(self, service):
        """测试消息创建的租户隔离"""
        message_data = {
            "content": "Hello",
            "session_id": "session_123",
            "user_id": "user_456"
        }
        tenant_id = "tenant_789"

        with patch.object(service, '_validate_session_belongs_to_tenant') as mock_validate:
            mock_validate.return_value = True

            result = service.create_message(message_data, tenant_id)

            assert result["tenant_id"] == tenant_id
            mock_validate.assert_called_once_with("session_123", tenant_id)

    def test_get_messages_filters_by_tenant(self, service):
        """测试消息查询的租户过滤"""
        session_id = "session_123"
        tenant_id = "tenant_789"

        with patch.object(service.repository, 'get_by_session') as mock_get:
            mock_get.return_value = [
                {"id": "msg_1", "tenant_id": tenant_id},
                {"id": "msg_2", "tenant_id": tenant_id}
            ]

            result = service.get_session_messages(session_id, tenant_id)

            assert len(result) == 2
            assert all(msg["tenant_id"] == tenant_id for msg in result)
```

#### 🔐 Authentication测试
```python
# tests/unit/auth/test_jwt_service.py
class TestJWTService:
    def test_generate_token_includes_tenant_id(self, jwt_service):
        """测试JWT生成包含租户ID"""
        user_data = {
            "user_id": "user_123",
            "tenant_id": "tenant_456",
            "role": "staff"
        }

        token = jwt_service.generate_token(user_data)
        decoded = jwt_service.decode_token(token)

        assert decoded["tenant_id"] == user_data["tenant_id"]
        assert decoded["user_id"] == user_data["user_id"]
        assert decoded["role"] == user_data["role"]

    def test_decode_expired_token_raises_exception(self, jwt_service):
        """测试解码过期Token抛出异常"""
        with patch('time.time', return_value=time.time() + 3700):  # 1小时后
            with pytest.raises(TokenExpiredError):
                jwt_service.decode_token("expired_token")
```

### 2.2 前端组件单元测试

#### 🎨 Vue组件测试
```typescript
// tests/unit/components/MessageInput.test.ts
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import MessageInput from '@/components/MessageInput.vue'

describe('MessageInput', () => {
  it('emits send event with message content', async () => {
    const wrapper = mount(MessageInput)
    const input = wrapper.find('input[type="text"]')
    const sendButton = wrapper.find('[data-testid="send-button"]')

    await input.setValue('Hello world')
    await sendButton.trigger('click')

    expect(wrapper.emitted('send')).toHaveLength(1)
    expect(wrapper.emitted('send')?.[0]).toEqual(['Hello world'])
  })

  it('clears input after sending message', async () => {
    const wrapper = mount(MessageInput)
    const input = wrapper.find('input[type="text"]')

    await input.setValue('Test message')
    await wrapper.find('[data-testid="send-button"]').trigger('click')

    expect((input.element as HTMLInputElement).value).toBe('')
  })

  it('disables send button when input is empty', () => {
    const wrapper = mount(MessageInput)
    const sendButton = wrapper.find('[data-testid="send-button"]')

    expect(sendButton.attributes('disabled')).toBeDefined()
  })
})
```

### 2.3 AstrBot实例单元测试

#### 🤖 消息处理逻辑测试
```python
# tests/unit/astrbot/test_message_processor.py
class TestMessageProcessor:
    def test_blacklist_check_blocks_user(self, processor):
        """测试黑名单检查拦截用户消息"""
        message = {
            "user_id": "blocked_user",
            "platform": "wechat",
            "content": "Hello"
        }

        with patch.object(processor.blacklist_cache, 'is_blocked') as mock_check:
            mock_check.return_value = True

            result = processor.process_message(message)

            assert result["action"] == "blocked"
            assert result["reason"] == "blacklist"

    def test_voice_message_triggers_asr(self, processor):
        """测试语音消息触发ASR处理"""
        voice_message = {
            "type": "voice",
            "content": "voice_file_url",
            "user_id": "user_123"
        }

        with patch.object(processor.asr_service, 'process_async') as mock_asr:
            result = processor.process_message(voice_message)

            assert result["action"] == "asr_processing"
            mock_asr.assert_called_once()
```

---

## 3. 集成测试

### 3.1 API契约测试

#### 📡 SaaS主平台API测试
```python
# tests/integration/test_message_api.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

@pytest.fixture
def authenticated_client():
    client = TestClient(app)
    # 设置认证token
    token = generate_test_token(tenant_id="test_tenant")
    client.headers.update({"Authorization": f"Bearer {token}"})
    return client

class TestMessageAPI:
    def test_create_message_success(self, authenticated_client):
        """测试创建消息API - 成功场景"""
        payload = {
            "content": "Hello, World!",
            "type": "text",
            "session_id": "session_123"
        }

        response = authenticated_client.post("/api/v1/messages", json=payload)

        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert data["data"]["content"] == payload["content"]
        assert "id" in data["data"]
        assert "timestamp" in data["data"]

    def test_get_messages_filters_by_tenant(self, authenticated_client):
        """测试获取消息API的租户过滤"""
        session_id = "session_123"

        response = authenticated_client.get(f"/api/v1/sessions/{session_id}/messages")

        assert response.status_code == 200
        data = response.json()
        # 验证所有返回的消息都属于当前租户
        for message in data["data"]:
            assert message["tenant_id"] == "test_tenant"

    def test_create_message_cross_tenant_blocked(self, authenticated_client):
        """测试跨租户创建消息被阻止"""
        payload = {
            "content": "Unauthorized message",
            "type": "text",
            "session_id": "other_tenant_session"  # 属于其他租户的session
        }

        response = authenticated_client.post("/api/v1/messages", json=payload)

        assert response.status_code == 403
        assert "无权限" in response.json()["error"]["message"]
```

### 3.2 核心消息流测试

#### 🔄 用户消息上行完整链路测试
```python
# tests/integration/test_message_flow.py
class TestMessageFlow:
    @pytest.mark.asyncio
    async def test_user_message_upward_flow(self):
        """测试用户消息上行完整流程"""
        # 1. 模拟IM平台发送消息到AstrBot
        im_message = {
            "user_id": "user_123",
            "platform": "wechat",
            "content": "我需要帮助",
            "message_type": "text"
        }

        # 2. AstrBot处理消息
        with patch('astrbot.webhook_reporter.report') as mock_webhook:
            astrbot_result = await astrbot_instance.process_message(im_message)

            # 验证AstrBot处理成功
            assert astrbot_result["status"] == "processed"

            # 验证Webhook上报被调用
            mock_webhook.assert_called_once()
            webhook_payload = mock_webhook.call_args[0][0]
            assert webhook_payload["content"] == im_message["content"]

        # 3. SaaS平台接收Webhook
        webhook_response = await saas_platform.handle_webhook(webhook_payload)
        assert webhook_response["status"] == "success"

        # 4. 验证消息存储到数据库
        stored_message = await db.get_message_by_id(webhook_payload["message_id"])
        assert stored_message is not None
        assert stored_message["content"] == im_message["content"]

        # 5. 验证WebSocket推送给客服
        websocket_events = await websocket_mock.get_events()
        assert len(websocket_events) == 1
        assert websocket_events[0]["type"] == "new_message"
```

#### 📞 语音消息ASR处理测试
```python
class TestVoiceMessageFlow:
    @pytest.mark.asyncio
    async def test_voice_message_asr_processing(self):
        """测试语音消息ASR处理完整流程"""
        voice_message = {
            "user_id": "user_123",
            "platform": "wechat",
            "content": "voice_file_url",
            "message_type": "voice"
        }

        # 1. 模拟语音消息处理
        with patch('astrbot.storage.upload_file') as mock_upload:
            with patch('astrbot.asr_service.process') as mock_asr:
                mock_upload.return_value = "signed_url"
                mock_asr.return_value = {
                    "text": "你好，我需要帮助",
                    "confidence": 0.95
                }

                result = await astrbot_instance.process_voice_message(voice_message)

                # 验证文件上传
                mock_upload.assert_called_once()

                # 验证ASR调用
                mock_asr.assert_called_once()

                # 验证结果上报
                assert result["asr_result"]["text"] == "你好，我需要帮助"
```

### 3.3 黑名单功能测试

#### 🛡️ 黑名单同步和拦截测试
```python
class TestBlacklistFlow:
    def test_blacklist_sync_and_blocking(self):
        """测试黑名单同步和拦截功能"""
        # 1. 在SaaS平台添加黑名单
        blacklist_entry = {
            "user_id": "spam_user",
            "platform": "wechat",
            "reason": "恶意用户",
            "tenant_id": "test_tenant"
        }

        saas_response = saas_client.post("/api/v1/blacklist", json=blacklist_entry)
        assert saas_response.status_code == 201

        # 2. 验证AstrBot实例收到同步推送
        with patch('astrbot.blacklist_cache.add_user') as mock_add:
            # 模拟配置推送
            config_update = {
                "config_type": "blacklist",
                "action": "add",
                "data": blacklist_entry
            }

            astrbot_response = astrbot_client.post("/config/update", json=config_update)
            assert astrbot_response.status_code == 200
            mock_add.assert_called_once_with("spam_user", "wechat")

        # 3. 验证黑名单用户消息被拦截
        blocked_message = {
            "user_id": "spam_user",
            "platform": "wechat",
            "content": "spam content"
        }

        with patch('astrbot.webhook_reporter.report') as mock_webhook:
            result = astrbot_instance.process_message(blocked_message)

            assert result["action"] == "blocked"
            # 验证拦截事件上报
            mock_webhook.assert_called_once()
            webhook_payload = mock_webhook.call_args[0][0]
            assert webhook_payload["message_status"] == "blocked_by_blacklist"
```

---

## 4. 端到端测试

### 4.1 客服工作台E2E测试

#### 🎭 客服接待流程测试
```typescript
// tests/e2e/customer-service-workflow.spec.ts
import { test, expect } from '@playwright/test'

test.describe('客服工作台完整流程', () => {
  test.beforeEach(async ({ page }) => {
    // 登录客服系统
    await page.goto('/login')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', 'password123')
    await page.click('[data-testid="login-button"]')
    await expect(page).toHaveURL('/dashboard')
  })

  test('完整的客服接待流程', async ({ page }) => {
    // 1. 等待新消息通知
    await page.waitForSelector('[data-testid="new-message-notification"]')

    // 2. 查看消息列表
    const sessionList = page.locator('[data-testid="session-list"]')
    await expect(sessionList).toBeVisible()

    // 3. 点击第一个会话
    await page.click('[data-testid="session-item"]:first-child')

    // 4. 验证会话详情加载
    const messageArea = page.locator('[data-testid="message-area"]')
    await expect(messageArea).toBeVisible()

    // 5. 查看用户消息
    const userMessage = page.locator('[data-testid="user-message"]').first()
    await expect(userMessage).toContainText('用户问题')

    // 6. 使用智能回复建议
    const smartReply = page.locator('[data-testid="smart-reply-suggestion"]').first()
    if (await smartReply.isVisible()) {
      await smartReply.click()
    }

    // 7. 编辑并发送回复
    const replyInput = page.locator('[data-testid="reply-input"]')
    await replyInput.fill('感谢您的咨询，我来为您详细解答...')
    await page.click('[data-testid="send-button"]')

    // 8. 验证消息发送成功
    await expect(page.locator('[data-testid="message-sent-indicator"]')).toBeVisible()

    // 9. 验证消息显示在对话中
    const staffMessage = page.locator('[data-testid="staff-message"]').last()
    await expect(staffMessage).toContainText('感谢您的咨询')

    // 10. 结束会话
    await page.click('[data-testid="end-session-button"]')
    await page.click('[data-testid="confirm-end-session"]')

    // 11. 验证会话状态更新
    await expect(page.locator('[data-testid="session-status"]')).toContainText('已结束')
  })

  test('语音消息处理流程', async ({ page }) => {
    // 选择包含语音消息的会话
    await page.click('[data-testid="session-with-voice"]')

    // 等待语音消息加载
    const voiceMessage = page.locator('[data-testid="voice-message"]')
    await expect(voiceMessage).toBeVisible()

    // 点击播放语音
    await page.click('[data-testid="play-voice-button"]')

    // 验证ASR转写结果显示
    const asrResult = page.locator('[data-testid="asr-text"]')
    await expect(asrResult).toBeVisible()
    await expect(asrResult).toContainText('转写结果')

    // 基于转写结果回复
    await page.fill('[data-testid="reply-input"]', '根据您的语音描述，建议您...')
    await page.click('[data-testid="send-button"]')
  })

  test('会话转接流程', async ({ page }) => {
    // 打开会话
    await page.click('[data-testid="session-item"]:first-child')

    // 发起转接
    await page.click('[data-testid="transfer-button"]')

    // 选择转接目标客服
    await page.selectOption('[data-testid="transfer-target"]', '<EMAIL>')
    await page.fill('[data-testid="transfer-note"]', '用户问题较复杂，需要专家处理')
    await page.click('[data-testid="confirm-transfer"]')

    // 验证转接成功
    await expect(page.locator('[data-testid="transfer-success-message"]')).toBeVisible()

    // 验证会话状态变更
    await expect(page.locator('[data-testid="session-status"]')).toContainText('已转接')
  })
})
```

### 4.2 管理后台E2E测试

#### 👨‍💼 租户管理流程测试
```typescript
// tests/e2e/admin-panel.spec.ts
test.describe('管理后台功能', () => {
  test('租户创建和配置流程', async ({ page }) => {
    // 管理员登录
    await page.goto('/admin/login')
    await page.fill('[data-testid="admin-email"]', '<EMAIL>')
    await page.fill('[data-testid="admin-password"]', 'admin123')
    await page.click('[data-testid="admin-login-button"]')

    // 创建新租户
    await page.click('[data-testid="create-tenant-button"]')
    await page.fill('[data-testid="tenant-name"]', '测试企业')
    await page.fill('[data-testid="tenant-email"]', '<EMAIL>')
    await page.selectOption('[data-testid="tenant-plan"]', 'standard')
    await page.click('[data-testid="submit-tenant"]')

    // 验证租户创建成功
    await expect(page.locator('[data-testid="tenant-created-message"]')).toBeVisible()

    // 配置租户AstrBot实例
    await page.click('[data-testid="configure-instance-button"]')
    await page.fill('[data-testid="wechat-app-id"]', 'wx123456789')
    await page.fill('[data-testid="wechat-secret"]', 'secret123')
    await page.fill('[data-testid="llm-api-key"]', 'sk-xxxxxxxxx')
    await page.click('[data-testid="save-config"]')

    // 验证实例部署状态
    await expect(page.locator('[data-testid="instance-status"]')).toContainText('运行中')
  })
})
```

---

## 5. 性能测试

### 5.1 消息并发处理测试

#### ⚡ 高并发消息处理
```python
# tests/performance/test_message_concurrency.py
import asyncio
import aiohttp
import time
from concurrent.futures import ThreadPoolExecutor

class TestMessageConcurrency:
    async def test_concurrent_message_processing(self):
        """测试消息并发处理能力"""
        concurrent_users = 100
        messages_per_user = 10

        async def send_message(session, user_id):
            payload = {
                "content": f"消息来自用户{user_id}",
                "type": "text",
                "session_id": f"session_{user_id}"
            }

            start_time = time.time()
            async with session.post("/api/v1/messages", json=payload) as response:
                end_time = time.time()
                return {
                    "status_code": response.status,
                    "response_time": end_time - start_time,
                    "user_id": user_id
                }

        async with aiohttp.ClientSession() as session:
            tasks = []
            for user_id in range(concurrent_users):
                for _ in range(messages_per_user):
                    task = send_message(session, user_id)
                    tasks.append(task)

            start_time = time.time()
            results = await asyncio.gather(*tasks)
            end_time = time.time()

            # 性能指标验证
            total_requests = len(results)
            successful_requests = sum(1 for r in results if r["status_code"] == 201)
            avg_response_time = sum(r["response_time"] for r in results) / total_requests
            throughput = total_requests / (end_time - start_time)

            # 断言性能要求
            assert successful_requests / total_requests > 0.95  # 成功率 > 95%
            assert avg_response_time < 0.2  # 平均响应时间 < 200ms
            assert throughput > 500  # 吞吐量 > 500 TPS
```

### 5.2 数据库查询性能测试

#### 🗄️ 大数据量查询测试
```python
class TestDatabasePerformance:
    def test_message_search_performance(self):
        """测试消息搜索查询性能"""
        # 准备测试数据（10万条消息）
        self.setup_large_dataset(100000)

        # 测试各种查询场景
        test_cases = [
            {"type": "keyword_search", "keyword": "帮助"},
            {"type": "date_range", "start_date": "2024-01-01", "end_date": "2024-01-31"},
            {"type": "user_filter", "user_id": "user_12345"},
            {"type": "complex_query", "keyword": "问题", "date_range": True}
        ]

        for case in test_cases:
            start_time = time.time()
            results = self.execute_search_query(case)
            end_time = time.time()

            query_time = end_time - start_time

            # 查询性能要求
            assert query_time < 0.1  # 查询时间 < 100ms
            assert len(results) > 0  # 有结果返回

            # 验证结果正确性
            if case["type"] == "keyword_search":
                assert all(case["keyword"] in r["content"] for r in results)
```

---

## 6. 安全测试

### 6.1 权限绕过测试

#### 🔒 多租户安全测试
```python
# tests/security/test_tenant_isolation.py
class TestTenantSecurity:
    def test_cross_tenant_data_access_blocked(self):
        """测试跨租户数据访问被阻止"""
        # 租户A的token
        tenant_a_token = generate_test_token(tenant_id="tenant_a")
        # 租户B的资源ID
        tenant_b_session_id = "session_belongs_to_tenant_b"

        response = client.get(
            f"/api/v1/sessions/{tenant_b_session_id}/messages",
            headers={"Authorization": f"Bearer {tenant_a_token}"}
        )

        assert response.status_code == 403
        assert "无权限" in response.json()["error"]["message"]

    def test_sql_injection_protection(self):
        """测试SQL注入防护"""
        malicious_payloads = [
            "'; DROP TABLE messages; --",
            "1' OR '1'='1",
            "admin'/**/OR/**/1=1#"
        ]

        for payload in malicious_payloads:
            response = client.get(f"/api/v1/messages?search={payload}")

            # 应该返回正常响应，而不是数据库错误
            assert response.status_code in [200, 400]
            assert "database error" not in response.text.lower()

    def test_xss_protection(self):
        """测试XSS攻击防护"""
        xss_payload = "<script>alert('XSS')</script>"

        response = client.post("/api/v1/messages", json={
            "content": xss_payload,
            "type": "text",
            "session_id": "session_123"
        })

        assert response.status_code == 201
        message = response.json()["data"]

        # 验证内容被正确转义
        assert "<script>" not in message["content"]
        assert "&lt;script&gt;" in message["content"] or message["content"] != xss_payload
```

### 6.2 认证授权测试

#### 🔑 Token安全测试
```python
class TestAuthSecurity:
    def test_expired_token_rejected(self):
        """测试过期Token被拒绝"""
        expired_token = generate_expired_token()

        response = client.get(
            "/api/v1/messages",
            headers={"Authorization": f"Bearer {expired_token}"}
        )

        assert response.status_code == 401
        assert "token expired" in response.json()["error"]["message"].lower()

    def test_invalid_token_rejected(self):
        """测试无效Token被拒绝"""
        invalid_tokens = [
            "invalid_token",
            "",
            "Bearer ",
            "malformed.jwt.token"
        ]

        for token in invalid_tokens:
            response = client.get(
                "/api/v1/messages",
                headers={"Authorization": f"Bearer {token}"}
            )

            assert response.status_code == 401
```

---

## 7. 可用性与容错测试

### 7.1 服务故障测试

#### 🚨 AstrBot实例故障测试
```python
class TestServiceFailure:
    def test_astrbot_instance_failure_handling(self):
        """测试AstrBot实例故障处理"""
        # 1. 模拟AstrBot实例不可用
        with patch('astrbot_client.health_check') as mock_health:
            mock_health.side_effect = ConnectionError("Instance unreachable")

            # 2. SaaS平台应该检测到故障
            health_status = saas_platform.check_instance_health("tenant_123")
            assert health_status["status"] == "unhealthy"

            # 3. 验证告警被触发
            alerts = monitoring_system.get_alerts()
            assert any("AstrBot instance down" in alert["message"] for alert in alerts)

            # 4. 验证实例重启被触发
            with patch('kubernetes_client.restart_pod') as mock_restart:
                saas_platform.handle_instance_failure("tenant_123")
                mock_restart.assert_called_once()

    def test_database_connection_failure(self):
        """测试数据库连接故障处理"""
        with patch('database.connection.execute') as mock_db:
            mock_db.side_effect = ConnectionError("Database unreachable")

            response = client.get("/api/v1/messages")

            # 应该返回服务不可用，而不是崩溃
            assert response.status_code == 503
            assert "服务暂时不可用" in response.json()["error"]["message"]
```

### 7.2 降级服务测试

#### 📉 服务降级测试
```python
class TestServiceDegradation:
    def test_llm_service_unavailable_fallback(self):
        """测试LLM服务不可用时的降级处理"""
        with patch('llm_service.infer') as mock_llm:
            mock_llm.side_effect = TimeoutError("LLM service timeout")

            # 仍应能接收和存储消息
            response = client.post("/api/v1/messages", json={
                "content": "需要智能回复的消息",
                "type": "text",
                "session_id": "session_123"
            })

            assert response.status_code == 201

            # 验证消息被存储
            message = response.json()["data"]
            assert message["content"] == "需要智能回复的消息"

            # 验证降级提示
            assert "智能回复暂时不可用" in message.get("note", "")
```

---

## 8. 灰度与回滚测试

### 8.1 灰度发布测试

#### 🎯 金丝雀发布测试
```python
class TestCanaryDeployment:
    def test_canary_release_gradual_rollout(self):
        """测试金丝雀发布逐步上线"""
        # 1. 部署新版本到10%流量
        canary_config = {
            "version": "v1.1.0",
            "traffic_percentage": 10,
            "target_tenants": ["tenant_canary_1", "tenant_canary_2"]
        }

        deployment_result = k8s_manager.deploy_canary(canary_config)
        assert deployment_result["status"] == "success"

        # 2. 验证部分用户使用新版本
        for tenant_id in canary_config["target_tenants"]:
            instance_info = k8s_manager.get_instance_info(tenant_id)
            assert instance_info["version"] == "v1.1.0"

        # 3. 监控关键指标
        metrics = monitoring.get_canary_metrics(duration_minutes=30)
        assert metrics["error_rate"] < 0.01  # 错误率 < 1%
        assert metrics["response_time_p95"] < 200  # P95响应时间 < 200ms

        # 4. 如果指标正常，扩大发布范围
        if metrics["error_rate"] < 0.005:
            expanded_config = {**canary_config, "traffic_percentage": 50}
            k8s_manager.update_canary(expanded_config)
```

### 8.2 回滚测试

#### ⏪ 快速回滚测试
```python
class TestRollback:
    def test_automatic_rollback_on_high_error_rate(self):
        """测试高错误率触发自动回滚"""
        # 1. 模拟新版本出现高错误率
        with patch('monitoring.get_error_rate') as mock_error_rate:
            mock_error_rate.return_value = 0.05  # 5%错误率

            # 2. 自动回滚应该被触发
            rollback_result = deployment_manager.check_and_rollback()
            assert rollback_result["action"] == "rollback_triggered"
            assert rollback_result["reason"] == "high_error_rate"

        # 3. 验证回滚到上一稳定版本
        instances = k8s_manager.list_instances()
        for instance in instances:
            assert instance["version"] == "v1.0.0"  # 回滚到稳定版本

        # 4. 验证服务恢复正常
        health_check = service_monitor.overall_health()
        assert health_check["status"] == "healthy"
```

---

## 9. 测试覆盖率要求

### 9.1 覆盖率目标

#### 📊 分模块覆盖率要求
| 模块 | 单元测试覆盖率 | 集成测试覆盖率 | 整体要求 |
|------|---------------|---------------|----------|
| **认证授权** | > 95% | > 90% | 关键安全模块 |
| **租户管理** | > 90% | > 85% | 核心业务逻辑 |
| **消息处理** | > 85% | > 80% | 主要功能模块 |
| **数据统计** | > 80% | > 70% | 分析报表模块 |
| **系统配置** | > 85% | > 75% | 运维管理模块 |

### 9.2 自动化测试覆盖率

#### 🤖 CI/CD集成测试
```yaml
# .github/workflows/test.yml
name: 测试流水线

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: 运行单元测试
        run: |
          pytest tests/unit/ --cov=app --cov-report=xml
          pytest tests/unit/ --cov-fail-under=80

      - name: 上传覆盖率报告
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml

  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - name: 启动测试环境
        run: docker-compose -f docker-compose.test.yml up -d

      - name: 运行集成测试
        run: pytest tests/integration/ --maxfail=5

      - name: 运行E2E测试
        run: npx playwright test

  performance-tests:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: 运行性能测试
        run: pytest tests/performance/ --benchmark-only
```

---

## 📋 测试执行总结

### ✅ 测试执行策略
- **每次提交**: 运行单元测试和快速集成测试
- **每日构建**: 运行完整的集成测试套件
- **发布前**: 运行全量测试包括E2E和性能测试
- **生产监控**: 持续运行健康检查和烟雾测试

### ✅ 质量门禁
- **代码覆盖率**: 必须达到设定的最低要求
- **测试通过率**: 100%的测试必须通过
- **性能指标**: 不能低于基准性能
- **安全扫描**: 不能有高危安全问题

---

**测试计划版本**: v1.0
**最后更新**: 2024年
**下一步**: 测试环境搭建和测试数据准备
