#!/usr/bin/env python3
"""
调试租户API密钥认证问题
"""
import asyncio
from uuid import uuid4

import httpx
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import engine
from app.models.tenant import Tenant, TenantPlan, TenantStatus


async def test_api_key_auth():
    """测试API密钥认证功能"""

    # 创建数据库会话
    async with AsyncSession(engine) as session:
        # 创建测试租户
        sample_tenant = Tenant(
            name="调试测试租户",
            email=f"debug-test-{uuid4().hex[:8]}@test.com",
            plan=TenantPlan.BASIC,
            status=TenantStatus.ACTIVE,
            api_key="debug_api_key_12345678",
        )
        session.add(sample_tenant)
        await session.commit()
        await session.refresh(sample_tenant)

        print(f"创建测试租户: {sample_tenant.id}")
        print(f"API密钥: {sample_tenant.api_key}")

        # 测试API调用
        async with httpx.AsyncClient(base_url="http://localhost:8000") as client:
            try:
                response = await client.get(
                    f"/api/v1/tenants/{sample_tenant.id}",
                    headers={"X-API-Key": "debug_api_key_12345678"},
                )

                print(f"响应状态: {response.status_code}")
                print(f"响应内容: {response.text}")

                if response.status_code != 200:
                    print("认证失败！")
                else:
                    print("认证成功！")

            except Exception as e:
                print(f"请求异常: {e}")

        # 清理测试数据
        await session.delete(sample_tenant)
        await session.commit()


if __name__ == "__main__":
    asyncio.run(test_api_key_auth())
