"""
性能测试套件

包含完整的性能测试场景，支持：
- API响应时间测试
- 数据库查询性能测试
- 并发用户测试
- 内存和CPU使用率监控
- 吞吐量测试
"""

import asyncio
import time
import psutil
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
import logging

import httpx
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from .baseline_manager import (
    PerformanceData, PerformanceMetric, BaselineManager,
    create_performance_data
)
# 注释掉暂时不需要的导入
# from ..conftest import setup_test_db, get_test_client

logger = logging.getLogger(__name__)


@dataclass
class TestScenario:
    """测试场景配置"""
    name: str
    description: str
    endpoint: str
    method: str = "GET"
    payload: Optional[Dict] = None
    headers: Optional[Dict] = None
    expected_response_time: float = 1.0  # 秒
    concurrent_users: int = 1
    iterations: int = 10


@dataclass 
class PerformanceTestResult:
    """性能测试结果"""
    scenario_name: str
    metrics: List[PerformanceData]
    success_count: int
    error_count: int
    total_requests: int
    test_duration: float
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []


class SystemMonitor:
    """系统资源监控器"""
    
    def __init__(self, interval: float = 0.5):
        """
        初始化系统监控器
        
        Args:
            interval: 监控间隔（秒）
        """
        self.interval = interval
        self.monitoring = False
        self.cpu_samples = []
        self.memory_samples = []
        self._monitor_thread = None
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.cpu_samples.clear()
        self.memory_samples.clear()
        
        self._monitor_thread = threading.Thread(target=self._monitor_loop)
        self._monitor_thread.daemon = True
        self._monitor_thread.start()
        
        logger.debug("系统监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=1.0)
        
        logger.debug("系统监控已停止")
    
    def get_average_cpu_usage(self) -> float:
        """获取平均CPU使用率"""
        if not self.cpu_samples:
            return 0.0
        return sum(self.cpu_samples) / len(self.cpu_samples)
    
    def get_average_memory_usage(self) -> float:
        """获取平均内存使用率"""
        if not self.memory_samples:
            return 0.0
        return sum(self.memory_samples) / len(self.memory_samples)
    
    def get_peak_cpu_usage(self) -> float:
        """获取峰值CPU使用率"""
        return max(self.cpu_samples) if self.cpu_samples else 0.0
    
    def get_peak_memory_usage(self) -> float:
        """获取峰值内存使用率"""
        return max(self.memory_samples) if self.memory_samples else 0.0
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 获取CPU使用率
                cpu_percent = psutil.cpu_percent()
                self.cpu_samples.append(cpu_percent)
                
                # 获取内存使用率
                memory = psutil.virtual_memory()
                self.memory_samples.append(memory.percent)
                
                time.sleep(self.interval)
            except Exception as e:
                logger.error(f"系统监控异常: {e}")
                break


class PerformanceTestSuite:
    """性能测试套件"""
    
    # 预定义的测试场景
    DEFAULT_SCENARIOS = [
        TestScenario(
            name="租户详情查询",
            description="获取租户详细信息的API性能测试",
            endpoint="/api/v1/tenants/{tenant_id}",
            method="GET",
            expected_response_time=0.2,
            concurrent_users=5,
            iterations=20
        ),
        TestScenario(
            name="用户列表查询",
            description="分页获取用户列表的性能测试",
            endpoint="/api/v1/users?skip=0&limit=20",
            method="GET",
            expected_response_time=0.3,
            concurrent_users=3,
            iterations=15
        ),
        TestScenario(
            name="会话创建",
            description="创建新会话的性能测试",
            endpoint="/api/v1/sessions",
            method="POST",
            payload={
                "user_id": "test:user_001",
                "platform": "test",
                "channel_type": "direct"
            },
            expected_response_time=0.5,
            concurrent_users=2,
            iterations=10
        ),
        TestScenario(
            name="消息发送",
            description="发送消息的性能测试",
            endpoint="/api/v1/messages",
            method="POST",
            payload={
                "session_id": "test-session-id",
                "content": "测试消息内容",
                "message_type": "text",
                "sender_type": "user",
                "sender_id": "test:user_001"
            },
            expected_response_time=0.4,
            concurrent_users=3,
            iterations=15
        ),
        TestScenario(
            name="数据分析查询",
            description="获取数据分析报告的性能测试",
            endpoint="/api/v1/analytics/sessions-overview",
            method="GET",
            expected_response_time=1.0,
            concurrent_users=2,
            iterations=8
        )
    ]
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化性能测试套件
        
        Args:
            base_url: API基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.baseline_manager = BaselineManager()
        self.system_monitor = SystemMonitor()
        self.test_results = []
        
        # 测试环境设置
        self.test_tenant_id = None
        self.test_api_key = None
        self.test_user_id = None
        
        logger.info(f"性能测试套件初始化完成，目标URL: {self.base_url}")
    
    async def setup_test_data(self):
        """设置测试数据"""
        try:
            # 这里可以创建必要的测试数据
            # 比如测试租户、用户、会话等
            from tests.conftest import sample_tenant, sample_user
            
            # 模拟获取测试数据
            self.test_tenant_id = "test-tenant-id"
            self.test_API_KEY = "test_api_key_for_testing"
            self.test_user_id = "test:user_001"
            
            logger.info("测试数据设置完成")
        except Exception as e:
            logger.error(f"设置测试数据失败: {e}")
            raise
    
    async def run_baseline_tests(self, 
                                scenarios: Optional[List[TestScenario]] = None,
                                environment: str = "test",
                                version: str = "1.0.0") -> PerformanceTestResult:
        """
        运行基准性能测试
        
        Args:
            scenarios: 测试场景列表，如果为None则使用默认场景
            environment: 环境名称
            version: 版本号
            
        Returns:
            PerformanceTestResult: 测试结果
        """
        scenarios = scenarios or self.DEFAULT_SCENARIOS
        
        logger.info(f"开始基准性能测试，场景数: {len(scenarios)}")
        
        # 设置测试数据
        await self.setup_test_data()
        
        # 收集所有性能数据
        all_metrics = []
        total_success = 0
        total_errors = 0
        total_requests = 0
        start_time = time.time()
        
        # 启动系统监控
        self.system_monitor.start_monitoring()
        
        try:
            # 执行每个测试场景
            for scenario in scenarios:
                logger.info(f"执行测试场景: {scenario.name}")
                
                result = await self._run_scenario(scenario, environment, version)
                
                # 累计统计
                all_metrics.extend(result.metrics)
                total_success += result.success_count
                total_errors += result.error_count
                total_requests += result.total_requests
                
                # 短暂休息，避免对系统造成过大压力
                await asyncio.sleep(1.0)
        
        finally:
            # 停止系统监控
            self.system_monitor.stop_monitoring()
        
        test_duration = time.time() - start_time
        
        # 添加系统资源指标
        cpu_metrics = create_performance_data(
            PerformanceMetric.CPU_USAGE,
            self.system_monitor.get_average_cpu_usage(),
            "percent",
            "system_monitoring",
            environment,
            version
        )
        all_metrics.append(cpu_metrics)
        
        memory_metrics = create_performance_data(
            PerformanceMetric.MEMORY_USAGE,
            self.system_monitor.get_average_memory_usage(),
            "percent",
            "system_monitoring",
            environment,
            version
        )
        all_metrics.append(memory_metrics)
        
        # 创建基准数据
        baseline = self.baseline_manager.create_baseline(
            all_metrics,
            environment,
            version,
            f"基准测试 - {len(scenarios)}个场景"
        )
        
        result = PerformanceTestResult(
            scenario_name="baseline_test_suite",
            metrics=all_metrics,
            success_count=total_success,
            error_count=total_errors,
            total_requests=total_requests,
            test_duration=test_duration
        )
        
        logger.info(f"基准性能测试完成，总请求数: {total_requests}, "
                   f"成功率: {total_success/total_requests*100:.1f}%, "
                   f"总耗时: {test_duration:.2f}秒")
        
        return result
    
    async def run_performance_test(self,
                                  scenarios: Optional[List[TestScenario]] = None,
                                  environment: str = "test") -> PerformanceTestResult:
        """
        运行性能测试并与基准对比
        
        Args:
            scenarios: 测试场景列表
            environment: 环境名称
            
        Returns:
            PerformanceTestResult: 测试结果
        """
        scenarios = scenarios or self.DEFAULT_SCENARIOS
        
        logger.info(f"开始性能测试，场景数: {len(scenarios)}")
        
        # 设置测试数据
        await self.setup_test_data()
        
        # 收集性能数据
        all_metrics = []
        total_success = 0
        total_errors = 0
        total_requests = 0
        start_time = time.time()
        
        # 启动系统监控
        self.system_monitor.start_monitoring()
        
        try:
            # 执行测试场景
            for scenario in scenarios:
                result = await self._run_scenario(scenario, environment, "current")
                all_metrics.extend(result.metrics)
                total_success += result.success_count
                total_errors += result.error_count
                total_requests += result.total_requests
                
                await asyncio.sleep(0.5)
        
        finally:
            self.system_monitor.stop_monitoring()
        
        test_duration = time.time() - start_time
        
        # 添加系统资源指标
        all_metrics.extend([
            create_performance_data(
                PerformanceMetric.CPU_USAGE,
                self.system_monitor.get_average_cpu_usage(),
                "percent",
                "system_monitoring",
                environment,
                "current"
            ),
            create_performance_data(
                PerformanceMetric.MEMORY_USAGE,
                self.system_monitor.get_average_memory_usage(),
                "percent",
                "system_monitoring",
                environment,
                "current"
            )
        ])
        
        # 与基准对比
        baseline = self.baseline_manager.get_baseline(environment)
        if baseline:
            comparison_results = self.baseline_manager.compare_with_baseline(all_metrics, baseline)
            self._log_comparison_results(comparison_results)
        else:
            logger.warning(f"未找到环境 {environment} 的基准数据，无法进行对比")
        
        return PerformanceTestResult(
            scenario_name="performance_test_suite",
            metrics=all_metrics,
            success_count=total_success,
            error_count=total_errors,
            total_requests=total_requests,
            test_duration=test_duration
        )
    
    async def _run_scenario(self, 
                           scenario: TestScenario,
                           environment: str,
                           version: str) -> PerformanceTestResult:
        """运行单个测试场景"""
        logger.debug(f"运行场景: {scenario.name}, 并发用户: {scenario.concurrent_users}, "
                    f"迭代次数: {scenario.iterations}")
        
        # 准备URL
        url = scenario.endpoint
        if "{tenant_id}" in url:
            url = url.replace("{tenant_id}", self.test_tenant_id or "test-tenant")
        
        # 准备请求参数
        headers = scenario.headers or {}
        if self.test_api_key:
            headers["X-API-Key"] = self.test_api_key
        
        # 收集指标
        response_times = []
        success_count = 0
        error_count = 0
        errors = []
        
        start_time = time.time()
        
        # 并发执行请求
        if scenario.concurrent_users > 1:
            # 使用线程池进行并发测试
            with ThreadPoolExecutor(max_workers=scenario.concurrent_users) as executor:
                # 创建任务
                futures = []
                for _ in range(scenario.iterations):
                    future = executor.submit(
                        self._execute_request, 
                        url, scenario.method, scenario.payload, headers
                    )
                    futures.append(future)
                
                # 收集结果
                for future in as_completed(futures):
                    try:
                        response_time, success = future.result()
                        response_times.append(response_time)
                        if success:
                            success_count += 1
                        else:
                            error_count += 1
                    except Exception as e:
                        error_count += 1
                        errors.append(str(e))
        else:
            # 串行执行
            for _ in range(scenario.iterations):
                try:
                    response_time, success = await self._execute_async_request(
                        url, scenario.method, scenario.payload, headers
                    )
                    response_times.append(response_time)
                    if success:
                        success_count += 1
                    else:
                        error_count += 1
                except Exception as e:
                    error_count += 1
                    errors.append(str(e))
        
        total_duration = time.time() - start_time
        
        # 计算性能指标
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
        else:
            avg_response_time = max_response_time = min_response_time = 0
        
        # 计算吞吐量（请求/秒）
        throughput = len(response_times) / total_duration if total_duration > 0 else 0
        
        # 计算错误率
        total_requests = success_count + error_count
        error_rate = error_count / total_requests if total_requests > 0 else 0
        
        # 创建性能数据
        metrics = [
            create_performance_data(
                PerformanceMetric.RESPONSE_TIME,
                avg_response_time,
                "seconds",
                scenario.name,
                environment,
                version
            ),
            create_performance_data(
                PerformanceMetric.THROUGHPUT,
                throughput,
                "requests/second",
                scenario.name,
                environment,
                version
            ),
            create_performance_data(
                PerformanceMetric.ERROR_RATE,
                error_rate,
                "percent",
                scenario.name,
                environment,
                version
            )
        ]
        
        # 记录详细统计
        logger.info(f"场景 {scenario.name} 完成: "
                   f"平均响应时间 {avg_response_time:.3f}s, "
                   f"吞吐量 {throughput:.1f} req/s, "
                   f"错误率 {error_rate*100:.1f}%")
        
        return PerformanceTestResult(
            scenario_name=scenario.name,
            metrics=metrics,
            success_count=success_count,
            error_count=error_count,
            total_requests=total_requests,
            test_duration=total_duration,
            errors=errors
        )
    
    def _execute_request(self, 
                        url: str, 
                        method: str, 
                        payload: Optional[Dict], 
                        headers: Dict) -> tuple[float, bool]:
        """执行同步HTTP请求（用于线程池）"""
        import requests
        
        start_time = time.time()
        try:
            full_url = f"{self.base_url}{url}"
            
            if method.upper() == "GET":
                response = requests.get(full_url, headers=headers, timeout=10)
            elif method.upper() == "POST":
                response = requests.post(full_url, json=payload, headers=headers, timeout=10)
            elif method.upper() == "PUT":
                response = requests.put(full_url, json=payload, headers=headers, timeout=10)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            response_time = time.time() - start_time
            success = response.status_code < 400
            
            return response_time, success
            
        except Exception as e:
            response_time = time.time() - start_time
            logger.debug(f"请求失败: {e}")
            return response_time, False
    
    async def _execute_async_request(self,
                                   url: str,
                                   method: str,
                                   payload: Optional[Dict],
                                   headers: Dict) -> tuple[float, bool]:
        """执行异步HTTP请求"""
        start_time = time.time()
        try:
            full_url = f"{self.base_url}{url}"
            
            async with httpx.AsyncClient() as client:
                if method.upper() == "GET":
                    response = await client.get(full_url, headers=headers, timeout=10)
                elif method.upper() == "POST":
                    response = await client.post(full_url, json=payload, headers=headers, timeout=10)
                elif method.upper() == "PUT":
                    response = await client.put(full_url, json=payload, headers=headers, timeout=10)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
            
            response_time = time.time() - start_time
            success = response.status_code < 400
            
            return response_time, success
            
        except Exception as e:
            response_time = time.time() - start_time
            logger.debug(f"异步请求失败: {e}")
            return response_time, False
    
    def _log_comparison_results(self, comparison_results: List):
        """记录性能对比结果"""
        logger.info("=== 性能对比结果 ===")
        
        improved_count = 0
        degraded_count = 0
        stable_count = 0
        
        for result in comparison_results:
            status_emoji = {
                "improved": "🟢",
                "degraded": "🔴", 
                "stable": "🟡"
            }.get(result.status, "⚪")
            
            logger.info(f"{status_emoji} {result.metric_name}: "
                       f"基准值 {result.baseline_value:.3f} -> "
                       f"当前值 {result.current_value:.3f} "
                       f"({result.percentage_change:+.1%})")
            
            if result.status == "improved":
                improved_count += 1
            elif result.status == "degraded":
                degraded_count += 1
            else:
                stable_count += 1
        
        logger.info(f"总结: 改善 {improved_count}, 退化 {degraded_count}, 稳定 {stable_count}")
        
        # 如果有严重退化，记录警告
        critical_degradations = [r for r in comparison_results 
                               if r.status == "degraded" and r.threshold_exceeded]
        if critical_degradations:
            logger.warning(f"发现 {len(critical_degradations)} 个严重性能退化!")
            for result in critical_degradations:
                logger.warning(f"⚠️ {result.metric_name}: 退化 {result.percentage_change:.1%}")


# 便捷函数
async def run_quick_performance_test() -> PerformanceTestResult:
    """运行快速性能测试"""
    suite = PerformanceTestSuite()
    
    # 简化的测试场景
    quick_scenarios = [
        TestScenario(
            name="健康检查",
            description="应用健康状态检查",
            endpoint="/health",
            method="GET",
            expected_response_time=0.1,
            concurrent_users=1,
            iterations=5
        )
    ]
    
    return await suite.run_performance_test(quick_scenarios)


async def establish_performance_baseline() -> PerformanceTestResult:
    """建立性能基准"""
    suite = PerformanceTestSuite()
    return await suite.run_baseline_tests()


if __name__ == "__main__":
    # 示例用法
    async def main():
        print("运行性能基准测试...")
        result = await establish_performance_baseline()
        print(f"基准测试完成，总请求数: {result.total_requests}")
        
        print("\n运行性能回归测试...")
        result = await run_quick_performance_test()
        print(f"回归测试完成，成功率: {result.success_count/result.total_requests*100:.1f}%")
    
    asyncio.run(main()) 