# 📖 技术文档：Webhook服务 (WebhookService)

## 🎯 1. 模块概述

**功能**：处理来自AstrBot实例的Webhook请求，包括消息上报和状态同步。

**核心职责**：
- **签名验证**：验证Webhook请求的HMAC-SHA256签名，确保请求的合法性。
- **事件分发**：根据`event_type`将请求分发到不同的处理函数。
- **消息处理**：处理`message.received`等事件，创建会话和存储消息。
- **状态处理**：处理`instance.status_changed`等事件，更新实例状态。

## 🚀 2. 快速使用

### 2.1 依赖注入

在API端点中注入`WebhookService`：

```python
from app.services.webhook_service import WebhookService, get_webhook_service

@router.post("/webhooks/astrbot/{tenant_id}")
async def handle_astrbot_webhook(
    # ...
    service: WebhookService = Depends(get_webhook_service),
):
    # ...
```

### 2.2 核心方法

- **`process_message_webhook(tenant_id, webhook_data, signature, raw_body)`** - 处理消息相关的Webhook
- **`process_status_webhook(tenant_id, webhook_data, signature)`** - 处理状态相关的Webhook

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    subgraph "核心依赖"
        A[FastAPI] --> B(WebhookService)
        C[SQLAlchemy] --> B
    end

    subgraph "模块交互"
        B --> E(SessionService)
        B --> F(MessageService)
        B --> G(Tenant Model)
    end

    style B fill:#c8e6c9
```

### 3.2 数据流

**Webhook处理流程**：
1. **API接收**：接收来自AstrBot实例的Webhook请求。
2. **服务处理**：
   - 验证签名（如果提供）。
   - 根据`event_type`分发到相应的处理函数。
   - 调用`SessionService`和`MessageService`处理业务逻辑。
3. **响应返回**：返回处理结果。

## 🔧 4. API参考

| 方法 | HTTP动词 | 端点 | 描述 |
|---|---|---|---|
| `handle_astrbot_webhook` | `POST` | `/api/v1/webhooks/astrbot/{tenant_id}` | 统一的Webhook入口 |
| `handle_message_webhook`| `POST` | `/api/v1/webhooks/messages/{tenant_id}` | 兼容的消息Webhook入口|

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_webhook_service.py`
- **集成测试**：`tests/integration/test_webhooks_api.py`

## 💡 6. 维护与扩展

- **事件扩展**：可以在`handler_map`中添加新的事件类型和处理函数。
- **重试机制**：可以添加异步任务和重试逻辑来处理失败的Webhook请求。
- **死信队列**：对于处理失败的Webhook，可以将其发送到死信队列进行后续分析。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 