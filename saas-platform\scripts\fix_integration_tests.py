#!/usr/bin/env python3
"""
修复集成测试问题的脚本

主要修复：
1. Message模型的timestamp字段问题
2. Session模型的platform字段NOT NULL约束
3. Logger的exc_info参数问题
"""

import os
import sys
import re
from pathlib import Path

def fix_conftest_message_creation():
    """修复conftest.py中的消息创建问题"""
    conftest_path = Path("tests/conftest.py")
    
    if not conftest_path.exists():
        print("❌ tests/conftest.py not found")
        return False
    
    content = conftest_path.read_text(encoding='utf-8')
    
    # 确保导入datetime
    if "from datetime import datetime" not in content:
        content = content.replace(
            "from uuid import uuid4, UUID",
            "from uuid import uuid4, UUID\nfrom datetime import datetime"
        )
        print("✅ 添加datetime导入")
    
    # 修复sample_message fixture，确保timestamp是datetime对象
    pattern = r'(message = Message\([\s\S]*?)timestamp="2023-01-01T00:00:00",'
    replacement = r'\1timestamp=datetime.fromisoformat("2023-01-01T00:00:00"),'
    content = re.sub(pattern, replacement, content)
    
    # 修复session创建，确保platform字段
    pattern = r'(session = Session\([\s\S]*?)(\s+\))'
    if 'platform="test_platform"' not in content:
        content = re.sub(
            pattern,
            r'\1\n        platform="test_platform",\2',
            content
        )
        print("✅ 修复Session的platform字段")
    
    conftest_path.write_text(content, encoding='utf-8')
    print("✅ 修复conftest.py中的Message和Session创建问题")
    return True

def fix_logger_exc_info_issues():
    """修复Logger的exc_info参数问题"""
    # 查找所有包含logger.error with exc_info=True的文件
    files_to_fix = [
        "app/api/v1/tenants.py",
        "app/api/v1/sessions.py", 
        "app/api/v1/messages.py",
        "app/api/v1/analytics.py",
        "app/services/analytics_service.py"
    ]
    
    fixed_count = 0
    for file_path in files_to_fix:
        path = Path(file_path)
        if not path.exists():
            continue
            
        content = path.read_text(encoding='utf-8')
        original_content = content
        
        # 修复exc_info=True参数问题
        # 方式1: 将exc_info=True替换为exc_info=1（整数形式）
        content = re.sub(
            r'logger\.(error|exception)\([^)]+exc_info=True\)',
            lambda m: m.group(0).replace('exc_info=True', 'exc_info=1'),
            content
        )
        
        # 方式2: 或者使用logger.exception代替
        content = re.sub(
            r'logger\.error\(([^)]+), exc_info=1\)',
            r'logger.exception(\1)',
            content
        )
        
        if content != original_content:
            path.write_text(content, encoding='utf-8')
            print(f"✅ 修复 {file_path} 中的Logger exc_info问题")
            fixed_count += 1
    
    return fixed_count > 0

def fix_message_factory_method():
    """修复Message工厂方法，确保正确创建消息"""
    conftest_path = Path("tests/conftest.py")
    
    if not conftest_path.exists():
        return False
    
    content = conftest_path.read_text(encoding='utf-8')
    
    # 添加消息工厂函数
    factory_code = '''

def create_test_message(
    tenant_id: str,
    session_id: str, 
    sender_id: str = "test-sender",
    content: str = "Test message",
    message_type=MessageType.TEXT,
    sender_type=SenderType.USER,
    timestamp=None
):
    """创建测试消息，确保所有必需字段都有正确的值"""
    from datetime import datetime
    
    return Message(
        tenant_id=tenant_id,
        session_id=session_id,
        content=content,
        message_type=message_type,
        sender_type=sender_type,
        sender_id=sender_id,
        timestamp=timestamp or datetime.utcnow(),
        attachments=[],
        extra_data={}
    )

def create_test_session(
    tenant_id: str,
    user_id: str = "test-user",
    platform: str = "web"
):
    """创建测试会话，确保所有必需字段都有正确的值"""
    from app.models.session import ChannelType
    
    return Session(
        id=uuid4(),
        tenant_id=tenant_id,
        user_id=user_id,
        platform=platform,  # 确保platform字段不为空
        status=SessionStatus.WAITING,
        channel_type=ChannelType.DIRECT,
        priority=5,
        extra_data={}
    )
'''
    
    if "def create_test_message(" not in content:
        content += factory_code
        print("✅ 添加测试消息工厂函数")
        
        conftest_path.write_text(content, encoding='utf-8')
        return True
    
    return False

def fix_session_model_constraints():
    """检查并修复Session模型的约束问题"""
    session_model_path = Path("app/models/session.py")
    
    if not session_model_path.exists():
        print("❌ app/models/session.py not found")
        return False
    
    content = session_model_path.read_text(encoding='utf-8')
    
    # 确保platform字段有默认值或者nullable=False是正确的
    if 'platform = Column(' in content:
        # 检查platform字段定义
        pattern = r'platform = Column\([^)]+\)'
        match = re.search(pattern, content)
        if match and 'nullable=False' in match.group():
            print("✅ Session.platform字段已正确设置为NOT NULL")
        elif match:
            # 如果没有设置nullable=False，需要确保测试创建Session时提供platform
            print("ℹ️ Session.platform字段需要在测试中提供值")
    
    return True

def main():
    """主函数：执行所有修复"""
    print("🔧 开始修复集成测试问题...")
    
    fixes_applied = []
    
    # 修复1: conftest.py中的消息创建
    if fix_conftest_message_creation():
        fixes_applied.append("Message和Session创建修复")
    
    # 修复2: Logger exc_info问题  
    if fix_logger_exc_info_issues():
        fixes_applied.append("Logger exc_info参数修复")
    
    # 修复3: 添加测试工厂函数
    if fix_message_factory_method():
        fixes_applied.append("测试工厂函数添加")
    
    # 修复4: 检查Session模型约束
    if fix_session_model_constraints():
        fixes_applied.append("Session模型约束检查")
    
    print(f"\n✅ 修复完成！应用的修复：")
    for fix in fixes_applied:
        print(f"  - {fix}")
    
    print(f"\n📋 后续建议：")
    print("  1. 重新运行集成测试验证修复效果")
    print("  2. 检查是否还有其他Logger使用问题")
    print("  3. 确保所有测试数据工厂正确设置必需字段")

if __name__ == "__main__":
    main() 