"""
专业的SessionSummaryService测试
使用标准Mock策略和依赖注入，遵循后端开发最佳实践
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4, UUID
from datetime import datetime
from typing import List
from collections.abc import AsyncIterator

from app.services.session_summary_service import SessionSummaryService
from app.services.llm.base_provider import (
    BaseLLMProvider,
    LLMMessage,
    LLMResponse,
    LLMConfig,
)
from app.models.session import Session, SessionStatus
from app.schemas.message import MessageRead


class TestMockLLMProvider(BaseLLMProvider):
    """专业Mock LLM提供商，用于测试"""

    def __init__(self, config: LLMConfig = None, api_key: str = "test-key"):
        if config is None:
            config = LLMConfig(model="test-model")
        super().__init__(config, api_key)
        self.call_count = 0
        self.last_context = None

    @property
    def provider_name(self) -> str:
        return "TestMock"

    @property
    def supported_models(self) -> list[str]:
        return ["test-model", "test-model-turbo"]

    async def generate_response(
        self, messages: List[LLMMessage], config_override=None
    ) -> LLMResponse:
        """模拟LLM响应生成"""
        self.call_count += 1
        self.last_context = messages

        user_message = messages[-1].content if messages else ""

        if "简要总结" in user_message:
            content = "用户咨询产品问题，客服提供了专业解答，问题得到解决。"
        elif "详细总结" in user_message:
            content = "1. 用户咨询背景：关于产品功能的询问\n2. 主要问题和需求：了解产品特性和使用方法"
        elif "深度分析" in user_message:
            content = "1. 用户情绪变化趋势：从疑惑到理解，情绪积极\n2. 问题复杂程度评估：中等复杂度"
        else:
            content = "模拟LLM响应内容"

        return LLMResponse(
            content=content,
            finish_reason="stop",
            usage={"prompt_tokens": 100, "completion_tokens": 50, "total_tokens": 150},
            metadata={},
        )

    async def generate_stream_response(
        self, messages: List[LLMMessage], config_override=None
    ) -> AsyncIterator[str]:
        """模拟流式响应"""
        response = await self.generate_response(messages, config_override)
        yield response.content

    async def validate_config(self) -> bool:
        """验证配置"""
        return True

    async def get_token_count(self, text: str) -> int:
        """计算token数量"""
        return len(text.split())


class TestSessionSummaryServiceProfessional:
    """专业的SessionSummaryService测试类"""

    @pytest.fixture
    def mock_db_session(self):
        """标准数据库会话Mock"""
        mock_db = AsyncMock()
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none = AsyncMock()
        mock_result.scalars = AsyncMock()
        mock_result.scalars.return_value.all = AsyncMock(return_value=[])
        mock_db.execute = AsyncMock(return_value=mock_result)
        mock_db.add = MagicMock()
        mock_db.commit = AsyncMock()
        mock_db.rollback = AsyncMock()
        mock_db.refresh = AsyncMock()
        return mock_db

    @pytest.fixture
    def mock_llm_provider(self):
        """Mock LLM提供商"""
        config = LLMConfig(model="test-model")
        return TestMockLLMProvider(config, "test-api-key")

    @pytest.fixture
    def sample_session(self):
        """示例会话对象"""
        session = Session(
            id=uuid4(),
            tenant_id=uuid4(),
            user_id="test_user_123",
            platform="telegram",
            status=SessionStatus.ACTIVE,
        )
        session.created_at = datetime(2024, 1, 1, 10, 0, 0)
        session.updated_at = datetime(2024, 1, 1, 11, 30, 0)
        return session

    @pytest.fixture
    def sample_messages(self):
        """示例消息列表"""
        base_time = datetime(2024, 1, 1, 10, 0, 0)
        session_id = uuid4()
        tenant_id = uuid4()

        return [
            MessageRead(
                id=1,
                tenant_id=tenant_id,
                session_id=session_id,
                content="你好，我想了解一下产品功能",
                sender_type="user",
                sender_id="test_user_123",
                message_type="text",
                timestamp=base_time,
                created_at=base_time,
                metadata={},
            ),
            MessageRead(
                id=2,
                tenant_id=tenant_id,
                session_id=session_id,
                content="您好！我很乐意为您介绍我们的产品功能。",
                sender_type="bot",
                sender_id="agent_001",
                message_type="text",
                timestamp=base_time.replace(minute=5),
                created_at=base_time.replace(minute=5),
                metadata={},
            ),
            MessageRead(
                id=3,
                tenant_id=tenant_id,
                session_id=session_id,
                content="明白了，谢谢你的详细解答！",
                sender_type="user",
                sender_id="test_user_123",
                message_type="text",
                timestamp=base_time.replace(minute=10),
                created_at=base_time.replace(minute=10),
                metadata={},
            ),
        ]

    @pytest.fixture
    def summary_service(self, mock_db_session, mock_llm_provider):
        """使用依赖注入的SessionSummaryService实例"""
        return SessionSummaryService(db=mock_db_session, llm_provider=mock_llm_provider)

    @pytest.mark.asyncio
    async def test_generate_brief_summary(
        self, summary_service, sample_session, sample_messages
    ):
        """测试简要总结生成"""
        with patch.object(
            summary_service.session_service, "get_session"
        ) as mock_get_session:
            mock_get_session.return_value = sample_session

            with patch.object(
                summary_service, "_get_session_messages"
            ) as mock_get_messages:
                mock_get_messages.return_value = sample_messages

                result = await summary_service.generate_session_summary(
                    session_id=sample_session.id,
                    tenant_id=sample_session.tenant_id,
                    summary_type="brief",
                )

                assert result["summary_type"] == "brief"
                assert len(result["summary_content"]) > 0

    @pytest.mark.asyncio
    async def test_session_not_found_error(self, summary_service):
        """测试会话不存在的错误处理"""
        with patch.object(
            summary_service.session_service, "get_session"
        ) as mock_get_session:
            mock_get_session.return_value = None

            with pytest.raises(ValueError, match="Session .* not found"):
                await summary_service.generate_session_summary(
                    session_id=uuid4(), tenant_id=uuid4()
                )

    @pytest.mark.asyncio
    async def test_empty_messages_handling(self, summary_service, sample_session):
        """测试空消息列表处理"""
        with patch.object(
            summary_service.session_service, "get_session"
        ) as mock_get_session:
            mock_get_session.return_value = sample_session

            with patch.object(
                summary_service, "_get_session_messages"
            ) as mock_get_messages:
                mock_get_messages.return_value = []

                result = await summary_service.generate_session_summary(
                    session_id=sample_session.id, tenant_id=sample_session.tenant_id
                )

                assert result["summary_type"] == "empty"
                assert result["reason"] == "无对话内容"

    @pytest.mark.asyncio
    async def test_invalid_summary_type_error(
        self, summary_service, sample_session, sample_messages
    ):
        """测试无效总结类型错误"""
        with patch.object(
            summary_service.session_service, "get_session"
        ) as mock_get_session:
            mock_get_session.return_value = sample_session

            with patch.object(
                summary_service, "_get_session_messages"
            ) as mock_get_messages:
                mock_get_messages.return_value = sample_messages

                with pytest.raises(ValueError, match="Unsupported summary type"):
                    await summary_service.generate_session_summary(
                        session_id=sample_session.id,
                        tenant_id=sample_session.tenant_id,
                        summary_type="invalid_type",
                    )

    @pytest.mark.asyncio
    async def test_llm_provider_dependency_injection(self, mock_db_session):
        """测试在没有显式提供LLM Provider时，服务能够正确初始化"""
        service = SessionSummaryService(db=mock_db_session)  # 不注入LLM提供商

        provider = service._get_llm_provider(uuid4())
        assert provider is not None
        # 验证获取到了默认提供商（OpenAI或Mock）

    @pytest.mark.asyncio
    async def test_comprehensive_workflow(self, mock_db_session):
        """测试完整工作流程"""
        config = LLMConfig(model="workflow-test")
        mock_llm = TestMockLLMProvider(config, "workflow-key")
        service = SessionSummaryService(db=mock_db_session, llm_provider=mock_llm)

        # 准备测试数据
        session_id = uuid4()
        tenant_id = uuid4()

        session = Session(
            id=session_id,
            tenant_id=tenant_id,
            user_id="workflow_user",
            platform="test",
            status=SessionStatus.CLOSED,
        )
        session.created_at = datetime.utcnow()
        session.updated_at = datetime.utcnow()

        messages = [
            MessageRead(
                id=1,
                tenant_id=tenant_id,
                session_id=session_id,
                content="工作流程测试消息",
                sender_type="user",
                sender_id="workflow_user",
                message_type="text",
                timestamp=datetime.utcnow(),
                created_at=datetime.utcnow(),
                metadata={},
            )
        ]

        with patch.object(service.session_service, "get_session") as mock_get_session:
            mock_get_session.return_value = session

            with patch.object(service, "_get_session_messages") as mock_get_messages:
                mock_get_messages.return_value = messages

                # 测试三种总结类型
                for summary_type in ["brief", "detailed", "analysis"]:
                    result = await service.generate_session_summary(
                        session_id=session_id,
                        tenant_id=tenant_id,
                        summary_type=summary_type,
                    )

                    assert result["summary_type"] == summary_type
                    assert len(result["summary_content"]) > 0

                # 验证LLM被多次调用
                assert mock_llm.call_count >= 3
