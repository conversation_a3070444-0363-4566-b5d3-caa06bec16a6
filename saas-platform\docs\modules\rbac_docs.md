# 📖 技术文档：RBAC API (rbac.py)

## 🎯 1. 模块概述

**功能**：提供基于角色的访问控制（RBAC）功能，管理角色和权限。

**核心职责**：
- **角色管理**：提供角色的CRUD操作。
- **权限管理**：提供权限的CRUD操作。
- **角色-权限关联**：提供将权限分配给角色的功能。
- **用户-角色关联**：提供将角色分配给用户的功能。

## 🚀 2. 快速使用

### 2.1 依赖注入

```python
from app.services.rbac_service import RBACService, get_rbac_service

@router.post("/roles")
async def create_role(
    # ...
    rbac_service: RBACService = Depends(get_rbac_service),
):
    # ...
```

### 2.2 核心端点

- `POST /roles` - 创建角色
- `GET /roles` - 获取角色列表
- `POST /permissions` - 创建权限
- `GET /permissions` - 获取权限列表

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    A[API Endpoints] --> B(RBACService)
    B --> C(Role Model)
    B --> D(Permission Model)
    B --> E(UserRole Model)
```

### 3.2 数据流

**创建角色流程**：
1. **API接收**：接收创建角色的请求。
2. **服务调用**：调用`RBACService.create_role`。
3. **响应返回**：返回创建的角色。

## 🔧 4. API参考

| HTTP动词 | 端点 | 描述 |
|---|---|---|
| `POST` | `/roles` | 创建角色 |
| `GET` | `/roles` | 获取角色列表 |
|`GET`|`/roles/{role_id}`|获取角色详情|
|`PUT`|`/roles/{role_id}`|更新角色|
|`DELETE`|`/roles/{role_id}`|删除角色|
|`POST`|`/permissions`|创建权限|
|`GET`|`/permissions`|获取权限列表|
|`POST`|`/users/{user_id}/roles`|为用户分配角色|

## 🧪 5. 测试策略

- **集成测试**：`tests/integration/test_rbac_api.py`

## 💡 6. 维护与扩展

- **权限继承**：可以添加角色继承功能，实现更复杂的权限模型。
- **动态权限**：可以添加基于上下文的动态权限检查。
- **权限审计**：可以添加权限变更的审计日志。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 