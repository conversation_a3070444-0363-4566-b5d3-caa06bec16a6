# 质量保证工具知识体系

## 🛠️ 核心工具栈

### 1. 代码质量分析工具

#### SonarQube - 企业级代码质量平台
```yaml
# sonar-project.properties
sonar.projectKey=astrbot-saas
sonar.projectName=AstrBot SaaS Platform
sonar.projectVersion=1.0
sonar.sources=app/
sonar.tests=tests/
sonar.python.coverage.reportPaths=coverage.xml
sonar.python.xunit.reportPath=pytest-results.xml

# 质量门禁配置
sonar.qualitygate.wait=true
sonar.coverage.exclusions=**/*_test.py,**/migrations/**
sonar.cpd.exclusions=**/*_test.py
```

#### PyLint - Python代码静态分析
```ini
# .pylintrc
[MASTER]
load-plugins=pylint_django,pylint_flask

[MESSAGES CONTROL]
disable=missing-docstring,too-few-public-methods

[FORMAT]
max-line-length=88
good-names=i,j,k,ex,Run,_,id,db

[DESIGN]
max-args=7
max-locals=15
max-returns=6
max-branches=12
```

#### Black + isort - 代码格式化
```toml
# pyproject.toml
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  migrations
  | venv
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
```

### 2. 安全扫描工具

#### Bandit - Python安全漏洞扫描
```yaml
# .bandit
exclude_dirs:
  - tests
  - migrations
  - venv

skips:
  - B101  # assert_used
  - B601  # paramiko_calls

tests:
  - B201  # flask_debug_true
  - B501  # request_with_no_cert_validation
  - B502  # ssl_with_bad_version
```

#### Safety - 依赖安全检查
```bash
# 检查已知安全漏洞
safety check --json --output safety-report.json

# 检查许可证合规性
safety license --json
```

#### Trivy - 容器安全扫描
```bash
# 扫描容器镜像
trivy image --format json --output trivy-report.json astrbot-saas:latest

# 扫描文件系统
trivy fs --format json --output fs-scan.json .
```

### 3. 性能测试工具

#### Locust - 负载测试框架
```python
# tests/performance/locustfile.py
from locust import HttpUser, task, between

class AstrBotUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        """用户登录"""
        response = self.client.post("/api/v1/auth/login", json={
            "email": "<EMAIL>",
            "password": "testpass123"
        })
        self.token = response.json()["access_token"]
        self.client.headers.update({
            "Authorization": f"Bearer {self.token}"
        })
    
    @task(3)
    def get_sessions(self):
        """获取会话列表"""
        self.client.get("/api/v1/sessions")
    
    @task(2)
    def create_session(self):
        """创建新会话"""
        self.client.post("/api/v1/sessions", json={
            "name": "Test Session",
            "bot_id": "test-bot-123"
        })
    
    @task(1)
    def send_message(self):
        """发送消息"""
        self.client.post("/api/v1/sessions/test-session/messages", json={
            "content": "Hello, AstrBot!",
            "type": "text"
        })
```

#### pytest-benchmark - 微基准测试
```python
# tests/performance/test_benchmarks.py
import pytest
from app.services.session_service import SessionService

def test_session_creation_performance(benchmark, db_session):
    """测试会话创建性能"""
    service = SessionService(db_session)
    
    def create_session():
        return service.create_session({
            "name": "Benchmark Session",
            "bot_id": "bench-bot-123"
        })
    
    result = benchmark(create_session)
    assert result.id is not None

# 运行基准测试
# pytest tests/performance/test_benchmarks.py --benchmark-only
```

### 4. 测试框架与工具

#### pytest - 主测试框架
```python
# conftest.py - 全局测试配置
import pytest
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from app.core.database import get_db

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def test_engine():
    """测试数据库引擎"""
    engine = create_async_engine(
        "postgresql+asyncpg://test:test@localhost/testdb",
        echo=False
    )
    yield engine
    await engine.dispose()

@pytest.fixture
async def db_session(test_engine):
    """数据库会话固件"""
    async with AsyncSession(test_engine) as session:
        transaction = await session.begin()
        yield session
        await transaction.rollback()
```

#### pytest-cov - 覆盖率测试
```ini
# .coveragerc
[run]
source = app
omit = 
    app/migrations/*
    app/tests/*
    app/core/config.py
    */venv/*

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
```

#### Playwright - E2E测试
```python
# tests/e2e/test_user_flows.py
import pytest
from playwright.async_api import async_playwright

@pytest.fixture(scope="session")
async def browser():
    """浏览器实例"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        yield browser
        await browser.close()

@pytest.mark.asyncio
async def test_complete_user_journey(browser):
    """完整用户旅程测试"""
    page = await browser.new_page()
    
    # 登录
    await page.goto("http://localhost:3000/login")
    await page.fill('[data-testid="email"]', "<EMAIL>")
    await page.fill('[data-testid="password"]', "testpass123")
    await page.click('[data-testid="login-button"]')
    
    # 验证登录成功
    await page.wait_for_selector('[data-testid="dashboard"]')
    
    # 创建会话
    await page.click('[data-testid="new-session"]')
    await page.fill('[data-testid="session-name"]', "E2E Test Session")
    await page.click('[data-testid="create-session"]')
    
    # 发送消息
    await page.fill('[data-testid="message-input"]', "Hello, AstrBot!")
    await page.click('[data-testid="send-button"]')
    
    # 验证消息发送
    await page.wait_for_selector('[data-testid="message-sent"]')
```

### 5. CI/CD集成工具

#### GitHub Actions - 持续集成
```yaml
# .github/workflows/quality-pipeline.yml
name: Quality Assurance Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  code-quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install dependencies
        run: |
          pip install -r requirements-dev.txt
      
      - name: Code formatting check
        run: |
          black --check app/ tests/
          isort --check-only app/ tests/
      
      - name: Linting
        run: |
          pylint app/
          flake8 app/
      
      - name: Security scan
        run: |
          bandit -r app/ -f json -o bandit-report.json
          safety check --json --output safety-report.json
      
      - name: Upload security reports
        uses: actions/upload-artifact@v3
        with:
          name: security-reports
          path: |
            bandit-report.json
            safety-report.json

  unit-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      - name: Run unit tests
        run: |
          pytest tests/unit/ \
            --cov=app \
            --cov-report=xml \
            --cov-report=html \
            --junitxml=pytest-results.xml
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          fail_ci_if_error: true

  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - uses: actions/checkout@v3
      - name: Run integration tests
        run: |
          pytest tests/integration/ -v --tb=short
      
      - name: Performance tests
        run: |
          locust -f tests/performance/locustfile.py \
            --headless -u 10 -r 2 -t 60s \
            --host http://localhost:8000

  e2e-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    steps:
      - uses: actions/checkout@v3
      - name: Install Playwright
        run: |
          pip install playwright
          playwright install
      
      - name: Run E2E tests
        run: |
          pytest tests/e2e/ -v --tb=short
```

### 6. 监控与报告工具

#### Grafana - 质量指标可视化
```json
{
  "dashboard": {
    "title": "Test Quality Dashboard",
    "panels": [
      {
        "title": "Test Pass Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "test_pass_rate",
            "legendFormat": "Pass Rate"
          }
        ]
      },
      {
        "title": "Code Coverage Trend",
        "type": "graph",
        "targets": [
          {
            "expr": "code_coverage_percentage",
            "legendFormat": "Coverage %"
          }
        ]
      },
      {
        "title": "Test Execution Time",
        "type": "graph",
        "targets": [
          {
            "expr": "test_execution_duration_seconds",
            "legendFormat": "Duration"
          }
        ]
      }
    ]
  }
}
```

#### Allure - 测试报告生成
```python
# tests/conftest.py
import allure
import pytest

@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """生成Allure报告"""
    outcome = yield
    rep = outcome.get_result()
    
    if rep.when == "call" and rep.failed:
        # 截图附件
        if hasattr(item, "funcargs") and "page" in item.funcargs:
            page = item.funcargs["page"]
            screenshot = page.screenshot()
            allure.attach(
                screenshot,
                name="screenshot",
                attachment_type=allure.attachment_type.PNG
            )
        
        # 日志附件
        allure.attach(
            rep.longreprtext,
            name="failure_log",
            attachment_type=allure.attachment_type.TEXT
        )

# 使用Allure装饰器
@allure.feature("用户认证")
@allure.story("用户登录")
@allure.severity(allure.severity_level.CRITICAL)
def test_user_login():
    """测试用户登录功能"""
    with allure.step("输入用户凭据"):
        # 测试步骤
        pass
    
    with allure.step("点击登录按钮"):
        # 测试步骤
        pass
    
    with allure.step("验证登录成功"):
        # 验证步骤
        pass
```

### 7. 自动化工具链集成

#### pre-commit - Git钩子自动化
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
  
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.11
  
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
  
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
  
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: ['-r', 'app/', '-f', 'json', '-o', 'bandit-report.json']
```

#### Makefile - 命令自动化
```makefile
# Makefile
.PHONY: test quality security install clean

install:
	pip install -r requirements-dev.txt
	pre-commit install

test:
	pytest tests/ -v --cov=app --cov-report=html

test-unit:
	pytest tests/unit/ -v --cov=app

test-integration:
	pytest tests/integration/ -v

test-e2e:
	pytest tests/e2e/ -v

quality:
	black --check app/ tests/
	isort --check-only app/ tests/
	pylint app/
	flake8 app/

security:
	bandit -r app/ -f json -o bandit-report.json
	safety check --json --output safety-report.json

performance:
	locust -f tests/performance/locustfile.py \
		--headless -u 50 -r 10 -t 120s \
		--host http://localhost:8000

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -rf .coverage htmlcov/ .pytest_cache/
	rm -f *.json *.xml

all: install quality security test performance
```

这个工具知识体系提供了完整的质量保证工具链，从代码质量到安全扫描，从性能测试到CI/CD集成，确保全方位的质量保障。 