# AstrBot SaaS Platform Environment Variables
# 复制此文件为 .env 并填入真实值

# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/astrbot_saas
DATABASE_PASSWORD=your_secure_password_here

# 安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-too

# API 密钥
OPENAI_API_KEY=your-openai-api-key
DIFY_API_KEY=your-dify-api-key

# Redis 配置
REDIS_URL=redis://localhost:6379/0

# 应用配置
ENVIRONMENT=development
DEBUG=false
LOG_LEVEL=INFO

# 实例配置
DEFAULT_LLM_PROVIDER=openai
MAX_SESSIONS_PER_TENANT=1000
