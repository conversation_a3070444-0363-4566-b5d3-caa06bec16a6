"""
AstrBot SaaS Platform - FastAPI应用主入口

这是SaaS平台的FastAPI应用主入口文件，包含应用初始化和路由配置。
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
import time
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request

from app.api.v1 import api_router
from app.core.config import settings
from app.utils.logging import get_logger

# 配置日志
logger = get_logger(__name__)

class PerformanceMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""
    
    async def dispatch(self, request: Request, call_next):
        start_time = time.perf_counter()
        
        # 处理请求
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.perf_counter() - start_time
        
        # 添加性能头信息
        response.headers["X-Process-Time"] = str(process_time)
        
        # 记录慢请求（超过1秒）
        if process_time > 1.0:
            logger.warning(
                "Slow request detected",
                path=request.url.path,
                method=request.method,
                process_time=process_time,
                client_ip=request.client.host if request.client else "unknown"
            )
        
        return response

class CacheMiddleware(BaseHTTPMiddleware):
    """API响应缓存中间件"""
    
    def __init__(self, app, cache_ttl: int = 300):
        super().__init__(app)
        self.cache_ttl = cache_ttl
        # 定义可缓存的路径模式
        self.cacheable_patterns = [
            "/api/v1/tenants/statistics",  # 租户统计信息
            "/api/v1/health",              # 健康检查
        ]
    
    async def dispatch(self, request: Request, call_next):
        # 只缓存GET请求
        if request.method != "GET":
            return await call_next(request)
        
        # 检查路径是否可缓存
        path = request.url.path
        if not any(pattern in path for pattern in self.cacheable_patterns):
            return await call_next(request)
        
        # 生成缓存键
        cache_key = f"api_cache:{path}:{str(request.query_params)}"
        
        try:
            from app.utils.logging import cache_manager
            
            # 尝试从缓存获取响应
            cached_response = await cache_manager.get(cache_key)
            if cached_response:
                # 返回缓存的响应
                from fastapi.responses import JSONResponse
                response = JSONResponse(
                    content=cached_response,
                    headers={"X-Cache": "HIT"}
                )
                return response
            
            # 执行请求
            response = await call_next(request)
            
            # 只缓存成功的响应
            if response.status_code == 200:
                # 读取响应体
                body = b""
                async for chunk in response.body_iterator:
                    body += chunk
                
                # 解析JSON并缓存
                import json
                try:
                    json_data = json.loads(body.decode())
                    await cache_manager.set(cache_key, json_data, expire=self.cache_ttl)
                except json.JSONDecodeError:
                    pass  # 非JSON响应不缓存
                
                # 重新创建响应
                from fastapi.responses import Response
                response = Response(
                    content=body,
                    status_code=response.status_code,
                    headers=dict(response.headers),
                    media_type=response.media_type
                )
                response.headers["X-Cache"] = "MISS"
            
            return response
            
        except Exception as e:
            logger.error(f"Cache middleware error: {e}")
            # 缓存出错时直接返回原始响应
            return await call_next(request)

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="AstrBot多租户智能客服SaaS平台",
    version="0.1.0",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    # 性能优化配置
    generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}" if route.tags else route.name,
    swagger_ui_parameters={"defaultModelsExpandDepth": 0, "defaultModelExpandDepth": 0},
    docs_url=f"{settings.API_V1_STR}/docs" if settings.DEBUG else None,
    redoc_url=f"{settings.API_V1_STR}/redoc" if settings.DEBUG else None,
)

# 添加性能和安全中间件
app.add_middleware(PerformanceMiddleware)

# 添加API缓存中间件（仅在生产环境启用）
if not settings.DEBUG:
    app.add_middleware(CacheMiddleware, cache_ttl=300)  # 5分钟缓存

# 启用GZIP压缩（减少响应体积）
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 信任的主机中间件（安全）
if not settings.DEBUG:
    app.add_middleware(
        TrustedHostMiddleware, 
        allowed_hosts=["*"] if settings.DEBUG else settings.BACKEND_CORS_ORIGINS
    )

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "AstrBot SaaS Platform API",
        "version": "0.1.0",
        "status": "running",
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "astrbot-saas-platform"}

# ================================
# 应用生命周期事件（性能优化）
# ================================

@app.on_event("startup")
async def startup_event():
    """应用启动事件 - 预热缓存和连接池"""
    try:
        # 预热Redis连接
        from app.utils.logging import cache_manager
        await cache_manager.get_redis_client()
        logger.info("应用启动完成", event="startup", cache_initialized=True)
        
        # 预热数据库连接池（可选）
        from app.core.database import engine
        async with engine.begin() as conn:
            await conn.execute("SELECT 1")
        logger.info("数据库连接池预热完成")
        
    except Exception as e:
        logger.error("应用启动时发生错误", error=str(e))

@app.on_event("shutdown") 
async def shutdown_event():
    """应用关闭事件 - 清理资源"""
    try:
        # 关闭Redis连接
        from app.utils.logging import cache_manager
        await cache_manager.close()
        logger.info("应用关闭完成", event="shutdown", cache_closed=True)
        
    except Exception as e:
        logger.error("应用关闭时发生错误", error=str(e))

# ================================
# 性能监控端点
# ================================

@app.get(f"{settings.API_V1_STR}/metrics", tags=["监控"])
async def get_metrics():
    """获取应用性能指标"""
    import psutil
    import time
    
    try:
        # 系统资源信息
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        
        # 数据库连接池信息
        from app.core.database import engine
        pool_status = {
            "pool_size": engine.pool.size(),
            "checked_in": engine.pool.checkedin(),
            "checked_out": engine.pool.checkedout(),
        }
        
        return {
            "timestamp": time.time(),
            "system": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_mb": memory.available // 1024 // 1024,
            },
            "database": pool_status,
            "cache": {
                "type": "redis",
                "status": "healthy"
            }
        }
    except Exception as e:
        logger.error("获取监控指标失败", error=str(e))
        return {"error": "无法获取监控指标", "details": str(e)}