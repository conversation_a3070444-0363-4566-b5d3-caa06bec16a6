# 📖 技术文档：用户角色API (user_roles.py)

## 🎯 1. 模块概述

**功能**：提供用户和角色之间关联的管理功能。

**核心职责**：
- **分配角色**：为用户分配角色。
- **撤销角色**：从用户撤销角色。
- **查询用户角色**：获取用户拥有的所有角色。
- **查询角色用户**：获取拥有某个角色的所有用户。

## 🚀 2. 快速使用

### 2.1 依赖注入

```python
from app.services.rbac_service import RBACService, get_rbac_service

@router.post("/")
async def assign_role_to_user(
    # ...
    rbac_service: RBACService = Depends(get_rbac_service),
):
    # ...
```

### 2.2 核心端点

- `POST /` - 为用户分配角色
- `DELETE /{role_id}` - 从用户撤销角色
- `GET /` - 获取用户的所有角色

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    A[API Endpoints] --> B(RBACService)
    B --> C(UserRole Model)
    B --> D(User Model)
    B --> E(Role Model)
```

### 3.2 数据流

**分配角色流程**：
1. **API接收**：接收为用户分配角色的请求。
2. **服务调用**：调用`RBACService.assign_role_to_user`。
3. **响应返回**：返回成功或失败信息。

## 🔧 4. API参考

| HTTP动词 | 端点 | 描述 |
|---|---|---|
| `POST` | `/` | 为用户分配角色 |
| `DELETE`| `/{role_id}` | 从用户撤销角色 |
| `GET` | `/` | 获取用户的所有角色 |
|`GET`|`/{role_id}/users`|获取拥有某个角色的所有用户|

## 🧪 5. 测试策略

- **集成测试**：`tests/integration/test_user_roles_api.py`

## 💡 6. 维护与扩展

- **批量操作**：可以添加批量为用户分配或撤销角色的功能。
- **角色有效期**：可以为角色分配添加有效期限制。
- **临时角色**：可以添加临时角色的功能。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 