"""
智能测试选择器
基于代码变更智能选择需要执行的测试
"""

import ast
import os
import subprocess
from pathlib import Path
from typing import Set, List, Dict, Any
from dataclasses import dataclass


@dataclass
class TestMapping:
    """测试映射关系"""

    source_file: str
    test_files: List[str]
    dependencies: List[str]


class SmartTestSelector:
    """智能测试选择器"""

    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.app_dir = project_root / "app"
        self.test_dir = project_root / "tests"

    def get_changed_files(self, base_branch: str = "main") -> Set[str]:
        """获取相对于基分支的变更文件"""
        try:
            result = subprocess.run(
                ["git", "diff", "--name-only", f"{base_branch}...HEAD"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                changed_files = set()
                for line in result.stdout.strip().split("\n"):
                    if line and line.endswith(".py"):
                        changed_files.add(line)
                return changed_files
            else:
                # 如果git命令失败，返回所有Python文件
                return self._get_all_python_files()

        except Exception:
            return self._get_all_python_files()

    def _get_all_python_files(self) -> Set[str]:
        """获取所有Python文件"""
        python_files = set()

        for root, dirs, files in os.walk(self.app_dir):
            for file in files:
                if file.endswith(".py"):
                    rel_path = os.path.relpath(
                        os.path.join(root, file), self.project_root
                    )
                    python_files.add(rel_path.replace("\\", "/"))

        return python_files

    def analyze_dependencies(self, file_path: str) -> List[str]:
        """分析文件的依赖关系"""
        dependencies = []
        full_path = self.project_root / file_path

        if not full_path.exists():
            return dependencies

        try:
            with open(full_path, "r", encoding="utf-8") as f:
                content = f.read()

            tree = ast.parse(content)

            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if alias.name.startswith("app."):
                            dep_path = self._module_to_path(alias.name)
                            if dep_path:
                                dependencies.append(dep_path)

                elif isinstance(node, ast.ImportFrom):
                    if node.module and node.module.startswith("app."):
                        dep_path = self._module_to_path(node.module)
                        if dep_path:
                            dependencies.append(dep_path)

        except Exception:
            pass

        return dependencies

    def _module_to_path(self, module_name: str) -> str:
        """将模块名转换为文件路径"""
        if not module_name.startswith("app."):
            return ""

        # app.models.tenant -> app/models/tenant.py
        path_parts = module_name.split(".")
        file_path = "/".join(path_parts) + ".py"

        full_path = self.project_root / file_path
        if full_path.exists():
            return file_path

        # 尝试 __init__.py
        init_path = "/".join(path_parts) + "/__init__.py"
        full_init_path = self.project_root / init_path
        if full_init_path.exists():
            return init_path

        return ""

    def find_related_tests(self, source_file: str) -> List[str]:
        """查找与源文件相关的测试文件"""
        related_tests = []

        # 直接映射：app/models/tenant.py -> tests/unit/test_tenant.py
        if source_file.startswith("app/"):
            rel_path = source_file[4:]  # 移除 'app/' 前缀

            # 尝试多种测试文件命名模式
            test_patterns = [
                f"tests/unit/test_{Path(rel_path).stem}.py",
                f"tests/unit/test_{Path(rel_path).stem}_model.py",
                f"tests/unit/test_{Path(rel_path).stem}_service.py",
                f"tests/integration/test_{Path(rel_path).stem}_integration.py",
                f"tests/e2e/test_{Path(rel_path).stem}_e2e.py",
            ]

            for pattern in test_patterns:
                test_file = self.project_root / pattern
                if test_file.exists():
                    related_tests.append(pattern)

        # 查找包含该源文件导入的测试文件
        module_name = self._path_to_module(source_file)
        if module_name:
            related_tests.extend(self._find_tests_importing_module(module_name))

        return list(set(related_tests))  # 去重

    def _path_to_module(self, file_path: str) -> str:
        """将文件路径转换为模块名"""
        if not file_path.startswith("app/"):
            return ""

        # app/models/tenant.py -> app.models.tenant
        path_without_ext = file_path[:-3]  # 移除 .py
        return path_without_ext.replace("/", ".")

    def _find_tests_importing_module(self, module_name: str) -> List[str]:
        """查找导入指定模块的测试文件"""
        importing_tests = []

        for root, dirs, files in os.walk(self.test_dir):
            for file in files:
                if file.endswith(".py") and file.startswith("test_"):
                    test_file_path = os.path.join(root, file)
                    rel_test_path = os.path.relpath(test_file_path, self.project_root)

                    if self._file_imports_module(test_file_path, module_name):
                        importing_tests.append(rel_test_path.replace("\\", "/"))

        return importing_tests

    def _file_imports_module(self, file_path: str, module_name: str) -> bool:
        """检查文件是否导入了指定模块"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            tree = ast.parse(content)

            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if alias.name == module_name or alias.name.startswith(
                            module_name + "."
                        ):
                            return True

                elif isinstance(node, ast.ImportFrom):
                    if node.module == module_name or (
                        node.module and node.module.startswith(module_name + ".")
                    ):
                        return True

        except Exception:
            pass

        return False

    def select_tests(
        self, changed_files: Set[str] = None, include_dependencies: bool = True
    ) -> List[str]:
        """选择需要执行的测试"""
        if changed_files is None:
            changed_files = self.get_changed_files()

        selected_tests = set()

        # 处理每个变更的文件
        for file_path in changed_files:
            if file_path.startswith("app/") and file_path.endswith(".py"):
                # 查找直接相关的测试
                related_tests = self.find_related_tests(file_path)
                selected_tests.update(related_tests)

                # 如果启用依赖分析，查找依赖该文件的其他文件的测试
                if include_dependencies:
                    dependent_files = self._find_dependent_files(file_path)
                    for dep_file in dependent_files:
                        dep_tests = self.find_related_tests(dep_file)
                        selected_tests.update(dep_tests)

        # 如果没有找到相关测试，运行所有测试
        if not selected_tests:
            return ["tests/"]

        return sorted(list(selected_tests))

    def _find_dependent_files(self, changed_file: str) -> List[str]:
        """查找依赖于变更文件的其他文件"""
        dependent_files = []
        changed_module = self._path_to_module(changed_file)

        if not changed_module:
            return dependent_files

        # 扫描所有app目录下的文件
        for root, dirs, files in os.walk(self.app_dir):
            for file in files:
                if file.endswith(".py"):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, self.project_root)
                    rel_path = rel_path.replace("\\", "/")

                    if rel_path != changed_file:
                        dependencies = self.analyze_dependencies(rel_path)
                        if changed_file in dependencies:
                            dependent_files.append(rel_path)

        return dependent_files

    def generate_test_command(self, test_files: List[str]) -> str:
        """生成pytest命令"""
        if not test_files or test_files == ["tests/"]:
            return "python -m pytest tests/ -v"

        # 按测试类型分组
        unit_tests = [f for f in test_files if f.startswith("tests/unit/")]
        integration_tests = [
            f for f in test_files if f.startswith("tests/integration/")
        ]
        e2e_tests = [f for f in test_files if f.startswith("tests/e2e/")]

        commands = []

        if unit_tests:
            commands.append(f"python -m pytest {' '.join(unit_tests)} -v --tb=short")

        if integration_tests:
            commands.append(
                f"python -m pytest {' '.join(integration_tests)} -v --tb=short"
            )

        if e2e_tests:
            commands.append(f"python -m pytest {' '.join(e2e_tests)} -v --tb=short")

        return " && ".join(commands) if commands else "python -m pytest tests/ -v"

    def print_selection_summary(
        self, changed_files: Set[str], selected_tests: List[str]
    ):
        """打印选择摘要"""
        print("🔍 智能测试选择结果")
        print("=" * 50)
        print(f"📝 变更文件数量: {len(changed_files)}")

        if changed_files:
            print("变更的文件:")
            for file in sorted(changed_files):
                print(f"  • {file}")

        print(f"\n🧪 选择的测试数量: {len(selected_tests)}")

        if selected_tests and selected_tests != ["tests/"]:
            print("选择的测试:")
            for test in selected_tests:
                print(f"  • {test}")
        else:
            print("将运行所有测试")

        print("=" * 50)


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="智能测试选择器")
    parser.add_argument("--base", default="main", help="基准分支")
    parser.add_argument("--no-deps", action="store_true", help="不包含依赖分析")
    parser.add_argument("--dry-run", action="store_true", help="只显示选择结果，不执行")

    args = parser.parse_args()

    project_root = Path(__file__).parent.parent.parent
    selector = SmartTestSelector(project_root)

    # 获取变更文件
    changed_files = selector.get_changed_files(args.base)

    # 选择测试
    selected_tests = selector.select_tests(
        changed_files, include_dependencies=not args.no_deps
    )

    # 打印摘要
    selector.print_selection_summary(changed_files, selected_tests)

    if args.dry_run:
        print(f"\n💡 建议执行命令:")
        print(selector.generate_test_command(selected_tests))
    else:
        # 执行测试
        command = selector.generate_test_command(selected_tests)
        print(f"\n🚀 执行测试命令: {command}")

        os.chdir(project_root)
        exit_code = os.system(command)
        exit(exit_code >> 8)  # 转换系统退出码


if __name__ == "__main__":
    main()
