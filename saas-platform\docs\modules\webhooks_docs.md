# 📖 技术文档：Webhook API (webhooks.py)

## 🎯 1. 模块概述

**功能**：处理来自AstrBot实例的Webhook请求，包括消息上报和状态同步。

**核心职责**：
- **接收Webhook**：提供统一的端点接收来自AstrBot实例的Webhook请求
- **事件分发**：根据事件类型将请求分发到不同的服务处理
- **安全验证**：验证Webhook请求的签名，确保请求的合法性

## 🚀 2. 快速使用

### 2.1 依赖注入

```python
from app.services.webhook_service import WebhookService, get_webhook_service

@router.post("/astrbot/{tenant_id}")
async def handle_astrbot_webhook(
    # ...
    service: WebhookService = Depends(get_webhook_service),
):
    # ...
```

### 2.2 核心端点

- `POST /astrbot/{tenant_id}` - 统一的Webhook入口
- `POST /messages/{tenant_id}` - 兼容的消息Webhook入口

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    A[API Endpoints] --> B(WebhookService)
    B --> C(SessionService)
    B --> D(MessageService)
    B --> E(Tenant Model)
```

### 3.2 数据流

**Webhook处理流程**：
1. **API接收**：接收Webhook请求
2. **服务调用**：调用`WebhookService`处理请求
3. **签名验证**：服务层验证签名
4. **事件分发**：根据事件类型调用不同的处理方法
5. **响应返回**：返回处理结果

## 🔧 4. API参考

| HTTP动词 | 端点 | 描述 |
|---|---|---|
| `POST` | `/astrbot/{tenant_id}` | 统一的Webhook入口 |
| `POST` | `/messages/{tenant_id}` | 兼容的消息Webhook入口|
| `GET` | `/{tenant_id}/health` | Webhook健康检查 |
| `POST`| `/{tenant_id}/test` | 测试Webhook端点 |
| `GET` | `/{tenant_id}/config` | 获取Webhook配置 |

## 🧪 5. 测试策略

- **集成测试**：`tests/integration/test_webhooks_api.py`

## 💡 6. 维护与扩展

- **事件扩展**：可以添加新的端点来处理不同类型的Webhook事件
- **异步处理**：对于耗时的Webhook处理，可以将其放入后台任务队列中
- **日志监控**：监控Webhook请求的成功率和延迟，及时发现问题

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 