# AstrBot SaaS Platform - Docker Compose配置
# 用于本地开发、测试和演示环境
version: '3.8'

# ─────────────── 网络 ───────────────
networks:
  astrbot-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ─────────────── 数据卷 ───────────────
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# ─────────────── 服务 ───────────────
services:
  # ---------- PostgreSQL ----------
  postgres:
    image: postgres:15-alpine
    container_name: astrbot-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=astrbot_saas
      - POSTGRES_USER=astrbot
      - POSTGRES_PASSWORD=astrbot123
      - POSTGRES_INITDB_ARGS=--encoding=UTF8 --locale=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      astrbot-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U astrbot -d astrbot_saas"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ---------- Redis ----------
  redis:
    image: redis:7-alpine
    container_name: astrbot-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      astrbot-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ---------- FastAPI 应用 ----------
  app:
    build:
      context: ..
      dockerfile: ./saas-platform/Dockerfile
      target: production
      args:
        - BUILDKIT_INLINE_CACHE=1
    image: astrbot/saas-platform:latest
    container_name: astrbot-app
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # 应用基础配置
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      # 数据库
      - DATABASE_URL=postgresql+asyncpg://astrbot:astrbot123@postgres:5432/astrbot_saas
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_NAME=astrbot_saas
      - DATABASE_USER=astrbot
      - DATABASE_PASSWORD=astrbot123
      # Redis
      - REDIS_URL=redis://:redis123@redis:6379/0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis123
      # JWT
      - SECRET_KEY=your-super-secret-key-change-in-production
      - JWT_SECRET_KEY=jwt-secret-change-in-production
      - JWT_ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      # CORS
      - CORS_ORIGINS=["http://localhost:3000","http://localhost:8080"]
      # 监控
      - PROMETHEUS_ENABLED=true
      - JAEGER_ENABLED=false
      # 外部服务（可为空）
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - DIFY_API_KEY=${DIFY_API_KEY:-}
    volumes:
      - app_logs:/app/logs
    ports:
      - "8000:8000"
      - "8080:8080"   # Prometheus 指标
    networks:
      astrbot-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # ---------- Nginx（生产用，可选） ----------
  nginx:
    image: nginx:alpine
    container_name: astrbot-nginx
    restart: unless-stopped
    depends_on:
      - app
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx-ssl.conf:/etc/nginx/conf.d/default.conf:ro
      - ./static:/usr/share/nginx/html/static:ro
    ports:
      - "80:80"
      - "443:443"
    networks:
      astrbot-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    profiles:
      - production        # 仅生产模式启用

  # ---------- Prometheus ----------
  prometheus:
    image: prom/prometheus:latest
    container_name: astrbot-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      astrbot-network:
        ipv4_address: ***********
    profiles:
      - monitoring

  # ---------- Grafana ----------
  grafana:
    image: grafana/grafana:latest
    container_name: astrbot-grafana
    restart: unless-stopped
    depends_on:
      - prometheus
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning/:/etc/grafana/provisioning/
      - ./monitoring/grafana/dashboards/:/var/lib/grafana/dashboards/
    ports:
      - "3000:3000"
    networks:
      astrbot-network:
        ipv4_address: ***********
    profiles:
      - monitoring

  # ---------- Fluentd ----------
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: astrbot-fluentd
    restart: unless-stopped
    volumes:
      - ./monitoring/fluentd/fluent.conf:/fluentd/etc/fluent.conf:ro
      - app_logs:/var/log/app:ro
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    networks:
      astrbot-network:
        ipv4_address: ***********
    profiles:
      - monitoring

  # ---------- Jaeger ----------
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: astrbot-jaeger
    restart: unless-stopped
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    ports:
      - "16686:16686"   # UI
      - "14268:14268"   # Collector
    networks:
      astrbot-network:
        ipv4_address: ***********
    profiles:
      - monitoring

  # ---------- Pytest 运行器 ----------
  test-runner:
    build:
      context: ..
      dockerfile: saas-platform/Dockerfile
      target: builder
    container_name: astrbot-test-runner
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - ENVIRONMENT=test
      - DATABASE_URL=postgresql+asyncpg://astrbot:astrbot123@postgres:5432/astrbot_saas_test
      - REDIS_URL=redis://:redis123@redis:6379/1
    volumes:
      - .:/app
    working_dir: /app
    command: >
      sh -c "
        echo 'Waiting for services...' &&
        sleep 8 &&
        pytest tests/ -v --cov=app --cov-report=term-missing
      "
    networks:
      - astrbot-network
    profiles:
      - test

# 开发环境快速启动配置
x-development: &development
  environment:
    - ENVIRONMENT=development
    - DEBUG=true
    - LOG_LEVEL=DEBUG

# 生产环境配置模板
x-production: &production
  environment:
    - ENVIRONMENT=production
    - DEBUG=false
    - LOG_LEVEL=INFO
  deploy:
    resources:
      limits:
        memory: 2G
        cpus: '2.0'
      reservations:
        memory: 1G
        cpus: '1.0'
