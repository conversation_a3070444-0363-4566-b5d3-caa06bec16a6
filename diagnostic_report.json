{"timestamp": "2025-06-24T19:34:08.825161", "diagnostics": {"port_status": {"saas_platform": {"port": 8000, "listening": false, "details": "No output"}, "astrbot_web": {"port": 6185, "listening": true, "details": "TCP    0.0.0.0:6185           0.0.0.0:0              LISTENING\n  TCP    192.168.110.53:52982   1.95.166.226:6185      ESTABLISHED\n  TCP    [::]:6185              [::]:0                 LISTENING\n  TCP    [::1]:6185             [::]:0                 LISTENING"}, "astrbot_admin": {"port": 6195, "listening": true, "details": "TCP    0.0.0.0:6195           0.0.0.0:0              LISTENING\n  TCP    [::]:6195              [::]:0                 LISTENING\n  TCP    [::1]:6195             [::]:0                 LISTENING"}, "astrbot_api": {"port": 6199, "listening": true, "details": "TCP    0.0.0.0:6199           0.0.0.0:0              LISTENING\n  TCP    [::]:6199              [::]:0                 LISTENING\n  TCP    [::1]:6199             [::]:0                 LISTENING"}, "postgresql": {"port": 5432, "listening": true, "details": "TCP    0.0.0.0:5432           0.0.0.0:0              LISTENING\n  TCP    [::]:5432              [::]:0                 LISTENING\n  TCP    [::1]:5432             [::]:0                 LISTENING"}, "redis": {"port": 6379, "listening": true, "details": "TCP    0.0.0.0:6379           0.0.0.0:0              LISTENING\n  TCP    [::]:6379              [::]:0                 LISTENING\n  TCP    [::1]:6379             [::]:0                 LISTENING"}}, "processes": [{"pid": 7736, "name": "python.exe", "cmdline": "C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.13-windows-x86_64-none\\python.exe D:\\tool\\AstrBot\\serena\\.venv\\Scripts\\serena-mcp-server.exe --context ide-assistant", "status": "running"}, {"pid": 7852, "name": "python.exe", "cmdline": "D:\\tool\\AstrBot\\serena\\.venv\\Scripts\\python.exe D:\\tool\\AstrBot\\serena\\.venv\\Scripts\\serena-mcp-server.exe --context ide-assistant", "status": "running"}, {"pid": 27948, "name": "python.exe", "cmdline": "D:\\tool\\AstrBot\\serena\\.venv\\Scripts\\python.exe D:\\tool\\AstrBot\\serena\\.venv\\Scripts\\serena-mcp-server.exe --context ide-assistant", "status": "running"}, {"pid": 30144, "name": "python.exe", "cmdline": "C:\\ProgramData\\anaconda3\\python.exe astrbot_system_diagnostics.py", "status": "running"}, {"pid": 32908, "name": "python.exe", "cmdline": "C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.11.13-windows-x86_64-none\\python.exe D:\\tool\\AstrBot\\serena\\.venv\\Scripts\\serena-mcp-server.exe --context ide-assistant", "status": "running"}], "endpoints": {"SaaS平台根路径": {"url": "http://localhost:8000/", "status_code": 502, "accessible": false, "response_time": 2.062}, "SaaS平台健康检查": {"url": "http://localhost:8000/health", "status_code": 502, "accessible": false, "response_time": 2.047}, "SaaS平台API文档": {"url": "http://localhost:8000/docs", "status_code": 502, "accessible": false, "response_time": 2.047}, "AstrBot Web界面": {"url": "http://localhost:6185/", "status_code": 200, "accessible": true, "response_time": 0.078}, "AstrBot管理接口": {"url": "http://localhost:6195/", "status_code": 502, "accessible": false, "response_time": 0.016}, "AstrBot API接口": {"url": "http://localhost:6199/", "status_code": 502, "accessible": false, "response_time": 0.0}}, "configuration": {"saas_platform_env": {"path": "D:\\tool\\AstrBot\\saas-platform\\.env", "exists": true, "size": 775, "has_database_url": true, "has_secret_key": true}}, "database": {"connection": "failed", "error": "error parsing value for field \"BACKEND_CORS_ORIGINS\" from source \"EnvSettingsSource\""}, "logs": {"D:\\tool\\AstrBot\\saas-platform\\deploy_test.log": {"size": 11157, "error_count": 1, "warning_count": 0, "recent_content": "ll complete \n 8e7dfe758b13 Pull complete \n 639ffb3d4c66 Pull complete \n postgres Pulled \nfailed to solve: failed to read dockerfile: open Dockerfile.dev: no such file or directory\n\n2025-06-20 14:57:40,535 - INFO - 尝试使用标准配置启动...\n2025-06-20 14:57:41,597 - INFO -   基础服务(数据库、缓存)启动成功\n2025-06-20 14:57:41,597 - INFO -   服务启动完成\n2025-06-20 14:57:41,598 - INFO - 3. 等待服务就绪\n2025-06-20 14:57:41,598 - INFO -   等待中... (0/60秒)\n2025-06-20 14:57:41,739 - ERROR - 部署测试失败: 'NoneType' object has no attribute 'count'\n"}, "D:\\tool\\AstrBot\\saas-platform\\integration_test.log": {"size": 0, "error_count": 0, "warning_count": 0, "recent_content": ""}, "D:\\tool\\AstrBot\\saas-platform\\postgresql_install.log": {"size": 216, "error_count": 0, "warning_count": 0, "recent_content": "2025-06-20 15:55:31,286 - INFO - 🚀 开始PostgreSQL安装和测试流程...\n2025-06-20 15:55:31,287 - INFO - 🔍 检查系统前置条件...\n2025-06-20 15:55:31,287 - INFO - ✅ 系统前置条件检查通过\n"}, "D:\\tool\\AstrBot\\saas-platform\\quality_check.log": {"size": 6570, "error_count": 0, "warning_count": 0, "recent_content": "在高复杂度代码\n2025-06-20 15:50:08,732 - INFO -   - 测试覆盖率不足\n2025-06-20 15:50:08,732 - INFO - \n[建议] 改进建议:\n2025-06-20 15:50:08,733 - INFO -   1. 定期进行代码审查\n2025-06-20 15:50:08,733 - INFO -   2. 增加单元测试和集成测试\n2025-06-20 15:50:08,733 - INFO -   3. 完善API文档和用户手册\n2025-06-20 15:50:08,733 - INFO -   4. 使用自动化工具监控代码质量\n2025-06-20 15:50:08,733 - INFO - ======================================================================\n2025-06-20 15:50:08,737 - INFO - [保存] 详细报告已保存到: D:\\tool\\AstrBot\\saas-platform\\quality_report.json\n"}, "D:\\tool\\AstrBot\\saas-platform\\quality_fix.log": {"size": 5257, "error_count": 0, "warning_count": 0, "recent_content": "FO - \n[安全提醒]:\n2025-06-20 15:48:37,223 - INFO -   1. 已创建 .env.template 环境变量模板\n2025-06-20 15:48:37,223 - INFO -   2. 已创建 SECURITY_GUIDE.md 安全指南\n2025-06-20 15:48:37,223 - INFO -   3. 已更新 .gitignore 文件\n2025-06-20 15:48:37,223 - INFO -   4. 请检查修复后的文件确保功能正常\n2025-06-20 15:48:37,223 - INFO -   5. 生产环境请使用真实的强密钥\n2025-06-20 15:48:37,223 - INFO - ============================================================\n2025-06-20 15:48:37,224 - INFO - [保存] 详细报告已保存到: D:\\tool\\AstrBot\\saas-platform\\quality_fix_report.json\n"}}}, "issues": [], "recommendations": ["1. 检查AstrBot服务是否正确启动", "2. 验证AstrBot配置文件是否正确", "3. 检查端口冲突和防火墙设置", "4. 查看AstrBot启动日志获取详细错误信息", "5. 修复数据库连接问题"], "critical_issues": ["SaaS平台(8000)未运行"], "warnings": []}