#!/usr/bin/env python3
"""
🚀 最简化SaaS平台启动脚本
只依赖.env文件，不设置额外环境变量
"""

import subprocess
import sys


def main():
    """主启动流程"""
    print("🚀 最简化SaaS平台启动")
    print("=" * 40)
    print("📍 依赖.env文件配置")
    print("🌐 启动Uvicorn服务器...")
    print("🎯 服务地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/api/v1/docs")
    print("🔍 健康检查: http://localhost:8000/health")
    print("⏹️ 停止服务: Ctrl+C")
    print("\n" + "=" * 40)
    
    try:
        # 直接使用uvicorn启动，不设置任何环境变量
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "app.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--log-level", "info"
        ]
        
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
