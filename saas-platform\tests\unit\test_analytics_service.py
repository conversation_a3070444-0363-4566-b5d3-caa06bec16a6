"""
数据分析服务单元测试
"""
import uuid
from datetime import datetime
from unittest.mock import AsyncM<PERSON>, MagicMock, PropertyMock, patch

import pytest

from app.schemas.analytics import AnalyticsFilter
from app.services.analytics_service import AnalyticsService


@pytest.fixture
def mock_db_session():
    """创建一个 mock 的异步数据库会话"""
    session = AsyncMock()
    # 模拟链式调用
    session.query.return_value.filter.return_value.count.return_value = 10
    session.query.return_value.filter.return_value.all.return_value = []
    session.query.return_value.filter.return_value.scalar.return_value = 120.5
    return session


@pytest.fixture
def analytics_service(mock_db_session):
    """创建一个带有 mock 数据库的 AnalyticsService 实例"""
    return AnalyticsService(mock_db_session)


class TestAnalyticsService:
    """测试 AnalyticsService"""

    TENANT_ID = uuid.uuid4()
    FILTERS = AnalyticsFilter(start_date=datetime(2023, 1, 1), end_date=datetime(2023, 1, 31))

    @pytest.mark.asyncio
    async def test_get_session_stats_success(self, analytics_service, mock_db_session):
        """测试 get_session_stats 成功返回统计数据"""
        # Arrange
        # 模拟状态分布的返回
        status_stat_active = MagicMock()
        type(status_stat_active).status = PropertyMock(return_value="active")
        type(status_stat_active).count = PropertyMock(return_value=8)

        status_stat_closed = MagicMock()
        type(status_stat_closed).status = PropertyMock(return_value="closed")
        type(status_stat_closed).count = PropertyMock(return_value=2)
        
        mock_db_session.query.return_value.filter.return_value.group_by.return_value.all.return_value = [
            status_stat_active, status_stat_closed
        ]
        
        # 模拟时间序列的返回
        with patch.object(analytics_service, "_get_sessions_time_series", return_value=[]) as mock_time_series:
            # Act
            result = await analytics_service.get_session_stats(self.TENANT_ID, self.FILTERS)

            # Assert
            assert result.total_sessions == 10
            assert result.avg_duration_seconds == 120.5
            assert result.status_distribution == {"active": 8, "closed": 2}
            mock_time_series.assert_awaited_once_with(self.TENANT_ID, self.FILTERS)
            # 验证 query 和 filter 被多次调用
            assert mock_db_session.query.call_count > 0
            assert mock_db_session.query.return_value.filter.call_count > 0

    @pytest.mark.asyncio
    async def test_get_session_stats_db_error(self, analytics_service, mock_db_session):
        """测试在数据库查询失败时 get_session_stats 抛出异常"""
        # Arrange
        mock_db_session.query.side_effect = Exception("DB connection error")

        # Act & Assert
        with pytest.raises(Exception) as exc_info:
            await analytics_service.get_session_stats(self.TENANT_ID, self.FILTERS)
        assert "DB connection error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_message_stats_success(self, analytics_service, mock_db_session):
        """测试 get_message_stats 成功返回统计数据"""
        # Arrange
        mock_db_session.query.return_value.filter.return_value.count.return_value = 50

        # 模拟类型分布
        type_stat_text = MagicMock()
        type(type_stat_text).type = PropertyMock(return_value="text")
        type(type_stat_text).count = PropertyMock(return_value=40)
        
        type_stat_image = MagicMock()
        type(type_stat_image).type = PropertyMock(return_value="image")
        type(type_stat_image).count = PropertyMock(return_value=10)

        mock_db_session.query.return_value.filter.return_value.group_by.return_value.all.return_value = [
            type_stat_text, type_stat_image
        ]
        
        # 模拟内部调用的辅助函数
        with patch.object(analytics_service, "_calculate_avg_response_time", return_value=95.5) as mock_avg_resp, \
             patch.object(analytics_service, "_get_messages_time_series", return_value=[]) as mock_time_series, \
             patch.object(analytics_service, "_get_top_active_sessions", return_value=[]) as mock_top_sessions:
            
            # Act
            result = await analytics_service.get_message_stats(self.TENANT_ID, self.FILTERS)

            # Assert
            assert result.total_messages == 50
            assert result.type_distribution == {"text": 40, "image": 10}
            assert result.avg_response_time_seconds == 95.5
            
            mock_avg_resp.assert_awaited_once_with(self.TENANT_ID, self.FILTERS)
            mock_time_series.assert_awaited_once_with(self.TENANT_ID, self.FILTERS)
            mock_top_sessions.assert_awaited_once_with(self.TENANT_ID, self.FILTERS)
    
    @pytest.mark.asyncio
    async def test_get_realtime_metrics_success(self, analytics_service, mock_db_session):
        """测试 get_realtime_metrics 成功返回实时数据"""
        # Arrange
        # 模拟不同的查询返回不同的计数值
        def query_side_effect(*args, **kwargs):
            mock_query = MagicMock()
            # 根据查询的实体来决定返回什么
            if "Session" in str(args):
                # .filter().count()
                filter_mock = MagicMock()
                filter_mock.count.return_value = 5 # active_sessions
                mock_query.filter.return_value = filter_mock
            elif "Message" in str(args):
                 # .filter().count()
                filter_mock = MagicMock()
                filter_mock.count.return_value = 20 # messages_hour
                mock_query.filter.return_value = filter_mock
            return mock_query

        mock_db_session.query.side_effect = query_side_effect

        # Act
        result = await analytics_service.get_realtime_metrics(self.TENANT_ID)

        # Assert
        assert result.active_sessions == 5
        assert result.messages_last_hour == 20
        # 验证 query 被调用了两次（一次Session，一次Message）
        assert mock_db_session.query.call_count == 2
