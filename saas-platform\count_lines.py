#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AstrBot SaaS Platform 代码行数统计工具
"""

import os
import glob
from pathlib import Path
from collections import defaultdict

def count_lines_in_file(file_path):
    """统计文件行数"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return len(f.readlines())
    except:
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                return len(f.readlines())
        except:
            return 0

def get_file_category(file_path):
    """根据文件扩展名分类"""
    ext = Path(file_path).suffix.lower()
    name = Path(file_path).name.lower()
    
    if ext == '.py':
        return 'Python'
    elif ext in ['.js', '.ts']:
        return 'JavaScript/TypeScript'
    elif ext == '.vue':
        return 'Vue'
    elif ext in ['.html', '.htm']:
        return 'HTML'
    elif ext in ['.css', '.scss', '.sass']:
        return 'CSS/SCSS'
    elif ext == '.json':
        return 'JSON配置'
    elif ext in ['.yaml', '.yml']:
        return 'YAML配置'
    elif ext == '.sql':
        return 'SQL'
    elif ext == '.md':
        return 'Markdown文档'
    elif ext in ['.toml', '.ini', '.conf'] or name in ['dockerfile', '.env', '.gitignore']:
        return '配置文件'
    elif 'docker-compose' in name:
        return '配置文件'
    else:
        return '其他'

def main():
    """主函数"""
    print("🔍 AstrBot SaaS Platform 代码行数统计")
    print("=" * 50)
    
    # 要统计的文件类型
    patterns = [
        '**/*.py',
        '**/*.js', '**/*.ts',
        '**/*.vue',
        '**/*.html', '**/*.htm',
        '**/*.css', '**/*.scss', '**/*.sass',
        '**/*.json',
        '**/*.yaml', '**/*.yml',
        '**/*.sql',
        '**/*.md',
        '**/*.toml', '**/*.ini', '**/*.conf',
        '**/Dockerfile*', '**/docker-compose*',
        '**/.env*', '**/.gitignore'
    ]
    
    # 统计结果
    stats = defaultdict(lambda: {'files': 0, 'lines': 0})
    total_files = 0
    total_lines = 0
    
    # 遍历文件
    project_root = Path.cwd()
    all_files = set()
    
    for pattern in patterns:
        for file_path in project_root.glob(pattern):
            if file_path.is_file() and str(file_path) not in all_files:
                # 跳过某些目录
                skip_dirs = [
                    '__pycache__', '.git', 'node_modules', 
                    'venv', 'env', '.pytest_cache', 
                    'htmlcov', '.vscode', '.idea',
                    'backup_quality_fix'
                ]
                
                if any(skip_dir in str(file_path) for skip_dir in skip_dirs):
                    continue
                    
                all_files.add(str(file_path))
                
                category = get_file_category(file_path)
                lines = count_lines_in_file(file_path)
                
                stats[category]['files'] += 1
                stats[category]['lines'] += lines
                
                total_files += 1
                total_lines += lines
    
    # 输出统计结果
    print(f"📊 按文件类型统计:")
    print("-" * 50)
    
    # 按行数排序
    sorted_stats = sorted(stats.items(), key=lambda x: x[1]['lines'], reverse=True)
    
    for category, data in sorted_stats:
        if data['files'] > 0:
            print(f"📁 {category:20} {data['files']:4d} 个文件  {data['lines']:8,} 行")
    
    print("-" * 50)
    print(f"📈 总计: {total_files:4d} 个文件  {total_lines:8,} 行")
    
    # 代码规模评估
    print(f"\n🎯 项目规模评估:")
    if total_lines < 10000:
        scale = "小型项目"
    elif total_lines < 50000:
        scale = "中型项目" 
    elif total_lines < 100000:
        scale = "大型项目"
    else:
        scale = "超大型项目"
    
    print(f"   规模等级: {scale}")
    print(f"   核心代码: {stats['Python']['lines']:,} 行 Python")
    
    if stats['JavaScript/TypeScript']['lines'] > 0:
        print(f"   前端代码: {stats['JavaScript/TypeScript']['lines']:,} 行 JS/TS")
    
    print(f"   文档资料: {stats['Markdown文档']['lines']:,} 行 Markdown")
    print(f"   配置文件: {stats['JSON配置']['lines'] + stats['YAML配置']['lines'] + stats['配置文件']['lines']:,} 行")
    
    # 代码密度分析
    if stats['Python']['files'] > 0:
        avg_lines_per_py_file = stats['Python']['lines'] // stats['Python']['files']
        print(f"\n📏 代码密度:")
        print(f"   Python文件平均: {avg_lines_per_py_file} 行/文件")
        
        if avg_lines_per_py_file > 500:
            print("   💡 建议: 部分文件较大，可考虑模块化拆分")
        elif avg_lines_per_py_file < 100:
            print("   ✅ 良好: 文件大小适中，便于维护")
        else:
            print("   👍 合理: 文件结构较为合理")

if __name__ == "__main__":
    main() 