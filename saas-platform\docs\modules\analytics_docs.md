# 📖 技术文档：数据分析API (analytics.py)

## 🎯 1. 模块概述

**功能**：提供数据统计和分析的API端点。

**核心职责**：
- **会话统计**：提供获取会话统计数据的API。
- **消息统计**：提供获取消息统计数据的API。
- **实时监控**：提供获取实时监控指标的API。
- **趋势分析**：提供获取数据趋势分析的API。

## 🚀 2. 快速使用

### 2.1 依赖注入

```python
from app.services.analytics_service import AnalyticsService

@router.get("/sessions")
async def get_session_stats(
    # ...
    db: AsyncSession = Depends(get_db),
):
    analytics_service = AnalyticsService(db)
    # ...
```

### 2.2 核心端点

- `GET /sessions` - 获取会话统计
- `GET /messages` - 获取消息统计
- `GET /realtime` - 获取实时指标
- `GET /trends` - 获取趋势分析

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    A[API Endpoints] --> B(AnalyticsService)
    B --> C(Session Model)
    B --> D(Message Model)
```

### 3.2 数据流

**获取会话统计流程**：
1. **API接收**：接收获取会话统计的请求。
2. **服务调用**：调用`AnalyticsService.get_session_stats`。
3. **响应返回**：返回`SessionStatsResponse`。

## 🔧 4. API参考

| HTTP动词 | 端点 | 描述 |
|---|---|---|
| `GET` | `/sessions` | 获取会话统计 |
| `GET` | `/messages` | 获取消息统计 |
| `GET`|`/realtime`|获取实时指标|
| `GET`|`/trends`|获取趋势分析|

## 🧪 5. 测试策略

- **集成测试**：`tests/integration/test_analytics_api.py`

## 💡 6. 维护与扩展

- **自定义报表**：可以添加创建和管理自定义报表的功能。
- **数据导出**：可以添加将统计数据导出为CSV或Excel的功能。
- **数据可视化**：可以与前端图表库集成，提供更丰富的数据可视化。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 