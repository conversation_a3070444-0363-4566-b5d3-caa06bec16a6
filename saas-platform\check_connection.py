#!/usr/bin/env python3
"""
AstrBot SaaS平台对接状态检查脚本
检查AstrBot实例和SaaS平台的连接状态
"""

import asyncio
import json
from typing import Dict, Any, Optional

import httpx


async def check_astrbot_instance(host: str = "localhost", port: int = 6185) -> Dict[str, Any]:
    """
    检查AstrBot实例状态
    
    Args:
        host: AstrBot主机地址
        port: AstrBot端口 (更新为6185)
        
    Returns:
        Dict[str, Any]: 状态检查结果
    """
    url = f"http://{host}:{port}"
    result = {
        "service": "AstrBot Instance",
        "url": url,
        "status": "unknown",
        "details": {}
    }
    
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            # 尝试多个可能的健康检查端点
            endpoints = ["/health", "/api/health", "/status", "/"]
            
            for endpoint in endpoints:
                try:
                    response = await client.get(f"{url}{endpoint}")
                    if response.status_code == 200:
                        result["status"] = "running"
                        result["endpoint"] = endpoint
                        try:
                            result["details"] = response.json()
                        except:
                            result["details"] = {"response": response.text[:200]}
                        break
                except:
                    continue
                    
            if result["status"] == "unknown":
                result["status"] = "unreachable"
                result["error"] = "所有健康检查端点都无法访问"
                
    except Exception as e:
        result["status"] = "error"
        result["error"] = str(e)
    
    return result


async def check_saas_platform(host: str = "localhost", port: int = 8000) -> Dict[str, Any]:
    """
    检查SaaS平台状态
    
    Args:
        host: SaaS平台主机地址  
        port: SaaS平台端口
        
    Returns:
        Dict[str, Any]: 状态检查结果
    """
    url = f"http://{host}:{port}"
    result = {
        "service": "SaaS Platform",
        "url": url,
        "status": "unknown",
        "details": {}
    }
    
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            # 检查主要API端点
            endpoints = [
                "/api/v1/health",
                "/health", 
                "/docs",  # FastAPI文档
                "/"
            ]
            
            for endpoint in endpoints:
                try:
                    response = await client.get(f"{url}{endpoint}")
                    if response.status_code == 200:
                        result["status"] = "running"
                        result["endpoint"] = endpoint
                        try:
                            result["details"] = response.json()
                        except:
                            result["details"] = {"response": response.text[:200]}
                        break
                except:
                    continue
                    
            if result["status"] == "unknown":
                result["status"] = "unreachable"
                result["error"] = "所有API端点都无法访问"
                
    except Exception as e:
        result["status"] = "error"
        result["error"] = str(e)
    
    return result


async def check_integration_readiness() -> Dict[str, Any]:
    """
    检查集成就绪状态
    
    Returns:
        Dict[str, Any]: 集成就绪状态
    """
    print("🔍 开始检查AstrBot SaaS平台对接状态...\n")
    
    # 并行检查两个服务
    astrbot_result, saas_result = await asyncio.gather(
        check_astrbot_instance(),
        check_saas_platform(),
        return_exceptions=True
    )
    
    # 处理异常结果
    if isinstance(astrbot_result, Exception):
        astrbot_result = {"service": "AstrBot Instance", "status": "error", "error": str(astrbot_result)}
    if isinstance(saas_result, Exception):
        saas_result = {"service": "SaaS Platform", "status": "error", "error": str(saas_result)}
    
    # 打印检查结果
    print("📊 服务状态检查结果:")
    print("=" * 50)
    
    for result in [astrbot_result, saas_result]:
        status_emoji = {
            "running": "✅",
            "unreachable": "❌", 
            "error": "💥",
            "unknown": "❓"
        }.get(result["status"], "❓")
        
        print(f"{status_emoji} {result['service']}: {result['status'].upper()}")
        print(f"   URL: {result['url']}")
        
        if "endpoint" in result:
            print(f"   活跃端点: {result['endpoint']}")
        if "error" in result:
            print(f"   错误: {result['error']}")
        if "details" in result and result["details"]:
            print(f"   详情: {json.dumps(result['details'], indent=6, ensure_ascii=False)}")
        print()
    
    # 集成就绪性分析
    astrbot_running = astrbot_result["status"] == "running"
    saas_running = saas_result["status"] == "running"
    
    print("🚀 集成就绪性分析:")
    print("=" * 50)
    
    if astrbot_running and saas_running:
        print("✅ 两个服务都在运行，可以开始对接配置!")
        readiness = "ready"
        next_steps = [
            "1. 配置AstrBot实例认证Token",
            "2. 设置Webhook回调URL", 
            "3. 测试消息双向通信",
            "4. 验证多租户隔离"
        ]
    elif saas_running and not astrbot_running:
        print("⚠️  SaaS平台运行正常，但AstrBot实例未启动")
        readiness = "partial"
        next_steps = [
            "1. 启动AstrBot实例服务",
            "2. 检查AstrBot配置和端口",
            "3. 确认AstrBot健康检查端点"
        ]
    elif astrbot_running and not saas_running:
        print("⚠️  AstrBot实例运行正常，但SaaS平台未启动")
        readiness = "partial"
        next_steps = [
            "1. 启动SaaS平台服务",
            "2. 检查数据库连接",
            "3. 验证FastAPI应用配置"
        ]
    else:
        print("❌ 两个服务都未运行，需要先启动服务")
        readiness = "not_ready"
        next_steps = [
            "1. 启动SaaS平台（uvicorn app.main:app --host 0.0.0.0 --port 8000）",
            "2. 启动AstrBot实例",
            "3. 重新运行此检查脚本"
        ]
    
    print(f"\n📋 下一步操作:")
    for step in next_steps:
        print(f"   {step}")
    
    return {
        "astrbot": astrbot_result,
        "saas_platform": saas_result,
        "readiness": readiness,
        "next_steps": next_steps
    }


async def main():
    """主函数"""
    try:
        result = await check_integration_readiness()
        return result
    except Exception as e:
        print(f"💥 检查过程中发生错误: {e}")
        return {"error": str(e)}


if __name__ == "__main__":
    # 运行检查
    result = asyncio.run(main()) 