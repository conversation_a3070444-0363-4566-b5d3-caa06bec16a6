#!/usr/bin/env python3
"""
调试API端点检测脚本
"""

import ast
from pathlib import Path


class FunctionVisitor(ast.NodeVisitor):
    """函数访问器"""

    def __init__(self):
        self.functions = []

    def visit_FunctionDef(self, node):
        """访问函数定义"""
        self.functions.append(node)
        self.generic_visit(node)


def debug_api_file(file_path: Path):
    """调试单个API文件"""
    print(f"\n分析文件: {file_path}")

    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    tree = ast.parse(content)

    # 使用访问者模式查找所有函数
    visitor = FunctionVisitor()
    visitor.visit(tree)

    print(f"找到 {len(visitor.functions)} 个函数")

    for node in visitor.functions:
        print(f"\n函数: {node.name} (行 {node.lineno})")
        print(f"装饰器数量: {len(node.decorator_list)}")

        for i, decorator in enumerate(node.decorator_list):
            print(f"  装饰器 {i+1}: {type(decorator).__name__}")

            # 检查是否是路由装饰器
            if isinstance(decorator, ast.Call):
                if hasattr(decorator.func, "attr") and hasattr(decorator.func, "value"):
                    if hasattr(decorator.func.value, "id"):
                        print(
                            f"    Call: {decorator.func.value.id}.{decorator.func.attr}()"
                        )
                        if (
                            decorator.func.value.id == "router"
                            and decorator.func.attr
                            in ["get", "post", "put", "patch", "delete"]
                        ):
                            print(f"    ✅ 这是一个路由函数!")
            elif isinstance(decorator, ast.Attribute):
                if hasattr(decorator, "value") and hasattr(decorator.value, "id"):
                    print(f"    Attribute: {decorator.value.id}.{decorator.attr}")


def main():
    """主函数"""
    # 检查几个文件
    files = [
        "app/api/v1/tenants.py",
        "app/api/v1/instances.py",
        "app/api/v1/messages.py",
    ]

    for file_path in files:
        if Path(file_path).exists():
            debug_api_file(Path(file_path))


if __name__ == "__main__":
    main()
