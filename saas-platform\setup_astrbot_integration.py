#!/usr/bin/env python3
"""
AstrBot SaaS平台集成配置脚本

实现M9阶段的核心对接功能：
- 检查AstrBot API端口连通性
- 创建测试租户  
- 生成实例认证Token
- 测试Webhook双向通信
- 配置API对接
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

import httpx


class AstrBotSaaSIntegrator:
    """AstrBot SaaS平台集成器"""
    
    def __init__(self):
        self.saas_base_url = "http://localhost:8000"
        self.astrbot_web_url = "http://localhost:6185"     # Web界面
        self.astrbot_admin_url = "http://localhost:6195"   # 管理接口
        self.astrbot_api_url = "http://localhost:6199"     # API接口 - 关键对接端点
        self.http_client = httpx.AsyncClient(timeout=30.0)
        
        # 测试数据
        self.test_tenant_data = {
            "name": "测试企业A",
            "email": f"test-tenant-{uuid.uuid4().hex[:8]}@astrbot.demo",
            "contact_person": "张三",
            "phone": "13800138000"
        }
        
        self.test_instance_data = {
            "instance_id": f"astrbot_test_{uuid.uuid4().hex[:8]}",
            "instance_name": "测试AstrBot实例",
            "platform": "qq",  # 模拟QQ平台
            "api_endpoint": self.astrbot_api_url
        }
    
    async def step0_check_astrbot_endpoints(self) -> Dict[str, bool]:
        """第零步：检查AstrBot各端点连通性"""
        print("🔍 第零步：检查AstrBot端点连通性...")
        
        endpoints = {
            "Web界面 (6185)": self.astrbot_web_url,
            "管理接口 (6195)": self.astrbot_admin_url, 
            "API接口 (6199)": self.astrbot_api_url
        }
        
        results = {}
        
        for name, url in endpoints.items():
            try:
                # 尝试多个可能的健康检查路径
                test_paths = ["/health", "/api/health", "/status", "/api/status", "/"]
                connected = False
                
                for path in test_paths:
                    try:
                        response = await self.http_client.get(f"{url}{path}", timeout=5.0)
                        if response.status_code in [200, 404]:  # 404也说明端口是开放的
                            connected = True
                            print(f"   ✅ {name}: 连通 ({path} -> {response.status_code})")
                            break
                    except:
                        continue
                
                if not connected:
                    print(f"   ❌ {name}: 无法连接")
                
                results[name] = connected
                
            except Exception as e:
                print(f"   💥 {name}: 检查失败 - {e}")
                results[name] = False
        
        return results
        
    async def step1_create_test_tenant(self) -> Optional[Dict[str, Any]]:
        """第一步：创建测试租户"""
        print("\n🏢 第一步：创建测试租户...")
        
        try:
            # 首先检查租户API是否可用
            health_response = await self.http_client.get(f"{self.saas_base_url}/api/v1/health")
            if health_response.status_code != 200:
                print(f"❌ SaaS平台健康检查失败: {health_response.status_code}")
                return None
            
            # 创建租户
            response = await self.http_client.post(
                f"{self.saas_base_url}/api/v1/tenants",
                json=self.test_tenant_data
            )
            
            if response.status_code == 201:
                tenant = response.json()["data"]
                print(f"✅ 租户创建成功:")
                print(f"   租户ID: {tenant['id']}")
                print(f"   租户名称: {tenant['name']}")
                print(f"   API密钥: {tenant.get('api_key', 'N/A')}")
                return tenant
            elif response.status_code == 422:
                # 可能是验证错误，尝试更简单的数据
                simple_data = {
                    "name": f"测试租户_{uuid.uuid4().hex[:6]}",
                    "email": f"test_{uuid.uuid4().hex[:6]}@example.com"
                }
                print("   ⚠️ 尝试简化的租户数据...")
                response = await self.http_client.post(
                    f"{self.saas_base_url}/api/v1/tenants",
                    json=simple_data
                )
                if response.status_code == 201:
                    tenant = response.json()["data"]
                    print(f"✅ 租户创建成功（简化数据）:")
                    print(f"   租户ID: {tenant['id']}")
                    return tenant
                else:
                    print(f"❌ 简化数据也失败: {response.status_code} - {response.text}")
                    return None
            else:
                print(f"❌ 租户创建失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"💥 创建租户时出错: {e}")
            return None
    
    async def step2_test_astrbot_api_connectivity(self) -> Dict[str, Any]:
        """第二步：测试AstrBot API连通性"""
        print("\n🔌 第二步：测试AstrBot API连通性...")
        
        try:
            # 测试AstrBot API的各种端点
            api_endpoints = [
                "/api/v1/status",
                "/api/status", 
                "/api/health",
                "/status",
                "/health",
                "/api/v1/info",
                "/info"
            ]
            
            api_info = {}
            
            for endpoint in api_endpoints:
                try:
                    response = await self.http_client.get(
                        f"{self.astrbot_api_url}{endpoint}",
                        timeout=5.0
                    )
                    
                    if response.status_code == 200:
                        print(f"   ✅ API端点 {endpoint}: 可用 (200)")
                        try:
                            data = response.json()
                            api_info[endpoint] = data
                            print(f"      数据: {json.dumps(data, indent=6, ensure_ascii=False)[:100]}...")
                        except:
                            api_info[endpoint] = {"response": response.text[:100]}
                        break
                    elif response.status_code in [404, 405]:
                        print(f"   ⚠️ API端点 {endpoint}: 端口开放但端点不存在 ({response.status_code})")
                    else:
                        print(f"   ⚠️ API端点 {endpoint}: 异常状态 ({response.status_code})")
                        
                except Exception as e:
                    print(f"   ❌ API端点 {endpoint}: 连接失败 ({e})")
                    continue
            
            # 如果没有找到可用的API端点，尝试Web界面
            if not api_info:
                print("   🔄 尝试Web界面端点...")
                try:
                    response = await self.http_client.get(self.astrbot_web_url, timeout=5.0)
                    if response.status_code in [200, 404]:
                        print(f"   ✅ Web界面可访问: {response.status_code}")
                        api_info["web_interface"] = {
                            "status": response.status_code,
                            "accessible": True
                        }
                except Exception as e:
                    print(f"   ❌ Web界面也无法访问: {e}")
            
            return api_info
            
        except Exception as e:
            print(f"💥 测试API连通性时出错: {e}")
            return {}
    
    async def step3_test_webhook_communication(self, tenant_id: str) -> bool:
        """第三步：测试Webhook双向通信"""
        print("\n🔄 第三步：测试Webhook通信...")
        
        try:
            # 模拟AstrBot实例发送Webhook到SaaS平台
            webhook_payload = {
                "event_type": "message.received",
                "timestamp": datetime.utcnow().isoformat(),
                "data": {
                    "session_id": f"test_session_{uuid.uuid4().hex[:8]}",
                    "message": {
                        "id": f"msg_{uuid.uuid4().hex[:8]}",
                        "content": "Hello from AstrBot integration test!",
                        "sender_type": "customer",
                        "sender_id": "user_12345",
                        "platform": "qq"
                    }
                },
                "metadata": {
                    "instance_id": self.test_instance_data["instance_id"],
                    "version": "1.0.0",
                    "platform": "qq"
                }
            }
            
            # 发送Webhook到SaaS平台
            webhook_url = f"{self.saas_base_url}/api/v1/webhooks/astrbot/{tenant_id}"
            headers = {
                "Content-Type": "application/json",
                "X-AstrBot-Signature": "test-signature",
                "User-Agent": "AstrBot/1.0.0"
            }
            
            response = await self.http_client.post(
                webhook_url,
                json=webhook_payload,
                headers=headers
            )
            
            if response.status_code in [200, 201]:
                print(f"✅ Webhook通信测试成功:")
                print(f"   状态码: {response.status_code}")
                print(f"   响应: {response.json()}")
                return True
            else:
                print(f"❌ Webhook通信测试失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"💥 测试Webhook通信时出错: {e}")
            return False
    
    async def step4_generate_integration_config(self, tenant_id: str, api_info: Dict[str, Any]) -> Dict[str, Any]:
        """第四步：生成集成配置"""
        print("\n⚙️ 第四步：生成AstrBot集成配置...")
        
        config = {
            "tenant_id": tenant_id,
            "astrbot_endpoints": {
                "web_interface": self.astrbot_web_url,
                "admin_interface": self.astrbot_admin_url,
                "api_interface": self.astrbot_api_url
            },
            "saas_platform": {
                "api_base_url": f"{self.saas_base_url}/api/v1",
                "webhook_endpoint": f"{self.saas_base_url}/api/v1/webhooks/astrbot/{tenant_id}"
            },
            "integration_settings": {
                "instance_id": self.test_instance_data["instance_id"],
                "polling_interval": 30,  # 秒
                "retry_attempts": 3,
                "timeout": 30
            },
            "discovered_api_info": api_info
        }
        
        print("✅ 集成配置生成完成:")
        print(f"   租户ID: {tenant_id}")
        print(f"   AstrBot API: {self.astrbot_api_url}")
        print(f"   Webhook URL: {config['saas_platform']['webhook_endpoint']}")
        
        return config
    
    async def run_integration_setup(self) -> Dict[str, Any]:
        """运行完整的集成设置流程"""
        print("🚀 开始AstrBot SaaS平台M9阶段集成设置...")
        print("=" * 60)
        
        results = {}
        
        # 第零步：检查端点连通性
        endpoint_status = await self.step0_check_astrbot_endpoints()
        results["endpoints_checked"] = endpoint_status
        
        # 第一步：创建测试租户
        tenant = await self.step1_create_test_tenant()
        results["tenant_created"] = tenant is not None
        
        if not tenant:
            print("❌ 租户创建失败，跳过需要租户ID的步骤")
            return results
            
        tenant_id = tenant["id"]
        
        # 第二步：测试AstrBot API
        api_info = await self.step2_test_astrbot_api_connectivity()
        results["api_tested"] = len(api_info) > 0
        
        # 第三步：测试通信
        comm_success = await self.step3_test_webhook_communication(tenant_id)
        results["communication_tested"] = comm_success
        
        # 第四步：生成配置
        integration_config = await self.step4_generate_integration_config(tenant_id, api_info)
        results["config_generated"] = True
        results["integration_config"] = integration_config
        
        print(f"\n🎉 M9阶段集成设置完成！")
        print(f"✅ 租户ID: {tenant_id}")
        print(f"✅ AstrBot API端点: {self.astrbot_api_url}")
        print(f"✅ SaaS平台端点: {self.saas_base_url}")
        
        return results
    
    async def cleanup(self):
        """清理资源"""
        await self.http_client.aclose()


async def main():
    """主函数"""
    integrator = AstrBotSaaSIntegrator()
    
    try:
        results = await integrator.run_integration_setup()
        return results
    except Exception as e:
        print(f"💥 集成过程中发生错误: {e}")
        return {"error": str(e)}
    finally:
        await integrator.cleanup()


if __name__ == "__main__":
    # 运行集成设置
    results = asyncio.run(main())
    print(f"\n🏁 集成设置完成，结果摘要: {list(results.keys())}") 