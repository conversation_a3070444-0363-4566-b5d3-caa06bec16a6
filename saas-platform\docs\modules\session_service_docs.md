# 📖 技术文档：会话服务 (SessionService)

## 🎯 1. 模块概述

**功能**：提供会话的生命周期管理、状态控制和相关业务逻辑。

**核心职责**：
- **会话创建/获取**：根据用户ID和平台创建或获取会话，保证幂等性。
- **状态管理**：更新会话状态，并验证状态转换的有效性。
- **会话检索**：根据租户ID、状态等条件检索会话列表。
- **时间戳更新**：在消息发送时更新会话的最后消息时间。

## 🚀 2. 快速使用

### 2.1 依赖注入

在API端点中注入`SessionService`：

```python
from app.services.session_service import SessionService, get_session_service

@router.post("/sessions")
async def create_session(
    session_data: SessionCreate,
    session_service: SessionService = Depends(get_session_service),
):
    return await session_service.create_or_get_session(...)
```

### 2.2 核心方法

- **`create_or_get_session(user_id, platform, tenant_id, ...)`** - 创建或获取会话
- **`update_session_status(session_id, tenant_id, status_update)`** - 更新会话状态
- **`get_session(session_id, tenant_id)`** - 获取会话详情
- **`list_tenant_sessions(tenant_id, ...)`** - 获取租户会话列表

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    subgraph "核心依赖"
        A[FastAPI] --> B(SessionService)
        C[SQLAlchemy] --> B
        D[Pydantic] --> B
    end

    subgraph "模块交互"
        B --> E(Session Model)
    end

    style B fill:#c8e6c9
```

### 3.2 数据流

**创建会话流程**：
1. **API接收**：接收创建会话的请求。
2. **服务处理**：
   - 查找当前用户的活跃会话。
   - 如果存在，直接返回现有会话。
   - 如果不存在，创建新的`Session`对象。
   - 将会话对象添加到数据库并提交。
3. **响应返回**：返回创建的`SessionRead`对象。

## 🔧 4. API参考

| 方法 | HTTP动词 | 端点 | 描述 |
|---|---|---|---|
| `create_or_get_session` | `POST` | `/api/v1/sessions` | 创建或获取会话 |
| `update_session_status`| `PATCH` | `/api/v1/sessions/{session_id}/status` | 更新会话状态 |
| `get_session` | `GET` | `/api/v1/sessions/{session_id}` | 获取会话详情 |
| `list_tenant_sessions`| `GET` | `/api/v1/sessions` | 获取会话列表 |

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_session_service.py`
- **集成测试**：`tests/integration/test_sessions_api.py`

## 💡 6. 维护与扩展

- **状态机**：可以使用更复杂的状态机库来管理会话状态转换。
- **会话超时**：可以添加定时任务来自动关闭长时间不活跃的会话。
- **会话分配**：可以扩展`create_or_get_session`逻辑，实现更复杂的会话分配策略。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 