"""
实例管理API集成测试
测试实例的注册、查询、凭证管理等功能的API端点
"""
import uuid

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.tenant import Tenant
from app.core.security import create_access_token


@pytest.fixture
async def authenticated_client_for_instance(
    client: AsyncClient, db_session: AsyncSession
) -> tuple[AsyncClient, Tenant]:
    """创建一个带有认证头信息的HTTPX客户端和关联的租户"""
    test_tenant = Tenant(
        name="Instance Test Tenant", email=f"instance-test-{uuid.uuid4().hex[:6]}@test.com"
    )
    db_session.add(test_tenant)
    await db_session.commit()
    await db_session.refresh(test_tenant)

    token = create_access_token(
        subject=f"tenant:{test_tenant.id}",
        extra_data={"tenant_id": str(test_tenant.id)},
    )
    client.headers["Authorization"] = f"Bearer {token}"
    return client, test_tenant


class TestInstanceAPIIntegration:
    """实例管理API集成测试类"""

    @pytest.mark.asyncio
    async def test_register_list_and_revoke_instance(
        self, authenticated_client_for_instance: tuple[AsyncClient, Tenant], db_session: AsyncSession
    ):
        """测试实例的完整生命周期：注册 -> 列表 -> 撤销"""
        client, tenant = authenticated_client_for_instance
        
        # 1. 注册新实例
        register_data = {"instance_name": "My Test Bot"}
        response = await client.post("/api/v1/instances", json=register_data)
        
        assert response.status_code == 201
        response_data = response.json()["data"]
        instance_id = response_data["instance_id"]
        assert "api_token" in response_data
        assert "webhook_secret" in response_data
        assert response_data["name"] == register_data["instance_name"]

        # 验证数据库
        await db_session.refresh(tenant)
        assert instance_id in tenant.extra_data["instances"]
        assert tenant.extra_data["instances"][instance_id]["name"] == register_data["instance_name"]
        
        # 2. 获取实例列表
        response = await client.get("/api/v1/instances")
        
        assert response.status_code == 200
        list_data = response.json()["data"]
        assert len(list_data) == 1
        assert list_data[0]["instance_id"] == instance_id
        assert list_data[0]["status"] == "active"
        
        # 3. 撤销实例
        response = await client.delete(f"/api/v1/instances/{instance_id}")
        
        assert response.status_code == 200
        assert response.json()["data"]["status"] == "revoked"

        # 验证数据库状态
        await db_session.refresh(tenant)
        assert tenant.extra_data["instances"][instance_id]["status"] == "revoked"

        # 4. 再次获取列表，应不再包含被撤销的实例
        response = await client.get("/api/v1/instances")
        
        assert response.status_code == 200
        active_list_data = response.json()["data"]
        assert len(active_list_data) == 0

        # 5. 获取包含已撤销实例的列表
        response = await client.get("/api/v1/instances?include_revoked=true")
        
        assert response.status_code == 200
        full_list_data = response.json()["data"]
        assert len(full_list_data) == 1
        assert full_list_data[0]["status"] == "revoked" 