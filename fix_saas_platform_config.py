#!/usr/bin/env python3
"""
🔧 SaaS平台配置修复工具
修复.env文件格式问题，启动SaaS平台服务

主要问题：
1. .env文件格式错误
2. 数据库连接配置问题
3. CORS配置格式错误
"""

import os
import subprocess
import sys
import time
from pathlib import Path


class SaasPlatformConfigFixer:
    """SaaS平台配置修复器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.saas_platform_dir = self.project_root / "saas-platform"
        self.env_file = self.saas_platform_dir / ".env"
    
    def fix_env_file(self):
        """修复.env文件"""
        print("🔧 修复SaaS平台.env文件...")
        
        # 创建正确的.env文件内容
        env_content = """# AstrBot SaaS Platform Environment Variables
# 核心应用设置
SECRET_KEY=09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7
JWT_SECRET_KEY=your-jwt-secret-key-change-this-too

# 数据库配置 - 使用本地PostgreSQL
DATABASE_URL=postgresql+asyncpg://astrbot:astrbot123@localhost:5432/astrbot_saas
POSTGRES_SERVER=localhost
POSTGRES_USER=astrbot
POSTGRES_PASSWORD=astrbot123
POSTGRES_DB=astrbot_saas
POSTGRES_PORT=5432

# Redis配置
REDIS_URL=redis://:redis123@localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379

# 初始超级用户
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=ChangeMeASAP!

# CORS设置 - 修复JSON格式
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8080","http://localhost:6185"]

# 应用配置
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# API配置
API_V1_STR=/api/v1
PROJECT_NAME=AstrBot SaaS Platform

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
"""
        
        # 备份原文件
        if self.env_file.exists():
            backup_file = self.env_file.with_suffix('.env.backup')
            self.env_file.rename(backup_file)
            print(f"  ✅ 原文件已备份到: {backup_file}")
        
        # 写入新的.env文件
        with open(self.env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print(f"  ✅ 新的.env文件已创建: {self.env_file}")
        return True
    
    def test_database_connection(self):
        """测试数据库连接"""
        print("\n🗄️ 测试数据库连接...")
        
        try:
            # 切换到saas-platform目录
            os.chdir(self.saas_platform_dir)
            
            # 设置环境变量
            os.environ['DATABASE_URL'] = 'postgresql+asyncpg://astrbot:astrbot123@localhost:5432/astrbot_saas'
            
            # 尝试导入并测试
            sys.path.insert(0, str(self.saas_platform_dir))
            
            from app.core.config.settings import settings
            print(f"  ✅ 配置加载成功")
            print(f"  📊 数据库URL: {settings.DATABASE_URL}")
            print(f"  🌐 CORS Origins: {settings.BACKEND_CORS_ORIGINS}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 配置测试失败: {e}")
            return False
    
    def start_saas_platform(self):
        """启动SaaS平台"""
        print("\n🚀 启动SaaS平台...")
        
        try:
            # 切换到saas-platform目录
            os.chdir(self.saas_platform_dir)
            
            # 检查是否有simple_start.py
            simple_start = self.saas_platform_dir / "simple_start.py"
            if simple_start.exists():
                print("  📋 使用simple_start.py启动...")
                cmd = [sys.executable, "simple_start.py"]
            else:
                print("  📋 使用uvicorn直接启动...")
                cmd = [sys.executable, "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
            
            print(f"  🔧 执行命令: {' '.join(cmd)}")
            
            # 启动服务（非阻塞）
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.saas_platform_dir
            )
            
            print(f"  ✅ SaaS平台启动中... (PID: {process.pid})")
            print("  ⏳ 等待服务就绪...")
            
            # 等待几秒让服务启动
            time.sleep(5)
            
            # 检查进程状态
            if process.poll() is None:
                print("  ✅ SaaS平台服务正在运行")
                print("  🌐 访问地址: http://localhost:8000")
                print("  📚 API文档: http://localhost:8000/docs")
                return True, process
            else:
                stdout, stderr = process.communicate()
                print(f"  ❌ 服务启动失败")
                print(f"  📋 输出: {stdout}")
                print(f"  ❌ 错误: {stderr}")
                return False, None
                
        except Exception as e:
            print(f"  ❌ 启动失败: {e}")
            return False, None
    
    def test_endpoints(self):
        """测试端点"""
        print("\n🌐 测试SaaS平台端点...")
        
        import httpx
        
        endpoints = [
            "http://localhost:8000/",
            "http://localhost:8000/health",
            "http://localhost:8000/docs"
        ]
        
        for url in endpoints:
            try:
                with httpx.Client(timeout=10.0) as client:
                    response = client.get(url)
                    status_icon = "✅" if response.status_code < 400 else "❌"
                    print(f"  {status_icon} {url} - {response.status_code}")
            except Exception as e:
                print(f"  ❌ {url} - 连接失败: {e}")
    
    def run_complete_fix(self):
        """运行完整修复流程"""
        print("🔧 AstrBot SaaS平台配置修复开始")
        print("=" * 50)
        
        try:
            # 1. 修复.env文件
            if not self.fix_env_file():
                return False
            
            # 2. 测试配置
            if not self.test_database_connection():
                print("⚠️ 配置测试失败，但继续尝试启动...")
            
            # 3. 启动服务
            success, process = self.start_saas_platform()
            if success:
                # 4. 测试端点
                self.test_endpoints()
                
                print("\n" + "=" * 50)
                print("🎉 SaaS平台修复和启动完成！")
                print("📋 服务状态:")
                print("  ✅ 配置文件已修复")
                print("  ✅ 服务已启动")
                print("  🌐 访问: http://localhost:8000")
                print("  📚 文档: http://localhost:8000/docs")
                print("\n💡 提示: 服务在后台运行，可以继续其他操作")
                
                return True
            else:
                print("\n❌ SaaS平台启动失败")
                return False
                
        except Exception as e:
            print(f"\n❌ 修复过程出错: {e}")
            return False


def main():
    """主函数"""
    fixer = SaasPlatformConfigFixer()
    success = fixer.run_complete_fix()
    
    if success:
        print("\n🎯 下一步建议:")
        print("1. 验证SaaS平台功能正常")
        print("2. 测试租户创建功能")
        print("3. 建立与AstrBot的连接")
        return 0
    else:
        print("\n💡 如果问题持续，请检查:")
        print("1. PostgreSQL服务是否运行")
        print("2. Redis服务是否运行")
        print("3. Python依赖是否完整")
        return 1


if __name__ == "__main__":
    sys.exit(main())
