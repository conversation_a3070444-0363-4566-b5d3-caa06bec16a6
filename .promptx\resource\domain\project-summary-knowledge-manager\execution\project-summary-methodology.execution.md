<execution>
  <constraint>
    ## 客观技术限制
    - **时间限制**：项目已完成，无法重新获取实时数据和过程信息
    - **记忆完整性**：依赖参与者的记忆，可能存在遗漏或偏差
    - **数据可用性**：项目文档和产出物的完整性和可访问性有限
    - **主观性影响**：不同角色对同一事件的理解和评价可能不同
    - **复杂性约束**：项目涉及多个维度，完全复现所有细节不现实
  </constraint>

  <rule>
    ## 强制性执行规则
    - **全面性要求**：必须覆盖项目的所有关键阶段和核心成果
    - **客观性原则**：基于事实和数据进行分析，避免主观臆断
    - **结构化输出**：所有总结必须遵循标准化的文档结构和格式
    - **可验证性**：总结内容必须有据可查，支持后续验证
    - **价值导向**：聚焦于可复用和可传承的价值内容
    - **完整归档**：所有总结文档必须完整保存，建立检索体系
  </rule>

  <guideline>
    ## 项目总结指导原则
    - **多角度视角**：从技术、管理、质量、效率等多个角度进行总结
    - **层次化分析**：从操作层面到战略层面的多层次深度分析
    - **模式识别**：重点识别可复用的成功模式和应避免的失败模式
    - **经验萃取**：将具体经验抽象为通用的方法论和指导原则
    - **持续改进**：基于总结结果制定持续改进的行动计划
    - **知识传承**：确保关键知识和经验能有效传递给团队
  </guideline>

  <process>
    ## 项目总结执行流程

    ### Phase 1: 项目回顾准备 (30分钟)
    
    #### 1.1 信息收集
    - **项目文档整理**：收集项目计划、执行记录、变更日志、最终交付物
    - **参与者清单**：确定所有关键参与者和利益相关方
    - **时间线梳理**：建立项目完整的时间线和里程碑节点
    - **成果清单**：列出所有可见的项目成果和产出物
    
    #### 1.2 总结框架设计
    ```markdown
    ## 项目总结标准框架
    
    ### 1. 项目概览
    - 项目背景和目标
    - 关键参与者和角色
    - 项目周期和里程碑
    
    ### 2. 执行过程分析
    - 各阶段执行情况
    - 关键决策点和转折点
    - 问题和挑战应对
    
    ### 3. 成果价值评估
    - 定量指标分析
    - 定性价值评估
    - 预期目标达成度
    
    ### 4. 经验教训总结
    - 成功经验提炼
    - 失败教训汲取
    - 改进建议制定
    
    ### 5. 知识资产沉淀
    - 方法论总结
    - 工具和模板
    - 最佳实践文档
    ```

    ### Phase 2: 多维度数据收集 (90分钟)
    
    #### 2.1 定量数据收集
    - **时间效率分析**：
      - 计划用时 vs 实际用时
      - 各阶段时间分配比例
      - 关键任务完成效率
    
    - **质量指标统计**：
      - 交付物质量评分
      - 问题发现和解决率
      - 返工率和修复成本
    
    - **资源使用分析**：
      - 人力资源投入分配
      - 工具和技术使用情况
      - 成本控制和预算执行
    
    #### 2.2 定性信息采集
    - **过程复盘**：详细梳理每个关键节点的决策过程和执行细节
    - **问题根因分析**：识别问题的根本原因和解决路径
    - **创新突破点**：记录项目中的创新做法和突破性解决方案
    - **协作模式分析**：总结团队协作的有效模式和改进空间

    ### Phase 3: 成果价值量化 (60分钟)
    
    #### 3.1 直接成果评估
    ```markdown
    ## 成果评估矩阵
    
    | 成果类型 | 具体内容 | 量化指标 | 价值评估 |
    |---------|---------|---------|---------|
    | 技术成果 | 具体实现和改进 | 效率提升%、质量改善度 | 高/中/低 |
    | 管理成果 | 流程和方法优化 | 时间节省、成本降低 | 高/中/低 |
    | 质量成果 | 标准和机制建立 | 缺陷减少率、满意度 | 高/中/低 |
    | 知识成果 | 经验和技能积累 | 能力提升度、复用性 | 高/中/低 |
    ```
    
    #### 3.2 间接价值评估
    - **能力建设价值**：团队技能提升和经验积累
    - **流程优化价值**：工作方式改进和效率提升
    - **文化塑造价值**：团队协作文化和质量意识建立
    - **创新示范价值**：为类似项目提供参考和启发

    ### Phase 4: 模式识别与抽象 (45分钟)
    
    #### 4.1 成功模式识别
    ```mermaid
    flowchart TD
      A[成功事件识别] --> B[关键因素分析]
      B --> C[条件和环境分析]
      C --> D[可复用要素提取]
      D --> E[通用模式抽象]
      E --> F[适用场景定义]
    ```
    
    #### 4.2 失败教训提炼
    - **问题根因分析**：使用鱼骨图等工具分析问题根本原因
    - **预防措施设计**：针对根因制定具体的预防措施
    - **早期预警机制**：建立问题早期识别和预警机制
    - **应对预案制定**：为类似问题制定标准应对预案

    ### Phase 5: 知识结构化与文档化 (60分钟)
    
    #### 5.1 方法论文档化
    ```markdown
    ## 方法论文档标准结构
    
    ### 方法名称：[明确简洁的方法名称]
    
    ### 适用场景
    - 适用的项目类型和规模
    - 适用的团队配置和能力要求
    - 适用的时间和资源约束条件
    
    ### 核心步骤
    1. 步骤1：[具体操作说明]
    2. 步骤2：[具体操作说明]
    3. ...
    
    ### 关键成功因素
    - 因素1：[详细说明]
    - 因素2：[详细说明]
    
    ### 风险和应对
    - 风险1：[风险描述] → [应对措施]
    - 风险2：[风险描述] → [应对措施]
    
    ### 预期效果
    - 定量效果：[具体数据指标]
    - 定性效果：[效果描述]
    
    ### 案例应用
    - 成功案例：[具体案例描述]
    - 经验教训：[关键学习点]
    ```
    
    #### 5.2 工具模板库建设
    - **标准化模板**：制定各类工作的标准化模板
    - **检查清单**：建立关键环节的检查清单
    - **评估工具**：开发项目和质量评估工具
    - **决策辅助**：创建决策支持工具和参考资料

    ### Phase 6: 传承机制设计 (45分钟)
    
    #### 6.1 知识传承体系
    ```mermaid
    graph TD
      A[项目知识库] --> B[基础知识]
      A --> C[方法论库]
      A --> D[案例库]
      A --> E[工具模板库]
      B --> F[新人培训]
      C --> G[能力建设]
      D --> H[经验分享]
      E --> I[实践应用]
    ```
    
    #### 6.2 持续更新机制
    - **版本管理**：建立知识文档的版本控制和更新机制
    - **反馈收集**：设立知识应用效果的反馈收集渠道
    - **定期评估**：定期评估知识的时效性和适用性
    - **持续优化**：基于反馈和评估结果持续优化知识内容

    ### Phase 7: 总结报告输出 (30分钟)
    
    #### 7.1 综合报告编写
    - **执行摘要**：项目总体情况和关键成果的高度概括
    - **详细分析**：各维度的深度分析和评估结果
    - **改进建议**：具体可行的改进建议和行动计划
    - **附件资料**：支撑分析的详细数据和参考资料
    
    #### 7.2 多格式输出
    - **标准报告**：完整的PDF格式总结报告
    - **简化版本**：关键内容的简化PPT版本
    - **知识条目**：结构化的知识库条目
    - **行动清单**：可执行的改进行动清单
  </process>

  <criteria>
    ## 项目总结质量标准

    ### 完整性标准
    - ✅ 覆盖项目全生命周期的所有关键阶段
    - ✅ 涵盖技术、管理、质量、效率等多个维度
    - ✅ 包含定量数据和定性分析的双重视角
    - ✅ 体现直接成果和间接价值的全面评估

    ### 准确性标准
    - ✅ 基于真实数据和事实，避免主观臆断
    - ✅ 分析逻辑清晰，结论有理有据
    - ✅ 量化指标准确，定性评估客观
    - ✅ 经验教训提炼准确，具有指导价值

    ### 实用性标准
    - ✅ 识别的模式和方法具有可复用性
    - ✅ 制定的改进建议具体可行
    - ✅ 沉淀的知识资产便于查找和应用
    - ✅ 传承机制有效，支持知识传递

    ### 结构化标准
    - ✅ 使用标准化的文档结构和格式
    - ✅ 内容组织逻辑清晰，层次分明
    - ✅ 支持多种输出格式和应用场景
    - ✅ 建立有效的索引和检索机制

    ### 可维护性标准
    - ✅ 文档结构便于后续更新和扩展
    - ✅ 建立版本控制和变更管理机制
    - ✅ 支持增量更新和持续优化
    - ✅ 与组织知识管理体系有效集成
  </criteria>
</execution> 