{"mcpServers": {"playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "Desktop Commander": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@wonderwhy-er/desktop-commander", "--key", "25922b0e-47f6-4b99-a214-3e3281279482"], "env": {}}, "Exa Search": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "exa", "--key", "25922b0e-47f6-4b99-a214-3e3281279482"], "env": {}}, "Context7": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp", "--key", "25922b0e-47f6-4b99-a214-3e3281279482"], "env": {}}, "Sequential Thinking": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@smithery-ai/server-sequential-thinking", "--key", "25922b0e-47f6-4b99-a214-3e3281279482"], "env": {}}, "promptx": {"command": "npx", "args": ["-y", "-f", "dpml-prompt@snapshot", "mcp-server"]}, "serena": {"command": "c:\\users\\<USER>\\.local\\bin\\uv.exe", "args": ["run", "--directory", "D:\\tool\\AstrBot\\serena", "serena-mcp-server", "--context", "ide-assistant"]}}}