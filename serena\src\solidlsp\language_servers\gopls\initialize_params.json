{"_description": "The parameters sent by the client when initializing the language server with the \"initialize\" request. More details at https://microsoft.github.io/language-server-protocol/specifications/lsp/3.17/specification/#initialize", "processId": "os.getpid()", "locale": "en", "rootPath": "$rootPath", "rootUri": "$rootUri", "capabilities": {"textDocument": {"synchronization": {"didSave": true, "dynamicRegistration": true}, "completion": {"dynamicRegistration": true, "completionItem": {"snippetSupport": true}}, "definition": {"dynamicRegistration": true}, "documentSymbol": {"dynamicRegistration": true, "hierarchicalDocumentSymbolSupport": true, "symbolKind": {"valueSet": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]}}}, "workspace": {"workspaceFolders": true, "didChangeConfiguration": {"dynamicRegistration": true}}}, "workspaceFolders": [{"uri": "$uri", "name": "$name"}]}