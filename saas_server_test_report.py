#!/usr/bin/env python3
"""
AstrBot SaaS Platform 服务器测试报告生成器
DevOps执行专家 - 专业服务器状态报告
"""

import requests
import json
import time
from datetime import datetime
import subprocess
import sys

def print_section(title, level=1):
    """打印章节标题"""
    if level == 1:
        print(f"\n{'='*80}")
        print(f"🚀 {title}")
        print(f"{'='*80}")
    else:
        print(f"\n{'-'*60}")
        print(f"📋 {title}")
        print(f"{'-'*60}")

def check_docker_services():
    """检查Docker服务状态"""
    print_section("Docker基础服务状态检查", 2)
    
    try:
        result = subprocess.run(['docker', 'ps'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            services = []
            for line in lines[1:]:  # 跳过标题行
                if 'postgres' in line.lower():
                    status = "🟢 运行中" if "Up" in line else "🔴 停止"
                    services.append(f"PostgreSQL: {status}")
                elif 'redis' in line.lower():
                    status = "🟢 运行中" if "Up" in line else "🔴 停止"
                    services.append(f"Redis: {status}")
                elif 'astrbot' in line.lower():
                    status = "🟢 运行中" if "Up" in line else "🔴 停止"
                    services.append(f"AstrBot: {status}")
            
            for service in services:
                print(f"  {service}")
            
            return len(services) > 0
        else:
            print("  ❌ Docker命令执行失败")
            return False
    except Exception as e:
        print(f"  ❌ Docker检查异常: {e}")
        return False

def test_saas_server():
    """测试SaaS服务器"""
    print_section("SaaS平台服务器测试", 2)
    
    base_url = "http://localhost:8000"
    tests = []
    
    # 1. 健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        success = response.status_code == 200
        tests.append(("健康检查", success, f"状态码: {response.status_code}"))
        if success:
            data = response.json()
            print(f"  ✅ 健康检查通过: {data.get('service', 'N/A')}")
    except Exception as e:
        tests.append(("健康检查", False, f"连接错误: {e}"))
        print(f"  ❌ 健康检查失败: {e}")
    
    # 2. API文档
    try:
        response = requests.get(f"{base_url}/api/v1/docs", timeout=5)
        success = response.status_code == 200
        tests.append(("API文档", success, f"状态码: {response.status_code}"))
        if success:
            print(f"  ✅ API文档可访问: http://localhost:8000/api/v1/docs")
    except Exception as e:
        tests.append(("API文档", False, f"连接错误: {e}"))
        print(f"  ❌ API文档访问失败: {e}")
    
    # 3. OpenAPI规范
    try:
        response = requests.get(f"{base_url}/api/v1/openapi.json", timeout=5)
        success = response.status_code == 200
        if success:
            spec = response.json()
            api_count = len(spec.get('paths', {}))
            tests.append(("OpenAPI规范", success, f"API数量: {api_count}"))
            print(f"  ✅ OpenAPI规范正常: {api_count}个API端点")
        else:
            tests.append(("OpenAPI规范", False, f"状态码: {response.status_code}"))
    except Exception as e:
        tests.append(("OpenAPI规范", False, f"连接错误: {e}"))
        print(f"  ❌ OpenAPI规范获取失败: {e}")
    
    # 4. 主要API端点测试
    endpoints = [
        ("/api/v1/tenants/", "租户管理"),
        ("/api/v1/sessions/", "会话管理"),
        ("/api/v1/messages/", "消息管理"),
        ("/api/v1/webhooks/", "Webhook"),
        ("/api/v1/instances/", "实例管理"),
    ]
    
    print(f"\n  📡 API端点测试:")
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            # 401表示需要认证，这是正常的
            success = response.status_code in [200, 401, 422]
            status_desc = {
                200: "正常",
                401: "需要认证(正常)",
                422: "参数验证(正常)",
            }.get(response.status_code, f"状态码{response.status_code}")
            
            tests.append((name, success, status_desc))
            status_icon = "✅" if success else "❌"
            print(f"    {status_icon} {name}: {status_desc}")
        except Exception as e:
            tests.append((name, False, f"连接错误: {e}"))
            print(f"    ❌ {name}: 连接错误")
    
    return tests

def generate_report():
    """生成完整测试报告"""
    print_section("AstrBot SaaS Platform 服务器测试报告")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 测试环境: Windows + Docker")
    print(f"🎯 测试目标: http://localhost:8000")
    
    # Docker服务检查
    docker_ok = check_docker_services()
    
    # SaaS服务器测试
    saas_tests = test_saas_server()
    
    # 性能测试
    print_section("性能测试", 2)
    try:
        start_time = time.time()
        response = requests.get("http://localhost:8000/health", timeout=5)
        response_time = (time.time() - start_time) * 1000
        
        if response_time < 500:
            print(f"  ✅ 响应时间优秀: {response_time:.2f}ms")
        elif response_time < 2000:
            print(f"  ⚠️ 响应时间可接受: {response_time:.2f}ms")
        else:
            print(f"  ❌ 响应时间较慢: {response_time:.2f}ms")
    except Exception as e:
        print(f"  ❌ 性能测试失败: {e}")
    
    # 测试总结
    print_section("测试总结", 2)
    passed = sum(1 for _, success, _ in saas_tests if success)
    total = len(saas_tests)
    success_rate = (passed / total) * 100 if total > 0 else 0
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    print(f"📈 成功率: {success_rate:.1f}%")
    print(f"🐳 Docker服务: {'✅ 正常' if docker_ok else '❌ 异常'}")
    
    # 结论和建议
    print_section("结论和建议", 2)
    if success_rate >= 80 and docker_ok:
        print("🎉 SaaS平台服务器运行状态良好！")
        print("✅ 基础服务正常")
        print("✅ API端点可访问")
        print("✅ 文档系统正常")
        print("\n🌐 可以开始使用以下服务:")
        print("   • API文档: http://localhost:8000/api/v1/docs")
        print("   • 健康检查: http://localhost:8000/health")
        print("   • 租户管理: http://localhost:8000/api/v1/tenants/")
        print("   • 会话管理: http://localhost:8000/api/v1/sessions/")
    elif success_rate >= 60:
        print("⚠️ SaaS平台基本可用，但有部分问题需要关注")
        print("💡 建议检查日志文件排查具体问题")
    else:
        print("❌ SaaS平台存在严重问题，需要立即修复")
        print("🔧 建议重新启动服务或检查配置")
    
    return success_rate >= 80

if __name__ == "__main__":
    success = generate_report()
    sys.exit(0 if success else 1)
