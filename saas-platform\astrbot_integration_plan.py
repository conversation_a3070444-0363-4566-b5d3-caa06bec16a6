#!/usr/bin/env python3
"""
🚀 AstrBot SaaS平台 M9阶段实际对接方案

基于当前实际环境状态制定的对接实施计划：
- AstrBot Web界面 (6185) ✅ 正常运行
- AstrBot 管理接口 (6195) ❌ 502错误  
- AstrBot API接口 (6199) ❌ 502错误
- SaaS平台 (8000) ✅ 正常运行，但租户创建有问题

实施策略：逐步集成，先建立基础连接，再完善功能
"""

import asyncio
import json
import uuid
import time
from datetime import datetime
from typing import Dict, Any, Optional

import httpx


class AstrBotM9Integrator:
    """M9阶段AstrBot实际对接实施器"""
    
    def __init__(self):
        # 已确认的端点
        self.saas_platform_url = "http://localhost:8000"
        self.astrbot_web_url = "http://localhost:6185"     # ✅ 可用
        self.astrbot_admin_url = "http://localhost:6195"   # ❌ 502
        self.astrbot_api_url = "http://localhost:6199"     # ❌ 502
        
        self.http_client = httpx.AsyncClient(timeout=30.0)
    
    async def phase1_analyze_current_state(self) -> Dict[str, Any]:
        """第一阶段：分析当前环境状态"""
        print("🔍 第一阶段：环境状态分析")
        print("=" * 50)
        
        analysis = {
            "saas_platform": await self._check_saas_platform(),
            "astrbot_services": await self._check_astrbot_services(),
            "integration_readiness": {}
        }
        
        # 分析集成就绪度
        saas_ok = analysis["saas_platform"]["health_ok"]
        astrbot_web_ok = analysis["astrbot_services"]["web_interface"]["accessible"]
        
        analysis["integration_readiness"] = {
            "basic_connectivity": saas_ok and astrbot_web_ok,
            "api_integration": False,  # API端口502
            "recommended_approach": "web_scraping_and_config_based" if saas_ok and astrbot_web_ok else "fix_services_first"
        }
        
        self._print_analysis_report(analysis)
        return analysis
    
    async def _check_saas_platform(self) -> Dict[str, Any]:
        """检查SaaS平台状态"""
        result = {"health_ok": False, "tenant_api_ok": False, "details": {}}
        
        try:
            # 健康检查
            health_response = await self.http_client.get(f"{self.saas_platform_url}/api/v1/health")
            if health_response.status_code == 200:
                result["health_ok"] = True
                result["details"]["health"] = health_response.json()
                print("   ✅ SaaS平台健康检查通过")
            else:
                print(f"   ❌ SaaS平台健康检查失败: {health_response.status_code}")
                
        except Exception as e:
            print(f"   💥 SaaS平台检查错误: {e}")
            result["details"]["error"] = str(e)
            
        return result
    
    async def _check_astrbot_services(self) -> Dict[str, Any]:
        """检查AstrBot各服务状态"""
        services = {
            "web_interface": {"url": self.astrbot_web_url, "accessible": False},
            "admin_interface": {"url": self.astrbot_admin_url, "accessible": False},
            "api_interface": {"url": self.astrbot_api_url, "accessible": False}
        }
        
        for service_name, service_info in services.items():
            try:
                response = await self.http_client.get(service_info["url"], timeout=5.0)
                service_info["status_code"] = response.status_code
                service_info["accessible"] = response.status_code in [200, 404]
                
                if response.status_code == 200:
                    print(f"   ✅ {service_name}: 正常运行")
                    service_info["content_type"] = response.headers.get("content-type", "")
                elif response.status_code == 502:
                    print(f"   ❌ {service_name}: 502错误 (后端服务问题)")
                else:
                    print(f"   ⚠️ {service_name}: {response.status_code}")
                    
            except Exception as e:
                print(f"   💥 {service_name}: 连接失败 - {e}")
                service_info["error"] = str(e)
        
        return services
    
    def _print_analysis_report(self, analysis: Dict[str, Any]) -> None:
        """打印分析报告"""
        print("\n📊 环境分析报告:")
        print("-" * 30)
        
        readiness = analysis["integration_readiness"]
        
        if readiness["basic_connectivity"]:
            print("🟢 基础连通性: 正常")
        else:
            print("🔴 基础连通性: 异常")
            
        if readiness["api_integration"]:
            print("🟢 API集成能力: 可用")
        else:
            print("🟡 API集成能力: 受限 (建议使用替代方案)")
            
        print(f"\n💡 推荐方案: {readiness['recommended_approach']}")
    
    async def phase2_implement_basic_integration(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """第二阶段：实施基础集成"""
        print("\n🔧 第二阶段：实施基础集成")
        print("=" * 50)
        
        integration_results = {}
        
        if analysis["integration_readiness"]["basic_connectivity"]:
            # 方案A：配置文件集成
            config_result = await self._create_integration_config()
            integration_results["config_created"] = config_result
            
            # 方案B：Webhook测试
            webhook_result = await self._test_webhook_endpoints()
            integration_results["webhook_tested"] = webhook_result
            
        else:
            print("❌ 基础连通性不足，跳过集成实施")
            integration_results["skipped"] = True
            
        return integration_results
    
    async def _create_integration_config(self) -> Dict[str, Any]:
        """创建集成配置文件"""
        print("📄 创建集成配置...")
        
        config = {
            "astrbot_saas_integration": {
                "version": "M9.1.0",
                "created_at": datetime.utcnow().isoformat(),
                "integration_mode": "hybrid",
                
                "endpoints": {
                    "saas_platform": {
                        "base_url": self.saas_platform_url,
                        "health_endpoint": f"{self.saas_platform_url}/api/v1/health",
                        "webhook_endpoint": f"{self.saas_platform_url}/api/v1/webhooks/astrbot/{{tenant_id}}"
                    },
                    "astrbot": {
                        "web_interface": self.astrbot_web_url,
                        "admin_interface": self.astrbot_admin_url,
                        "api_interface": self.astrbot_api_url,
                        "fallback_mode": "web_interface_only"
                    }
                }
            }
        }
        
        # 保存配置文件
        config_filename = f"astrbot_integration_config_{int(time.time())}.json"
        with open(config_filename, "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ 配置文件已保存: {config_filename}")
        return {"config_file": config_filename, "config": config}
    
    async def _test_webhook_endpoints(self) -> Dict[str, Any]:
        """测试Webhook端点"""
        print("🔗 测试Webhook端点...")
        
        # 模拟租户ID
        mock_tenant_id = f"mock_tenant_{uuid.uuid4().hex[:8]}"
        webhook_url = f"{self.saas_platform_url}/api/v1/webhooks/astrbot/{mock_tenant_id}"
        
        # 模拟Webhook数据
        test_payload = {
            "event_type": "message.received",
            "timestamp": datetime.utcnow().isoformat(),
            "data": {
                "session_id": f"test_session_{uuid.uuid4().hex[:8]}",
                "message": {
                    "content": "集成测试消息",
                    "sender_type": "customer",
                    "platform": "qq"
                }
            }
        }
        
        try:
            response = await self.http_client.post(
                webhook_url,
                json=test_payload,
                headers={
                    "Content-Type": "application/json",
                    "X-AstrBot-Signature": "test-signature"
                }
            )
            
            if response.status_code in [200, 201]:
                print(f"   ✅ Webhook测试成功: {response.status_code}")
                return {"success": True, "response": response.json()}
            else:
                print(f"   ⚠️ Webhook返回: {response.status_code}")
                return {"success": False, "status_code": response.status_code}
                
        except Exception as e:
            print(f"   💥 Webhook测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def run_m9_integration(self) -> Dict[str, Any]:
        """运行完整的M9阶段集成"""
        print("🚀 AstrBot SaaS平台 M9阶段实际对接开始")
        print("=" * 60)
        
        try:
            # 第一阶段：环境分析
            analysis = await self.phase1_analyze_current_state()
            
            # 第二阶段：基础集成
            integration_results = await self.phase2_implement_basic_integration(analysis)
            
            print("\n🎉 M9阶段对接完成！")
            print("📊 整体状态: 基础框架已建立")
            
            return {
                "phase1_analysis": analysis,
                "phase2_integration": integration_results,
                "overall_status": "basic_success"
            }
            
        except Exception as e:
            print(f"💥 M9集成过程出错: {e}")
            return {"error": str(e), "status": "failed"}
    
    async def cleanup(self):
        """清理资源"""
        await self.http_client.aclose()


async def main():
    """主函数"""
    integrator = AstrBotM9Integrator()
    
    try:
        result = await integrator.run_m9_integration()
        return result
    finally:
        await integrator.cleanup()


if __name__ == "__main__":
    print("🔥 启动AstrBot SaaS平台M9阶段实际对接...")
    result = asyncio.run(main())
    print(f"\n✨ M9对接完成!") 