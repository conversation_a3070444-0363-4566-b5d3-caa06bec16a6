"""
测试 deps 模块
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from uuid import uuid4
from fastapi import HTTP<PERSON>x<PERSON>, status

from app.api.deps import get_current_tenant, get_current_user, get_db
from app.models.tenant import Tenant, TenantStatus
from app.models.user import User
from app.schemas.user import UserRead
from app.schemas.tenant import TenantRead


class TestDeps:
    """测试API依赖模块"""

    def test_deps_functions_exist(self):
        """测试依赖函数存在"""
        # 验证主要依赖函数可以导入
        assert callable(get_current_tenant)
        assert callable(get_current_user)
        assert callable(get_db)

    @pytest.mark.asyncio
    async def test_get_db_dependency(self):
        """测试数据库会话依赖"""
        # 由于get_db是一个生成器函数，我们需要测试它能正常调用
        with patch('app.api.deps.AsyncSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value = mock_session
            
            # 模拟依赖调用
            db_gen = get_db()
            db_session = await db_gen.__anext__()
            
            # 验证返回的是session实例
            assert db_session is not None

    @pytest.mark.asyncio
    async def test_get_current_user_success(self):
        """测试成功获取当前用户"""
        # Arrange
        mock_user = Mock(spec=User)
        mock_user.id = uuid4()
        mock_user.email = "<EMAIL>"
        mock_user.is_active = True
        mock_user.tenant_id = uuid4()
        
        mock_token = "valid_jwt_token"
        
        with patch('app.api.deps.verify_token') as mock_verify, \
             patch('app.api.deps.get_user_by_id') as mock_get_user:
            
            # 模拟token验证成功
            mock_verify.return_value = {"sub": str(mock_user.id)}
            
            # 模拟用户查询成功
            mock_get_user.return_value = UserRead(
                id=mock_user.id,
                email=mock_user.email,
                is_active=mock_user.is_active,
                tenant_id=mock_user.tenant_id,
                created_at="2023-01-01T00:00:00",
                updated_at="2023-01-01T00:00:00"
            )
            
            # Act
            result = await get_current_user(mock_token)
            
            # Assert
            assert result is not None
            assert result.id == mock_user.id
            assert result.email == mock_user.email
            mock_verify.assert_called_once_with(mock_token)
            mock_get_user.assert_called_once_with(str(mock_user.id))

    @pytest.mark.asyncio
    async def test_get_current_user_invalid_token(self):
        """测试无效token获取当前用户"""
        # Arrange
        invalid_token = "invalid_token"
        
        with patch('app.api.deps.verify_token') as mock_verify:
            # 模拟token验证失败
            mock_verify.side_effect = HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
            
            # Act & Assert
            with pytest.raises(HTTPException) as exc_info:
                await get_current_user(invalid_token)
            
            assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.asyncio
    async def test_get_current_user_user_not_found(self):
        """测试用户不存在的情况"""
        # Arrange
        mock_token = "valid_jwt_token"
        user_id = str(uuid4())
        
        with patch('app.api.deps.verify_token') as mock_verify, \
             patch('app.api.deps.get_user_by_id') as mock_get_user:
            
            # 模拟token验证成功
            mock_verify.return_value = {"sub": user_id}
            
            # 模拟用户不存在
            mock_get_user.return_value = None
            
            # Act & Assert
            with pytest.raises(HTTPException) as exc_info:
                await get_current_user(mock_token)
            
            assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.asyncio
    async def test_get_current_tenant_success(self):
        """测试成功获取当前租户"""
        # Arrange
        mock_tenant = Mock(spec=Tenant)
        mock_tenant.id = uuid4()
        mock_tenant.name = "Test Corporation"
        mock_tenant.status = TenantStatus.ACTIVE
        
        mock_user = UserRead(
            id=uuid4(),
            email="<EMAIL>",
            is_active=True,
            tenant_id=mock_tenant.id,
            created_at="2023-01-01T00:00:00",
            updated_at="2023-01-01T00:00:00"
        )
        
        with patch('app.api.deps.get_tenant_by_id') as mock_get_tenant:
            # 模拟租户查询成功
            mock_get_tenant.return_value = TenantRead(
                id=mock_tenant.id,
                name=mock_tenant.name,
                email="<EMAIL>",
                plan="basic",
                status=mock_tenant.status,
                api_key="ak_test_123",
                created_at="2023-01-01T00:00:00",
                updated_at="2023-01-01T00:00:00",
                is_active=True,
                display_name=mock_tenant.name,
                metadata={}
            )
            
            # Act
            result = await get_current_tenant(mock_user)
            
            # Assert
            assert result is not None
            assert result.id == mock_tenant.id
            assert result.name == mock_tenant.name
            mock_get_tenant.assert_called_once_with(mock_user.tenant_id)

    @pytest.mark.asyncio
    async def test_get_current_tenant_not_found(self):
        """测试租户不存在的情况"""
        # Arrange
        mock_user = UserRead(
            id=uuid4(),
            email="<EMAIL>",
            is_active=True,
            tenant_id=uuid4(),
            created_at="2023-01-01T00:00:00",
            updated_at="2023-01-01T00:00:00"
        )
        
        with patch('app.api.deps.get_tenant_by_id') as mock_get_tenant:
            # 模拟租户不存在
            mock_get_tenant.return_value = None
            
            # Act & Assert
            with pytest.raises(HTTPException) as exc_info:
                await get_current_tenant(mock_user)
            
            assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.asyncio
    async def test_get_current_tenant_inactive_user(self):
        """测试非活跃用户获取租户"""
        # Arrange
        mock_user = UserRead(
            id=uuid4(),
            email="<EMAIL>",
            is_active=False,  # 用户非活跃
            tenant_id=uuid4(),
            created_at="2023-01-01T00:00:00",
            updated_at="2023-01-01T00:00:00"
        )
        
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await get_current_tenant(mock_user)
        
        assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN

    def test_dependency_module_structure(self):
        """测试deps模块结构完整性"""
        # 验证模块包含必要的导入
        import app.api.deps as deps_module
        
        # 验证关键依赖函数存在
        assert hasattr(deps_module, 'get_current_user')
        assert hasattr(deps_module, 'get_current_tenant')
        assert hasattr(deps_module, 'get_db')
        
        # 验证函数签名正确（可调用）
        assert callable(deps_module.get_current_user)
        assert callable(deps_module.get_current_tenant)
        assert callable(deps_module.get_db)
