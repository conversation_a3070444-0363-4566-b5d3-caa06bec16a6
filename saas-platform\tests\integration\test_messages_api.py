"""
消息API集成测试
测试消息API端点与服务层、数据库的完整集成
"""
import uuid

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.tenant import Tenant
from app.models.session import Session
from app.models.message import SenderType
from app.core.security import create_access_token


@pytest.fixture
async def setup_session(
    client: AsyncClient, db_session: AsyncSession
) -> tuple[AsyncClient, Tenant, Session]:
    """创建一个带有认证头信息、租户和会话的客户端"""
    # 创建租户
    test_tenant = Tenant(
        name="Message Test Tenant", email=f"message-test-{uuid.uuid4().hex[:6]}@test.com"
    )
    db_session.add(test_tenant)
    await db_session.commit()
    await db_session.refresh(test_tenant)

    # 创建会话
    test_session = Session(tenant_id=test_tenant.id, user_id="msg-user", platform="web")
    db_session.add(test_session)
    await db_session.commit()
    await db_session.refresh(test_session)

    # 创建认证token
    token = create_access_token(
        subject=f"tenant:{test_tenant.id}",
        extra_data={"tenant_id": str(test_tenant.id)},
    )
    client.headers["Authorization"] = f"Bearer {token}"
    return client, test_tenant, test_session


class TestMessageAPIIntegration:
    """消息API集成测试类"""

    @pytest.mark.asyncio
    async def test_create_message_in_session(
        self, setup_session: tuple[AsyncClient, Tenant, Session], db_session: AsyncSession
    ):
        """测试在会话中创建一条新消息"""
        client, tenant, session = setup_session
        
        message_data = {
            "session_id": str(session.id),
            "content": "This is an integration test message.",
            "sender_type": SenderType.USER.value,
            "sender_id": "test-sender-id"
        }

        response = await client.post("/api/v1/messages", json=message_data)
        
        assert response.status_code == 201
        response_data = response.json()["data"]
        assert response_data["content"] == message_data["content"]
        assert response_data["session_id"] == str(session.id)
        assert response_data["sender_type"] == message_data["sender_type"]

    @pytest.mark.asyncio
    async def test_list_messages_for_session(
        self, setup_session: tuple[AsyncClient, Tenant, Session], db_session: AsyncSession
    ):
        """测试获取一个会话的消息列表"""
        client, tenant, session = setup_session

        # 在数据库中预先创建几条消息
        from app.models.message import Message
        for i in range(5):
            msg = Message(
                tenant_id=tenant.id,
                session_id=session.id,
                content=f"Message {i}",
                sender_type=SenderType.USER,
                sender_id="test-sender"
            )
            db_session.add(msg)
        await db_session.commit()
        
        # 通过API获取消息列表
        response = await client.get(f"/api/v1/sessions/{session.id}/messages")
        
        assert response.status_code == 200
        response_data = response.json()["data"]
        assert len(response_data) == 5
        assert response_data[0]["content"] == "Message 0"

        # 测试分页
        response = await client.get(f"/api/v1/sessions/{session.id}/messages?skip=2&limit=2")
        
        assert response.status_code == 200
        response_data = response.json()["data"]
        assert len(response_data) == 2
        assert response_data[0]["content"] == "Message 2" 