#!/usr/bin/env python3
"""
AstrBot SaaS Platform PostgreSQL 集成测试
验证应用与数据库的完整集成功能
"""

import asyncio
import asyncpg
import logging
import sys
import os
import subprocess
import json
from datetime import datetime, timedelta
from pathlib import Path

# 添加app目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "app"))

try:
    # 尝试导入应用模块
    from app.core.config import Settings
    from app.models.tenant import Tenant
    from app.models.user import User
    from app.models.session import Session
    APP_AVAILABLE = True
except ImportError as e:
    logging.warning(f"应用模块导入失败: {e}")
    APP_AVAILABLE = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('integration_test.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DatabaseIntegrationTester:
    """数据库集成测试器"""
    
    def __init__(self):
        """初始化测试器"""
        # 数据库配置
        self.db_config = {
            "host": "localhost",
            "port": 5432,
            "user": "astrbot", 
            "password": "astrbot123",
            "database": "astrbot_saas"
        }
        
        self.test_db_config = {
            "host": "localhost",
            "port": 5432,
            "user": "astrbot",
            "password": "astrbot123", 
            "database": "astrbot_test"
        }

    def get_database_url(self, config: dict) -> str:
        """获取数据库连接URL"""
        return f"postgresql://{config['user']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}"

    async def test_database_connection(self) -> bool:
        """测试数据库连接"""
        logger.info("🔗 测试数据库连接...")
        
        try:
            conn = await asyncpg.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            
            version = await conn.fetchval('SELECT version()')
            logger.info(f"✅ 数据库连接成功: {version.split(',')[0]}")
            
            await conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False

    async def create_core_tables(self) -> bool:
        """创建核心数据表"""
        logger.info("🏗️ 创建核心数据表...")
        
        try:
            conn = await asyncpg.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            
            # 创建租户表
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS tenants (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    slug VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    status VARCHAR(20) DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            logger.info("  ✅ 租户表创建成功")
            
            # 创建用户表
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id SERIAL PRIMARY KEY,
                    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
                    username VARCHAR(50) NOT NULL,
                    email VARCHAR(255) NOT NULL,
                    hashed_password VARCHAR(255) NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    is_superuser BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(tenant_id, username),
                    UNIQUE(tenant_id, email)
                )
            ''')
            logger.info("  ✅ 用户表创建成功")
            
            # 创建会话表
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id SERIAL PRIMARY KEY,
                    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
                    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    session_id VARCHAR(255) UNIQUE NOT NULL,
                    platform VARCHAR(50) NOT NULL,
                    status VARCHAR(20) DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ended_at TIMESTAMP
                )
            ''')
            logger.info("  ✅ 会话表创建成功")
            
            # 创建消息表
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS messages (
                    id SERIAL PRIMARY KEY,
                    session_id INTEGER REFERENCES sessions(id) ON DELETE CASCADE,
                    sender_type VARCHAR(20) NOT NULL, -- 'user' or 'bot'
                    content TEXT NOT NULL,
                    message_type VARCHAR(50) DEFAULT 'text',
                    metadata JSONB DEFAULT '{}',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            logger.info("  ✅ 消息表创建成功")
            
            await conn.close()
            logger.info("🎉 所有核心表创建完成!")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建数据表失败: {e}")
            return False

    async def test_tenant_operations(self) -> bool:
        """测试租户操作"""
        logger.info("🏢 测试租户操作...")
        
        try:
            conn = await asyncpg.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            
            # 测试1: 创建租户
            tenant_data = {
                'name': 'AstrBot测试公司',
                'slug': 'astrbot-test',
                'email': '<EMAIL>'
            }
            
            tenant_id = await conn.fetchval('''
                INSERT INTO tenants (name, slug, email)
                VALUES ($1, $2, $3)
                RETURNING id
            ''', tenant_data['name'], tenant_data['slug'], tenant_data['email'])
            
            logger.info(f"  ✅ 租户创建成功: ID={tenant_id}")
            
            # 测试2: 查询租户
            tenant = await conn.fetchrow('''
                SELECT * FROM tenants WHERE id = $1
            ''', tenant_id)
            
            logger.info(f"  ✅ 租户查询成功: {tenant['name']}")
            
            # 测试3: 更新租户
            await conn.execute('''
                UPDATE tenants SET name = $1 WHERE id = $2
            ''', 'AstrBot测试公司(已更新)', tenant_id)
            
            logger.info("  ✅ 租户更新成功")
            
            await conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 租户操作测试失败: {e}")
            return False

    async def test_user_operations(self) -> bool:
        """测试用户操作"""
        logger.info("👤 测试用户操作...")
        
        try:
            conn = await asyncpg.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            
            # 获取租户ID
            tenant_id = await conn.fetchval('''
                SELECT id FROM tenants WHERE slug = $1
            ''', 'astrbot-test')
            
            if not tenant_id:
                logger.error("❌ 未找到测试租户")
                return False
            
            # 测试1: 创建用户
            user_data = {
                'username': 'testuser',
                'email': '<EMAIL>',
                'hashed_password': 'hashed_password_123'
            }
            
            user_id = await conn.fetchval('''
                INSERT INTO users (tenant_id, username, email, hashed_password)
                VALUES ($1, $2, $3, $4)
                RETURNING id
            ''', tenant_id, user_data['username'], user_data['email'], user_data['hashed_password'])
            
            logger.info(f"  ✅ 用户创建成功: ID={user_id}")
            
            # 测试2: 查询用户
            user = await conn.fetchrow('''
                SELECT u.*, t.name as tenant_name 
                FROM users u
                JOIN tenants t ON u.tenant_id = t.id
                WHERE u.id = $1
            ''', user_id)
            
            logger.info(f"  ✅ 用户查询成功: {user['username']}@{user['tenant_name']}")
            
            await conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 用户操作测试失败: {e}")
            return False

    async def test_session_operations(self) -> bool:
        """测试会话操作"""
        logger.info("💬 测试会话操作...")
        
        try:
            conn = await asyncpg.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            
            # 获取租户和用户ID
            tenant_id = await conn.fetchval('SELECT id FROM tenants WHERE slug = $1', 'astrbot-test')
            user_id = await conn.fetchval('SELECT id FROM users WHERE tenant_id = $1 LIMIT 1', tenant_id)
            
            # 测试1: 创建会话
            session_data = {
                'session_id': f'test_session_{int(datetime.now().timestamp())}',
                'platform': 'web'
            }
            
            session_id = await conn.fetchval('''
                INSERT INTO sessions (tenant_id, user_id, session_id, platform)
                VALUES ($1, $2, $3, $4)
                RETURNING id
            ''', tenant_id, user_id, session_data['session_id'], session_data['platform'])
            
            logger.info(f"  ✅ 会话创建成功: ID={session_id}")
            
            # 测试2: 添加消息
            messages = [
                {'sender_type': 'user', 'content': '你好，我需要帮助'},
                {'sender_type': 'bot', 'content': '您好！我是AstrBot，很高兴为您服务。请问有什么可以帮助您的？'}
            ]
            
            for msg in messages:
                await conn.execute('''
                    INSERT INTO messages (session_id, sender_type, content)
                    VALUES ($1, $2, $3)
                ''', session_id, msg['sender_type'], msg['content'])
            
            logger.info(f"  ✅ 消息创建成功: {len(messages)}条")
            
            # 测试3: 查询会话历史
            session_history = await conn.fetch('''
                SELECT s.session_id, s.platform, s.created_at,
                       COUNT(m.id) as message_count
                FROM sessions s
                LEFT JOIN messages m ON s.id = m.session_id
                WHERE s.tenant_id = $1
                GROUP BY s.id, s.session_id, s.platform, s.created_at
                ORDER BY s.created_at DESC
            ''', tenant_id)
            
            logger.info(f"  ✅ 会话历史查询成功: {len(session_history)}个会话")
            
            await conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 会话操作测试失败: {e}")
            return False

    async def test_complex_queries(self) -> bool:
        """测试复杂查询"""
        logger.info("🔍 测试复杂查询...")
        
        try:
            conn = await asyncpg.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            
            # 测试1: 租户统计查询
            tenant_stats = await conn.fetchrow('''
                SELECT 
                    t.name,
                    COUNT(DISTINCT u.id) as user_count,
                    COUNT(DISTINCT s.id) as session_count,
                    COUNT(m.id) as message_count
                FROM tenants t
                LEFT JOIN users u ON t.id = u.tenant_id
                LEFT JOIN sessions s ON t.id = s.tenant_id
                LEFT JOIN messages m ON s.id = m.session_id
                WHERE t.slug = $1
                GROUP BY t.id, t.name
            ''', 'astrbot-test')
            
            logger.info(f"  ✅ 租户统计查询成功:")
            logger.info(f"    租户: {tenant_stats['name']}")
            logger.info(f"    用户数: {tenant_stats['user_count']}")
            logger.info(f"    会话数: {tenant_stats['session_count']}")
            logger.info(f"    消息数: {tenant_stats['message_count']}")
            
            # 测试2: 最近活动查询
            recent_activities = await conn.fetch('''
                SELECT 
                    'message' as activity_type,
                    m.content as description,
                    m.created_at,
                    u.username
                FROM messages m
                JOIN sessions s ON m.session_id = s.id
                JOIN users u ON s.user_id = u.id
                JOIN tenants t ON s.tenant_id = t.id
                WHERE t.slug = $1
                ORDER BY m.created_at DESC
                LIMIT 10
            ''', 'astrbot-test')
            
            logger.info(f"  ✅ 最近活动查询成功: {len(recent_activities)}条活动")
            
            # 测试3: JSON查询（消息元数据）
            await conn.execute('''
                UPDATE messages SET metadata = $1 
                WHERE id = (SELECT id FROM messages LIMIT 1)
            ''', json.dumps({"test": True, "priority": "high"}))
            
            json_result = await conn.fetchval('''
                SELECT metadata->'priority' 
                FROM messages 
                WHERE metadata ? 'test'
                LIMIT 1
            ''')
            
            logger.info(f"  ✅ JSON查询测试成功: {json_result}")
            
            await conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 复杂查询测试失败: {e}")
            return False

    async def test_database_performance(self) -> bool:
        """测试数据库性能"""
        logger.info("⚡ 测试数据库性能...")
        
        try:
            conn = await asyncpg.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            
            # 性能测试1: 批量插入
            start_time = datetime.now()
            
            batch_data = [(f'batch_user_{i}', f'batch{i}@test.com', 'hashed_pwd') 
                         for i in range(100)]
            
            tenant_id = await conn.fetchval('SELECT id FROM tenants WHERE slug = $1', 'astrbot-test')
            
            await conn.executemany('''
                INSERT INTO users (tenant_id, username, email, hashed_password)
                VALUES ($1, $2, $3, $4)
            ''', [(tenant_id, username, email, pwd) for username, email, pwd in batch_data])
            
            insert_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"  ✅ 批量插入100条用户记录: {insert_time:.3f}秒")
            
            # 性能测试2: 复杂关联查询
            start_time = datetime.now()
            
            complex_result = await conn.fetch('''
                SELECT 
                    t.name as tenant_name,
                    u.username,
                    COUNT(s.id) as session_count,
                    MAX(s.created_at) as last_session
                FROM tenants t
                JOIN users u ON t.id = u.tenant_id
                LEFT JOIN sessions s ON u.id = s.user_id
                WHERE t.slug = $1
                GROUP BY t.id, t.name, u.id, u.username
                ORDER BY session_count DESC
                LIMIT 10
            ''', 'astrbot-test')
            
            query_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"  ✅ 复杂关联查询: {query_time:.3f}秒, 返回{len(complex_result)}条记录")
            
            await conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 性能测试失败: {e}")
            return False

    async def cleanup_test_data(self) -> bool:
        """清理测试数据"""
        logger.info("🧹 清理测试数据...")
        
        try:
            conn = await asyncpg.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            
            # 删除测试租户相关的所有数据（由于外键约束，会级联删除）
            await conn.execute('DELETE FROM tenants WHERE slug = $1', 'astrbot-test')
            
            logger.info("✅ 测试数据清理完成")
            
            await conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试数据清理失败: {e}")
            return False

    def generate_test_report(self, results: dict) -> None:
        """生成测试报告"""
        logger.info("\n" + "="*70)
        logger.info("📊 AstrBot SaaS Platform PostgreSQL 集成测试报告")
        logger.info("="*70)
        
        total_tests = len(results)
        passed_tests = sum(1 for success in results.values() if success)
        
        logger.info(f"\n📈 测试统计:")
        logger.info(f"  总测试数: {total_tests}")
        logger.info(f"  通过测试: {passed_tests}")
        logger.info(f"  失败测试: {total_tests - passed_tests}")
        logger.info(f"  成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        logger.info(f"\n📋 详细结果:")
        for test_name, success in results.items():
            status = "✅ 通过" if success else "❌ 失败"
            logger.info(f"  {test_name}: {status}")
        
        if passed_tests == total_tests:
            logger.info(f"\n🎉 所有测试通过! AstrBot SaaS Platform与PostgreSQL集成完美!")
            logger.info("📋 推荐配置:")
            logger.info(f"  DATABASE_URL=postgresql://astrbot:astrbot123@localhost:5432/astrbot_saas")
            logger.info("📋 下一步:")
            logger.info("  1. 运行Alembic数据库迁移")
            logger.info("  2. 启动FastAPI应用服务")
            logger.info("  3. 进行API端点测试")
        else:
            logger.warning(f"\n⚠️ 存在测试失败，请检查日志并修复问题")
        
        logger.info("="*70)

    async def run_full_integration_test(self) -> bool:
        """运行完整集成测试"""
        logger.info("🚀 开始AstrBot SaaS Platform PostgreSQL完整集成测试...")
        logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {}
        
        # 运行所有测试
        test_cases = [
            ("数据库连接测试", self.test_database_connection),
            ("核心表创建测试", self.create_core_tables),
            ("租户操作测试", self.test_tenant_operations),
            ("用户操作测试", self.test_user_operations),
            ("会话操作测试", self.test_session_operations),
            ("复杂查询测试", self.test_complex_queries),
            ("数据库性能测试", self.test_database_performance),
            ("测试数据清理", self.cleanup_test_data)
        ]
        
        for test_name, test_func in test_cases:
            logger.info(f"\n{'='*20} {test_name} {'='*20}")
            try:
                results[test_name] = await test_func()
            except Exception as e:
                logger.error(f"❌ {test_name}执行出错: {e}")
                results[test_name] = False
        
        # 生成报告
        self.generate_test_report(results)
        
        return all(results.values())

def main():
    """主函数"""
    print("🔧 AstrBot SaaS Platform PostgreSQL 集成测试工具")
    print("="*60)
    
    tester = DatabaseIntegrationTester()
    
    try:
        # 运行完整集成测试
        result = asyncio.run(tester.run_full_integration_test())
        
        if result:
            print("\n🎉 PostgreSQL集成测试完全成功!")
        else:
            print("\n❌ PostgreSQL集成测试存在问题")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        logger.error(f"❌ 测试过程出错: {e}")

if __name__ == "__main__":
    main() 