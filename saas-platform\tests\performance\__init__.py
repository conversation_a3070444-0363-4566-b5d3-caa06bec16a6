"""
AstrBot SaaS Platform - 性能测试框架

本模块提供完整的性能测试解决方案，包括：
- 基准性能测试
- 负载测试  
- 压力测试
- 性能回归测试
- 实时性能监控

Usage:
    from tests.performance import PerformanceTestSuite, BaselineManager
    
    # 运行基准测试
    suite = PerformanceTestSuite()
    results = suite.run_baseline_tests()
    
    # 性能对比分析
    baseline = BaselineManager.get_baseline()
    comparison = suite.compare_with_baseline(results, baseline)
"""

from .baseline_manager import BaselineManager
# from .performance_test_suite import PerformanceTestSuite
# from .load_test_runner import LoadTestRunner
# from .metrics_collector import MetricsCollector
# from .performance_analyzer import PerformanceAnalyzer

__all__ = [
    'BaselineManager',
    # 'PerformanceTestSuite', 
    # 'LoadTestRunner',
    # 'MetricsCollector',
    # 'PerformanceAnalyzer'
]

__version__ = '1.0.0' 