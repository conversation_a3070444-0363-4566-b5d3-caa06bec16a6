<execution>
  <constraint>
    ## 客观技术限制
    - **质量工具限制**：现有质量检测工具的能力边界和检测精度
    - **计算资源约束**：质量分析的计算复杂度和时间成本
    - **数据可用性**：历史质量数据的完整性和可比性
    - **标准兼容性**：不同质量标准和规范的兼容性要求
    - **实时性要求**：质量验证的时效性和反馈速度要求
  </constraint>

  <rule>
    ## 强制性验证规则
    - **质量门禁**：所有质量指标必须满足预设阈值才能通过
    - **零容忍项**：安全漏洞、数据泄露风险、关键功能失效零容忍
    - **回归检测**：任何性能、功能、安全的回归都必须被检测和报告
    - **标准合规**：必须符合行业标准和公司质量规范
    - **可追溯性**：所有质量验证结果必须可追溯到具体的代码变更
    - **完整性检查**：质量验证必须覆盖所有关键质量维度
  </rule>

  <guideline>
    ## 验证指导原则
    - **多维度验证**：从功能、性能、安全、可用性等多个维度进行质量验证
    - **分层验证**：代码级→模块级→系统级→业务级的分层质量验证
    - **持续验证**：集成到CI/CD流程中的持续质量验证
    - **智能分析**：基于历史数据和机器学习的智能质量分析
    - **风险导向**：重点关注高风险区域和关键业务路径
    - **用户视角**：从最终用户体验角度验证质量
  </guideline>

  <process>
    ## 质量验证执行流程

    ### Phase 1: 质量基线建立 (初始化阶段)
    ```bash
    # 1.1 建立代码质量基线
    sonarqube-scanner \
      -Dsonar.projectKey=astrbot-saas \
      -Dsonar.sources=app/ \
      -Dsonar.python.coverage.reportPaths=coverage.xml
    
    # 1.2 建立性能基线
    python tests/performance/establish_baseline.py \
      --output=baseline/performance_baseline.json
    
    # 1.3 建立安全基线
    bandit -r app/ -f json -o baseline/security_baseline.json
    
    # 1.4 建立依赖安全基线
    safety check --json --output=baseline/dependency_baseline.json
    ```

    ### Phase 2: 实时质量检测 (每次提交)
    ```bash
    # 2.1 代码质量检测
    python scripts/run_quality_checks.py --mode=commit
    
    # 2.2 增量安全扫描
    python scripts/incremental_security_scan.py \
      --base=main --current=HEAD
    
    # 2.3 依赖变更检测
    python scripts/dependency_change_detector.py \
      --requirements=requirements.txt
    
    # 2.4 质量趋势分析
    python scripts/quality_trend_analyzer.py --window=7days
    ```

    ### Phase 3: 深度质量验证 (集成阶段)
    ```bash
    # 3.1 全面代码质量分析
    python scripts/comprehensive_code_analysis.py \
      --include-complexity --include-duplication --include-maintainability
    
    # 3.2 性能回归检测
    python scripts/performance_regression_detector.py \
      --baseline=baseline/performance_baseline.json \
      --threshold=5%
    
    # 3.3 安全漏洞扫描
    python scripts/comprehensive_security_scan.py \
      --include-static --include-dynamic --include-dependencies
    
    # 3.4 API契约验证
    python scripts/api_contract_validator.py \
      --spec=docs/api_contracts/saas_platform_api.yaml
    ```

    ### Phase 4: 业务质量验证 (发布前)
    ```bash
    # 4.1 用户体验质量检测
    python tests/e2e/user_experience_validator.py \
      --scenarios=critical_user_journeys.json
    
    # 4.2 数据完整性验证
    python scripts/data_integrity_validator.py \
      --check-constraints --check-relationships --check-business-rules
    
    # 4.3 多租户隔离验证
    python tests/security/tenant_isolation_validator.py \
      --test-all-boundaries
    
    # 4.4 负载承载能力验证
    locust -f tests/performance/load_capacity_test.py \
      --headless -u 100 -r 20 -t 300s
    ```

    ### Phase 5: 质量报告生成 (验证完成)
    ```bash
    # 5.1 综合质量报告
    python scripts/quality_report_generator.py \
      --format=html,json,pdf \
      --include-trends --include-recommendations
    
    # 5.2 质量评分计算
    python scripts/quality_score_calculator.py \
      --weights=config/quality_weights.yaml
    
    # 5.3 风险评估报告
    python scripts/risk_assessment_generator.py \
      --include-technical-debt --include-security-risks
    
    # 5.4 质量改进建议
    python scripts/quality_improvement_advisor.py \
      --priority=high --actionable-only
    ```

    ## 专项质量验证流程

    ### 安全质量验证
    ```bash
    # 1. 静态安全分析
    bandit -r app/ -ll -i
    semgrep --config=auto app/
    
    # 2. 依赖安全检查
    safety check --json
    pip-audit --format=json
    
    # 3. 容器安全扫描
    trivy image astrbot-saas:latest --format json
    
    # 4. 配置安全检查
    python scripts/config_security_validator.py \
      --check-secrets --check-permissions --check-encryption
    
    # 5. 运行时安全验证
    python tests/security/runtime_security_test.py
    ```

    ### 性能质量验证
    ```bash
    # 1. 响应时间验证
    python tests/performance/response_time_validator.py \
      --endpoints=critical_endpoints.json --threshold=500ms
    
    # 2. 吞吐量验证
    python tests/performance/throughput_validator.py \
      --target-rps=100 --duration=60s
    
    # 3. 资源消耗验证
    python tests/performance/resource_usage_validator.py \
      --memory-limit=512MB --cpu-limit=80%
    
    # 4. 并发性能验证
    python tests/performance/concurrency_validator.py \
      --max-users=50 --ramp-up=10s
    
    # 5. 数据库性能验证
    python tests/performance/database_performance_validator.py \
      --slow-query-threshold=100ms
    ```

    ### 可用性质量验证
    ```bash
    # 1. 服务可用性验证
    python tests/availability/service_availability_test.py \
      --uptime-requirement=99.9%
    
    # 2. 故障恢复验证
    python tests/availability/disaster_recovery_test.py \
      --scenarios=config/disaster_scenarios.yaml
    
    # 3. 负载均衡验证
    python tests/availability/load_balancing_test.py \
      --verify-distribution --verify-failover
    
    # 4. 监控告警验证
    python tests/availability/monitoring_alert_test.py \
      --test-all-alerts --verify-notification
    ```

    ### 数据质量验证
    ```bash
    # 1. 数据一致性验证
    python scripts/data_consistency_validator.py \
      --check-referential-integrity --check-business-constraints
    
    # 2. 数据准确性验证
    python scripts/data_accuracy_validator.py \
      --sample-size=1000 --confidence-level=95%
    
    # 3. 数据完整性验证
    python scripts/data_completeness_validator.py \
      --check-required-fields --check-data-ranges
    
    # 4. 数据隐私合规验证
    python scripts/data_privacy_validator.py \
      --check-gdpr --check-data-anonymization
    ```
  </process>

  <criteria>
    ## 质量验证评价标准

    ### 验证覆盖度
    - ✅ 功能质量验证覆盖率 = 100%（所有功能模块）
    - ✅ 性能质量验证覆盖率 ≥ 90%（关键性能指标）
    - ✅ 安全质量验证覆盖率 = 100%（所有安全维度）
    - ✅ 数据质量验证覆盖率 ≥ 95%（核心数据实体）

    ### 验证准确性
    - ✅ 误报率 ≤ 5%（质量问题检测）
    - ✅ 漏报率 ≤ 2%（关键质量缺陷）
    - ✅ 趋势预测准确率 ≥ 85%（质量趋势分析）
    - ✅ 风险评估准确率 ≥ 90%（风险等级判断）

    ### 验证效率
    - ✅ 实时检测响应时间 ≤ 2分钟
    - ✅ 深度验证完成时间 ≤ 20分钟
    - ✅ 报告生成时间 ≤ 5分钟
    - ✅ 问题定位时间 ≤ 10分钟

    ### 质量标准合规
    - ✅ 代码质量评分 ≥ A级（SonarQube标准）
    - ✅ 安全漏洞等级 ≤ Medium（无High/Critical漏洞）
    - ✅ 性能指标达标率 = 100%（所有SLA指标）
    - ✅ 可用性指标 ≥ 99.9%（服务可用性）

    ### 持续改进
    - ✅ 质量评分月度提升 ≥ 2%
    - ✅ 缺陷密度月度下降 ≥ 5%
    - ✅ 验证工具优化频率 ≥ 1次/月
    - ✅ 质量流程改进频率 ≥ 1次/季度
  </criteria>
</execution> 