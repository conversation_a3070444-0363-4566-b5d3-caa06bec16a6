"""
自动回复服务单元测试
"""
import os
import uuid
from unittest.mock import AsyncMock, MagicMock, patch, Mock

import pytest

from app.services.auto_reply_service import AutoReplyService
from app.services.llm.base_provider import LLMMessage, LLMResponse


@pytest.fixture
def mock_db_session():
    return AsyncMock()

@pytest.fixture
def auto_reply_service(mock_db_session):
    """创建一个带有 mock 依赖的 AutoReplyService 实例"""
    with patch("app.services.auto_reply_service.ContextManager"), \
         patch("app.services.auto_reply_service.SessionService"), \
         patch("app.services.auto_reply_service.MessageService") as MockMessageService:
        MockMessageService.return_value = AsyncMock()
        service = AutoReplyService(mock_db_session)
        return service

class TestAutoReplyService:
    """测试 AutoReplyService"""

    SESSION_ID = uuid.uuid4()
    TENANT_ID = uuid.uuid4()
    USER_MESSAGE = "Hello, I need help."

    @pytest.mark.asyncio
    async def test_generate_reply_in_test_env(self, auto_reply_service):
        """测试在 pytest 环境下 generate_reply 返回模拟回复"""
        # Arrange
        os.environ["PYTEST_CURRENT_TEST"] = "1"  # 模拟测试环境

        # Act
        reply = await auto_reply_service.generate_reply(
            self.SESSION_ID, self.TENANT_ID, self.USER_MESSAGE
        )

        # Assert
        assert "感谢您的留言" in reply
        assert "我是AI客服助手" in reply
        
        # 清理环境变量
        del os.environ["PYTEST_CURRENT_TEST"]

    @pytest.mark.asyncio
    async def test_generate_reply_production_flow(self, auto_reply_service):
        """测试生产环境下的 generate_reply 完整流程"""
        # Arrange
        # 确保不在测试环境
        if "PYTEST_CURRENT_TEST" in os.environ:
            del os.environ["PYTEST_CURRENT_TEST"]

        mock_provider = AsyncMock()
        mock_provider.provider_name = "mock_provider"
        mock_provider.config.model = "mock_model"
        
        llm_response = LLMResponse(
            content="This is a generated response.",
            usage={"total_tokens": 20},
            finish_reason="stop"
        )
        mock_provider.generate_response.return_value = llm_response

        with patch.object(auto_reply_service, "_get_llm_provider", return_value=mock_provider), \
             patch.object(auto_reply_service, "_build_conversation_context", return_value=[]), \
             patch.object(auto_reply_service, "_content_safety_check", side_effect=lambda c, t: c):
            
            # Act
            reply = await auto_reply_service.generate_reply(
                self.SESSION_ID, self.TENANT_ID, self.USER_MESSAGE
            )

            # Assert
            assert reply == "This is a generated response."
            auto_reply_service._get_llm_provider.assert_called_once()
            auto_reply_service._build_conversation_context.assert_awaited_once()
            mock_provider.generate_response.assert_awaited_once()
            auto_reply_service.message_service.store_message.assert_awaited()
            # 应该调用两次: 一次用户消息，一次AI回复
            assert auto_reply_service.message_service.store_message.await_count == 2
    
    @pytest.mark.asyncio
    async def test_generate_reply_empty_message_error(self, auto_reply_service):
        """测试当用户消息为空时 generate_reply 抛出 ValueError"""
        with pytest.raises(ValueError) as exc_info:
            await auto_reply_service.generate_reply(
                self.SESSION_ID, self.TENANT_ID, "   "
            )
        assert "cannot be empty" in str(exc_info.value)

    def test_get_llm_provider(self, auto_reply_service):
        """测试 _get_llm_provider 工厂方法"""
        # Arrange
        with patch("app.services.auto_reply_service.OpenAIProvider") as mock_openai, \
             patch("app.services.auto_reply_service.DifyProvider") as mock_dify, \
             patch.object(auto_reply_service, "_get_default_llm_config") as mock_config, \
             patch("app.services.auto_reply_service.LLMConfig") as mock_llm_config:
            
            # Mock配置，包含provider和api_key，但分离LLM模型配置
            mock_config.return_value = {
                "provider": "openai",
                "api_key": "test_api_key",
                "model": "gpt-3.5-turbo",
                "temperature": 0.7,
                "max_tokens": 1500,
                "top_p": 1.0,
                "frequency_penalty": 0.0,
                "presence_penalty": 0.0,
            }
            
            # Mock LLMConfig构造
            mock_llm_config.return_value = Mock()
            
            # Act: 获取 OpenAI 提供商
            provider1 = auto_reply_service._get_llm_provider(
                self.TENANT_ID, {"provider_name": "openai"}
            )
            
            # Act: 获取 Dify 提供商
            mock_config.return_value = {
                "provider": "dify",
                "api_key": "test_dify_key",
                "model": "gpt-3.5-turbo",
                "temperature": 0.7,
                "base_url": "https://test.dify.ai"
            }
            provider2 = auto_reply_service._get_llm_provider(
                self.TENANT_ID, {"provider_name": "dify"}
            )
            
            # Act: 再次获取，应从缓存中读取
            mock_config.return_value = {
                "provider": "openai", 
                "api_key": "test_api_key",
                "model": "gpt-3.5-turbo",
                "temperature": 0.7
            }
            provider3 = auto_reply_service._get_llm_provider(
                self.TENANT_ID, {"provider_name": "openai"}
            )

            # Assert
            mock_openai.assert_called()
            mock_dify.assert_called()
            # 验证LLMConfig被正确调用
            assert mock_llm_config.call_count >= 2
            assert provider1 is provider3 # 验证缓存机制
            assert provider1 != provider2
