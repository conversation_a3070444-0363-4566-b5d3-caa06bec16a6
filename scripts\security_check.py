#!/usr/bin/env python3
"""
AstrBot SaaS Platform 安全配置检查工具

检查系统安全配置，发现潜在的安全风险。
"""

import os
import sys
import warnings
from pathlib import Path
from typing import List, Dict, Any

# 添加项目路径
project_root = Path(__file__).parent.parent / "saas-platform"
sys.path.insert(0, str(project_root))

try:
    from app.core.config import settings
    from app.core.security import validate_api_key_format
except ImportError as e:
    print(f"❌ 无法导入应用模块: {e}")
    sys.exit(1)


class SecurityChecker:
    """安全配置检查器"""
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        
    def add_issue(self, category: str, description: str, severity: str = "HIGH"):
        """添加安全问题"""
        self.issues.append({
            "category": category,
            "description": description,
            "severity": severity
        })
        
    def add_warning(self, category: str, description: str):
        """添加安全警告"""
        self.warnings.append({
            "category": category,
            "description": description
        })
    
    def check_secret_key_security(self):
        """检查SECRET_KEY安全性"""
        print("🔐 检查SECRET_KEY安全性...")
        
        secret_key = settings.SECRET_KEY
        
        # 检查是否使用默认值
        default_keys = [
            "your-secret-key-here-change-in-production",
            "dev-key-must-change-in-production",
            "your-super-secret-key-change-in-production",
            "dev-secret-key"
        ]
        
        if secret_key in default_keys:
            if settings.is_production:
                self.add_issue(
                    "密钥安全",
                    "生产环境使用默认SECRET_KEY，存在严重安全风险",
                    "CRITICAL"
                )
            else:
                self.add_warning(
                    "密钥安全",
                    "开发环境使用默认SECRET_KEY"
                )
        
        # 检查密钥长度
        if len(secret_key) < 32:
            self.add_issue(
                "密钥安全",
                f"SECRET_KEY长度过短 ({len(secret_key)}字符)，推荐至少32字符",
                "HIGH"
            )
        elif len(secret_key) < 64:
            self.add_warning(
                "密钥安全",
                f"SECRET_KEY长度 ({len(secret_key)}字符) 可以更长以提高安全性"
            )
        
        # 检查密钥复杂度
        if secret_key.isalnum():
            self.add_warning(
                "密钥安全",
                "SECRET_KEY建议包含特殊字符以提高复杂度"
            )
    
    def check_password_policies(self):
        """检查密码策略"""
        print("🔑 检查密码策略...")
        
        # 检查超级用户密码
        if hasattr(settings, 'FIRST_SUPERUSER_PASSWORD'):
            password = settings.FIRST_SUPERUSER_PASSWORD
            
            if password == "admin123":
                if settings.is_production:
                    self.add_issue(
                        "密码安全",
                        "生产环境使用默认超级用户密码",
                        "CRITICAL"
                    )
                else:
                    self.add_warning(
                        "密码安全", 
                        "开发环境使用默认超级用户密码"
                    )
            
            if len(password) < 12:
                self.add_issue(
                    "密码安全",
                    f"超级用户密码长度过短 ({len(password)}字符)，推荐至少12字符",
                    "HIGH"
                )
    
    def check_database_security(self):
        """检查数据库安全配置"""
        print("🗄️ 检查数据库安全配置...")
        
        database_url = getattr(settings, 'DATABASE_URL', '')
        
        # 检查是否使用默认数据库密码
        if 'password' in database_url.lower() or 'astrbot123' in database_url:
            if settings.is_production:
                self.add_issue(
                    "数据库安全",
                    "生产环境可能使用默认数据库密码",
                    "HIGH"
                )
            else:
                self.add_warning(
                    "数据库安全",
                    "开发环境使用默认数据库密码"
                )
        
        # 检查数据库连接加密
        if 'sslmode' not in database_url and settings.is_production:
            self.add_warning(
                "数据库安全",
                "生产环境建议启用数据库SSL连接"
            )
    
    def check_redis_security(self):
        """检查Redis安全配置"""
        print("🔴 检查Redis安全配置...")
        
        redis_url = getattr(settings, 'REDIS_URL', '')
        
        # 检查Redis密码
        if 'redis123' in redis_url or ':@' in redis_url:
            if settings.is_production:
                self.add_issue(
                    "Redis安全",
                    "生产环境Redis可能未设置密码或使用默认密码",
                    "HIGH"
                )
            else:
                self.add_warning(
                    "Redis安全",
                    "开发环境Redis使用默认配置"
                )
    
    def check_cors_configuration(self):
        """检查CORS配置"""
        print("🌐 检查CORS配置...")
        
        cors_origins = settings.BACKEND_CORS_ORIGINS
        
        # 检查是否允许所有域名
        if '*' in cors_origins:
            self.add_issue(
                "CORS安全",
                "CORS配置允许所有域名，存在安全风险",
                "HIGH"
            )
        
        # 检查localhost配置
        localhost_patterns = ['localhost', '127.0.0.1', '0.0.0.0']
        localhost_origins = [
            origin for origin in cors_origins 
            if any(pattern in origin for pattern in localhost_patterns)
        ]
        
        if localhost_origins and settings.is_production:
            self.add_warning(
                "CORS安全",
                f"生产环境CORS包含localhost域名: {localhost_origins}"
            )
    
    def check_debug_settings(self):
        """检查调试设置"""
        print("🐛 检查调试设置...")
        
        if settings.DEBUG and settings.is_production:
            self.add_issue(
                "调试安全",
                "生产环境启用了DEBUG模式，可能泄露敏感信息",
                "HIGH"
            )
    
    def check_environment_files(self):
        """检查环境文件安全"""
        print("📁 检查环境文件...")
        
        env_files = ['.env', '.env.local', '.env.production']
        
        for env_file in env_files:
            env_path = project_root / env_file
            if env_path.exists():
                # 检查文件权限
                import stat
                file_stat = env_path.stat()
                file_mode = stat.filemode(file_stat.st_mode)
                
                if file_stat.st_mode & stat.S_IROTH:
                    self.add_issue(
                        "文件安全",
                        f"环境文件 {env_file} 对其他用户可读",
                        "MEDIUM"
                    )
                
                if file_stat.st_mode & stat.S_IWOTH:
                    self.add_issue(
                        "文件安全", 
                        f"环境文件 {env_file} 对其他用户可写",
                        "HIGH"
                    )
    
    def check_token_expiration(self):
        """检查Token过期时间配置"""
        print("⏰ 检查Token过期时间...")
        
        access_expire = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        refresh_expire = settings.REFRESH_TOKEN_EXPIRE_MINUTES
        
        # 检查访问Token过期时间
        if access_expire > 1440:  # 超过24小时
            self.add_warning(
                "Token安全",
                f"访问Token过期时间过长 ({access_expire}分钟)，建议不超过24小时"
            )
        
        if access_expire < 5:  # 少于5分钟
            self.add_warning(
                "Token安全",
                f"访问Token过期时间过短 ({access_expire}分钟)，可能影响用户体验"
            )
        
        # 检查刷新Token过期时间
        if refresh_expire > 43200:  # 超过30天
            self.add_warning(
                "Token安全",
                f"刷新Token过期时间过长 ({refresh_expire//1440}天)"
            )
    
    def run_all_checks(self):
        """运行所有安全检查"""
        print("🛡️ 开始安全配置检查...")
        print(f"📍 环境: {settings.ENVIRONMENT}")
        print(f"📍 调试模式: {settings.DEBUG}")
        print()
        
        # 执行各项检查
        self.check_secret_key_security()
        self.check_password_policies()
        self.check_database_security()
        self.check_redis_security()
        self.check_cors_configuration()
        self.check_debug_settings()
        self.check_environment_files()
        self.check_token_expiration()
        
        # 输出结果
        self.print_results()
        
        # 返回是否存在严重问题
        critical_issues = [i for i in self.issues if i['severity'] == 'CRITICAL']
        return len(critical_issues) == 0
    
    def print_results(self):
        """打印检查结果"""
        print("\n" + "="*60)
        print("🛡️  安全检查结果")
        print("="*60)
        
        if not self.issues and not self.warnings:
            print("✅ 没有发现安全问题")
            return
        
        # 打印安全问题
        if self.issues:
            print("\n🚨 发现安全问题:")
            for issue in self.issues:
                severity_icon = {
                    'CRITICAL': '🔴',
                    'HIGH': '🟠', 
                    'MEDIUM': '🟡'
                }.get(issue['severity'], '⚪')
                
                print(f"  {severity_icon} [{issue['severity']}] {issue['category']}: {issue['description']}")
        
        # 打印警告
        if self.warnings:
            print("\n⚠️  安全警告:")
            for warning in self.warnings:
                print(f"  🟡 {warning['category']}: {warning['description']}")
        
        # 统计信息
        critical_count = len([i for i in self.issues if i['severity'] == 'CRITICAL'])
        high_count = len([i for i in self.issues if i['severity'] == 'HIGH'])
        medium_count = len([i for i in self.issues if i['severity'] == 'MEDIUM'])
        
        print(f"\n📊 统计:")
        print(f"  严重问题: {critical_count}")
        print(f"  高风险问题: {high_count}")
        print(f"  中风险问题: {medium_count}")
        print(f"  警告: {len(self.warnings)}")
        
        if critical_count > 0:
            print(f"\n❌ 发现 {critical_count} 个严重安全问题，强烈建议立即修复！")
        elif high_count > 0:
            print(f"\n⚠️  发现 {high_count} 个高风险安全问题，建议尽快修复")
        else:
            print(f"\n✅ 未发现严重安全问题")


def main():
    """主函数"""
    try:
        checker = SecurityChecker()
        success = checker.run_all_checks()
        
        if not success:
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 安全检查失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 