# AstrBot SaaS Platform 安全认证模块技术文档

## 📋 模块概述

`app/core/security.py` 是 AstrBot SaaS Platform 的安全认证核心模块，实现了基于 JWT 的身份认证、密码加密存储、API 密钥管理等关键安全功能。该模块为整个应用提供安全保障的基础设施。

### 核心价值
- **身份认证：** 基于 JWT 的无状态认证机制
- **密码安全：** 使用 bcrypt 的密码加密存储
- **API 密钥：** 安全的 API 密钥生成和验证
- **Token 管理：** 访问和刷新 Token 的完整生命周期管理

## 🏗️ 架构设计

### 整体架构图
```mermaid
graph TD
    A[Security Module] --> B[JWT Authentication]
    A --> C[Password Management]
    A --> D[API Key Management]
    A --> E[Token Blacklist]
    
    B --> B1[Access Token]
    B --> B2[Refresh Token]
    B --> B3[Token Verification]
    
    C --> C1[Password Hashing]
    C --> C2[Password Verification]
    C --> C3[Bcrypt Context]
    
    D --> D1[Key Generation]
    D --> D2[Format Validation]
    D --> D3[Environment Prefix]
    
    E --> E1[Token Logout]
    E --> E2[Blacklist Check]
    E --> E3[Cleanup Job]
```

### 认证流程设计
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as API层
    participant Security as 安全模块
    participant DB as 数据库

    Client->>API: 登录请求(email, password)
    API->>DB: 查询用户信息
    DB->>API: 返回用户数据
    API->>Security: verify_password()
    Security->>API: 密码验证结果
    API->>Security: create_auth_tokens()
    Security->>API: 返回Token对
    API->>Client: 登录成功+Tokens
    
    Note over Client,DB: 后续请求使用Token
    
    Client->>API: API请求+Bearer Token
    API->>Security: verify_token()
    Security->>API: Token验证结果
    API->>Client: 业务响应
```

### 安全层次图
```mermaid
graph LR
    A[请求入口] --> B[Token提取]
    B --> C[Token验证]
    C --> D[黑名单检查]
    D --> E[权限验证]
    E --> F[业务处理]
    
    G[密码输入] --> H[密码哈希]
    H --> I[数据库存储]
    
    J[API密钥请求] --> K[密钥生成]
    K --> L[格式验证]
    L --> M[安全存储]
```

## 🔧 核心功能模块

### 1. 密码管理系统

#### 密码加密实现
```python
# 基于 Passlib + Bcrypt 的密码管理
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    """
    使用 bcrypt 加密密码
    
    特性：
    - 自适应哈希算法
    - 自动盐值生成
    - 抗彩虹表攻击
    """
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证密码正确性
    
    安全特性：
    - 常量时间比较
    - 防时序攻击
    """
    return pwd_context.verify(plain_password, hashed_password)
```

#### 密码安全特性
- **Bcrypt算法：** 自适应哈希，计算成本可调节
- **自动盐值：** 每个密码使用唯一随机盐值
- **向后兼容：** 支持密码哈希算法升级
- **时间安全：** 防止时序攻击的密码比较

#### 密码策略建议
```python
# 密码强度验证（建议实现）
import re

def validate_password_strength(password: str) -> tuple[bool, list[str]]:
    """
    密码强度验证
    
    返回：
    - bool: 是否符合要求
    - List[str]: 不符合的规则列表
    """
    errors = []
    
    if len(password) < 8:
        errors.append("密码至少需要8个字符")
    
    if not re.search(r"[A-Z]", password):
        errors.append("密码需要包含大写字母")
    
    if not re.search(r"[a-z]", password):
        errors.append("密码需要包含小写字母")
    
    if not re.search(r"\d", password):
        errors.append("密码需要包含数字")
    
    if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", password):
        errors.append("密码需要包含特殊字符")
    
    return len(errors) == 0, errors
```

### 2. JWT 认证系统

#### Access Token 创建
```python
def create_access_token(
    subject: Union[str, UUID],
    expires_delta: Optional[timedelta] = None,
    extra_data: Optional[dict[str, Any]] = None,
) -> str:
    """
    创建访问Token
    
    Token结构：
    - exp: 过期时间
    - iat: 签发时间
    - sub: 主题（用户ID）
    - type: Token类型
    - tenant_id: 租户ID（多租户支持）
    - role: 用户角色
    """
    expire = datetime.now(UTC) + (
        expires_delta or timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    )
    
    payload = {
        "exp": expire,
        "iat": datetime.now(UTC),
        "sub": str(subject),
        "type": "access",
    }
    
    if extra_data:
        payload.update(extra_data)
    
    return jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
```

#### Refresh Token 管理
```python
def create_refresh_token(subject: Union[str, UUID]) -> str:
    """
    创建刷新Token
    
    特性：
    - 更长的有效期
    - 仅包含必要信息
    - 用于获取新的Access Token
    """
    expire = datetime.now(UTC) + timedelta(
        minutes=settings.REFRESH_TOKEN_EXPIRE_MINUTES
    )
    
    payload = {
        "exp": expire,
        "iat": datetime.now(UTC),
        "sub": str(subject),
        "type": "refresh",
    }
    
    return jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
```

#### Token 验证机制
```python
def verify_token(token: str, token_type: str = "access") -> dict[str, Any]:
    """
    多层Token验证
    
    验证步骤：
    1. JWT 格式验证
    2. 签名验证
    3. 过期时间检查
    4. Token类型验证
    5. 黑名单检查
    """
    try:
        # 解码JWT
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        
        # 验证Token类型
        if payload.get("type") != token_type:
            raise InvalidTokenError(f"Invalid token type: expected {token_type}")
        
        # 验证过期时间
        exp = payload.get("exp")
        if not exp or datetime.fromtimestamp(exp, tz=UTC) < datetime.now(UTC):
            raise TokenExpiredError("Token has expired")
        
        return payload
        
    except jwt.ExpiredSignatureError:
        raise TokenExpiredError("Token has expired")
    except jwt.InvalidTokenError as e:
        raise InvalidTokenError(f"Invalid token: {str(e)}")
```

#### 完整认证Token对
```python
def create_auth_tokens(
    user_id: Union[str, UUID], 
    tenant_id: UUID, 
    user_email: str, 
    user_role: str = "user"
) -> dict[str, str]:
    """
    创建完整的认证Token对
    
    返回格式：
    {
        "access_token": "eyJ...",
        "refresh_token": "eyJ...",
        "token_type": "bearer"
    }
    """
    extra_data = {
        "tenant_id": str(tenant_id),
        "email": user_email,
        "role": user_role
    }
    
    access_token = create_access_token(subject=user_id, extra_data=extra_data)
    refresh_token = create_refresh_token(subject=user_id)
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
    }
```

### 3. API 密钥管理

#### 密钥生成算法
```python
def generate_api_key(prefix: str = "ak") -> str:
    """
    生成安全的API密钥
    
    格式：{prefix}_{env}_{random_part}
    示例：ak_live_ABcd1234EFgh5678IJkl9012MNop3456
    
    特性：
    - 环境区分（live/test）
    - 高熵随机部分
    - 可识别前缀
    """
    import secrets
    import string
    
    alphabet = string.ascii_letters + string.digits
    random_part = "".join(secrets.choice(alphabet) for _ in range(32))
    env = "live" if settings.ENVIRONMENT == "production" else "test"
    
    return f"{prefix}_{env}_{random_part}"
```

#### 密钥格式验证
```python
def validate_api_key_format(api_key: str) -> bool:
    """
    验证API密钥格式
    
    验证规则：
    - 三段式结构：prefix_env_random
    - 前缀：ak/sk
    - 环境：live/test
    - 随机部分：32字符
    """
    if not api_key:
        return False
    
    parts = api_key.split("_")
    if len(parts) != 3:
        return False
    
    prefix, env, random_part = parts
    
    return (
        prefix in ["ak", "sk"] and
        env in ["live", "test"] and
        len(random_part) == 32
    )
```

### 4. Token 黑名单系统

#### 黑名单实现
```python
class TokenBlacklist:
    """
    Token黑名单管理
    
    功能：
    - Token注销管理
    - 黑名单检查
    - 过期清理
    """
    
    def __init__(self):
        # 生产环境建议使用Redis
        self._blacklisted_tokens = set()
    
    def add_token(self, token: str) -> None:
        """将Token加入黑名单"""
        self._blacklisted_tokens.add(token)
    
    def is_blacklisted(self, token: str) -> bool:
        """检查Token是否在黑名单中"""
        return token in self._blacklisted_tokens
    
    def remove_expired_tokens(self) -> None:
        """清理过期Token（定期任务）"""
        # TODO: 实现基于Token过期时间的清理
        pass
```

#### 登出机制
```python
def logout_token(token: str) -> None:
    """
    安全登出Token
    
    操作：
    1. 将Token加入黑名单
    2. 使Token立即失效
    3. 防止Token重放攻击
    """
    token_blacklist.add_token(token)

def is_token_valid(token: str) -> bool:
    """
    综合Token有效性检查
    
    检查项：
    1. 黑名单状态
    2. JWT格式和签名
    3. 过期时间
    """
    # 黑名单检查
    if token_blacklist.is_blacklisted(token):
        return False
    
    try:
        verify_token(token)
        return True
    except (TokenExpiredError, InvalidTokenError):
        return False
```

### 5. 安全异常体系

#### 异常层次结构
```python
class SecurityError(Exception):
    """安全相关异常基类"""
    pass

class TokenExpiredError(SecurityError):
    """Token过期异常"""
    pass

class InvalidTokenError(SecurityError):
    """无效Token异常"""
    pass
```

#### 统一错误处理
```python
# 在API层统一处理安全异常
from fastapi import HTTPException

def handle_security_error(error: SecurityError) -> HTTPException:
    """安全异常统一处理"""
    if isinstance(error, TokenExpiredError):
        return HTTPException(
            status_code=401,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    elif isinstance(error, InvalidTokenError):
        return HTTPException(
            status_code=401,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    else:
        return HTTPException(
            status_code=500,
            detail="Internal security error"
        )
```

## 📊 安全配置参数

### JWT 配置
```python
# 关键配置参数
class SecuritySettings:
    SECRET_KEY: str                          # JWT签名密钥
    ALGORITHM: str = "HS256"                 # JWT算法
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30    # 访问Token过期时间
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 1440 # 刷新Token过期时间（24小时）
```

### 环境变量配置
```bash
# 生产环境必需配置
SECRET_KEY=your-very-secure-secret-key-at-least-32-characters
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_MINUTES=1440

# 可选配置
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL=true
```

### 安全配置检查
```python
def validate_security_config():
    """验证安全配置的完整性"""
    errors = []
    
    if not settings.SECRET_KEY:
        errors.append("SECRET_KEY 未配置")
    elif len(settings.SECRET_KEY) < 32:
        errors.append("SECRET_KEY 长度至少需要32个字符")
    
    if settings.ACCESS_TOKEN_EXPIRE_MINUTES <= 0:
        errors.append("ACCESS_TOKEN_EXPIRE_MINUTES 必须大于0")
    
    if settings.REFRESH_TOKEN_EXPIRE_MINUTES <= settings.ACCESS_TOKEN_EXPIRE_MINUTES:
        errors.append("REFRESH_TOKEN_EXPIRE_MINUTES 应该大于 ACCESS_TOKEN_EXPIRE_MINUTES")
    
    if errors:
        raise SecurityError(f"安全配置错误: {', '.join(errors)}")
```

## 🔧 集成使用指南

### 1. FastAPI 认证依赖

#### 基础认证依赖
```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

security = HTTPBearer()

async def get_current_user_token(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """提取并验证Token"""
    token = credentials.credentials
    
    if not is_token_valid(token):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return token

async def get_current_user_id(
    token: str = Depends(get_current_user_token)
) -> UUID:
    """从Token获取用户ID"""
    try:
        user_id_str = get_subject_from_token(token)
        return UUID(user_id_str)
    except (InvalidTokenError, ValueError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user ID in token"
        )
```

#### 多租户认证依赖
```python
async def get_current_tenant_id(
    token: str = Depends(get_current_user_token)
) -> UUID:
    """从Token获取租户ID"""
    tenant_id = get_tenant_id_from_token(token)
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Missing tenant information in token"
        )
    return tenant_id

async def get_current_user_role(
    token: str = Depends(get_current_user_token)
) -> str:
    """从Token获取用户角色"""
    try:
        payload = verify_token(token)
        role = payload.get("role", "user")
        return role
    except (TokenExpiredError, InvalidTokenError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token for role extraction"
        )
```

### 2. 用户注册和登录

#### 用户注册流程
```python
@router.post("/register")
async def register_user(
    user_data: UserRegister,
    db: AsyncSession = Depends(get_db)
):
    """用户注册"""
    # 1. 验证密码强度
    is_strong, errors = validate_password_strength(user_data.password)
    if not is_strong:
        raise HTTPException(400, f"密码不符合要求: {', '.join(errors)}")
    
    # 2. 检查邮箱唯一性
    existing_user = await get_user_by_email(db, user_data.email)
    if existing_user:
        raise HTTPException(409, "邮箱已被注册")
    
    # 3. 创建用户
    hashed_password = get_password_hash(user_data.password)
    user = User(
        email=user_data.email,
        username=user_data.username,
        hashed_password=hashed_password,
        tenant_id=user_data.tenant_id
    )
    
    db.add(user)
    await db.commit()
    await db.refresh(user)
    
    # 4. 生成Token
    tokens = create_auth_tokens(
        user_id=user.id,
        tenant_id=user.tenant_id,
        user_email=user.email,
        user_role="user"
    )
    
    return {
        "user": UserRead.from_orm(user),
        **tokens
    }
```

#### 用户登录流程
```python
@router.post("/login")
async def login_user(
    credentials: UserLogin,
    db: AsyncSession = Depends(get_db)
):
    """用户登录"""
    # 1. 查找用户
    user = await get_user_by_email(db, credentials.email)
    if not user:
        raise HTTPException(401, "邮箱或密码错误")
    
    # 2. 验证密码
    if not verify_password(credentials.password, user.hashed_password):
        raise HTTPException(401, "邮箱或密码错误")
    
    # 3. 检查用户状态
    if not user.is_active:
        raise HTTPException(403, "账户已被禁用")
    
    # 4. 更新最后登录时间
    user.last_login_at = datetime.now(UTC)
    await db.commit()
    
    # 5. 生成Token
    tokens = create_auth_tokens(
        user_id=user.id,
        tenant_id=user.tenant_id,
        user_email=user.email,
        user_role=user.role
    )
    
    return {
        "user": UserRead.from_orm(user),
        **tokens
    }
```

### 3. Token 刷新和登出

#### Token 刷新
```python
@router.post("/refresh")
async def refresh_token(
    refresh_request: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
):
    """刷新访问Token"""
    try:
        # 1. 验证刷新Token
        payload = verify_token(refresh_request.refresh_token, token_type="refresh")
        user_id = UUID(payload["sub"])
        
        # 2. 检查用户状态
        user = await get_user_by_id(db, user_id)
        if not user or not user.is_active:
            raise HTTPException(401, "用户不存在或已被禁用")
        
        # 3. 生成新的访问Token
        new_access_token = create_access_token(
            subject=user_id,
            extra_data={
                "tenant_id": str(user.tenant_id),
                "email": user.email,
                "role": user.role
            }
        )
        
        return {
            "access_token": new_access_token,
            "token_type": "bearer"
        }
        
    except (TokenExpiredError, InvalidTokenError) as e:
        raise HTTPException(401, str(e))
```

#### 用户登出
```python
@router.post("/logout")
async def logout_user(
    token: str = Depends(get_current_user_token)
):
    """用户登出"""
    # 将Token加入黑名单
    logout_token(token)
    
    return {"message": "Successfully logged out"}

@router.post("/logout-all")
async def logout_all_sessions(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """登出所有会话"""
    # 这里可以实现将用户所有Token加入黑名单
    # 或者通过更新用户的token_version来使所有Token失效
    
    # 更新用户token版本（如果实现了版本控制）
    current_user.token_version += 1
    await db.commit()
    
    return {"message": "All sessions logged out"}
```

## 🔍 安全最佳实践

### 1. Token 安全策略

#### Token 生命周期管理
```python
class TokenManager:
    """Token生命周期管理器"""
    
    @staticmethod
    def get_token_ttl(token: str) -> int:
        """获取Token剩余生存时间（秒）"""
        try:
            payload = verify_token(token)
            exp = payload.get("exp")
            if exp:
                remaining = exp - datetime.now(UTC).timestamp()
                return max(0, int(remaining))
            return 0
        except (TokenExpiredError, InvalidTokenError):
            return 0
    
    @staticmethod
    def should_refresh_token(token: str, threshold_minutes: int = 5) -> bool:
        """判断是否应该刷新Token"""
        ttl = TokenManager.get_token_ttl(token)
        return ttl < threshold_minutes * 60
    
    @staticmethod
    async def cleanup_expired_blacklist():
        """清理过期的黑名单Token"""
        # 实现过期Token清理逻辑
        pass
```

#### 安全Header实现
```python
from fastapi import Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全Header中间件"""
    
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # 添加安全Header
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = (
            "max-age=31536000; includeSubDomains"
        )
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; script-src 'self'"
        )
        
        return response
```

### 2. 密码安全增强

#### 密码历史管理
```python
class PasswordHistory:
    """密码历史管理"""
    
    @staticmethod
    async def add_password_history(
        db: AsyncSession, 
        user_id: UUID, 
        password_hash: str
    ):
        """记录密码历史"""
        history = PasswordHistoryRecord(
            user_id=user_id,
            password_hash=password_hash,
            created_at=datetime.now(UTC)
        )
        db.add(history)
        
        # 只保留最近10个密码历史
        old_records = await db.execute(
            select(PasswordHistoryRecord)
            .where(PasswordHistoryRecord.user_id == user_id)
            .order_by(PasswordHistoryRecord.created_at.desc())
            .offset(10)
        )
        
        for record in old_records.scalars():
            await db.delete(record)
    
    @staticmethod
    async def check_password_reuse(
        db: AsyncSession, 
        user_id: UUID, 
        new_password: str
    ) -> bool:
        """检查密码是否重复使用"""
        history_records = await db.execute(
            select(PasswordHistoryRecord)
            .where(PasswordHistoryRecord.user_id == user_id)
            .order_by(PasswordHistoryRecord.created_at.desc())
            .limit(10)
        )
        
        for record in history_records.scalars():
            if verify_password(new_password, record.password_hash):
                return True
        
        return False
```

#### 密码强度动态调整
```python
class AdaptivePasswordPolicy:
    """自适应密码策略"""
    
    @staticmethod
    def get_password_requirements(user_role: str, failed_attempts: int) -> dict:
        """根据用户角色和失败次数动态调整密码要求"""
        base_requirements = {
            "min_length": 8,
            "require_uppercase": True,
            "require_lowercase": True,
            "require_numbers": True,
            "require_special": True,
        }
        
        # 管理员用户更严格的要求
        if user_role in ["admin", "super_admin"]:
            base_requirements.update({
                "min_length": 12,
                "require_mixed_case": True,
                "min_special_chars": 2,
            })
        
        # 登录失败次数多的用户需要更强密码
        if failed_attempts > 3:
            base_requirements["min_length"] += 2
        
        return base_requirements
```

### 3. API 密钥安全

#### 密钥权限管理
```python
class APIKeyPermissions:
    """API密钥权限管理"""
    
    @staticmethod
    def create_scoped_api_key(
        tenant_id: UUID,
        scopes: List[str],
        expires_at: Optional[datetime] = None
    ) -> str:
        """创建带权限范围的API密钥"""
        # 生成密钥
        api_key = generate_api_key(prefix="sk")  # sk = scoped key
        
        # 存储密钥权限信息
        key_info = {
            "tenant_id": str(tenant_id),
            "scopes": scopes,
            "expires_at": expires_at.isoformat() if expires_at else None,
            "created_at": datetime.now(UTC).isoformat()
        }
        
        # TODO: 存储到数据库或Redis
        return api_key
    
    @staticmethod
    def validate_api_key_scope(api_key: str, required_scope: str) -> bool:
        """验证API密钥是否有指定权限"""
        # TODO: 从存储中获取密钥权限信息
        # 简化实现
        return True
```

#### 密钥使用监控
```python
class APIKeyMonitor:
    """API密钥使用监控"""
    
    @staticmethod
    async def log_api_key_usage(
        api_key: str,
        endpoint: str,
        ip_address: str,
        user_agent: str
    ):
        """记录API密钥使用情况"""
        usage_log = {
            "api_key_hash": hashlib.sha256(api_key.encode()).hexdigest()[:16],
            "endpoint": endpoint,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "timestamp": datetime.now(UTC).isoformat()
        }
        
        # TODO: 存储到日志系统
        logger.info("API密钥使用", **usage_log)
    
    @staticmethod
    async def detect_suspicious_activity(api_key: str) -> bool:
        """检测可疑的API密钥使用"""
        # TODO: 实现异常检测逻辑
        # - 异常IP地址
        # - 高频请求
        # - 异常时间段
        return False
```

## 🐛 故障排除

### 常见安全问题

#### 1. Token 验证失败
**问题表现：**
```
HTTP 401 Unauthorized: "Invalid authentication credentials"
```

**诊断步骤：**
```python
def diagnose_token_issue(token: str):
    """Token问题诊断工具"""
    print(f"Token长度: {len(token)}")
    print(f"Token前缀: {token[:20]}...")
    
    # 检查格式
    if not token.startswith("eyJ"):
        print("❌ Token格式错误：不是有效的JWT")
        return
    
    try:
        # 不验证签名，仅解码查看内容
        header = jwt.get_unverified_header(token)
        payload = jwt.decode(token, options={"verify_signature": False})
        
        print(f"✅ Header: {header}")
        print(f"✅ Payload: {payload}")
        
        # 检查过期时间
        exp = payload.get("exp")
        if exp:
            exp_time = datetime.fromtimestamp(exp, tz=UTC)
            now = datetime.now(UTC)
            print(f"过期时间: {exp_time}")
            print(f"当前时间: {now}")
            
            if exp_time < now:
                print("❌ Token已过期")
            else:
                print(f"✅ Token还有 {exp_time - now} 有效")
        
        # 检查黑名单
        if token_blacklist.is_blacklisted(token):
            print("❌ Token在黑名单中")
        else:
            print("✅ Token不在黑名单中")
            
    except Exception as e:
        print(f"❌ Token解码失败: {str(e)}")
```

#### 2. 密码验证问题
**问题表现：**
```
登录失败：密码正确但验证不通过
```

**解决方案：**
```python
def debug_password_verification(plain_password: str, stored_hash: str):
    """密码验证调试"""
    print(f"明文密码长度: {len(plain_password)}")
    print(f"存储哈希长度: {len(stored_hash)}")
    print(f"哈希前缀: {stored_hash[:10]}...")
    
    # 检查哈希格式
    if not stored_hash.startswith("$2b$"):
        print("❌ 密码哈希格式错误，不是bcrypt格式")
        return False
    
    # 测试验证
    try:
        result = pwd_context.verify(plain_password, stored_hash)
        print(f"验证结果: {'✅ 成功' if result else '❌ 失败'}")
        return result
    except Exception as e:
        print(f"❌ 验证过程出错: {str(e)}")
        return False

# 重新生成密码哈希
def regenerate_password_hash(password: str) -> str:
    """重新生成密码哈希"""
    new_hash = get_password_hash(password)
    print(f"新密码哈希: {new_hash}")
    return new_hash
```

#### 3. JWT 配置问题
**问题表现：**
```
JWT签名验证失败
```

**检查清单：**
```python
def validate_jwt_config():
    """JWT配置验证"""
    issues = []
    
    # 检查SECRET_KEY
    if not settings.SECRET_KEY:
        issues.append("SECRET_KEY未设置")
    elif len(settings.SECRET_KEY) < 32:
        issues.append(f"SECRET_KEY太短：{len(settings.SECRET_KEY)}字符，建议至少32字符")
    
    # 检查算法
    if settings.ALGORITHM not in ["HS256", "HS512", "RS256"]:
        issues.append(f"不支持的JWT算法：{settings.ALGORITHM}")
    
    # 检查过期时间
    if settings.ACCESS_TOKEN_EXPIRE_MINUTES <= 0:
        issues.append("ACCESS_TOKEN_EXPIRE_MINUTES必须大于0")
    
    if settings.REFRESH_TOKEN_EXPIRE_MINUTES <= settings.ACCESS_TOKEN_EXPIRE_MINUTES:
        issues.append("REFRESH_TOKEN_EXPIRE_MINUTES应该大于ACCESS_TOKEN_EXPIRE_MINUTES")
    
    if issues:
        print("❌ JWT配置问题：")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("✅ JWT配置正确")
    
    return len(issues) == 0
```

### 性能优化

#### Token 缓存策略
```python
import redis.asyncio as redis
from typing import Optional

class TokenCache:
    """Token缓存管理"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.cache_ttl = 300  # 5分钟缓存
    
    async def get_cached_token_info(self, token: str) -> Optional[dict]:
        """获取缓存的Token信息"""
        cache_key = f"token:{hashlib.sha256(token.encode()).hexdigest()[:16]}"
        cached_data = await self.redis.get(cache_key)
        
        if cached_data:
            return json.loads(cached_data)
        return None
    
    async def cache_token_info(self, token: str, token_info: dict):
        """缓存Token信息"""
        cache_key = f"token:{hashlib.sha256(token.encode()).hexdigest()[:16]}"
        await self.redis.setex(
            cache_key,
            self.cache_ttl,
            json.dumps(token_info, default=str)
        )
    
    async def invalidate_token_cache(self, token: str):
        """使Token缓存失效"""
        cache_key = f"token:{hashlib.sha256(token.encode()).hexdigest()[:16]}"
        await self.redis.delete(cache_key)
```

## 🔮 安全增强规划

### 1. 高级认证功能

#### 多因素认证（MFA）
```python
class MFAManager:
    """多因素认证管理"""
    
    @staticmethod
    def generate_totp_secret() -> str:
        """生成TOTP密钥"""
        import pyotp
        return pyotp.random_base32()
    
    @staticmethod
    def verify_totp_code(secret: str, code: str) -> bool:
        """验证TOTP代码"""
        import pyotp
        totp = pyotp.TOTP(secret)
        return totp.verify(code, valid_window=1)
    
    @staticmethod
    async def send_sms_code(phone_number: str) -> str:
        """发送短信验证码"""
        import random
        code = f"{random.randint(100000, 999999)}"
        # TODO: 集成短信服务
        return code
```

#### OAuth2 集成
```python
class OAuth2Provider:
    """OAuth2第三方登录"""
    
    @staticmethod
    async def google_oauth_callback(code: str) -> dict:
        """Google OAuth回调处理"""
        # TODO: 实现Google OAuth流程
        pass
    
    @staticmethod
    async def github_oauth_callback(code: str) -> dict:
        """GitHub OAuth回调处理"""
        # TODO: 实现GitHub OAuth流程
        pass
```

### 2. 安全监控和告警

#### 安全事件检测
```python
class SecurityMonitor:
    """安全监控系统"""
    
    @staticmethod
    async def detect_brute_force(
        ip_address: str, 
        failed_attempts: int, 
        time_window: int = 300
    ) -> bool:
        """检测暴力破解攻击"""
        # TODO: 实现基于IP的失败次数统计
        return failed_attempts > 5
    
    @staticmethod
    async def detect_suspicious_login(
        user_id: UUID,
        ip_address: str,
        user_agent: str
    ) -> bool:
        """检测可疑登录"""
        # TODO: 实现异常登录检测
        # - 异常地理位置
        # - 异常设备
        # - 异常时间
        return False
    
    @staticmethod
    async def send_security_alert(event_type: str, details: dict):
        """发送安全告警"""
        # TODO: 集成告警系统
        logger.warning(f"安全事件：{event_type}", **details)
```

---

**文档维护：** 本文档应与安全策略变更同步更新，确保安全机制的有效性和时效性。
**更新日期：** 2024年12月17日
**版本：** v1.0.0
**维护者：** 技术文档专家 