#!/usr/bin/env python3
"""
AstrBot SaaS Platform - 本地部署测试脚本
============================================

全自动化的本地部署测试流程，包括：
1. 环境检查
2. 服务启动
3. 健康检查
4. 功能测试
5. 性能验证
6. 清理资源

作者: DevOps Executor  
日期: 2025/01/20
"""

import subprocess
import time
import requests
import json
import sys
from typing import Dict, List, Tuple
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('deploy_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DeploymentTester:
    """部署测试器"""
    
    def __init__(self):
        self.project_root = Path(".")
        self.services = {
            'postgres': 'http://localhost:5432',
            'redis': 'http://localhost:6379', 
            'app': 'http://localhost:8000',
            'nginx': 'http://localhost:80'
        }
        self.test_results = {
            'environment_check': False,
            'services_start': False,
            'health_check': False,
            'api_tests': False,
            'performance_test': False
        }
    
    def run_full_test(self):
        """运行完整的部署测试"""
        logger.info("🚀 开始AstrBot SaaS Platform本地部署测试")
        logger.info("=" * 60)
        
        try:
            # 1. 环境检查
            self._check_environment()
            
            # 2. 启动服务
            self._start_services()
            
            # 3. 等待服务就绪
            self._wait_for_services()
            
            # 4. 健康检查
            self._health_check()
            
            # 5. API功能测试
            self._api_tests()
            
            # 6. 性能测试
            self._performance_test()
            
            # 7. 生成报告
            self._generate_report()
            
        except Exception as e:
            logger.error(f"❌ 部署测试失败: {e}")
            return False
        
        return True
    
    def _check_environment(self):
        """检查部署环境"""
        logger.info("📋 1. 环境检查")
        
        # 检查Docker
        try:
            result = subprocess.run(
                ['docker', '--version'], 
                capture_output=True, text=True, check=True
            )
            logger.info(f"  ✅ Docker: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise Exception("Docker未安装或不可用")
        
        # 检查Docker Compose
        try:
            result = subprocess.run(
                ['docker-compose', '--version'], 
                capture_output=True, text=True, check=True
            )
            logger.info(f"  ✅ Docker Compose: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise Exception("Docker Compose未安装或不可用")
        
        # 检查必要文件
        required_files = [
            'docker-compose.yml',
            'Dockerfile', 
            '.env',
            'pyproject.toml'
        ]
        
        for file in required_files:
            if not (self.project_root / file).exists():
                raise Exception(f"缺少必要文件: {file}")
            logger.info(f"  ✅ 文件存在: {file}")
        
        # 检查端口占用
        self._check_ports()
        
        self.test_results['environment_check'] = True
        logger.info("  🎯 环境检查完成\n")
    
    def _check_ports(self):
        """检查端口占用情况"""
        import socket
        
        ports_to_check = [5432, 6379, 8000, 80]
        occupied_ports = []
        
        for port in ports_to_check:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                occupied_ports.append(port)
        
        if occupied_ports:
            logger.warning(f"  ⚠️ 端口已占用: {occupied_ports}")
            logger.info("    这些服务可能已在运行，将尝试停止...")
            self._stop_services()
        else:
            logger.info("  ✅ 所有端口可用")
    
    def _start_services(self):
        """启动Docker服务"""
        logger.info("🐳 2. 启动Docker服务")
        
        # 清理旧容器
        logger.info("  🧹 清理旧容器...")
        subprocess.run(
            ['docker-compose', 'down', '-v'], 
            capture_output=True
        )
        
        # 构建镜像
        logger.info("  🔨 构建应用镜像...")
        result = subprocess.run(
            ['docker-compose', 'build', '--no-cache'],
            capture_output=True, text=True
        )
        
        if result.returncode != 0:
            logger.error(f"镜像构建失败:\n{result.stderr}")
            raise Exception("Docker镜像构建失败")
        
        # 启动服务
        logger.info("  🚀 启动所有服务...")
        result = subprocess.run(
            ['docker-compose', 'up', '-d'],
            capture_output=True, text=True
        )
        
        if result.returncode != 0:
            logger.error(f"服务启动失败:\n{result.stderr}")
            raise Exception("Docker服务启动失败")
        
        self.test_results['services_start'] = True
        logger.info("  🎯 服务启动完成\n")
    
    def _wait_for_services(self):
        """等待服务就绪"""
        logger.info("⏳ 3. 等待服务就绪")
        
        max_wait = 120  # 最大等待2分钟
        wait_interval = 5
        
        for i in range(0, max_wait, wait_interval):
            logger.info(f"  ⏱️ 等待中... ({i}/{max_wait}秒)")
            
            # 检查容器状态
            result = subprocess.run(
                ['docker-compose', 'ps', '--format', 'json'],
                capture_output=True, text=True
            )
            
            if result.returncode == 0:
                containers = []
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        containers.append(json.loads(line))
                
                healthy_count = sum(1 for c in containers if c.get('Health') == 'healthy' or c.get('State') == 'running')
                total_count = len(containers)
                
                logger.info(f"    📊 容器状态: {healthy_count}/{total_count} 健康")
                
                if healthy_count >= 3:  # postgres, redis, app最少要运行
                    logger.info("  ✅ 主要服务已就绪")
                    break
            
            time.sleep(wait_interval)
        else:
            raise Exception("服务启动超时")
        
        logger.info("  🎯 服务就绪检查完成\n")
    
    def _health_check(self):
        """健康检查"""
        logger.info("🏥 4. 服务健康检查")
        
        # 检查应用健康接口
        health_endpoints = [
            ('应用健康检查', 'http://localhost:8000/api/v1/health'),
            ('数据库连接', 'http://localhost:8000/api/v1/health/db'),
            ('API文档', 'http://localhost:8000/docs'),
        ]
        
        for name, url in health_endpoints:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    logger.info(f"  ✅ {name}: OK")
                else:
                    logger.warning(f"  ⚠️ {name}: HTTP {response.status_code}")
            except requests.RequestException as e:
                logger.error(f"  ❌ {name}: {e}")
                raise Exception(f"健康检查失败: {name}")
        
        self.test_results['health_check'] = True
        logger.info("  🎯 健康检查完成\n")
    
    def _api_tests(self):
        """API功能测试"""
        logger.info("🔧 5. API功能测试")
        
        base_url = "http://localhost:8000/api/v1"
        
        # 测试用例
        test_cases = [
            ("GET", "/health", "健康检查接口"),
            ("GET", "/", "根路径"),
            ("GET", "/tenants", "租户列表", True),  # 需要认证
        ]
        
        # 首先获取认证token
        token = self._get_auth_token()
        headers = {"Authorization": f"Bearer {token}"} if token else {}
        
        for method, endpoint, description, *requires_auth in test_cases:
            url = f"{base_url}{endpoint}"
            test_headers = headers if requires_auth else {}
            
            try:
                if method == "GET":
                    response = requests.get(url, headers=test_headers, timeout=10)
                elif method == "POST":
                    response = requests.post(url, headers=test_headers, timeout=10)
                
                if response.status_code in [200, 201, 401]:  # 401可能是正常的认证失败
                    logger.info(f"  ✅ {description}: HTTP {response.status_code}")
                else:
                    logger.warning(f"  ⚠️ {description}: HTTP {response.status_code}")
                    
            except requests.RequestException as e:
                logger.error(f"  ❌ {description}: {e}")
        
        self.test_results['api_tests'] = True
        logger.info("  🎯 API测试完成\n")
    
    def _get_auth_token(self):
        """获取认证token"""
        try:
            # 尝试创建测试用户并登录
            auth_url = "http://localhost:8000/api/v1/auth/login"
            login_data = {
                "username": "<EMAIL>",
                "password": "admin123"
            }
            
            response = requests.post(auth_url, json=login_data, timeout=10)
            if response.status_code == 200:
                return response.json().get("access_token")
        except:
            pass
        
        return None
    
    def _performance_test(self):
        """性能测试"""
        logger.info("⚡ 6. 基础性能测试")
        
        url = "http://localhost:8000/api/v1/health"
        
        # 并发测试
        import concurrent.futures
        import time
        
        def single_request():
            try:
                start_time = time.time()
                response = requests.get(url, timeout=5)
                end_time = time.time()
                return response.status_code == 200, end_time - start_time
            except:
                return False, 0
        
        logger.info("  📊 执行10个并发请求...")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(single_request) for _ in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        success_count = sum(1 for success, _ in results if success)
        response_times = [rt for success, rt in results if success]
        
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            
            logger.info(f"  ✅ 成功率: {success_count}/10 ({success_count*10}%)")
            logger.info(f"  ⏱️ 平均响应时间: {avg_response_time:.3f}秒")
            logger.info(f"  🔥 最大响应时间: {max_response_time:.3f}秒")
            
            if avg_response_time < 1.0 and success_count >= 8:
                self.test_results['performance_test'] = True
            else:
                logger.warning("  ⚠️ 性能指标未达标")
        
        logger.info("  🎯 性能测试完成\n")
    
    def _generate_report(self):
        """生成测试报告"""
        logger.info("📊 7. 生成测试报告")
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        success_rate = (passed_tests / total_tests) * 100
        
        report = []
        report.append("=" * 60)
        report.append("🎯 AstrBot SaaS Platform 部署测试报告")
        report.append("=" * 60)
        report.append(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"🏆 总体成功率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        report.append("")
        
        report.append("📋 详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            report.append(f"  {test_name}: {status}")
        
        report.append("")
        
        if success_rate >= 80:
            report.append("🎉 部署测试成功！系统已准备就绪。")
            report.append("")
            report.append("🔗 访问地址:")
            report.append("  - 应用主页: http://localhost:8000")
            report.append("  - API文档: http://localhost:8000/docs")
            report.append("  - 监控面板: http://localhost:8080")
        else:
            report.append("⚠️ 部署测试存在问题，请检查日志并修复。")
        
        report.append("")
        report.append("🛠️ 管理命令:")
        report.append("  - 查看日志: docker-compose logs -f")
        report.append("  - 停止服务: docker-compose down")
        report.append("  - 重启服务: docker-compose restart")
        report.append("=" * 60)
        
        report_text = "\n".join(report)
        
        # 输出到控制台
        print("\n" + report_text)
        
        # 保存到文件
        with open("deployment_test_report.txt", "w", encoding="utf-8") as f:
            f.write(report_text)
        
        logger.info("  📄 报告已保存到: deployment_test_report.txt")
    
    def _stop_services(self):
        """停止服务"""
        logger.info("🛑 停止所有服务...")
        subprocess.run(['docker-compose', 'down'], capture_output=True)
        time.sleep(2)

def main():
    """主函数"""
    tester = DeploymentTester()
    
    try:
        success = tester.run_full_test()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\n⏹️ 用户中断测试")
        tester._stop_services()
        sys.exit(130)
    except Exception as e:
        logger.error(f"❌ 测试执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 