"""
Mock LLM提供商实现

用于测试环境，模拟LLM功能而不需要真实的API调用
"""

from collections.abc import AsyncIterator
from typing import Any, Optional

from app.utils.logging import get_logger

from .base_provider import BaseLLMProvider, LLMConfig, LLMMessage, LLMResponse

# 配置日志
logger = get_logger(__name__)


class MockLLMProvider(BaseLLMProvider):
    """Mock LLM提供商实现 - 用于测试环境"""

    def __init__(self, **kwargs) -> None:
        """
        初始化Mock提供商

        Args:
            **kwargs: 其他参数
        """
        # 创建默认配置
        config = LLMConfig(model="mock-gpt", temperature=0.3, max_tokens=1000)
        super().__init__(config, "mock-api-key", **kwargs)

    @property
    def provider_name(self) -> str:
        """提供商名称"""
        return "Mock"

    @property
    def supported_models(self) -> list[str]:
        """支持的模型列表"""
        return ["mock-gpt", "mock-gpt-turbo"]

    async def generate_response(
        self, messages: list[LLMMessage], config_override: Optional[LLMConfig] = None
    ) -> LLMResponse:
        """
        生成模拟响应

        Args:
            messages: 对话消息列表
            config_override: 配置覆盖（Mock忽略）

        Returns:
            LLMResponse: 模拟的LLM响应
        """
        # 根据最后一条消息内容生成合适的模拟响应
        last_message = messages[-1] if messages else None
        user_content = last_message.content if last_message else ""

        # 简单的模拟逻辑
        if "总结" in user_content or "分析" in user_content:
            mock_content = """会话总结：
1. 用户咨询背景：用户通过在线客服平台咨询相关问题
2. 主要问题和需求：寻求产品信息和技术支持
3. 客服处理过程：客服专业回应，提供详细信息
4. 解决方案：提供了满意的答案和后续支持方案
5. 最终结果：问题得到有效解决，用户表示满意
6. 用户满意度预估：较高（4/5分）"""
        elif "行为" in user_content:
            mock_content = """用户行为分析：
- 用户响应积极，询问具体且有针对性
- 交互风格：直接且友好
- 情感倾向：积极正面
- 紧急程度：中等"""
        elif "质量" in user_content:
            mock_content = """服务质量评估：
- 响应及时性：优秀 (5/5)
- 回复完整性：良好 (4/5)
- 专业语调：优秀 (5/5)
- 问题解决：良好 (4/5)
- 综合评分：4.5/5"""
        else:
            mock_content = (
                "这是一个模拟的LLM响应，用于测试环境。内容已根据输入进行了适当的调整。"
            )

        # 模拟使用token数
        prompt_tokens = sum(len(msg.content.split()) for msg in messages)
        completion_tokens = len(mock_content.split())

        return LLMResponse(
            content=mock_content,
            finish_reason="stop",
            usage={
                "prompt_tokens": prompt_tokens,
                "completion_tokens": completion_tokens,
                "total_tokens": prompt_tokens + completion_tokens,
            },
            metadata={
                "mock": True,
                "test_mode": True,
                "messages_count": len(messages),
                "model": self.config.model,
                "provider": self.provider_name,
            },
        )

    async def generate_stream_response(
        self, messages: list[LLMMessage], config_override: Optional[LLMConfig] = None
    ) -> AsyncIterator[str]:
        """
        生成流式模拟响应

        Args:
            messages: 对话消息列表
            config_override: 配置覆盖（Mock忽略）

        Yields:
            str: 流式响应片段
        """
        # 获取完整响应
        response = await self.generate_response(messages, config_override)
        content = response.content

        # 模拟流式返回，每次返回几个字符
        chunk_size = 20
        for i in range(0, len(content), chunk_size):
            chunk = content[i : i + chunk_size]
            yield chunk
            # 模拟网络延迟
            import asyncio

            await asyncio.sleep(0.1)

    def validate_config(self) -> bool:
        """
        验证配置状态

        Returns:
            bool: 配置是否有效（Mock总是返回True）
        """
        return True

    def get_token_count(self, text: str) -> int:
        """
        计算文本的token数

        Args:
            text: 输入文本

        Returns:
            int: 估算的token数量
        """
        # 简单估算：每个字符约0.8个token
        return int(len(text) * 0.8)

    def validate_connection(self) -> bool:
        """
        验证连接状态

        Returns:
            bool: 连接是否有效（Mock总是返回True）
        """
        return True

    def get_available_models(self) -> list[str]:
        """
        获取可用模型列表

        Returns:
            List[str]: 可用模型列表
        """
        return self.supported_models

    def get_cost_info(self) -> dict[str, Any]:
        """
        获取成本信息

        Returns:
            Dict[str, Any]: 成本信息（Mock返回零成本）
        """
        return {
            "input_cost_per_token": 0.0,
            "output_cost_per_token": 0.0,
            "currency": "USD",
            "billing_unit": "token",
            "mock_provider": True,
        }
