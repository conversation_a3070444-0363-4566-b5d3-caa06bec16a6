# AstrBot SaaS Platform 代码冗余清理计划

## 立即清理项目
1. **删除重复测试文件**：
   - tests/unit/test_tenant_service_improved.py
   - tests/unit/test_tenant_service_simplified.py
   - tests/unit/test_session_summary_service_refactored.py

2. **统一异常类**：
   - 创建 app/core/exceptions.py
   - 移除重复的异常定义

3. **优化Service工厂函数**：
   - 创建统一的依赖注入模式

## 中期优化项目
- Logger注入优化
- 异常处理装饰器
- 通用CRUD基类

## 预期收益
- 减少代码量15-20%
- 提升维护效率
- 降低Bug风险