# 📖 技术文档：自动回复服务 (AutoReplyService)

## 🎯 1. 模块概述

**功能**：提供基于LLM的智能自动回复功能。

**核心职责**：
- **生成回复**：根据用户消息和会话上下文，使用LLM生成智能回复。
- **流式回复**：支持流式响应，用于实时对话场景。
- **上下文管理**：调用`ContextManager`构建对话上下文。
- **内容安全**：对生成内容进行安全检查。

## 🚀 2. 快速使用

### 2.1 依赖注入

在API端点中注入`AutoReplyService`：

```python
from app.services.auto_reply_service import AutoReplyService

@router.post("/auto-reply")
async def generate_auto_reply(
    request: AutoReplyRequest,
    db: AsyncSession = Depends(get_db),
):
    auto_reply_service = AutoReplyService(db)
    # ...
```

### 2.2 核心方法

- **`generate_reply(session_id, tenant_id, user_message)`** - 生成单次回复
- **`generate_stream_reply(session_id, tenant_id, user_message)`** - 生成流式回复

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    subgraph "核心依赖"
        A[FastAPI] --> B(AutoReplyService)
        C[SQLAlchemy] --> B
        D[Pydantic] --> B
    end

    subgraph "模块交互"
        B --> E(ContextManager)
        B --> F(SessionService)
        B --> G(MessageService)
        B --> H(BaseLLMProvider)
    end

    style B fill:#c8e6c9
```

### 3.2 数据流

**生成回复流程**：
1. **API接收**：接收自动回复请求。
2. **服务处理**：
   - 获取LLM提供商实例。
   - 构建对话上下文。
   - 调用LLM生成回复。
   - 进行内容安全检查。
   - 存储用户和AI消息。
3. **响应返回**：返回生成的回复内容。

## 🔧 4. API参考

| 方法 | HTTP动词 | 端点 | 描述 |
|---|---|---|---|
| `generate_auto_reply` | `POST` | `/api/v1/ai/auto-reply` | 生成自动回复 |
| `generate_auto_reply_stream`|`POST`|`/api/v1/ai/auto-reply/stream`|生成流式自动回复|

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_auto_reply_service.py`
- **集成测试**：`tests/integration/test_ai_features_api.py`

## 💡 6. 维护与扩展

- **LLM提供商**：可以方便地在`_get_llm_provider`中添加新的LLM提供商。
- **内容安全**：可以对接第三方内容安全服务来增强`_content_safety_check`。
- **上下文构建**：可以优化`_build_conversation_context`的逻辑，例如引入RAG。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 