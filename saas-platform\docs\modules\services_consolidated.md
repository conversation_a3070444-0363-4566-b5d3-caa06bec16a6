# Services 综合文档

*合并自: services_comprehensive_docs.md, tenant_service_comprehensive_docs.md*

*合并时间: 2025-06-20 16:08:22*

---

## 来源: services_comprehensive_docs.md

# AstrBot SaaS 服务层(Services) 技术文档

## 📋 模块概览

### 🎯 模块定位
`app/services/` 模块是 AstrBot SaaS 平台的核心业务逻辑层。它作为API层和数据模型层之间的桥梁，封装了所有的业务规则、数据处理流程和与数据库的交互。服务层确保了业务逻辑的内聚性和可重用性。

### ✨ 核心功能
- **业务逻辑封装** - 将复杂的业务流程（如用户注册、会话创建、权限检查）封装成独立的服务。
- **数据处理** - 负责将来自API层的数据（Schemas）转换为数据库模型（Models），并将数据库模型转换为响应Schema。
- **事务管理** - 协调跨多个数据模型的数据库操作，确保数据一致性。
- **依赖注入** - 通过依赖注入机制获取数据库会话 (`AsyncSession`)，并被API层注入使用。
- **层级分离** - 彻底将API路由的HTTP相关逻辑与核心业务逻辑解耦。

### 🏗️ 服务层架构

```mermaid
graph TD
    subgraph "API层 (app/api/v1)"
        A[租户路由 /tenants]
        B[会话路由 /sessions]
        C[认证路由 /auth]
    end
    
    subgraph "服务层 (app/services)"
        D(TenantService)
        E(SessionService)
        F(AuthService)
        G(RBACService)
        H[...]
    end
    
    subgraph "数据模型层 (app/models)"
        I[Tenant Model]
        J[Session Model]
        K[User Model]
        L[Role Model]
    end
    
    subgraph "数据库"
        M[(PostgreSQL)]
    end
    
    A -- Depends --> D
    B -- Depends --> E
    C -- Depends --> F
    
    D -- 操作 --> I
    E -- 操作 --> J & K
    F -- 操作 --> I & K
    G -- 操作 --> L & K
    
    I -- ORM --> M
    J -- ORM --> M
    K -- ORM --> M
    L -- ORM --> M

    style D fill:#cde4ff,stroke:#6a8ebf,stroke-width:2px
    style E fill:#cde4ff,stroke:#6a8ebf,stroke-width:2px
    style F fill:#cde4ff,stroke:#6a8ebf,stroke-width:2px
```
**架构说明:**
1.  **API层调用**: API路由通过FastAPI的 `Depends` 关键字声明对特定服务的依赖（如 `Depends(get_tenant_service)`）。
2.  **服务实例化**: 依赖注入函数（如 `get_tenant_service`）负责创建服务类的实例，并将数据库会话 `AsyncSession` 注入到服务中。
3.  **业务处理**: 服务方法接收来自API层的Pydantic `schemas` 作为输入，执行业务逻辑。
4.  **数据交互**: 服务方法内部使用SQLAlchemy ORM模型与数据库进行交互（增、删、改、查）。
5.  **返回结果**: 服务方法通常返回ORM模型实例，这些实例随后在API层被自动序列化为响应 `schemas`。

## 🔧 核心服务分析

### 1. `TenantService` - 租户管理服务

- **职责**: 负责租户的整个生命周期管理。
- **核心方法**:
  - `create_tenant(tenant_data: TenantCreate)`: 处理新租户的创建，包括数据验证、模型创建和初始配置。
  - `get_tenant(tenant_id: UUID)`: 获取租户详情。
  - `update_tenant(tenant_id: UUID, tenant_data: TenantUpdate)`: 处理租户信息的更新。
  - `delete_tenant(tenant_id: UUID)`: 执行租户的软删除。
  - `get_tenant_statistics(tenant_id: UUID)`: 计算并返回租户的各项统计数据。
  - `regenerate_api_key(tenant_id: UUID)`: 为租户重新生成API密钥。
- **设计特点**: 所有方法都以 `tenant_id` 作为关键参数，隐式地支持了多租户操作，但具体的权限检查由API层的依赖项负责。

### 2. `AuthService` - 认证服务

- **职责**: 处理所有与用户认证、注册和Token相关的逻辑。
- **核心方法**:
  - `login(login_data: LoginRequest)`: 验证用户凭据，检查账户状态，并生成`access_token`和`refresh_token`。
  - `register(register_data: RegisterRequest)`: 创建新的租户和关联的管理员用户，处理密码哈希，并返回注册结果。
  - `refresh_token(refresh_data: RefreshTokenRequest)`: 验证`refresh_token`的有效性，并签发一个新的`access_token`。
  - `change_password(...)`: 处理用户修改密码的逻辑。
  - `logout(token: str)`: 将Token加入黑名单以实现登出。
- **安全实践**: 该服务与 `app/core/security.py` 模块紧密协作，调用其函数来处理密码哈希和JWT操作。

### 3. `SessionService` - 会话管理服务

- **职责**: 管理用户与客服的会话生命周期。
- **核心方法**:
  - `create_or_get_session(...)`: 一个幂等操作，如果用户存在活跃会话，则返回现有会话；否则，创建一个新会话。这是保证用户体验连续性的关键。
  - `update_session_status(...)`: 处理会话状态的流转（如 `WAITING` -> `ACTIVE` -> `CLOSED`），并包含状态转换规则的验证逻辑 `_is_valid_status_transition`。
  - `list_tenant_sessions(...)`: 提供带筛选条件（状态、客服、平台）和分页的会话列表查询。
  - `update_last_message_time(...)`: 在收到新消息时调用，用于更新会话的活跃时间，是实现会话超时机制的基础。
- **设计特点**: 包含了对会话状态机（State Machine）的业务逻辑实现。

### 4. `RBACService` - 角色权限服务

- **职责**: 提供基于角色的访问控制（RBAC）的完整实现。
- **核心方法**:
  - **权限管理**: `create_permission`, `list_permissions`。
  - **角色管理**: `create_role`, `get_role`, `update_role_permissions`。
  - **用户角色分配**: `assign_role_to_user`, `remove_role_from_user`。
  - **权限检查**: `check_user_permission(user_id, resource, action)`，这是整个权限系统的核心检查点，它会递归检查用户所拥有的所有角色的权限。
- **设计特点**:
  - **租户隔离**: 所有角色和权限都与 `tenant_id` 关联。
  - **预加载优化**: 在查询用户和角色时，使用 `selectinload` 预加载关联的权限，避免了N+1查询问题，提高了性能。

## 🚀 开发和使用指南

### 1. 服务设计原则

- **单一职责**: 每个服务类应该聚焦于一个特定的业务领域（如租户、会话）。
- **无状态**: 服务实例本身应该是无状态的。所有需要的状态（如数据库会话）都应该通过构造函数注入。这使得服务易于测试和管理。
- **依赖于抽象 (Schema)**: 服务方法的输入和输出应尽可能使用抽象的Pydantic Schema，而不是直接暴露数据库模型，以减少层间耦合。
- **异常处理**: 在服务层可以抛出自定义的业务异常（如 `AuthenticationError`），API层负责捕获这些异常并转换为合适的HTTP响应。

### 2. 添加新服务

1.  在 `app/services/` 目录下创建一个新文件，例如 `product_service.py`。
2.  定义服务类，并在构造函数中接收 `AsyncSession`。
    ```python
    from sqlalchemy.ext.asyncio import AsyncSession

    class ProductService:
        def __init__(self, db: AsyncSession):
            self.db = db
        
        async def create_product(self, product_data: ProductCreate, user: User):
            # ... 业务逻辑
            pass
    ```
3.  在服务文件中，创建一个依赖注入函数。
    ```python
    from app.core.database import get_db

    def get_product_service(db: AsyncSession = Depends(get_db)) -> ProductService:
        return ProductService(db)
    ```
4.  在API路由中使用 `Depends` 来注入服务。
    ```python
    from app.services.product_service import get_product_service

    @router.post("/")
    async def create_product(
        product_service: ProductService = Depends(get_product_service),
        # ... other dependencies
    ):
        # ...
        await product_service.create_product(...)
        # ...
    ```

### 3. 事务管理

服务层是处理数据库事务的理想位置。当一个操作需要修改多个数据模型时，应确保它们在同一个事务中完成。

```python
class OrderService:
    def __init__(self, db: AsyncSession):
        self.db = db
        
    async def place_order(self, order_data):
        try:
            # 步骤1: 创建订单记录
            new_order = OrderModel(**order_data.model_dump())
            self.db.add(new_order)
            
            # 步骤2: 更新库存
            product = await self.db.get(ProductModel, order_data.product_id)
            if product.stock < order_data.quantity:
                raise ValueError("Insufficient stock")
            product.stock -= order_data.quantity
            
            # 步骤3: 记录交易日志
            log = TransactionLog(order_id=new_order.id, ...)
            self.db.add(log)
            
            # 一次性提交所有更改
            await self.db.commit()
            
            return new_order
        except Exception as e:
            # 如果任何步骤失败，回滚整个事务
            await self.db.rollback()
            logger.error("Failed to place order", error=e)
            raise
```

## 🔮 扩展和维护

- **服务解耦**: 对于非常复杂的业务领域，可以考虑将一个大的服务拆分成多个更小的、更专注的服务。
- **引入缓存**: 对于频繁读取且不经常变化的数据（如角色权限），可以在服务层引入缓存逻辑，以减少数据库查询压力。
- **异步任务**: 对于耗时的操作（如生成报表、发送邮件），服务层可以调用后台任务队列（如Celery, ARQ），而不是同步执行，以避免阻塞API响应。

---

## 📝 总结

`app/services/` 模块是AstrBot应用的大脑，它将抽象的业务需求转化为具体的数据操作。通过清晰的职责划分、无状态设计和对事务的精心管理，服务层为整个应用提供了稳定、可靠且可扩展的业务逻辑核心。 

---

## 来源: tenant_service_comprehensive_docs.md

# AstrBot SaaS Platform 租户服务技术文档

## 📋 模块概述

`app/services/tenant_service.py` 是 AstrBot SaaS Platform 的租户管理核心模块，实现多租户架构的基础功能，包括租户生命周期管理、数据隔离、API密钥管理等关键业务逻辑。

### 核心价值
- **多租户隔离：** 确保每个租户的数据完全隔离
- **生命周期管理：** 租户从创建到删除的完整流程
- **安全保障：** API密钥生成、验证和撤销机制
- **运营支持：** 租户统计、状态管理等运营功能

## 🏗️ 架构设计

### 整体架构图
```mermaid
graph TD
    A[API Layer] --> B[TenantService]
    B --> C[Database Layer]
    B --> D[Security Layer]
    B --> E[Logging System]
    
    C --> C1[Tenant Model]
    C --> C2[User Model] 
    C --> C3[Session Model]
    
    D --> D1[API Key Generation]
    D --> D2[Data Isolation]
    D --> D3[Status Management]
    
    E --> E1[Operation Logs]
    E --> E2[Error Tracking]
    E --> E3[Performance Metrics]
```

### 数据流程设计
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as API层
    participant Service as TenantService
    participant DB as 数据库
    participant Security as 安全模块

    Client->>API: 创建租户请求
    API->>Service: create_tenant(tenant_data)
    Service->>DB: 验证邮箱唯一性
    Service->>DB: 验证企业名称唯一性
    Service->>Security: 生成API密钥
    Service->>DB: 保存租户记录
    Service->>API: 返回租户信息
    API->>Client: 租户创建成功
```

### 依赖关系图
```mermaid
graph LR
    A[TenantService] --> B[AsyncSession]
    A --> C[Tenant Model]
    A --> D[TenantSchemas]
    A --> E[User Model]
    A --> F[Logging Utils]
    
    B --> G[Database Engine]
    C --> H[SQLAlchemy ORM]
    D --> I[Pydantic Validation]
    E --> J[Foreign Key Relations]
    F --> K[Structured Logging]
```

## 🔧 核心功能模块

### 1. 租户创建功能

#### 核心实现逻辑
```python
async def create_tenant(self, tenant_data: TenantCreate) -> TenantRead:
    # 1. 唯一性验证
    existing_tenant = await self._get_tenant_by_email(tenant_data.email)
    existing_name = await self._get_tenant_by_name(tenant_data.name)
    
    # 2. API密钥生成
    api_key = Tenant.generate_api_key()
    
    # 3. 租户对象创建
    new_tenant = Tenant(
        name=tenant_data.name,
        email=tenant_data.email,
        plan=tenant_data.plan,
        extra_data=tenant_data.metadata or {},
        api_key=api_key,
    )
    
    # 4. 数据库事务提交
    self.db.add(new_tenant)
    await self.db.commit()
    await self.db.refresh(new_tenant)
```

#### 业务规则
- **邮箱唯一性：** 防止同一邮箱注册多个租户
- **企业名称唯一性：** 确保租户名称的全局唯一性
- **API密钥自动生成：** 每个租户自动分配唯一API密钥
- **元数据支持：** 支持存储额外的租户配置信息

#### 错误处理机制
```python
try:
    # 业务逻辑
    pass
except HTTPException:
    # 业务异常直接抛出
    raise
except Exception as e:
    # 系统异常回滚并记录
    await self.db.rollback()
    logger.error("租户创建失败", error=str(e))
    raise HTTPException(500, "租户创建失败，请稍后重试")
```

### 2. 租户查询功能

#### 单租户查询
```python
async def get_tenant(self, tenant_id: UUID) -> Optional[TenantRead]:
    """获取单个租户信息，支持缓存优化"""
    query = select(Tenant).where(Tenant.id == tenant_id)
    result = await self.db.execute(query)
    tenant = result.scalar_one_or_none()
    
    return TenantRead.model_validate(tenant) if tenant else None
```

#### 租户列表查询
```python
async def list_tenants(
    self,
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    is_active: Optional[bool] = None,
) -> list[TenantRead]:
    """支持分页、搜索、状态过滤的租户列表查询"""
    
    query = select(Tenant)
    
    # 搜索功能：支持名称和邮箱模糊匹配
    if search:
        search_pattern = f"%{search}%"
        query = query.where(
            or_(
                Tenant.name.ilike(search_pattern),
                Tenant.email.ilike(search_pattern),
            )
        )
    
    # 状态过滤
    if is_active is not None:
        if is_active:
            query = query.where(Tenant.status == TenantStatus.ACTIVE)
        else:
            query = query.where(
                Tenant.status.in_([
                    TenantStatus.SUSPENDED, 
                    TenantStatus.DEACTIVATED
                ])
            )
    
    # 分页和排序
    query = query.offset(skip).limit(limit).order_by(Tenant.created_at.desc())
```

### 3. 租户更新功能

#### 部分更新支持
```python
async def update_tenant(self, tenant_id: UUID, tenant_data: TenantUpdate) -> TenantRead:
    """支持部分字段更新，包含数据验证和冲突检查"""
    
    # 获取现有租户
    tenant = await self._get_existing_tenant(tenant_id)
    
    # 邮箱唯一性检查（仅当邮箱变更时）
    if tenant_data.email and tenant_data.email != tenant.email:
        await self._validate_email_uniqueness(tenant_data.email)
    
    # 动态字段更新
    update_data = tenant_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(tenant, field, value)
```

#### 字段级验证
- **邮箱格式验证：** Pydantic自动验证邮箱格式
- **计划类型验证：** 确保plan字段符合预定义枚举
- **元数据结构验证：** 验证extra_data的JSON结构

### 4. 租户删除功能

#### 智能删除策略
```python
async def delete_tenant(self, tenant_id: UUID) -> bool:
    """实现软删除和硬删除的智能选择"""
    
    tenant = await self._get_existing_tenant(tenant_id)
    user_count = await self._count_tenant_users(tenant_id)
    
    if user_count > 0:
        # 软删除：保护用户数据
        tenant.status = TenantStatus.DEACTIVATED
        await self.db.commit()
        logger.info("租户软删除成功（用户数据保护）", 
                   tenant_id=str(tenant_id), user_count=user_count)
    else:
        # 硬删除：完全清理
        await self.db.delete(tenant)
        await self.db.commit()
        logger.info("租户硬删除成功", tenant_id=str(tenant_id))
```

#### 删除安全机制
- **数据保护：** 有用户数据的租户仅软删除
- **完整清理：** 无关联数据的租户执行硬删除
- **审计追踪：** 所有删除操作都有完整日志记录

### 5. API密钥管理

#### 密钥生成算法
```python
async def regenerate_api_key(self, tenant_id: UUID) -> Optional[str]:
    """安全的API密钥重新生成"""
    
    tenant = await self._get_existing_tenant(tenant_id)
    
    # 生成随机字符串
    raw_key = secrets.token_urlsafe(32)
    
    # 创建带前缀的API密钥
    new_api_key = f"astr_{tenant.name}_{raw_key[:16]}"
    
    # 哈希存储
    api_key_hash = hashlib.sha256(new_api_key.encode()).hexdigest()
    tenant.api_key = api_key_hash
    
    await self.db.commit()
    
    # 返回明文密钥（仅此一次）
    return new_api_key
```

#### 安全特性
- **前缀标识：** `astr_` 前缀便于识别
- **租户标识：** 包含租户名称便于追踪
- **哈希存储：** 数据库仅存储哈希值
- **一次性显示：** 明文密钥仅生成时可见

### 6. 租户统计功能

#### 统计指标收集
```python
async def get_tenant_statistics(self, tenant_id: UUID) -> dict[str, Any]:
    """多维度租户统计信息"""
    
    await self.get_tenant_by_id_with_verification(tenant_id)
    
    # 基础统计
    user_count = await self._count_tenant_users(tenant_id)
    
    # 扩展统计（规划中）
    return {
        "tenant_id": str(tenant_id),
        "user_count": user_count,
        "sessions_count": 0,    # 待实现：会话统计
        "messages_count": 0,    # 待实现：消息统计
        "storage_usage": 0,     # 待实现：存储使用量
        "last_activity": None,  # 待实现：最后活跃时间
    }
```

## 📊 数据模型集成

### Tenant模型关系
```python
# 主要字段说明
class Tenant:
    id: UUID                    # 主键，UUID格式
    name: str                   # 企业名称，全局唯一
    email: str                  # 联系邮箱，全局唯一
    plan: str                   # 订阅计划类型
    status: TenantStatus        # 租户状态枚举
    api_key: str               # API密钥哈希值
    extra_data: dict           # 额外配置数据
    created_at: datetime       # 创建时间
    updated_at: datetime       # 更新时间
```

### 关联关系
```mermaid
erDiagram
    Tenant {
        UUID id PK
        string name UK
        string email UK
        string plan
        enum status
        string api_key
        json extra_data
        datetime created_at
        datetime updated_at
    }
    
    User {
        UUID id PK
        UUID tenant_id FK
        string username
        string email
    }
    
    Session {
        UUID id PK
        UUID tenant_id FK
        UUID user_id FK
        string name
    }
    
    Tenant ||--o{ User : "owns"
    Tenant ||--o{ Session : "contains"
    User ||--o{ Session : "creates"
```

## 🔧 API接口设计

### REST API端点

| HTTP方法 | 端点路径 | 功能描述 | 请求体 | 响应体 |
|----------|---------|---------|--------|--------|
| `POST` | `/api/v1/tenants` | 创建租户 | `TenantCreate` | `TenantRead` |
| `GET` | `/api/v1/tenants/{id}` | 获取租户详情 | - | `TenantRead` |
| `PUT` | `/api/v1/tenants/{id}` | 更新租户信息 | `TenantUpdate` | `TenantRead` |
| `DELETE` | `/api/v1/tenants/{id}` | 删除租户 | - | `{"success": true}` |
| `GET` | `/api/v1/tenants` | 租户列表查询 | Query参数 | `List[TenantRead]` |
| `POST` | `/api/v1/tenants/{id}/regenerate-key` | 重生成API密钥 | - | `{"api_key": "..."}` |
| `GET` | `/api/v1/tenants/{id}/statistics` | 租户统计信息 | - | `StatsResponse` |

### 请求/响应Schema

#### TenantCreate Schema
```python
class TenantCreate(BaseModel):
    name: str = Field(..., min_length=2, max_length=100)
    email: EmailStr
    plan: str = Field(default="basic")
    metadata: Optional[Dict[str, Any]] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "name": "Acme Corporation",
                "email": "<EMAIL>",
                "plan": "enterprise",
                "metadata": {"industry": "technology"}
            }
        }
```

#### TenantRead Schema
```python
class TenantRead(BaseModel):
    id: UUID
    name: str
    email: str
    plan: str
    status: TenantStatus
    created_at: datetime
    updated_at: datetime
    
    # 安全字段（不包含API密钥）
    model_config = ConfigDict(from_attributes=True)
```

#### TenantUpdate Schema
```python
class TenantUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=2, max_length=100)
    email: Optional[EmailStr] = None
    plan: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    model_config = ConfigDict(
        # 仅更新非空字段
        exclude_unset=True
    )
```

## 🔍 最佳实践

### 1. 服务层使用

#### 依赖注入模式
```python
# API路由中使用
@router.post("/tenants", response_model=TenantRead)
async def create_tenant(
    tenant_data: TenantCreate,
    tenant_service: TenantService = Depends(get_tenant_service),
    current_user: User = Depends(get_current_admin_user),  # 权限控制
) -> TenantRead:
    return await tenant_service.create_tenant(tenant_data)
```

#### 上下文管理
```python
# 在中间件或依赖中设置租户上下文
from app.utils.context_vars import tenant_context

async def set_tenant_context(tenant_id: UUID):
    tenant_context.set_tenant_id(tenant_id)
    
    # 后续所有操作自动应用租户隔离
    return await tenant_service.get_tenant_operations()
```

### 2. 错误处理策略

#### 统一异常处理
```python
class TenantNotFoundException(HTTPException):
    def __init__(self, tenant_id: UUID):
        super().__init__(
            status_code=404,
            detail=f"租户 {tenant_id} 不存在"
        )

class TenantEmailConflictException(HTTPException):
    def __init__(self, email: str):
        super().__init__(
            status_code=409,
            detail=f"邮箱 {email} 已被其他租户使用"
        )
```

#### 业务异常映射
```python
# 在服务层方法中
try:
    existing_tenant = await self._get_tenant_by_email(email)
    if existing_tenant:
        raise TenantEmailConflictException(email)
except TenantEmailConflictException:
    raise  # 直接抛出业务异常
except Exception as e:
    logger.error("数据库查询失败", error=str(e))
    raise HTTPException(500, "系统内部错误")
```

### 3. 性能优化

#### 数据库查询优化
```python
# 使用索引优化的查询
async def _get_tenant_by_email_optimized(self, email: str) -> Optional[Tenant]:
    """使用数据库索引的高效邮箱查询"""
    query = select(Tenant).where(Tenant.email == email)
    # 确保email字段有唯一索引
    result = await self.db.execute(query)
    return result.scalar_one_or_none()

# 批量查询优化
async def get_tenants_by_ids(self, tenant_ids: List[UUID]) -> List[Tenant]:
    """批量查询避免N+1问题"""
    query = select(Tenant).where(Tenant.id.in_(tenant_ids))
    result = await self.db.execute(query)
    return result.scalars().all()
```

#### 缓存策略
```python
from functools import lru_cache
from typing import Optional

class CachedTenantService(TenantService):
    @lru_cache(maxsize=1000)
    async def get_tenant_cached(self, tenant_id: UUID) -> Optional[TenantRead]:
        """租户信息缓存查询（适用于读多写少场景）"""
        return await super().get_tenant(tenant_id)
    
    async def update_tenant(self, tenant_id: UUID, tenant_data: TenantUpdate) -> TenantRead:
        """更新时清除缓存"""
        result = await super().update_tenant(tenant_id, tenant_data)
        # 清除对应缓存
        self.get_tenant_cached.cache_clear()
        return result
```

### 4. 安全性最佳实践

#### API密钥安全管理
```python
class SecureAPIKeyManager:
    """API密钥安全管理器"""
    
    @staticmethod
    def generate_secure_key(tenant_name: str) -> tuple[str, str]:
        """生成安全的API密钥对"""
        # 生成足够长的随机字符串
        raw_key = secrets.token_urlsafe(32)
        
        # 创建带时间戳的密钥
        timestamp = int(time.time())
        key_content = f"{tenant_name}_{timestamp}_{raw_key}"
        
        # 生成明文密钥（返回给用户）
        plain_key = f"astr_{hashlib.md5(key_content.encode()).hexdigest()[:16]}"
        
        # 生成存储密钥（数据库存储）
        stored_key = hashlib.sha256(plain_key.encode()).hexdigest()
        
        return plain_key, stored_key
    
    @staticmethod
    def verify_api_key(plain_key: str, stored_key: str) -> bool:
        """验证API密钥"""
        computed_hash = hashlib.sha256(plain_key.encode()).hexdigest()
        return computed_hash == stored_key
```

#### 权限控制集成
```python
from app.core.permissions import require_admin, require_tenant_access

class AuthorizedTenantService(TenantService):
    """带权限控制的租户服务"""
    
    @require_admin
    async def list_all_tenants(self, **kwargs) -> List[TenantRead]:
        """仅管理员可查看所有租户"""
        return await super().list_tenants(**kwargs)
    
    @require_tenant_access
    async def get_own_tenant(self, tenant_id: UUID) -> TenantRead:
        """租户仅可访问自己的信息"""
        return await super().get_tenant_by_id_with_verification(tenant_id)
```

## 🐛 故障排除

### 常见问题诊断

#### 1. 租户创建失败

**问题表现：**
```
HTTP 409 Conflict: "该邮箱已被其他租户使用"
```

**诊断步骤：**
```python
# 检查数据库中的重复邮箱
SELECT id, name, email, status FROM tenants WHERE email = '<EMAIL>';

# 检查是否为软删除的租户
SELECT * FROM tenants WHERE email = '<EMAIL>' AND status = 'DEACTIVATED';
```

**解决方案：**
```python
# 选项1：恢复已删除租户
async def restore_deactivated_tenant(self, email: str) -> Optional[TenantRead]:
    query = select(Tenant).where(
        and_(Tenant.email == email, Tenant.status == TenantStatus.DEACTIVATED)
    )
    result = await self.db.execute(query)
    tenant = result.scalar_one_or_none()
    
    if tenant:
        tenant.status = TenantStatus.ACTIVE
        await self.db.commit()
        return TenantRead.model_validate(tenant)
    
    return None

# 选项2：强制清理重复数据
async def force_cleanup_duplicate_email(self, email: str):
    # 仅在确认数据安全后使用
    query = delete(Tenant).where(
        and_(Tenant.email == email, Tenant.status == TenantStatus.DEACTIVATED)
    )
    await self.db.execute(query)
    await self.db.commit()
```

#### 2. API密钥验证失败

**问题表现：**
```
HTTP 401 Unauthorized: "Invalid API key"
```

**诊断步骤：**
```python
# 验证API密钥格式
def validate_api_key_format(api_key: str) -> bool:
    # 检查前缀
    if not api_key.startswith("astr_"):
        return False
    
    # 检查长度
    if len(api_key) < 20:
        return False
    
    return True

# 查找对应租户
async def find_tenant_by_api_key(self, api_key: str) -> Optional[Tenant]:
    key_hash = hashlib.sha256(api_key.encode()).hexdigest()
    query = select(Tenant).where(Tenant.api_key == key_hash)
    result = await self.db.execute(query)
    return result.scalar_one_or_none()
```

#### 3. 数据库连接问题

**问题表现：**
```
sqlalchemy.exc.DisconnectionError: Connection was invalidated
```

**解决方案：**
```python
# 在服务层添加连接重试机制
import asyncio
from sqlalchemy.exc import DisconnectionError

async def execute_with_retry(self, query, max_retries: int = 3):
    """带重试机制的数据库执行"""
    for attempt in range(max_retries):
        try:
            return await self.db.execute(query)
        except DisconnectionError:
            if attempt == max_retries - 1:
                raise
            
            logger.warning(f"数据库连接断开，重试 {attempt + 1}/{max_retries}")
            await asyncio.sleep(2 ** attempt)  # 指数退避
            
            # 获取新的数据库会话
            await self.db.close()
            # 这里需要重新获取db session
```

### 性能监控

#### 关键指标监控
```python
import time
from functools import wraps

def monitor_performance(operation_name: str):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                logger.info(
                    f"{operation_name}完成",
                    duration=duration,
                    status="success"
                )
                
                # 性能警告
                if duration > 2.0:
                    logger.warning(
                        f"{operation_name}执行较慢",
                        duration=duration
                    )
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(
                    f"{operation_name}失败",
                    duration=duration,
                    error=str(e),
                    status="error"
                )
                raise
                
        return wrapper
    return decorator

# 使用示例
class MonitoredTenantService(TenantService):
    @monitor_performance("租户创建")
    async def create_tenant(self, tenant_data: TenantCreate) -> TenantRead:
        return await super().create_tenant(tenant_data)
```

## 🔮 扩展规划

### 1. 功能扩展路线图

#### 短期规划（1-2个月）
- **租户配额管理：** 实现用户数量、存储空间等配额限制
- **计费集成：** 与计费系统集成，支持订阅管理
- **租户模板：** 预定义租户配置模板

#### 中期规划（3-6个月）
- **多地域支持：** 租户数据的地域化存储
- **备份恢复：** 租户数据的自动备份和恢复
- **审计日志：** 完整的租户操作审计追踪

#### 长期规划（6个月以上）
- **自助服务：** 租户自助管理界面
- **高级分析：** 租户使用情况的深度分析
- **API版本管理：** 支持多版本API密钥

### 2. 技术优化方向

#### 数据库优化
```python
# 分表策略规划
class ShardedTenantService(TenantService):
    """支持分表的租户服务"""
    
    def get_shard_key(self, tenant_id: UUID) -> str:
        """根据租户ID计算分片键"""
        return f"tenant_shard_{hash(str(tenant_id)) % 16:02d}"
    
    async def get_tenant_from_shard(self, tenant_id: UUID) -> Optional[TenantRead]:
        """从对应分片查询租户"""
        shard_key = self.get_shard_key(tenant_id)
        # 实现分片查询逻辑
        pass
```

#### 缓存架构
```python
# Redis缓存集成
import redis.asyncio as redis

class CachedTenantService(TenantService):
    def __init__(self, db: AsyncSession, cache: redis.Redis):
        super().__init__(db)
        self.cache = cache
    
    async def get_tenant_cached(self, tenant_id: UUID) -> Optional[TenantRead]:
        # 先查缓存
        cache_key = f"tenant:{tenant_id}"
        cached_data = await self.cache.get(cache_key)
        
        if cached_data:
            return TenantRead.model_validate_json(cached_data)
        
        # 缓存未命中，查询数据库
        tenant = await super().get_tenant(tenant_id)
        if tenant:
            # 写入缓存，TTL 1小时
            await self.cache.setex(
                cache_key, 
                3600, 
                tenant.model_dump_json()
            )
        
        return tenant
```

#### 事件驱动架构
```python
# 租户事件发布
from app.events import EventBus

class EventDrivenTenantService(TenantService):
    def __init__(self, db: AsyncSession, event_bus: EventBus):
        super().__init__(db)
        self.event_bus = event_bus
    
    async def create_tenant(self, tenant_data: TenantCreate) -> TenantRead:
        tenant = await super().create_tenant(tenant_data)
        
        # 发布租户创建事件
        await self.event_bus.publish("tenant.created", {
            "tenant_id": str(tenant.id),
            "tenant_name": tenant.name,
            "plan": tenant.plan,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        return tenant
```

---

**文档维护：** 本文档应与代码变更同步更新，确保技术方案的准确性和完整性。
**更新日期：** 2024年12月17日
**版本：** v1.0.0
**维护者：** 技术文档专家 

---

