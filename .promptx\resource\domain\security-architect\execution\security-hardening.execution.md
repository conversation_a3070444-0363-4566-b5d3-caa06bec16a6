<execution>
  <constraint>
    ## 客观安全限制
    - **法规合规约束**：必须符合GDPR、SOX、PCI-DSS等相关法规要求
    - **技术架构约束**：现有系统架构和技术栈的安全能力边界
    - **资源预算限制**：安全投入的人力、时间、成本预算约束
    - **业务连续性要求**：安全措施不得影响正常业务运营
    - **技术债务现状**：历史遗留的安全债务和技术限制
    - **第三方依赖约束**：外部服务和组件的安全能力限制
  </constraint>

  <rule>
    ## 安全加固强制规则
    - **零信任原则**：永不信任，始终验证，所有访问都需要验证授权
    - **最小权限原则**：用户和系统只获得完成任务所需的最小权限
    - **深度防御原则**：多层防护，单点故障不导致整体安全失效
    - **安全左移原则**：在设计和开发阶段就考虑安全，而非事后补救
    - **持续监控原则**：安全是动态过程，需要持续监控和改进
    - **数据分类保护**：根据数据敏感性级别实施相应的保护措施
    - **事件响应就绪**：必须有完整的安全事件响应计划和程序
    - **合规性覆盖**：所有安全措施必须满足相关法规和标准要求
  </rule>

  <guideline>
    ## 安全加固指导原则
    - **风险驱动决策**：基于风险评估结果优化资源配置和措施选择
    - **业务安全平衡**：在安全性和业务效率之间寻找最佳平衡点
    - **渐进式改进**：分阶段实施安全改进，避免一次性大规模变更
    - **自动化优先**：优先使用自动化工具和流程提高安全效率
    - **透明化管理**：安全状态和风险对相关方透明可见
    - **持续学习**：跟踪最新威胁情报和安全技术发展趋势
    - **团队协作**：安全是全员责任，需要跨团队协作配合
    - **文档驱动**：所有安全措施都要有完整的文档和操作指南
  </guideline>

  <process>
    ## 安全加固执行流程

    ### Phase 1: 安全态势评估
    
    #### 1.1 资产清单梳理
    ```
    目标：建立完整的资产清单和分类
    
    关键活动：
    - 应用系统清单：所有应用、服务、API的详细信息
    - 基础设施清单：服务器、网络设备、存储、云资源
    - 数据资产清单：数据库、文件、备份、日志
    - 人员权限清单：用户账号、角色、权限矩阵
    - 第三方服务清单：外部依赖、供应商、集成点
    
    输出成果：
    ✅ 完整资产清单表
    ✅ 资产分类和价值评估
    ✅ 资产依赖关系图
    ```

    #### 1.2 威胁建模分析
    ```
    目标：识别和评估面临的安全威胁
    
    分析维度：
    - STRIDE威胁分类：欺骗、篡改、抵赖、信息泄露、拒绝服务、权限提升
    - 攻击面分析：网络、应用、数据、物理、人员
    - 威胁行为者：内部人员、外部攻击者、国家级APT、脚本小子
    - 攻击路径：从初始访问到目标达成的完整路径
    
    输出成果：
    ✅ 威胁模型图
    ✅ 攻击路径分析报告
    ✅ 威胁优先级排序
    ```

    #### 1.3 漏洞风险评估
    ```
    目标：全面评估当前安全风险状况
    
    评估方法：
    - 自动化扫描：漏洞扫描、配置检查、代码审计
    - 人工审计：架构审查、代码审计、配置审计
    - 渗透测试：模拟攻击验证安全控制有效性
    - 合规性检查：对照标准要求评估合规差距
    
    风险计算：
    风险值 = 威胁可能性 × 漏洞严重性 × 资产价值
    
    输出成果：
    ✅ 漏洞清单和风险评级
    ✅ 风险热图和趋势分析
    ✅ 修复优先级建议
    ```

    ### Phase 2: 安全控制设计

    #### 2.1 安全架构设计
    ```
    目标：设计多层次的安全防护架构
    
    架构层次：
    - 网络安全层：防火墙、IDS/IPS、网络隔离、VPN
    - 主机安全层：端点保护、系统加固、补丁管理
    - 应用安全层：WAF、认证授权、加密传输、输入验证
    - 数据安全层：数据分类、加密存储、访问控制、DLP
    - 管理安全层：身份管理、特权管理、审计监控
    
    设计原则：
    - 零信任架构：验证每个连接和交易
    - 微分段：最小化爆炸半径
    - 深度防御：多层控制措施
    - 弹性设计：失效时优雅降级
    ```

    #### 2.2 控制措施选择
    ```
    目标：选择最适合的安全控制措施
    
    控制分类：
    - 预防性控制：防火墙、访问控制、加密、培训
    - 检测性控制：监控、日志、入侵检测、漏洞扫描
    - 响应性控制：事件响应、隔离、恢复、取证
    - 恢复性控制：备份、灾难恢复、业务连续性
    
    选择标准：
    - 风险缓解效果
    - 实施成本和复杂度
    - 对业务的影响
    - 技术可行性
    - 维护和运营成本
    ```

    ### Phase 3: 安全措施实施

    #### 3.1 Kubernetes安全加固
    ```
    关键配置项：
    
    # Pod安全策略
    - 禁用特权容器：securityContext.privileged: false
    - 只读根文件系统：securityContext.readOnlyRootFilesystem: true
    - 非root用户运行：securityContext.runAsNonRoot: true
    - 删除危险能力：securityContext.capabilities.drop: ["ALL"]
    - 资源限制：resources.limits.memory/cpu
    
    # 网络安全策略
    - NetworkPolicy：Pod间通信控制
    - 服务网格：Istio/Linkerd加密通信
    - Ingress安全：TLS终止、WAF保护
    
    # RBAC权限控制
    - ServiceAccount：最小权限原则
    - Role/ClusterRole：细粒度权限定义
    - RoleBinding：权限分配审计
    
    # 镜像安全
    - 镜像扫描：Trivy/Clair漏洞检测
    - 镜像签名：Cosign/Notary镜像验证
    - 准入控制：OPA Gatekeeper策略执行
    ```

    #### 3.2 容器安全加固
    ```
    Docker安全配置：
    
    # Dockerfile安全实践
    - 最小化基础镜像：FROM scratch/distroless
    - 非root用户：USER 1000:1000
    - 只读文件系统：--read-only
    - 删除包管理器：RUN rm -rf /var/lib/apt/lists/*
    - 多阶段构建：减少攻击面
    
    # 运行时安全
    - 资源限制：--memory --cpus
    - 安全选项：--security-opt no-new-privileges
    - 网络隔离：--network none/custom
    - 日志驱动：--log-driver syslog
    
    # 镜像仓库安全
    - Harbor：企业级镜像仓库
    - 漏洞扫描：定期扫描和更新
    - 访问控制：基于角色的访问控制
    ```

    ### Phase 4: 监控检测部署

    #### 4.1 安全监控体系
    ```
    监控层次：
    
    # 基础设施监控
    - 系统资源：CPU、内存、磁盘、网络异常
    - 网络流量：异常连接、大流量、端口扫描
    - 系统调用：文件访问、进程创建、权限变更
    
    # 应用安全监控
    - 认证事件：登录失败、权限提升、异常访问
    - 应用日志：错误率、响应时间、异常模式
    - API安全：频率限制、参数验证、异常调用
    
    # 数据安全监控
    - 数据访问：敏感数据访问、批量下载、异常查询
    - 数据传输：加密状态、传输路径、目标验证
    - 数据变更：增删改操作、权限变更、配置修改
    ```

    #### 4.2 威胁检测规则
    ```
    检测规则设计：
    
    # 基于签名的检测
    - 已知恶意文件哈希
    - 恶意域名和IP地址
    - 攻击特征字符串
    
    # 基于行为的检测
    - 异常登录模式
    - 权限滥用行为
    - 数据泄露迹象
    - 横向移动活动
    
    # 基于机器学习的检测
    - 用户行为基线建模
    - 网络流量异常检测
    - 系统调用序列分析
    ```

    ### Phase 5: 事件响应准备

    #### 5.1 事件响应计划
    ```
    响应流程：
    
    1. 事件发现：监控告警、人工报告、外部通知
    2. 初始评估：威胁等级、影响范围、紧急程度
    3. 遏制措施：隔离系统、阻断威胁、保护证据
    4. 根因分析：攻击路径、影响评估、漏洞分析
    5. 消除威胁：清除恶意代码、修复漏洞、加固系统
    6. 恢复业务：系统恢复、服务重启、验证安全
    7. 经验总结：事件报告、改进措施、预防方案
    
    角色职责：
    - 事件指挥官：统一指挥协调
    - 技术分析师：技术分析和处置
    - 通信联络员：内外部沟通协调
    - 法务合规：法律和合规事务
    ```

    ### Phase 6: 合规性验证

    #### 6.1 合规检查清单
    ```
    关键合规要求：
    
    # 数据保护合规（GDPR）
    ☐ 数据处理合法性基础
    ☐ 数据主体权利保护
    ☐ 数据泄露通知机制
    ☐ 数据保护影响评估
    ☐ 数据处理记录维护
    
    # 财务合规（SOX）
    ☐ 内部控制设计有效性
    ☐ 财务报告相关IT控制
    ☐ 变更管理程序
    ☐ 访问控制和授权
    ☐ 数据备份和恢复
    
    # 支付卡合规（PCI-DSS）
    ☐ 网络安全和防火墙
    ☐ 密码和加密要求
    ☐ 访问控制措施
    ☐ 监控和测试程序
    ☐ 信息安全政策
    ```
  </process>

  <criteria>
    ## 安全加固评价标准

    ### 风险缓解效果
    - ✅ 高风险漏洞修复率 ≥ 95%
    - ✅ 中风险漏洞修复率 ≥ 80%
    - ✅ 整体风险等级降低 ≥ 2个级别
    - ✅ 攻击面减少 ≥ 30%

    ### 控制措施有效性
    - ✅ 预防控制覆盖率 ≥ 90%
    - ✅ 检测控制响应时间 ≤ 15分钟
    - ✅ 响应控制处置时间 ≤ 4小时
    - ✅ 恢复控制完成时间 ≤ 24小时

    ### 合规性达成度
    - ✅ 关键合规要求满足率 = 100%
    - ✅ 一般合规要求满足率 ≥ 95%
    - ✅ 合规证据完整性 = 100%
    - ✅ 审计发现问题数量 ≤ 5个

    ### 业务影响度
    - ✅ 业务中断时间 ≤ 2小时
    - ✅ 性能影响 ≤ 5%
    - ✅ 用户体验影响 ≤ 10%
    - ✅ 运营成本增加 ≤ 15%

    ### 持续改进能力
    - ✅ 监控覆盖率 ≥ 95%
    - ✅ 威胁检测准确率 ≥ 85%
    - ✅ 误报率 ≤ 10%
    - ✅ 安全事件处理成功率 ≥ 95%
  </criteria>
</execution> 