# 📖 技术文档：权限模块 (permissions.py)

## 🎯 1. 模块概述

**功能**：提供基于角色的权限检查功能。

**核心职责**：
- **权限检查器**：提供`require_role`和`require_permission`装饰器，用于保护API端点。
- **租户隔离**：确保权限检查与租户隔离。
- **自定义权限**：支持自定义权限逻辑。

## 🚀 2. 快速使用

### 2.1 保护API端点

使用`require_role`装饰器保护需要特定角色的端点：

```python
from app.core.permissions import require_role

@router.get("/admin", dependencies=[Depends(require_role("admin"))])
async def admin_only_endpoint():
    # ...
```

使用`require_permission`装饰器保护需要特定权限的端点：

```python
from app.core.permissions import require_permission

@router.post("/users", dependencies=[Depends(require_permission("user", "create"))])
async def create_user():
    # ...
```

## 🏗️ 3. 架构设计

### 3.1 依赖关系

```mermaid
graph TD
    A[API Endpoints] --> B(require_role / require_permission)
    B --> C(get_current_user)
    C --> D(User Model)
    C --> E(Role Model)
    C --> F(Permission Model)
```

### 3.2 数据流

**权限检查流程**：
1. **API请求**：请求到达受保护的端点。
2. **依赖注入**：FastAPI调用权限检查装饰器。
3. **获取用户**：装饰器调用`get_current_user`获取当前用户信息（包括角色和权限）。
4. **权限验证**：检查用户是否具有所需的角色或权限。
5. **访问控制**：如果验证通过，执行端点逻辑；否则，返回403 Forbidden。

## 🧪 5. 测试策略

- **单元测试**：`tests/unit/test_permissions.py`

## 💡 6. 维护与扩展

- **动态权限**：可以扩展为支持基于资源所有权的动态权限。
- **权限缓存**：可以将用户的权限信息缓存起来，以提高性能。
- **自定义错误**：可以自定义权限不足时的错误响应。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 