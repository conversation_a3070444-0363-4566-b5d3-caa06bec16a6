"""
TenantService 单元测试

基于实际的TenantService API接口进行测试
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from uuid import UUID, uuid4
from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import Select
from datetime import datetime

from app.models.tenant import Tenant, TenantPlan, TenantStatus
from app.services.tenant_service import TenantService
from app.schemas.tenant import TenantCreate, TenantRead, TenantUpdate, TenantStatusUpdate


@pytest.fixture
def mock_db_session():
    """
    创建一个更健壮的 mock 异步数据库会话。
    明确区分 execute (async) 和其结果 (sync)。
    """
    session = AsyncMock(spec=AsyncSession)
    execute_result = MagicMock()
    execute_result.scalar_one_or_none.return_value = None  # Default behavior
    execute_result.scalars.return_value.all.return_value = [] # Default for scalars().all()
    session.execute = AsyncMock(return_value=execute_result)
    return session


@pytest.fixture
def tenant_service(mock_db_session):
    """TenantService实例, 使用新的mock_db_session"""
    return TenantService(mock_db_session)


@pytest.fixture
def sample_tenant_create():
    """示例租户创建数据"""
    return TenantCreate(name="Test Corporation", email="<EMAIL>")


@pytest.fixture
def mock_tenant_instance():
    """创建一个属性完整的 mock Tenant 实例"""
    tenant = Mock(spec=Tenant)
    tenant.id = uuid4()
    tenant.name = "Test Tenant"
    tenant.email = "<EMAIL>"
    tenant.plan = TenantPlan.BASIC
    tenant.status = TenantStatus.ACTIVE
    tenant.created_at = datetime.utcnow()
    tenant.updated_at = datetime.utcnow()
    tenant.is_active = True
    tenant.display_name = "Test Tenant"
    tenant.metadata = {}
    return tenant


class TestTenantService:
    """TenantService 测试类"""

    @pytest.mark.asyncio
    async def test_create_tenant_success(
        self, tenant_service, mock_db_session, sample_tenant_create, mock_tenant_instance
    ):
        """测试成功创建租户"""
        # Arrange - 确保邮箱和名称不存在
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

        # Mock a successful return from model_validate
        with patch(
            "app.services.tenant_service.TenantRead.model_validate",
            return_value=mock_tenant_instance
        ):
            # Act
            result = await tenant_service.create_tenant(sample_tenant_create)

            # Assert
            assert result is not None
            assert result.id == mock_tenant_instance.id
            mock_db_session.add.assert_called_once()
            mock_db_session.commit.assert_awaited_once()
            mock_db_session.refresh.assert_awaited_once()

    @pytest.mark.asyncio
    async def test_create_tenant_duplicate_email(
        self, tenant_service, mock_db_session, sample_tenant_create
    ):
        """测试重复邮箱创建租户失败"""
        # Arrange - 模拟已存在的租户
        existing_tenant = Mock()
        existing_tenant.email = sample_tenant_create.email
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = existing_tenant

        # Act & Assert
        with pytest.raises(Exception):  # 应该抛出HTTPException
            await tenant_service.create_tenant(sample_tenant_create)

    @pytest.mark.asyncio
    async def test_get_tenant_success(self, tenant_service, mock_db_session):
        """测试通过ID获取租户成功"""
        # Arrange
        tenant_id = uuid4()
        mock_tenant = Mock()
        mock_tenant.id = tenant_id
        mock_tenant.name = "Test Tenant"
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant

        with patch(
            "app.services.tenant_service.TenantRead.model_validate"
        ) as mock_validate:
            mock_validate.return_value = Mock(id=tenant_id, name="Test Tenant")

            # Act
            result = await tenant_service.get_tenant(tenant_id)

            # Assert
            assert result is not None
            assert result.id == tenant_id

    @pytest.mark.asyncio
    async def test_get_tenant_not_found(self, tenant_service, mock_db_session):
        """测试通过ID获取租户失败（不存在）"""
        # Arrange
        tenant_id = uuid4()
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

        # Act
        result = await tenant_service.get_tenant(tenant_id)

        # Assert
        assert result is None

    @pytest.mark.asyncio
    async def test_get_tenant_by_id_with_verification_success(
        self, tenant_service, mock_db_session
    ):
        """测试通过ID获取租户并验证存在成功"""
        # Arrange
        tenant_id = uuid4()
        mock_tenant_read = Mock()
        mock_tenant_read.id = tenant_id

        with patch.object(tenant_service, "get_tenant", return_value=mock_tenant_read):
            # Act
            result = await tenant_service.get_tenant_by_id_with_verification(tenant_id)

            # Assert
            assert result is not None
            assert result.id == tenant_id

    @pytest.mark.asyncio
    async def test_get_tenant_by_id_with_verification_not_found(
        self, tenant_service, mock_db_session
    ):
        """测试通过ID获取租户并验证存在失败"""
        # Arrange
        tenant_id = uuid4()

        with patch.object(tenant_service, "get_tenant", return_value=None):
            # Act & Assert
            with pytest.raises(Exception):  # 应该抛出HTTPException
                await tenant_service.get_tenant_by_id_with_verification(tenant_id)

    @pytest.mark.asyncio
    async def test_update_tenant_success(self, tenant_service, mock_db_session, mock_tenant_instance):
        """测试更新租户成功"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        update_data = TenantUpdate(name="Updated Corporation")

        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant_instance

        # Act
        result = await tenant_service.update_tenant(tenant_id, update_data)

        # Assert
        assert result is not None
        assert result.name == "Updated Corporation"
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_tenant_success(self, tenant_service, mock_db_session, mock_tenant_instance):
        """测试删除租户成功"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant_instance

        # Mock _count_tenant_users 返回0表示无用户
        with patch.object(tenant_service, "_count_tenant_users", return_value=0):
            # Act
            result = await tenant_service.delete_tenant(tenant_id)

            # Assert
            assert result is True
            mock_db_session.commit.assert_called()

    @pytest.mark.asyncio
    async def test_list_tenants_success(self, tenant_service, mock_db_session):
        """测试列出租户成功"""
        # Arrange
        mock_tenants = [Mock(id=uuid4(), name=f"Tenant {i}") for i in range(3)]
        mock_db_session.execute.return_value.scalars.return_value.all.return_value = (
            mock_tenants
        )

        with patch(
            "app.services.tenant_service.TenantRead.model_validate"
        ) as mock_validate:
            mock_validate.side_effect = [
                Mock(id=t.id, name=t.name) for t in mock_tenants
            ]

            # Act
            result = await tenant_service.list_tenants()

            # Assert
            assert len(result) == 3
            assert all(hasattr(tenant, "id") for tenant in result)

    @pytest.mark.asyncio
    async def test_get_tenant_statistics_success(self, tenant_service, mock_db_session):
        """测试获取租户统计信息成功"""
        # Arrange
        tenant_id = uuid4()
        mock_tenant = Mock()
        mock_tenant.id = tenant_id
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant

        # 模拟统计查询返回
        mock_db_session.execute.return_value.scalar.return_value = 10

        # Act
        result = await tenant_service.get_tenant_statistics(tenant_id)

        # Assert
        assert result is not None
        assert isinstance(result, dict)

    @pytest.mark.asyncio
    async def test_update_tenant_status_success(self, tenant_service, mock_db_session):
        """测试更新租户状态成功"""
        # Arrange
        tenant_id = uuid4()
        mock_tenant = Mock()
        mock_tenant.id = tenant_id
        mock_tenant.status = "ACTIVE"
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant

        with patch(
            "app.services.tenant_service.TenantRead.model_validate"
        ) as mock_validate:
            mock_validate.return_value = Mock(id=tenant_id, status="SUSPENDED")

            # Act
            result = await tenant_service.update_tenant_status(tenant_id, False)

            # Assert
            assert result is not None
            mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_regenerate_api_key_success(self, tenant_service, mock_db_session):
        """测试重新生成API Key成功"""
        # Arrange
        tenant_id = uuid4()
        mock_tenant = Mock()
        mock_tenant.id = tenant_id
        mock_tenant.API_KEY = "test_api_key_for_testing"
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant

        with patch(
            "app.services.tenant_service.Tenant.generate_api_key",
            return_value="new_api_key",
        ):
            # Act
            result = await tenant_service.regenerate_api_key(tenant_id)

            # Assert
            assert result == "new_api_key"
            mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_service_initialization(self, mock_db_session):
        """测试服务初始化"""
        # Act
        service = TenantService(mock_db_session)

        # Assert
        assert service.db == mock_db_session
        assert hasattr(service, "create_tenant")
        assert hasattr(service, "get_tenant")
        assert hasattr(service, "update_tenant")
        assert hasattr(service, "delete_tenant")
        assert hasattr(service, "list_tenants")
        assert hasattr(service, "get_tenant_statistics")
        assert hasattr(service, "update_tenant_status")
        assert hasattr(service, "regenerate_api_key")


class TestGetTenant:
    """测试获取租户信息"""

    @pytest.mark.asyncio
    async def test_get_tenant_success(self, tenant_service, mock_db_session, mock_tenant_instance):
        """测试成功获取租户信息"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = (
            mock_tenant_instance
        )

        # Act
        result = await tenant_service.get_tenant(tenant_id)

        # Assert
        assert result is not None
        assert result.id == tenant_id
        assert result.name == mock_tenant_instance.name

    @pytest.mark.asyncio
    async def test_get_tenant_not_found(self, tenant_service, mock_db_session):
        """测试获取不存在的租户"""
        # Arrange
        tenant_id = uuid4()
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

        # Act
        result = await tenant_service.get_tenant(tenant_id)

        # Assert
        assert result is None

    @pytest.mark.asyncio
    async def test_get_tenant_by_id_with_verification_not_found(
        self, tenant_service, mock_db_session
    ):
        """测试 get_tenant_by_id_with_verification 在租户不存在时抛出404异常"""
        # Arrange
        tenant_id = uuid4()
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

        with patch.object(tenant_service, "get_tenant", return_value=None):
            # Act & Assert
            with pytest.raises(HTTPException) as exc_info:
                await tenant_service.get_tenant_by_id_with_verification(tenant_id)

            assert exc_info.value.status_code == 404
            assert "租户不存在" in exc_info.value.detail


class TestUpdateTenant:
    """测试更新租户信息"""

    @pytest.mark.asyncio
    async def test_update_tenant_success(self, tenant_service, mock_db_session, mock_tenant_instance):
        """测试成功更新租户信息"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        tenant_update_data = TenantUpdate(name="New Name")

        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant_instance

        # Act
        result = await tenant_service.update_tenant(tenant_id, tenant_update_data)

        # Assert
        assert result is not None
        assert result.name == "New Name"
        mock_tenant_instance.name = "New Name" # 模拟DB更新
        mock_db_session.commit.assert_awaited_once()
        mock_db_session.refresh.assert_awaited_once_with(mock_tenant_instance)

    @pytest.mark.asyncio
    async def test_update_tenant_not_found(self, tenant_service, mock_db_session):
        """测试更新一个不存在的租户 (404)"""
        # Arrange
        tenant_id = uuid4()
        tenant_update_data = TenantUpdate(name="New Name")
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await tenant_service.update_tenant(tenant_id, tenant_update_data)

        assert exc_info.value.status_code == 404
        mock_db_session.rollback.assert_not_awaited()

    @pytest.mark.asyncio
    async def test_update_tenant_email_conflict(self, tenant_service, mock_db_session, mock_tenant_instance):
        """测试更新租户邮箱时发生冲突 (400)"""
        # Arrange
        tenant_id = uuid4() # new tenant to update
        conflicting_tenant = mock_tenant_instance # this one already exists
        tenant_update_data = TenantUpdate(email=conflicting_tenant.email)

        # Mock the tenant we are trying to update
        updating_tenant = Mock(spec=Tenant)
        updating_tenant.id = tenant_id
        updating_tenant.email = "<EMAIL>"
        mock_db_session.execute.return_value.scalar_one_or_none.side_effect = [
            updating_tenant,
            conflicting_tenant,
        ]

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await tenant_service.update_tenant(tenant_id, tenant_update_data)

        assert exc_info.value.status_code == 400

    @pytest.mark.asyncio
    async def test_update_tenant_db_error(self, tenant_service, mock_db_session, mock_tenant_instance):
        """测试更新租户时发生数据库错误 (500)"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        tenant_update_data = TenantUpdate(name="New Name")
        mock_db_session.commit.side_effect = Exception("DB error")

        # Mock tenant exists
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant_instance

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await tenant_service.update_tenant(tenant_id, tenant_update_data)

        assert exc_info.value.status_code == 500
        mock_db_session.rollback.assert_awaited_once()


class TestDeleteTenant:
    """测试删除租户信息"""

    @pytest.mark.asyncio
    async def test_delete_tenant_hard_delete_success(
        self, tenant_service, mock_db_session, mock_tenant_instance
    ):
        """测试成功硬删除租户 (无关联用户)"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant_instance

        with patch.object(
            tenant_service, "_count_tenant_users", return_value=0
        ) as mock_count_users:
            # Act
            result = await tenant_service.delete_tenant(tenant_id)

            # Assert
            assert result is True
            mock_count_users.assert_awaited_once_with(tenant_id)
            mock_db_session.delete.assert_called_once_with(mock_tenant_instance)
            mock_db_session.commit.assert_awaited_once()

    @pytest.mark.asyncio
    async def test_delete_tenant_soft_delete_success(
        self, tenant_service, mock_db_session, mock_tenant_instance
    ):
        """测试成功软删除租户 (有关联用户)"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant_instance

        with patch.object(
            tenant_service, "_count_tenant_users", return_value=5
        ) as mock_count_users:
            # Act
            result = await tenant_service.delete_tenant(tenant_id)

            # Assert
            assert result is True
            mock_count_users.assert_awaited_once_with(tenant_id)
            assert mock_tenant_instance.status == TenantStatus.DEACTIVATED
            mock_db_session.commit.assert_awaited_once()
            mock_db_session.delete.assert_not_called()  # 确保没有调用硬删除

    @pytest.mark.asyncio
    async def test_delete_tenant_not_found(self, tenant_service, mock_db_session):
        """测试删除不存在的租户 (404)"""
        # Arrange
        tenant_id = uuid4()
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await tenant_service.delete_tenant(tenant_id)

        assert exc_info.value.status_code == 404
        mock_db_session.rollback.assert_not_awaited()

    @pytest.mark.asyncio
    async def test_delete_tenant_db_error(self, tenant_service, mock_db_session, mock_tenant_instance):
        """测试删除租户时发生数据库错误 (500)"""
        # Arrange
        tenant_id = mock_tenant_instance.id
        mock_db_session.delete.side_effect = Exception("DB error")

        # Mock tenant exists
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_tenant_instance

        with patch.object(tenant_service, "_count_tenant_users", return_value=0):
            # Act & Assert
            with pytest.raises(HTTPException) as exc_info:
                await tenant_service.delete_tenant(tenant_id)

            assert exc_info.value.status_code == 500
            mock_db_session.rollback.assert_awaited_once()
