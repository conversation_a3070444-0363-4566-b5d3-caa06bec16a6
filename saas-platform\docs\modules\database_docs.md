# 📖 技术文档：数据库模块 (database.py)

## 🎯 1. 模块概述

**功能**：提供数据库连接配置和会话管理。

**核心职责**：
- **数据库引擎**：根据环境创建异步数据库引擎（SQLite或PostgreSQL）。
- **会话管理**：提供异步会话工厂和FastAPI依赖注入函数。
- **模型基类**：提供所有SQLAlchemy模型继承的`Base`类。
- **健康检查**：提供检查数据库连接状态的功能。

## 🚀 2. 快速使用

### 2.1 获取数据库会话

在API端点中通过依赖注入获取数据库会话：

```python
from app.core.database import get_db

@router.get("/")
async def read_root(db: AsyncSession = Depends(get_db)):
    # ...
```

### 2.2 定义数据模型

```python
from app.core.database import Base

class MyModel(Base):
    __tablename__ = "my_table"
    # ...
```

## 🏗️ 3. 架构设计

### 3.1 关键组件

- **`engine`**: SQLAlchemy异步数据库引擎。
- **`AsyncSessionLocal`**: 异步会话工厂。
- **`get_db()`**: FastAPI依赖注入函数，用于获取数据库会话。
- **`Base`**: 所有数据模型的声明性基类。

### 3.2 环境配置

- **测试环境**: 自动使用内存SQLite数据库。
- **生产环境**: 使用`settings.DATABASE_URL`配置的PostgreSQL数据库。

## 🧪 4. 测试策略

- **单元测试**：`tests/unit/test_database.py`

## 💡 5. 维护与扩展

- **读写分离**：可以扩展为支持读写分离的数据库架构。
- **多数据库**：可以添加对多个数据库连接的支持。
- **连接池监控**：可以添加对数据库连接池的监控。

---

**最后更新**: 2025-06-18 | **负责人**: AI-Copilot 